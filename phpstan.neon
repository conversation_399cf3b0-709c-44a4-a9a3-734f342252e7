includes:
  - phar://phpstan.phar/conf/bleedingEdge.neon
  - vendor/phpstan/phpstan-deprecation-rules/rules.neon
  - vendor/phpstan/phpstan-strict-rules/rules.neon
  - vendor/phpstan/phpstan-symfony/extension.neon
  - vendor/phpstan/phpstan-symfony/rules.neon
  - vendor/phpstan/phpstan-doctrine/extension.neon
  - vendor/phpstan/phpstan-doctrine/rules.neon
parameters:
  level: max
  phpVersion:
    min: 80400
    max: 80400
  strictRules:
    allRules: true
  symfony:
    containerXmlPath: var/cache/dev/App_Shared_Infrastructure_KernelDevDebugContainer.xml
  scanDirectories:
    - var/cache/dev/Symfony/Config
  paths:
    - app
    # - libs/bridge/http-factory-bundle/src
    # - libs/bridge/request-mapper-bundle/src
    # - libs/bridge/response-mapper-bundle/src
    # - libs/bridge/type-lang-mapper-bundle/src
    # - libs/component/http-factory/src
    # - libs/component/set/src
  tmpDir: vendor/.cache.phpstan
  tipsOfTheDay: false
  checkMissingCallableSignature: true
  ignoreErrors:
    # Doctrine type errors
    - '/database can contain int but property expects int<\-32768, 32767>/'
    - '/database can contain string but property expects non\-empty\-string/'
    #- '/database can contain string but property expects non\-empty\-lowercase\-string/'
    - '/database can contain string\|null but property expects non\-empty\-string\|null/'
    - '/database can contain string\|null but property expects \(lowercase\-string&non\-empty\-string\)\|null/'
    # Do not report DbC invariants and preconditions
    - identifier: function.alreadyNarrowedType
    - identifier: instanceof.alwaysTrue
    - identifier: smaller.alwaysFalse
    # - identifier: greater.alwaysFalse
    # - identifier: booleanOr.alwaysFalse
  excludePaths:
    #
    # --------------------------------------------------------------------------
    #   Blog
    # --------------------------------------------------------------------------
    # Blog: Skip all fixtures
    - app/Blog/Infrastructure/Persistence/Doctrine/Fixture

    #
    # --------------------------------------------------------------------------
    #   Documentation
    # --------------------------------------------------------------------------
    # Documentation: Skip all fixtures
    - app/Documentation/Infrastructure/Persistence/Doctrine/Fixture

    #
    # --------------------------------------------------------------------------
    #   Search
    # --------------------------------------------------------------------------
    # Search: Known deprecations usage
    - app/Search/Infrastructure/Persistence/Doctrine/Listener/AddHiddenSearchVectorColumn.php

    #
    # --------------------------------------------------------------------------
    #   Shared
    # --------------------------------------------------------------------------
    # Shared: Skip faker extensions
    - app/Shared/Infrastructure/Faker
    # Shared: Skip migrations
    - app/Shared/Infrastructure/Persistence/Doctrine/Migration
    # Shared: Skip (optionally) testing commands
    - app/Shared/Presentation/Console/TestCommand.php (?)
