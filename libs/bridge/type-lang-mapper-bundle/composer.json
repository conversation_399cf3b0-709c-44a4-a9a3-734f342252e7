{"name": "local/type-lang-mapper-bundle", "version": "1.0.0", "type": "symfony-bundle", "license": "MIT", "require": {"php": "^8.4", "symfony/http-kernel": "^6.4|^7.0", "type-lang/mapper": "^0.4", "type-lang/phpdoc-standard-tags": "^1.0"}, "autoload": {"psr-4": {"Local\\Bridge\\TypeLang\\": "src"}}, "require-dev": {"phpunit/phpunit": "^12.0"}, "autoload-dev": {"psr-4": {"Local\\Bridge\\TypeLang\\Tests\\": "tests"}}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev", "dev-main": "1.0.x-dev"}}, "config": {"sort-packages": true, "platform-check": true, "bin-compat": "full", "optimize-autoloader": true, "preferred-install": {"*": "dist"}}, "minimum-stability": "dev", "prefer-stable": true}