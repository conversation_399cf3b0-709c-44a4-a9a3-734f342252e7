{"name": "local/http-factory-component", "version": "1.0.0", "type": "library", "require": {"php": "^8.4", "symfony/http-foundation": "^6.4|^7.0"}, "autoload": {"psr-4": {"Local\\Component\\HttpFactory\\": "src"}}, "require-dev": {"phpunit/phpunit": "^12.0", "rybakit/msgpack": "^0.9|^1.0", "symfony/yaml": "^7.0"}, "autoload-dev": {"psr-4": {"Local\\Component\\HttpFactory\\Tests\\": "tests"}}, "suggest": {"ext-json": "Adds JSON decoder and encoder", "symfony/yaml": "Adds YAML decoder and encoder", "rybakit/msgpack": "Adds MSGPACK decoder and encoder"}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev", "dev-main": "1.0.x-dev"}}, "config": {"sort-packages": true, "platform-check": true, "bin-compat": "full", "optimize-autoloader": true, "preferred-install": {"*": "dist"}}, "minimum-stability": "dev", "prefer-stable": true}