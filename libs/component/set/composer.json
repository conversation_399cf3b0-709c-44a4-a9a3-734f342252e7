{"name": "local/set", "version": "1.0.0", "type": "library", "require": {"php": "^8.4", "doctrine/collections": "^2.2"}, "autoload": {"psr-4": {"Local\\Component\\Set\\": "src"}}, "require-dev": {"phpunit/phpunit": "^11.5"}, "autoload-dev": {"psr-4": {"Local\\Component\\Set\\Tests\\": "tests"}}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev", "dev-main": "1.0.x-dev"}}, "config": {"sort-packages": true, "platform-check": true, "bin-compat": "full", "optimize-autoloader": true, "preferred-install": {"*": "dist"}}, "minimum-stability": "dev", "prefer-stable": true}