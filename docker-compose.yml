x-environment: &environment
  env_file:
    - path: ./.env.example
      required: true
    - path: ./.env
      required: false

services:
  angie:
    <<: *environment
    build:
      context: .
      dockerfile: docker/angie/Dockerfile
    container_name: boson-angie
    volumes:
      - ./:/home/<USER>/bosonphp.com:ro
    ports:
      - "${APP_HTTP_PUBLIC_PORT:-80}:80"
      - "${APP_HTTPS_PUBLIC_PORT:-443}:443"
    depends_on:
      - php
    profiles:
      - ''
      - backend
    networks:
      frontend:
        ipv4_address: ************

  php:
    <<: *environment
    image: ghcr.io/boson-php/bosonphp.com:${APP_VERSION:-0.0.1}
    build:
      context: .
      dockerfile: docker/php/Dockerfile
      target: dev
    container_name: boson-php
    restart: on-failure
    volumes:
      - composer:/home/<USER>/.composer:rw
      - ./:/home/<USER>/bosonphp.com:rw
    profiles:
      - ''
      - backend
      - build
    networks:
      - frontend
      - database

  postgres:
    <<: *environment
    build:
      context: .
      dockerfile: docker/postgres/Dockerfile
    container_name: boson-postgres
    volumes:
      - postgres:/var/lib/postgresql/data:rw
    environment:
      - POSTGRES_DB=${DB_DATABASE:-boson}
      - POSTGRES_USER=${DB_USERNAME:-user}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-password}
    healthcheck:
      test: ["CMD", "pg_isready", "-d", "${POSTGRES_DB:-app}", "-U", "${POSTGRES_USER:-app}"]
      timeout: 5s
      retries: 5
      start_period: 60s
    ports:
      - "${APP_DATABASE_PUBLIC_PORT:-5432}:5432"
    profiles:
      - ''
      - backend
    networks:
      - database

  node:
    <<: *environment
    build: docker/node
    container_name: boson-node
    volumes:
      - ./:/home/<USER>/bosonphp.com:rw

networks:
  frontend:
    name: boson_org
    driver: bridge
    ipam:
      config:
        # RFC1918
        - subnet: **********/24
  database:

volumes:
  postgres:
  composer:
    driver_opts:
      type: tmpfs
      device: tmpfs
      o: "uid=${UID:-1000}"
