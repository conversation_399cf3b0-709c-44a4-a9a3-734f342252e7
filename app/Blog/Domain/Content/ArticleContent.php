<?php

declare(strict_types=1);

namespace App\Blog\Domain\Content;

use App\Shared\Domain\ValueObject\StringValueObjectInterface;
use Doctrine\ORM\Mapping as ORM;

/**
 * Value object representing the content of an {@see Article}.
 */
#[ORM\Embeddable]
final class ArticleContent implements StringValueObjectInterface
{
    /**
     * Rendered content string value.
     */
    #[ORM\Column(name: 'rendered', type: 'text', options: ['default' => ''])]
    public private(set) string $rendered;

    /**
     * Raw content string value.
     */
    #[ORM\Column(name: 'raw', type: 'text', options: ['default' => ''])]
    public string $value {
        get => $this->value;
        set(string|\Stringable $value) {
            $this->value = (string) $value;
            $this->rendered = '';
        }
    }

    public function __construct(string|\Stringable $value = '')
    {
        $this->value = $value;
    }

    public function render(ArticleContentRendererInterface $renderer): void
    {
        $this->rendered = $renderer->renderContent($this);
    }

    public function toString(): string
    {
        return $this->value;
    }

    public function equals(mixed $object): bool
    {
        return $this === $object
            || ($object instanceof self && $this->value === $object->value);
    }

    public function __toString(): string
    {
        return $this->rendered;
    }
}
