<?php

declare(strict_types=1);

namespace App\Documentation\Domain\Category;

use App\Documentation\Domain\Document;
use App\Documentation\Domain\Page;
use App\Documentation\Domain\Version\Version;
use App\Shared\Domain\AggregateRootInterface;
use App\Shared\Domain\Date\CreatedDateProvider;
use App\Shared\Domain\Date\CreatedDateProviderInterface;
use App\Shared\Domain\Date\UpdatedDateProvider;
use App\Shared\Domain\Date\UpdatedDateProviderInterface;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'doc_page_categories')]
#[ORM\Index(name: 'doc_page_categories_sorting_order_idx', columns: ['sorting_order'])]
class Category implements
    AggregateRootInterface,
    CreatedDateProviderInterface,
    UpdatedDateProviderInterface
{
    use CreatedDateProvider;
    use UpdatedDateProvider;

    #[ORM\Id]
    #[ORM\Column(name: 'id', type: CategoryId::class)]
    public private(set) CategoryId $id;

    /**
     * @var non-empty-string
     */
    #[ORM\Column(name: 'title', type: 'string', length: 255)]
    public string $title;

    /**
     * @var non-empty-lowercase-string
     */
    #[ORM\Column(name: 'hash', type: 'string', nullable: true)]
    public ?string $hash = null;

    /**
     * @var non-empty-string|null
     */
    #[ORM\Column(name: 'description', type: 'text', nullable: true)]
    public ?string $description = null;

    /**
     * @var non-empty-string
     */
    #[ORM\Column(name: 'icon', type: 'string', length: 255, nullable: true)]
    public ?string $icon = null;

    /**
     * @var int<-32768, 32767>
     */
    #[ORM\Column(name: 'sorting_order', type: 'smallint', options: ['default' => 0])]
    public int $order = 0;

    /**
     * @var CategoryPagesSet
     */
    #[ORM\OneToMany(targetEntity: Page::class, mappedBy: 'category', cascade: ['ALL'], fetch: 'EAGER')]
    #[ORM\OrderBy(['order' => 'ASC', 'createdAt' => 'DESC'])]
    public iterable $pages {
        /** @phpstan-ignore-next-line : PHPStan false-positive */
        get => CategoryPagesSet::for($this, $this->pages);
    }

    /**
     * Gets first documentation page
     */
    public ?Document $page {
        get {
            foreach ($this->pages as $page) {
                if ($page instanceof Document) {
                    return $page;
                }
            }

            return null;
        }
    }

    #[ORM\ManyToOne(targetEntity: Version::class, cascade: ['persist'], fetch: 'EAGER', inversedBy: 'categories')]
    #[ORM\JoinColumn(name: 'version_id', referencedColumnName: 'id', nullable: false)]
    public Version $version {
        get => $this->version;
        set(Version $new) {
            /** @phpstan-ignore-next-line : PHPStan false-positive */
            $previous = $this->version ?? null;

            if ($previous !== $new) {
                $previous?->categories->removeElement($this);

                $this->version = $new;
                $new->categories->add($this);
            }
        }
    }

    /**
     * @param non-empty-string $title
     * @param non-empty-string|null $description
     * @param non-empty-string|null $icon
     * @param int<-32768, 32767> $order
     * @param non-empty-lowercase-string|null $hash
     */
    public function __construct(
        Version $version,
        string $title,
        ?string $description = null,
        ?string $icon = null,
        int $order = 0,
        ?string $hash = null,
        ?CategoryId $id = null,
    ) {
        $this->version = $version;
        $this->title = $title;
        $this->description = $description;
        $this->icon = $icon;
        $this->order = $order;
        $this->hash = $hash;
        $this->pages = new CategoryPagesSet($this);
        $this->id = $id ?? CategoryId::new();
    }
}
