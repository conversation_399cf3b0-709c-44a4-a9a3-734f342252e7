{"type": "project", "license": "proprietary", "repositories": [{"type": "path", "url": "libs/bridge/*"}, {"type": "path", "url": "libs/component/*"}], "require": {"php": "^8.4", "ext-ctype": "*", "ext-iconv": "*", "ext-mbstring": "*", "doctrine/dbal": "^3.10.1", "doctrine/doctrine-bundle": "^2.15.1", "doctrine/doctrine-fixtures-bundle": "^4.1", "doctrine/doctrine-migrations-bundle": "^3.4.2", "doctrine/orm": "^3.5.2", "knplabs/github-api": "^3.16", "league/commonmark": "^2.7.1", "local/http-factory-bundle": "^1.0", "local/request-mapper-bundle": "^1.0", "local/response-mapper-bundle": "^1.0", "local/set": "^1.0", "local/type-lang-mapper-bundle": "^1.0", "nyholm/psr7": "^1.8.2", "pentatrion/vite-bundle": "^8.2.1", "ramsey/uuid": "^4.9", "symfony/clock": "7.3.*", "symfony/console": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/expression-language": "7.3.*", "symfony/framework-bundle": "7.3.*", "symfony/http-client": "7.3.*", "symfony/messenger": "7.3.*", "symfony/monolog-bundle": "^3.10", "symfony/runtime": "7.3.*", "symfony/security-bundle": "7.3.*", "symfony/translation": "7.3.*", "symfony/twig-bundle": "7.3.*", "symfony/validator": "7.3.*", "symfony/yaml": "7.3.*", "tempest/highlight": "^2.13.1", "twig/extra-bundle": "^2.12|^3.21", "twig/string-extra": "^3.21", "twig/twig": "^2.12|^3.21.1"}, "autoload": {"psr-4": {"App\\": "app"}}, "require-dev": {"fakerphp/faker": "^1.24.1", "friends-of-behat/symfony-extension": "^2.6.2", "friendsofphp/php-cs-fixer": "^3.86.0", "justinrainbow/json-schema": "^6.4.2", "phpstan/phpstan": "^2.1.22", "phpstan/phpstan-deprecation-rules": "^2.0.3", "phpstan/phpstan-doctrine": "^2.0.4", "phpstan/phpstan-strict-rules": "^2.0.6", "phpstan/phpstan-symfony": "^2.0.7", "phpunit/phpunit": "^12.3.6", "symfony/browser-kit": "7.3.*", "symfony/css-selector": "7.3.*", "symfony/debug-bundle": "7.3.*", "symfony/flex": "^2.8.2", "symfony/json-path": "7.3.*", "symfony/stopwatch": "7.3.*", "symfony/web-profiler-bundle": "7.3.*"}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests"}}, "conflict": {"symfony/symfony": "*"}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*", "symfony/polyfill-php83": "*", "symfony/polyfill-php84": "*"}, "scripts-aliases": {"phpcs:fix": ["phpcs", "cs"], "linter:check": ["linter", "stan"]}, "scripts": {"db:up": "@php bin/console doctrine:migrations:migrate --no-interaction", "db:down": "@php bin/console doctrine:migrations:migrate first --no-interaction", "db:info": "@php bin/console doctrine:mapping:info", "db:diff": "@php bin/console doctrine:migrations:diff -vvv", "db:warmup": ["@php bin/console doctrine:cache:clear-metadata", "@php bin/console doctrine:cache:clear-query", "@php bin/console doctrine:cache:clear-result"], "db:refresh": ["@db:down", "@db:up"], "db:fill": "@php bin/console doctrine:fixtures:load", "test": ["@test:unit", "@test:feature"], "test:unit": "phpunit --testdox --testsuite=unit", "test:feature": "behat", "linter:check": "phpstan analyse --configuration phpstan.neon --memory-limit 256M", "linter:baseline": "@linter:check -- --generate-baseline", "phpcs:check": "@phpcs:fix --dry-run", "phpcs:fix": "php-cs-fixer fix --config=.php-cs-fixer.php --allow-risky=yes --verbose --diff", "post-autoload-dump": ["@php -r \"is_file('.env') || copy('.env.example', '.env');\"", "@php bin/console cache:clear", "@php bin/console assets:install"], "auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "bump-after-update": true, "sort-packages": true}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}}, "minimum-stability": "dev", "prefer-stable": true}