{"base": "/build/", "entryPoints": {"app": {"css": ["/build/assets/app-D9n-2Bcf.css"], "dynamic": ["/build/assets/katex-A6QSACVP-DSQRLkG4.js", "/build/assets/dagre-VLAHSO5Z-B3nX9deM.js", "/build/assets/c4Diagram-SNKOAWKG-Bodtrker.js", "/build/assets/flowDiagram-ADV24XW4-Dqg4xQtp.js", "/build/assets/flowDiagram-ADV24XW4-Dqg4xQtp.js", "/build/assets/erDiagram-ORVVWRID-DU6zWWRk.js", "/build/assets/gitGraphDiagram-36KJ7O45-4afd3BB-.js", "/build/assets/ganttDiagram-NY35QN2E-DxHPGm_g.js", "/build/assets/infoDiagram-RMIF2PVV-CHcDAznn.js", "/build/assets/pieDiagram-QE7GMA4C-BMMIglqT.js", "/build/assets/quadrantDiagram-AK377B32-ytVIFuhu.js", "/build/assets/xychartDiagram-LLVWJMIC-MNFH-cq3.js", "/build/assets/requirementDiagram-MXMYOOAN-yFny-v4a.js", "/build/assets/sequenceDiagram-WN2LQSXR-Bt1Ry9U8.js", "/build/assets/classDiagram-DAIZTAAV-B3RpoHzw.js", "/build/assets/classDiagram-v2-ZI3HNCDV-B3RpoHzw.js", "/build/assets/stateDiagram-MNTUIWRF-BMJAHmzA.js", "/build/assets/stateDiagram-v2-OVHUGLUH-DVeMa-et.js", "/build/assets/journeyDiagram-JUIHLJSS-BdR9Xlhw.js", "/build/assets/flowDiagram-ADV24XW4-Dqg4xQtp.js", "/build/assets/timeline-definition-RLVNA6JK-DOsm1LCl.js", "/build/assets/mindmap-definition-KYUYEQSL-BaZWKut7.js", "/build/assets/kanban-definition-4447FNW2-CHGGnxha.js", "/build/assets/sankeyDiagram-FLEJ7MD6-pg7nIQ1I.js", "/build/assets/diagram-QU4HLOLC-rd-rBmYF.js", "/build/assets/diagram-SPVQQ4ZZ-BCE6md0x.js", "/build/assets/blockDiagram-5FNOSGNS-Bv_y2k7a.js", "/build/assets/architectureDiagram-L76ZKZTK-oYeuPISx.js", "/build/assets/diagram-TI2PIPJW-CuzR8ukY.js"], "js": ["/build/assets/app-BQZQgfaL.js"], "legacy": false, "preload": []}}, "legacy": false, "metadatas": {}, "version": ["8.2.1", 8, 2, 1], "viteServer": null}