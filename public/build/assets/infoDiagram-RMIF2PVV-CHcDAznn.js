import{p as o}from"./treemap-KMMF4GRG-5JCI3IDA-Dr_hM2q7.js";import{_ as e,l as s,a6 as i,e as g,a7 as p}from"./app-BQZQgfaL.js";import"./chunk-TGZYFRKZ-DbNMVLyI.js";var v={parse:e(async r=>{const a=await o("info",r);s.debug(a)},"parse")},d={version:p.version+""},m=e(()=>d.version,"getVersion"),c={getVersion:m},l=e((r,a,n)=>{s.debug(`rendering info diagram
`+r);const t=i(a);g(t,100,400,!0),t.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${n}`)},"draw"),f={draw:l},w={parser:v,db:c,renderer:f};export{w as diagram};
