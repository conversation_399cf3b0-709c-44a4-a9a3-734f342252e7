import{G as oe}from"./chunk-EOAU2GW5-BhUq1lxm.js";import{g as he}from"./chunk-SCLVSU6P-BtZgy7AN.js";import{_ as d,G as rt,d as O,e as de,l as L,z as ge,B as ue,C as pe,c as z,az as fe,af as xe,am as ye,aj as be,aA as j,au as Wt,aB as we,u as $,k as me,aC as Le,aD as xt,i as yt,aE as Se}from"./app-BQZQgfaL.js";import{c as ke}from"./chunk-TGZYFRKZ-DbNMVLyI.js";var bt=(function(){var e=d(function(D,y,g,f){for(g=g||{},f=D.length;f--;g[D[f]]=y);return g},"o"),t=[1,15],a=[1,7],i=[1,13],l=[1,14],s=[1,19],r=[1,16],n=[1,17],c=[1,18],u=[8,30],o=[8,10,21,28,29,30,31,39,43,46],x=[1,23],w=[1,24],b=[8,10,15,16,21,28,29,30,31,39,43,46],S=[8,10,15,16,21,27,28,29,30,31,39,43,46],v=[1,49],k={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,NODE_ID:31,nodeShapeNLabel:32,dirList:33,DIR:34,NODE_DSTART:35,NODE_DEND:36,BLOCK_ARROW_START:37,BLOCK_ARROW_END:38,classDef:39,CLASSDEF_ID:40,CLASSDEF_STYLEOPTS:41,DEFAULT:42,class:43,CLASSENTITY_IDS:44,STYLECLASS:45,style:46,STYLE_ENTITY_IDS:47,STYLE_DEFINITION_DATA:48,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"NODE_ID",34:"DIR",35:"NODE_DSTART",36:"NODE_DEND",37:"BLOCK_ARROW_START",38:"BLOCK_ARROW_END",39:"classDef",40:"CLASSDEF_ID",41:"CLASSDEF_STYLEOPTS",42:"DEFAULT",43:"class",44:"CLASSENTITY_IDS",45:"STYLECLASS",46:"style",47:"STYLE_ENTITY_IDS",48:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[33,1],[33,2],[32,3],[32,4],[23,3],[23,3],[24,3],[25,3]],performAction:d(function(y,g,f,m,_,h,W){var p=h.length-1;switch(_){case 4:m.getLogger().debug("Rule: separator (NL) ");break;case 5:m.getLogger().debug("Rule: separator (Space) ");break;case 6:m.getLogger().debug("Rule: separator (EOF) ");break;case 7:m.getLogger().debug("Rule: hierarchy: ",h[p-1]),m.setHierarchy(h[p-1]);break;case 8:m.getLogger().debug("Stop NL ");break;case 9:m.getLogger().debug("Stop EOF ");break;case 10:m.getLogger().debug("Stop NL2 ");break;case 11:m.getLogger().debug("Stop EOF2 ");break;case 12:m.getLogger().debug("Rule: statement: ",h[p]),typeof h[p].length=="number"?this.$=h[p]:this.$=[h[p]];break;case 13:m.getLogger().debug("Rule: statement #2: ",h[p-1]),this.$=[h[p-1]].concat(h[p]);break;case 14:m.getLogger().debug("Rule: link: ",h[p],y),this.$={edgeTypeStr:h[p],label:""};break;case 15:m.getLogger().debug("Rule: LABEL link: ",h[p-3],h[p-1],h[p]),this.$={edgeTypeStr:h[p],label:h[p-1]};break;case 18:const I=parseInt(h[p]),Z=m.generateId();this.$={id:Z,type:"space",label:"",width:I,children:[]};break;case 23:m.getLogger().debug("Rule: (nodeStatement link node) ",h[p-2],h[p-1],h[p]," typestr: ",h[p-1].edgeTypeStr);const V=m.edgeStrToEdgeData(h[p-1].edgeTypeStr);this.$=[{id:h[p-2].id,label:h[p-2].label,type:h[p-2].type,directions:h[p-2].directions},{id:h[p-2].id+"-"+h[p].id,start:h[p-2].id,end:h[p].id,label:h[p-1].label,type:"edge",directions:h[p].directions,arrowTypeEnd:V,arrowTypeStart:"arrow_open"},{id:h[p].id,label:h[p].label,type:m.typeStr2Type(h[p].typeStr),directions:h[p].directions}];break;case 24:m.getLogger().debug("Rule: nodeStatement (abc88 node size) ",h[p-1],h[p]),this.$={id:h[p-1].id,label:h[p-1].label,type:m.typeStr2Type(h[p-1].typeStr),directions:h[p-1].directions,widthInColumns:parseInt(h[p],10)};break;case 25:m.getLogger().debug("Rule: nodeStatement (node) ",h[p]),this.$={id:h[p].id,label:h[p].label,type:m.typeStr2Type(h[p].typeStr),directions:h[p].directions,widthInColumns:1};break;case 26:m.getLogger().debug("APA123",this?this:"na"),m.getLogger().debug("COLUMNS: ",h[p]),this.$={type:"column-setting",columns:h[p]==="auto"?-1:parseInt(h[p])};break;case 27:m.getLogger().debug("Rule: id-block statement : ",h[p-2],h[p-1]),m.generateId(),this.$={...h[p-2],type:"composite",children:h[p-1]};break;case 28:m.getLogger().debug("Rule: blockStatement : ",h[p-2],h[p-1],h[p]);const at=m.generateId();this.$={id:at,type:"composite",label:"",children:h[p-1]};break;case 29:m.getLogger().debug("Rule: node (NODE_ID separator): ",h[p]),this.$={id:h[p]};break;case 30:m.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",h[p-1],h[p]),this.$={id:h[p-1],label:h[p].label,typeStr:h[p].typeStr,directions:h[p].directions};break;case 31:m.getLogger().debug("Rule: dirList: ",h[p]),this.$=[h[p]];break;case 32:m.getLogger().debug("Rule: dirList: ",h[p-1],h[p]),this.$=[h[p-1]].concat(h[p]);break;case 33:m.getLogger().debug("Rule: nodeShapeNLabel: ",h[p-2],h[p-1],h[p]),this.$={typeStr:h[p-2]+h[p],label:h[p-1]};break;case 34:m.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",h[p-3],h[p-2]," #3:",h[p-1],h[p]),this.$={typeStr:h[p-3]+h[p],label:h[p-2],directions:h[p-1]};break;case 35:case 36:this.$={type:"classDef",id:h[p-1].trim(),css:h[p].trim()};break;case 37:this.$={type:"applyClass",id:h[p-1].trim(),styleClass:h[p].trim()};break;case 38:this.$={type:"applyStyles",id:h[p-1].trim(),stylesStr:h[p].trim()};break}},"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{10:t,11:3,13:4,19:5,20:6,21:a,22:8,23:9,24:10,25:11,26:12,28:i,29:l,31:s,39:r,43:n,46:c},{8:[1,20]},e(u,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,10:t,21:a,28:i,29:l,31:s,39:r,43:n,46:c}),e(o,[2,16],{14:22,15:x,16:w}),e(o,[2,17]),e(o,[2,18]),e(o,[2,19]),e(o,[2,20]),e(o,[2,21]),e(o,[2,22]),e(b,[2,25],{27:[1,25]}),e(o,[2,26]),{19:26,26:12,31:s},{10:t,11:27,13:4,19:5,20:6,21:a,22:8,23:9,24:10,25:11,26:12,28:i,29:l,31:s,39:r,43:n,46:c},{40:[1,28],42:[1,29]},{44:[1,30]},{47:[1,31]},e(S,[2,29],{32:32,35:[1,33],37:[1,34]}),{1:[2,7]},e(u,[2,13]),{26:35,31:s},{31:[2,14]},{17:[1,36]},e(b,[2,24]),{10:t,11:37,13:4,14:22,15:x,16:w,19:5,20:6,21:a,22:8,23:9,24:10,25:11,26:12,28:i,29:l,31:s,39:r,43:n,46:c},{30:[1,38]},{41:[1,39]},{41:[1,40]},{45:[1,41]},{48:[1,42]},e(S,[2,30]),{18:[1,43]},{18:[1,44]},e(b,[2,23]),{18:[1,45]},{30:[1,46]},e(o,[2,28]),e(o,[2,35]),e(o,[2,36]),e(o,[2,37]),e(o,[2,38]),{36:[1,47]},{33:48,34:v},{15:[1,50]},e(o,[2,27]),e(S,[2,33]),{38:[1,51]},{33:52,34:v,38:[2,31]},{31:[2,15]},e(S,[2,34]),{38:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:d(function(y,g){if(g.recoverable)this.trace(y);else{var f=new Error(y);throw f.hash=g,f}},"parseError"),parse:d(function(y){var g=this,f=[0],m=[],_=[null],h=[],W=this.table,p="",I=0,Z=0,V=2,at=1,ne=h.slice.call(arguments,1),R=Object.create(this.lexer),q={yy:{}};for(var gt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,gt)&&(q.yy[gt]=this.yy[gt]);R.setInput(y,q.yy),q.yy.lexer=R,q.yy.parser=this,typeof R.yylloc>"u"&&(R.yylloc={});var ut=R.yylloc;h.push(ut);var le=R.options&&R.options.ranges;typeof q.yy.parseError=="function"?this.parseError=q.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function ce(P){f.length=f.length-2*P,_.length=_.length-P,h.length=h.length-P}d(ce,"popStack");function Nt(){var P;return P=m.pop()||R.lex()||at,typeof P!="number"&&(P instanceof Array&&(m=P,P=m.pop()),P=g.symbols_[P]||P),P}d(Nt,"lex");for(var F,J,H,pt,Q={},st,G,Tt,it;;){if(J=f[f.length-1],this.defaultActions[J]?H=this.defaultActions[J]:((F===null||typeof F>"u")&&(F=Nt()),H=W[J]&&W[J][F]),typeof H>"u"||!H.length||!H[0]){var ft="";it=[];for(st in W[J])this.terminals_[st]&&st>V&&it.push("'"+this.terminals_[st]+"'");R.showPosition?ft="Parse error on line "+(I+1)+`:
`+R.showPosition()+`
Expecting `+it.join(", ")+", got '"+(this.terminals_[F]||F)+"'":ft="Parse error on line "+(I+1)+": Unexpected "+(F==at?"end of input":"'"+(this.terminals_[F]||F)+"'"),this.parseError(ft,{text:R.match,token:this.terminals_[F]||F,line:R.yylineno,loc:ut,expected:it})}if(H[0]instanceof Array&&H.length>1)throw new Error("Parse Error: multiple actions possible at state: "+J+", token: "+F);switch(H[0]){case 1:f.push(F),_.push(R.yytext),h.push(R.yylloc),f.push(H[1]),F=null,Z=R.yyleng,p=R.yytext,I=R.yylineno,ut=R.yylloc;break;case 2:if(G=this.productions_[H[1]][1],Q.$=_[_.length-G],Q._$={first_line:h[h.length-(G||1)].first_line,last_line:h[h.length-1].last_line,first_column:h[h.length-(G||1)].first_column,last_column:h[h.length-1].last_column},le&&(Q._$.range=[h[h.length-(G||1)].range[0],h[h.length-1].range[1]]),pt=this.performAction.apply(Q,[p,Z,I,q.yy,H[1],_,h].concat(ne)),typeof pt<"u")return pt;G&&(f=f.slice(0,-1*G*2),_=_.slice(0,-1*G),h=h.slice(0,-1*G)),f.push(this.productions_[H[1]][0]),_.push(Q.$),h.push(Q._$),Tt=W[f[f.length-2]][f[f.length-1]],f.push(Tt);break;case 3:return!0}}return!0},"parse")},C=(function(){var D={EOF:1,parseError:d(function(g,f){if(this.yy.parser)this.yy.parser.parseError(g,f);else throw new Error(g)},"parseError"),setInput:d(function(y,g){return this.yy=g||this.yy||{},this._input=y,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var y=this._input[0];this.yytext+=y,this.yyleng++,this.offset++,this.match+=y,this.matched+=y;var g=y.match(/(?:\r\n?|\n).*/g);return g?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),y},"input"),unput:d(function(y){var g=y.length,f=y.split(/(?:\r\n?|\n)/g);this._input=y+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-g),this.offset-=g;var m=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),f.length-1&&(this.yylineno-=f.length-1);var _=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:f?(f.length===m.length?this.yylloc.first_column:0)+m[m.length-f.length].length-f[0].length:this.yylloc.first_column-g},this.options.ranges&&(this.yylloc.range=[_[0],_[0]+this.yyleng-g]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:d(function(y){this.unput(this.match.slice(y))},"less"),pastInput:d(function(){var y=this.matched.substr(0,this.matched.length-this.match.length);return(y.length>20?"...":"")+y.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var y=this.match;return y.length<20&&(y+=this._input.substr(0,20-y.length)),(y.substr(0,20)+(y.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var y=this.pastInput(),g=new Array(y.length+1).join("-");return y+this.upcomingInput()+`
`+g+"^"},"showPosition"),test_match:d(function(y,g){var f,m,_;if(this.options.backtrack_lexer&&(_={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(_.yylloc.range=this.yylloc.range.slice(0))),m=y[0].match(/(?:\r\n?|\n).*/g),m&&(this.yylineno+=m.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:m?m[m.length-1].length-m[m.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+y[0].length},this.yytext+=y[0],this.match+=y[0],this.matches=y,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(y[0].length),this.matched+=y[0],f=this.performAction.call(this,this.yy,this,g,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),f)return f;if(this._backtrack){for(var h in _)this[h]=_[h];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var y,g,f,m;this._more||(this.yytext="",this.match="");for(var _=this._currentRules(),h=0;h<_.length;h++)if(f=this._input.match(this.rules[_[h]]),f&&(!g||f[0].length>g[0].length)){if(g=f,m=h,this.options.backtrack_lexer){if(y=this.test_match(f,_[h]),y!==!1)return y;if(this._backtrack){g=!1;continue}else return!1}else if(!this.options.flex)break}return g?(y=this.test_match(g,_[m]),y!==!1?y:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var g=this.next();return g||this.lex()},"lex"),begin:d(function(g){this.conditionStack.push(g)},"begin"),popState:d(function(){var g=this.conditionStack.length-1;return g>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(g){return g=this.conditionStack.length-1-Math.abs(g||0),g>=0?this.conditionStack[g]:"INITIAL"},"topState"),pushState:d(function(g){this.begin(g)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:d(function(g,f,m,_){switch(m){case 0:return g.getLogger().debug("Found block-beta"),10;case 1:return g.getLogger().debug("Found id-block"),29;case 2:return g.getLogger().debug("Found block"),10;case 3:g.getLogger().debug(".",f.yytext);break;case 4:g.getLogger().debug("_",f.yytext);break;case 5:return 5;case 6:return f.yytext=-1,28;case 7:return f.yytext=f.yytext.replace(/columns\s+/,""),g.getLogger().debug("COLUMNS (LEX)",f.yytext),28;case 8:this.pushState("md_string");break;case 9:return"MD_STR";case 10:this.popState();break;case 11:this.pushState("string");break;case 12:g.getLogger().debug("LEX: POPPING STR:",f.yytext),this.popState();break;case 13:return g.getLogger().debug("LEX: STR end:",f.yytext),"STR";case 14:return f.yytext=f.yytext.replace(/space\:/,""),g.getLogger().debug("SPACE NUM (LEX)",f.yytext),21;case 15:return f.yytext="1",g.getLogger().debug("COLUMNS (LEX)",f.yytext),21;case 16:return 42;case 17:return"LINKSTYLE";case 18:return"INTERPOLATE";case 19:return this.pushState("CLASSDEF"),39;case 20:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";case 21:return this.popState(),this.pushState("CLASSDEFID"),40;case 22:return this.popState(),41;case 23:return this.pushState("CLASS"),43;case 24:return this.popState(),this.pushState("CLASS_STYLE"),44;case 25:return this.popState(),45;case 26:return this.pushState("STYLE_STMNT"),46;case 27:return this.popState(),this.pushState("STYLE_DEFINITION"),47;case 28:return this.popState(),48;case 29:return this.pushState("acc_title"),"acc_title";case 30:return this.popState(),"acc_title_value";case 31:return this.pushState("acc_descr"),"acc_descr";case 32:return this.popState(),"acc_descr_value";case 33:this.pushState("acc_descr_multiline");break;case 34:this.popState();break;case 35:return"acc_descr_multiline_value";case 36:return 30;case 37:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 38:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 39:return this.popState(),g.getLogger().debug("Lex: ))"),"NODE_DEND";case 40:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 41:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 42:return this.popState(),g.getLogger().debug("Lex: (-"),"NODE_DEND";case 43:return this.popState(),g.getLogger().debug("Lex: -)"),"NODE_DEND";case 44:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 45:return this.popState(),g.getLogger().debug("Lex: ]]"),"NODE_DEND";case 46:return this.popState(),g.getLogger().debug("Lex: ("),"NODE_DEND";case 47:return this.popState(),g.getLogger().debug("Lex: ])"),"NODE_DEND";case 48:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";case 49:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";case 50:return this.popState(),g.getLogger().debug("Lex: )]"),"NODE_DEND";case 51:return this.popState(),g.getLogger().debug("Lex: )"),"NODE_DEND";case 52:return this.popState(),g.getLogger().debug("Lex: ]>"),"NODE_DEND";case 53:return this.popState(),g.getLogger().debug("Lex: ]"),"NODE_DEND";case 54:return g.getLogger().debug("Lexa: -)"),this.pushState("NODE"),35;case 55:return g.getLogger().debug("Lexa: (-"),this.pushState("NODE"),35;case 56:return g.getLogger().debug("Lexa: ))"),this.pushState("NODE"),35;case 57:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),35;case 58:return g.getLogger().debug("Lex: ((("),this.pushState("NODE"),35;case 59:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),35;case 60:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),35;case 61:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),35;case 62:return g.getLogger().debug("Lexc: >"),this.pushState("NODE"),35;case 63:return g.getLogger().debug("Lexa: (["),this.pushState("NODE"),35;case 64:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),35;case 65:return this.pushState("NODE"),35;case 66:return this.pushState("NODE"),35;case 67:return this.pushState("NODE"),35;case 68:return this.pushState("NODE"),35;case 69:return this.pushState("NODE"),35;case 70:return this.pushState("NODE"),35;case 71:return this.pushState("NODE"),35;case 72:return g.getLogger().debug("Lexa: ["),this.pushState("NODE"),35;case 73:return this.pushState("BLOCK_ARROW"),g.getLogger().debug("LEX ARR START"),37;case 74:return g.getLogger().debug("Lex: NODE_ID",f.yytext),31;case 75:return g.getLogger().debug("Lex: EOF",f.yytext),8;case 76:this.pushState("md_string");break;case 77:this.pushState("md_string");break;case 78:return"NODE_DESCR";case 79:this.popState();break;case 80:g.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 81:g.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 82:return g.getLogger().debug("LEX: NODE_DESCR:",f.yytext),"NODE_DESCR";case 83:g.getLogger().debug("LEX POPPING"),this.popState();break;case 84:g.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 85:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (right): dir:",f.yytext),"DIR";case 86:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (left):",f.yytext),"DIR";case 87:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (x):",f.yytext),"DIR";case 88:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (y):",f.yytext),"DIR";case 89:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (up):",f.yytext),"DIR";case 90:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (down):",f.yytext),"DIR";case 91:return f.yytext="]>",g.getLogger().debug("Lex (ARROW_DIR end):",f.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";case 92:return g.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;case 93:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 94:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 95:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 96:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 97:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 98:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 99:this.pushState("md_string");break;case 100:return g.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";case 101:return this.popState(),g.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;case 102:return this.popState(),g.getLogger().debug("Lex: LINK",f.yytext),15;case 103:return this.popState(),g.getLogger().debug("Lex: LINK",f.yytext),15;case 104:return g.getLogger().debug("Lex: COLON",f.yytext),f.yytext=f.yytext.slice(1),27}},"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block:)/,/^(?:block\b)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[28],inclusive:!1},STYLE_STMNT:{rules:[27],inclusive:!1},CLASSDEFID:{rules:[22],inclusive:!1},CLASSDEF:{rules:[20,21],inclusive:!1},CLASS_STYLE:{rules:[25],inclusive:!1},CLASS:{rules:[24],inclusive:!1},LLABEL:{rules:[99,100,101,102,103],inclusive:!1},ARROW_DIR:{rules:[85,86,87,88,89,90,91],inclusive:!1},BLOCK_ARROW:{rules:[76,81,84],inclusive:!1},NODE:{rules:[37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,77,80],inclusive:!1},md_string:{rules:[9,10,78,79],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[12,13,82,83],inclusive:!1},acc_descr_multiline:{rules:[34,35],inclusive:!1},acc_descr:{rules:[32],inclusive:!1},acc_title:{rules:[30],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,11,14,15,16,17,18,19,23,26,29,31,33,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,92,93,94,95,96,97,98,104],inclusive:!0}}};return D})();k.lexer=C;function E(){this.yy={}}return d(E,"Parser"),E.prototype=k,k.Parser=E,new E})();bt.parser=bt;var ve=bt,X=new Map,kt=[],wt=new Map,Ct="color",Bt="fill",_e="bgFill",Pt=",",Ee=z(),ct=new Map,De=d(e=>me.sanitizeText(e,Ee),"sanitizeText"),Ne=d(function(e,t=""){let a=ct.get(e);a||(a={id:e,styles:[],textStyles:[]},ct.set(e,a)),t?.split(Pt).forEach(i=>{const l=i.replace(/([^;]*);/,"$1").trim();if(RegExp(Ct).exec(i)){const r=l.replace(Bt,_e).replace(Ct,Bt);a.textStyles.push(r)}a.styles.push(l)})},"addStyleClass"),Te=d(function(e,t=""){const a=X.get(e);t!=null&&(a.styles=t.split(Pt))},"addStyle2Node"),Ce=d(function(e,t){e.split(",").forEach(function(a){let i=X.get(a);if(i===void 0){const l=a.trim();i={id:l,type:"na",children:[]},X.set(l,i)}i.classes||(i.classes=[]),i.classes.push(t)})},"setCssClass"),Yt=d((e,t)=>{const a=e.flat(),i=[],s=a.find(r=>r?.type==="column-setting")?.columns??-1;for(const r of a){if(typeof s=="number"&&s>0&&r.type!=="column-setting"&&typeof r.widthInColumns=="number"&&r.widthInColumns>s&&L.warn(`Block ${r.id} width ${r.widthInColumns} exceeds configured column width ${s}`),r.label&&(r.label=De(r.label)),r.type==="classDef"){Ne(r.id,r.css);continue}if(r.type==="applyClass"){Ce(r.id,r?.styleClass??"");continue}if(r.type==="applyStyles"){r?.stylesStr&&Te(r.id,r?.stylesStr);continue}if(r.type==="column-setting")t.columns=r.columns??-1;else if(r.type==="edge"){const n=(wt.get(r.id)??0)+1;wt.set(r.id,n),r.id=n+"-"+r.id,kt.push(r)}else{r.label||(r.type==="composite"?r.label="":r.label=r.id);const n=X.get(r.id);if(n===void 0?X.set(r.id,r):(r.type!=="na"&&(n.type=r.type),r.label!==r.id&&(n.label=r.label)),r.children&&Yt(r.children,r),r.type==="space"){const c=r.width??1;for(let u=0;u<c;u++){const o=ke(r);o.id=o.id+"-"+u,X.set(o.id,o),i.push(o)}}else n===void 0&&i.push(r)}}t.children=i},"populateBlockDatabase"),vt=[],et={id:"root",type:"composite",children:[],columns:-1},Be=d(()=>{L.debug("Clear called"),ge(),et={id:"root",type:"composite",children:[],columns:-1},X=new Map([["root",et]]),vt=[],ct=new Map,kt=[],wt=new Map},"clear");function Ht(e){switch(L.debug("typeStr2Type",e),e){case"[]":return"square";case"()":return L.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}d(Ht,"typeStr2Type");function Kt(e){switch(L.debug("typeStr2Type",e),e){case"==":return"thick";default:return"normal"}}d(Kt,"edgeTypeStr2Type");function Xt(e){switch(e.replace(/^[\s-]+|[\s-]+$/g,"")){case"x":return"arrow_cross";case"o":return"arrow_circle";case">":return"arrow_point";default:return""}}d(Xt,"edgeStrToEdgeData");var It=0,Ie=d(()=>(It++,"id-"+Math.random().toString(36).substr(2,12)+"-"+It),"generateId"),Oe=d(e=>{et.children=e,Yt(e,et),vt=et.children},"setHierarchy"),ze=d(e=>{const t=X.get(e);return t?t.columns?t.columns:t.children?t.children.length:-1:-1},"getColumns"),Re=d(()=>[...X.values()],"getBlocksFlat"),Ae=d(()=>vt||[],"getBlocks"),Me=d(()=>kt,"getEdges"),Fe=d(e=>X.get(e),"getBlock"),We=d(e=>{X.set(e.id,e)},"setBlock"),Pe=d(()=>L,"getLogger"),Ye=d(function(){return ct},"getClasses"),He={getConfig:d(()=>rt().block,"getConfig"),typeStr2Type:Ht,edgeTypeStr2Type:Kt,edgeStrToEdgeData:Xt,getLogger:Pe,getBlocksFlat:Re,getBlocks:Ae,getEdges:Me,setHierarchy:Oe,getBlock:Fe,setBlock:We,getColumns:ze,getClasses:Ye,clear:Be,generateId:Ie},Ke=He,nt=d((e,t)=>{const a=ue,i=a(e,"r"),l=a(e,"g"),s=a(e,"b");return pe(i,l,s,t)},"fade"),Xe=d(e=>`.label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .cluster-label text {
    fill: ${e.titleColor};
  }
  .cluster-label span,p {
    color: ${e.titleColor};
  }



  .label text,span,p {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${e.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${e.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${e.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${e.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${e.edgeLabelBackground};
      fill: ${e.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${nt(e.edgeLabelBackground,.5)};
    // background-color:
  }

  .node .cluster {
    // fill: ${nt(e.mainBkg,.5)};
    fill: ${nt(e.clusterBkg,.5)};
    stroke: ${nt(e.clusterBorder,.2)};
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${e.titleColor};
  }

  .cluster span,p {
    color: ${e.titleColor};
  }
  /* .cluster div {
    color: ${e.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${e.fontFamily};
    font-size: 12px;
    background: ${e.tertiaryColor};
    border: 1px solid ${e.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.textColor};
  }
  ${he()}
`,"getStyles"),Ue=Xe,je=d((e,t,a,i)=>{t.forEach(l=>{rr[l](e,a,i)})},"insertMarkers"),Ve=d((e,t,a)=>{L.trace("Making markers for ",a),e.append("defs").append("marker").attr("id",a+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),Ge=d((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),Ze=d((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),qe=d((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),Je=d((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",a+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),Qe=d((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),$e=d((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),tr=d((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),er=d((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),rr={extension:Ve,composition:Ge,aggregation:Ze,dependency:qe,lollipop:Je,point:Qe,circle:$e,cross:tr,barb:er},ar=je,B=z()?.block?.padding??8;function Ut(e,t){if(e===0||!Number.isInteger(e))throw new Error("Columns must be an integer !== 0.");if(t<0||!Number.isInteger(t))throw new Error("Position must be a non-negative integer."+t);if(e<0)return{px:t,py:0};if(e===1)return{px:0,py:t};const a=t%e,i=Math.floor(t/e);return{px:a,py:i}}d(Ut,"calculateBlockPosition");var sr=d(e=>{let t=0,a=0;for(const i of e.children){const{width:l,height:s,x:r,y:n}=i.size??{width:0,height:0,x:0,y:0};L.debug("getMaxChildSize abc95 child:",i.id,"width:",l,"height:",s,"x:",r,"y:",n,i.type),i.type!=="space"&&(l>t&&(t=l/(e.widthInColumns??1)),s>a&&(a=s))}return{width:t,height:a}},"getMaxChildSize");function ot(e,t,a=0,i=0){L.debug("setBlockSizes abc95 (start)",e.id,e?.size?.x,"block width =",e?.size,"siblingWidth",a),e?.size?.width||(e.size={width:a,height:i,x:0,y:0});let l=0,s=0;if(e.children?.length>0){for(const b of e.children)ot(b,t);const r=sr(e);l=r.width,s=r.height,L.debug("setBlockSizes abc95 maxWidth of",e.id,":s children is ",l,s);for(const b of e.children)b.size&&(L.debug(`abc95 Setting size of children of ${e.id} id=${b.id} ${l} ${s} ${JSON.stringify(b.size)}`),b.size.width=l*(b.widthInColumns??1)+B*((b.widthInColumns??1)-1),b.size.height=s,b.size.x=0,b.size.y=0,L.debug(`abc95 updating size of ${e.id} children child:${b.id} maxWidth:${l} maxHeight:${s}`));for(const b of e.children)ot(b,t,l,s);const n=e.columns??-1;let c=0;for(const b of e.children)c+=b.widthInColumns??1;let u=e.children.length;n>0&&n<c&&(u=n);const o=Math.ceil(c/u);let x=u*(l+B)+B,w=o*(s+B)+B;if(x<a){L.debug(`Detected to small sibling: abc95 ${e.id} siblingWidth ${a} siblingHeight ${i} width ${x}`),x=a,w=i;const b=(a-u*B-B)/u,S=(i-o*B-B)/o;L.debug("Size indata abc88",e.id,"childWidth",b,"maxWidth",l),L.debug("Size indata abc88",e.id,"childHeight",S,"maxHeight",s),L.debug("Size indata abc88 xSize",u,"padding",B);for(const v of e.children)v.size&&(v.size.width=b,v.size.height=S,v.size.x=0,v.size.y=0)}if(L.debug(`abc95 (finale calc) ${e.id} xSize ${u} ySize ${o} columns ${n}${e.children.length} width=${Math.max(x,e.size?.width||0)}`),x<(e?.size?.width||0)){x=e?.size?.width||0;const b=n>0?Math.min(e.children.length,n):e.children.length;if(b>0){const S=(x-b*B-B)/b;L.debug("abc95 (growing to fit) width",e.id,x,e.size?.width,S);for(const v of e.children)v.size&&(v.size.width=S)}}e.size={width:x,height:w,x:0,y:0}}L.debug("setBlockSizes abc94 (done)",e.id,e?.size?.x,e?.size?.width,e?.size?.y,e?.size?.height)}d(ot,"setBlockSizes");function _t(e,t){L.debug(`abc85 layout blocks (=>layoutBlocks) ${e.id} x: ${e?.size?.x} y: ${e?.size?.y} width: ${e?.size?.width}`);const a=e.columns??-1;if(L.debug("layoutBlocks columns abc95",e.id,"=>",a,e),e.children&&e.children.length>0){const i=e?.children[0]?.size?.width??0,l=e.children.length*i+(e.children.length-1)*B;L.debug("widthOfChildren 88",l,"posX");let s=0;L.debug("abc91 block?.size?.x",e.id,e?.size?.x);let r=e?.size?.x?e?.size?.x+(-e?.size?.width/2||0):-B,n=0;for(const c of e.children){const u=e;if(!c.size)continue;const{width:o,height:x}=c.size,{px:w,py:b}=Ut(a,s);if(b!=n&&(n=b,r=e?.size?.x?e?.size?.x+(-e?.size?.width/2||0):-B,L.debug("New row in layout for block",e.id," and child ",c.id,n)),L.debug(`abc89 layout blocks (child) id: ${c.id} Pos: ${s} (px, py) ${w},${b} (${u?.size?.x},${u?.size?.y}) parent: ${u.id} width: ${o}${B}`),u.size){const v=o/2;c.size.x=r+B+v,L.debug(`abc91 layout blocks (calc) px, pyid:${c.id} startingPos=X${r} new startingPosX${c.size.x} ${v} padding=${B} width=${o} halfWidth=${v} => x:${c.size.x} y:${c.size.y} ${c.widthInColumns} (width * (child?.w || 1)) / 2 ${o*(c?.widthInColumns??1)/2}`),r=c.size.x+v,c.size.y=u.size.y-u.size.height/2+b*(x+B)+x/2+B,L.debug(`abc88 layout blocks (calc) px, pyid:${c.id}startingPosX${r}${B}${v}=>x:${c.size.x}y:${c.size.y}${c.widthInColumns}(width * (child?.w || 1)) / 2${o*(c?.widthInColumns??1)/2}`)}c.children&&_t(c);let S=c?.widthInColumns??1;a>0&&(S=Math.min(S,a-s%a)),s+=S,L.debug("abc88 columnsPos",c,s)}}L.debug(`layout blocks (<==layoutBlocks) ${e.id} x: ${e?.size?.x} y: ${e?.size?.y} width: ${e?.size?.width}`)}d(_t,"layoutBlocks");function Et(e,{minX:t,minY:a,maxX:i,maxY:l}={minX:0,minY:0,maxX:0,maxY:0}){if(e.size&&e.id!=="root"){const{x:s,y:r,width:n,height:c}=e.size;s-n/2<t&&(t=s-n/2),r-c/2<a&&(a=r-c/2),s+n/2>i&&(i=s+n/2),r+c/2>l&&(l=r+c/2)}if(e.children)for(const s of e.children)({minX:t,minY:a,maxX:i,maxY:l}=Et(s,{minX:t,minY:a,maxX:i,maxY:l}));return{minX:t,minY:a,maxX:i,maxY:l}}d(Et,"findBounds");function jt(e){const t=e.getBlock("root");if(!t)return;ot(t,e,0,0),_t(t),L.debug("getBlocks",JSON.stringify(t,null,2));const{minX:a,minY:i,maxX:l,maxY:s}=Et(t),r=s-i,n=l-a;return{x:a,y:i,width:n,height:r}}d(jt,"layout");function mt(e,t){t&&e.attr("style",t)}d(mt,"applyStyle");function Vt(e,t){const a=O(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),i=a.append("xhtml:div"),l=e.label,s=e.isNode?"nodeLabel":"edgeLabel",r=i.append("span");return r.html(yt(l,t)),mt(r,e.labelStyle),r.attr("class",s),mt(i,e.labelStyle),i.style("display","inline-block"),i.style("white-space","nowrap"),i.attr("xmlns","http://www.w3.org/1999/xhtml"),a.node()}d(Vt,"addHtmlLabel");var ir=d(async(e,t,a,i)=>{let l=e||"";typeof l=="object"&&(l=l[0]);const s=z();if(j(s.flowchart.htmlLabels)){l=l.replace(/\\n|\n/g,"<br />"),L.debug("vertexText"+l);const r=await Le(xt(l)),n={isNode:i,label:r,labelStyle:t.replace("fill:","color:")};return Vt(n,s)}else{const r=document.createElementNS("http://www.w3.org/2000/svg","text");r.setAttribute("style",t.replace("color:","fill:"));let n=[];typeof l=="string"?n=l.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(l)?n=l:n=[];for(const c of n){const u=document.createElementNS("http://www.w3.org/2000/svg","tspan");u.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),u.setAttribute("dy","1em"),u.setAttribute("x","0"),a?u.setAttribute("class","title-row"):u.setAttribute("class","row"),u.textContent=c.trim(),r.appendChild(u)}return r}},"createLabel"),K=ir,nr=d((e,t,a,i,l)=>{t.arrowTypeStart&&Ot(e,"start",t.arrowTypeStart,a,i,l),t.arrowTypeEnd&&Ot(e,"end",t.arrowTypeEnd,a,i,l)},"addEdgeMarkers"),lr={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},Ot=d((e,t,a,i,l,s)=>{const r=lr[a];if(!r){L.warn(`Unknown arrow type: ${a}`);return}const n=t==="start"?"Start":"End";e.attr(`marker-${t}`,`url(${i}#${l}_${s}-${r}${n})`)},"addEdgeMarker"),Lt={},M={},cr=d(async(e,t)=>{const a=z(),i=j(a.flowchart.htmlLabels),l=t.labelType==="markdown"?Wt(e,t.label,{style:t.labelStyle,useHtmlLabels:i,addSvgBackground:!0},a):await K(t.label,t.labelStyle),s=e.insert("g").attr("class","edgeLabel"),r=s.insert("g").attr("class","label");r.node().appendChild(l);let n=l.getBBox();if(i){const u=l.children[0],o=O(l);n=u.getBoundingClientRect(),o.attr("width",n.width),o.attr("height",n.height)}r.attr("transform","translate("+-n.width/2+", "+-n.height/2+")"),Lt[t.id]=s,t.width=n.width,t.height=n.height;let c;if(t.startLabelLeft){const u=await K(t.startLabelLeft,t.labelStyle),o=e.insert("g").attr("class","edgeTerminals"),x=o.insert("g").attr("class","inner");c=x.node().appendChild(u);const w=u.getBBox();x.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),M[t.id]||(M[t.id]={}),M[t.id].startLeft=o,tt(c,t.startLabelLeft)}if(t.startLabelRight){const u=await K(t.startLabelRight,t.labelStyle),o=e.insert("g").attr("class","edgeTerminals"),x=o.insert("g").attr("class","inner");c=o.node().appendChild(u),x.node().appendChild(u);const w=u.getBBox();x.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),M[t.id]||(M[t.id]={}),M[t.id].startRight=o,tt(c,t.startLabelRight)}if(t.endLabelLeft){const u=await K(t.endLabelLeft,t.labelStyle),o=e.insert("g").attr("class","edgeTerminals"),x=o.insert("g").attr("class","inner");c=x.node().appendChild(u);const w=u.getBBox();x.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),o.node().appendChild(u),M[t.id]||(M[t.id]={}),M[t.id].endLeft=o,tt(c,t.endLabelLeft)}if(t.endLabelRight){const u=await K(t.endLabelRight,t.labelStyle),o=e.insert("g").attr("class","edgeTerminals"),x=o.insert("g").attr("class","inner");c=x.node().appendChild(u);const w=u.getBBox();x.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),o.node().appendChild(u),M[t.id]||(M[t.id]={}),M[t.id].endRight=o,tt(c,t.endLabelRight)}return l},"insertEdgeLabel");function tt(e,t){z().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}d(tt,"setTerminalWidth");var or=d((e,t)=>{L.debug("Moving label abc88 ",e.id,e.label,Lt[e.id],t);let a=t.updatedPath?t.updatedPath:t.originalPath;const i=z(),{subGraphTitleTotalMargin:l}=we(i);if(e.label){const s=Lt[e.id];let r=e.x,n=e.y;if(a){const c=$.calcLabelPosition(a);L.debug("Moving label "+e.label+" from (",r,",",n,") to (",c.x,",",c.y,") abc88"),t.updatedPath&&(r=c.x,n=c.y)}s.attr("transform",`translate(${r}, ${n+l/2})`)}if(e.startLabelLeft){const s=M[e.id].startLeft;let r=e.x,n=e.y;if(a){const c=$.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",a);r=c.x,n=c.y}s.attr("transform",`translate(${r}, ${n})`)}if(e.startLabelRight){const s=M[e.id].startRight;let r=e.x,n=e.y;if(a){const c=$.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",a);r=c.x,n=c.y}s.attr("transform",`translate(${r}, ${n})`)}if(e.endLabelLeft){const s=M[e.id].endLeft;let r=e.x,n=e.y;if(a){const c=$.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",a);r=c.x,n=c.y}s.attr("transform",`translate(${r}, ${n})`)}if(e.endLabelRight){const s=M[e.id].endRight;let r=e.x,n=e.y;if(a){const c=$.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",a);r=c.x,n=c.y}s.attr("transform",`translate(${r}, ${n})`)}},"positionEdgeLabel"),hr=d((e,t)=>{const a=e.x,i=e.y,l=Math.abs(t.x-a),s=Math.abs(t.y-i),r=e.width/2,n=e.height/2;return l>=r||s>=n},"outsideNode"),dr=d((e,t,a)=>{L.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(a)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const i=e.x,l=e.y,s=Math.abs(i-a.x),r=e.width/2;let n=a.x<t.x?r-s:r+s;const c=e.height/2,u=Math.abs(t.y-a.y),o=Math.abs(t.x-a.x);if(Math.abs(l-t.y)*r>Math.abs(i-t.x)*c){let x=a.y<t.y?t.y-c-l:l-c-t.y;n=o*x/u;const w={x:a.x<t.x?a.x+n:a.x-o+n,y:a.y<t.y?a.y+u-x:a.y-u+x};return n===0&&(w.x=t.x,w.y=t.y),o===0&&(w.x=t.x),u===0&&(w.y=t.y),L.debug(`abc89 topp/bott calc, Q ${u}, q ${x}, R ${o}, r ${n}`,w),w}else{a.x<t.x?n=t.x-r-i:n=i-r-t.x;let x=u*n/o,w=a.x<t.x?a.x+o-n:a.x-o+n,b=a.y<t.y?a.y+x:a.y-x;return L.debug(`sides calc abc89, Q ${u}, q ${x}, R ${o}, r ${n}`,{_x:w,_y:b}),n===0&&(w=t.x,b=t.y),o===0&&(w=t.x),u===0&&(b=t.y),{x:w,y:b}}},"intersection"),zt=d((e,t)=>{L.debug("abc88 cutPathAtIntersect",e,t);let a=[],i=e[0],l=!1;return e.forEach(s=>{if(!hr(t,s)&&!l){const r=dr(t,i,s);let n=!1;a.forEach(c=>{n=n||c.x===r.x&&c.y===r.y}),a.some(c=>c.x===r.x&&c.y===r.y)||a.push(r),l=!0}else i=s,l||a.push(s)}),a},"cutPathAtIntersect"),gr=d(function(e,t,a,i,l,s,r){let n=a.points;L.debug("abc88 InsertEdge: edge=",a,"e=",t);let c=!1;const u=s.node(t.v);var o=s.node(t.w);o?.intersect&&u?.intersect&&(n=n.slice(1,a.points.length-1),n.unshift(u.intersect(n[0])),n.push(o.intersect(n[n.length-1]))),a.toCluster&&(L.debug("to cluster abc88",i[a.toCluster]),n=zt(a.points,i[a.toCluster].node),c=!0),a.fromCluster&&(L.debug("from cluster abc88",i[a.fromCluster]),n=zt(n.reverse(),i[a.fromCluster].node).reverse(),c=!0);const x=n.filter(y=>!Number.isNaN(y.y));let w=ye;a.curve&&(l==="graph"||l==="flowchart")&&(w=a.curve);const{x:b,y:S}=fe(a),v=xe().x(b).y(S).curve(w);let k;switch(a.thickness){case"normal":k="edge-thickness-normal";break;case"thick":k="edge-thickness-thick";break;case"invisible":k="edge-thickness-thick";break;default:k=""}switch(a.pattern){case"solid":k+=" edge-pattern-solid";break;case"dotted":k+=" edge-pattern-dotted";break;case"dashed":k+=" edge-pattern-dashed";break}const C=e.append("path").attr("d",v(x)).attr("id",a.id).attr("class"," "+k+(a.classes?" "+a.classes:"")).attr("style",a.style);let E="";(z().flowchart.arrowMarkerAbsolute||z().state.arrowMarkerAbsolute)&&(E=be(!0)),nr(C,a,E,r,l);let D={};return c&&(D.updatedPath=n),D.originalPath=a.points,D},"insertEdge"),ur=d(e=>{const t=new Set;for(const a of e)switch(a){case"x":t.add("right"),t.add("left");break;case"y":t.add("up"),t.add("down");break;default:t.add(a);break}return t},"expandAndDeduplicateDirections"),pr=d((e,t,a)=>{const i=ur(e),l=2,s=t.height+2*a.padding,r=s/l,n=t.width+2*r+a.padding,c=a.padding/2;return i.has("right")&&i.has("left")&&i.has("up")&&i.has("down")?[{x:0,y:0},{x:r,y:0},{x:n/2,y:2*c},{x:n-r,y:0},{x:n,y:0},{x:n,y:-s/3},{x:n+2*c,y:-s/2},{x:n,y:-2*s/3},{x:n,y:-s},{x:n-r,y:-s},{x:n/2,y:-s-2*c},{x:r,y:-s},{x:0,y:-s},{x:0,y:-2*s/3},{x:-2*c,y:-s/2},{x:0,y:-s/3}]:i.has("right")&&i.has("left")&&i.has("up")?[{x:r,y:0},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:r,y:-s},{x:0,y:-s/2}]:i.has("right")&&i.has("left")&&i.has("down")?[{x:0,y:0},{x:r,y:-s},{x:n-r,y:-s},{x:n,y:0}]:i.has("right")&&i.has("up")&&i.has("down")?[{x:0,y:0},{x:n,y:-r},{x:n,y:-s+r},{x:0,y:-s}]:i.has("left")&&i.has("up")&&i.has("down")?[{x:n,y:0},{x:0,y:-r},{x:0,y:-s+r},{x:n,y:-s}]:i.has("right")&&i.has("left")?[{x:r,y:0},{x:r,y:-c},{x:n-r,y:-c},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:n-r,y:-s+c},{x:r,y:-s+c},{x:r,y:-s},{x:0,y:-s/2}]:i.has("up")&&i.has("down")?[{x:n/2,y:0},{x:0,y:-c},{x:r,y:-c},{x:r,y:-s+c},{x:0,y:-s+c},{x:n/2,y:-s},{x:n,y:-s+c},{x:n-r,y:-s+c},{x:n-r,y:-c},{x:n,y:-c}]:i.has("right")&&i.has("up")?[{x:0,y:0},{x:n,y:-r},{x:0,y:-s}]:i.has("right")&&i.has("down")?[{x:0,y:0},{x:n,y:0},{x:0,y:-s}]:i.has("left")&&i.has("up")?[{x:n,y:0},{x:0,y:-r},{x:n,y:-s}]:i.has("left")&&i.has("down")?[{x:n,y:0},{x:0,y:0},{x:n,y:-s}]:i.has("right")?[{x:r,y:-c},{x:r,y:-c},{x:n-r,y:-c},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:n-r,y:-s+c},{x:r,y:-s+c},{x:r,y:-s+c}]:i.has("left")?[{x:r,y:0},{x:r,y:-c},{x:n-r,y:-c},{x:n-r,y:-s+c},{x:r,y:-s+c},{x:r,y:-s},{x:0,y:-s/2}]:i.has("up")?[{x:r,y:-c},{x:r,y:-s+c},{x:0,y:-s+c},{x:n/2,y:-s},{x:n,y:-s+c},{x:n-r,y:-s+c},{x:n-r,y:-c}]:i.has("down")?[{x:n/2,y:0},{x:0,y:-c},{x:r,y:-c},{x:r,y:-s+c},{x:n-r,y:-s+c},{x:n-r,y:-c},{x:n,y:-c}]:[{x:0,y:0}]},"getArrowPoints");function Gt(e,t){return e.intersect(t)}d(Gt,"intersectNode");var fr=Gt;function Zt(e,t,a,i){var l=e.x,s=e.y,r=l-i.x,n=s-i.y,c=Math.sqrt(t*t*n*n+a*a*r*r),u=Math.abs(t*a*r/c);i.x<l&&(u=-u);var o=Math.abs(t*a*n/c);return i.y<s&&(o=-o),{x:l+u,y:s+o}}d(Zt,"intersectEllipse");var qt=Zt;function Jt(e,t,a){return qt(e,t,t,a)}d(Jt,"intersectCircle");var xr=Jt;function Qt(e,t,a,i){var l,s,r,n,c,u,o,x,w,b,S,v,k,C,E;if(l=t.y-e.y,r=e.x-t.x,c=t.x*e.y-e.x*t.y,w=l*a.x+r*a.y+c,b=l*i.x+r*i.y+c,!(w!==0&&b!==0&&St(w,b))&&(s=i.y-a.y,n=a.x-i.x,u=i.x*a.y-a.x*i.y,o=s*e.x+n*e.y+u,x=s*t.x+n*t.y+u,!(o!==0&&x!==0&&St(o,x))&&(S=l*n-s*r,S!==0)))return v=Math.abs(S/2),k=r*u-n*c,C=k<0?(k-v)/S:(k+v)/S,k=s*c-l*u,E=k<0?(k-v)/S:(k+v)/S,{x:C,y:E}}d(Qt,"intersectLine");function St(e,t){return e*t>0}d(St,"sameSign");var yr=Qt,br=$t;function $t(e,t,a){var i=e.x,l=e.y,s=[],r=Number.POSITIVE_INFINITY,n=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(S){r=Math.min(r,S.x),n=Math.min(n,S.y)}):(r=Math.min(r,t.x),n=Math.min(n,t.y));for(var c=i-e.width/2-r,u=l-e.height/2-n,o=0;o<t.length;o++){var x=t[o],w=t[o<t.length-1?o+1:0],b=yr(e,a,{x:c+x.x,y:u+x.y},{x:c+w.x,y:u+w.y});b&&s.push(b)}return s.length?(s.length>1&&s.sort(function(S,v){var k=S.x-a.x,C=S.y-a.y,E=Math.sqrt(k*k+C*C),D=v.x-a.x,y=v.y-a.y,g=Math.sqrt(D*D+y*y);return E<g?-1:E===g?0:1}),s[0]):e}d($t,"intersectPolygon");var wr=d((e,t)=>{var a=e.x,i=e.y,l=t.x-a,s=t.y-i,r=e.width/2,n=e.height/2,c,u;return Math.abs(s)*r>Math.abs(l)*n?(s<0&&(n=-n),c=s===0?0:n*l/s,u=n):(l<0&&(r=-r),c=r,u=l===0?0:r*s/l),{x:a+c,y:i+u}},"intersectRect"),mr=wr,N={node:fr,circle:xr,ellipse:qt,polygon:br,rect:mr},A=d(async(e,t,a,i)=>{const l=z();let s;const r=t.useHtmlLabels||j(l.flowchart.htmlLabels);a?s=a:s="node default";const n=e.insert("g").attr("class",s).attr("id",t.domId||t.id),c=n.insert("g").attr("class","label").attr("style",t.labelStyle);let u;t.labelText===void 0?u="":u=typeof t.labelText=="string"?t.labelText:t.labelText[0];const o=c.node();let x;t.labelType==="markdown"?x=Wt(c,yt(xt(u),l),{useHtmlLabels:r,width:t.width||l.flowchart.wrappingWidth,classes:"markdown-node-label"},l):x=o.appendChild(await K(yt(xt(u),l),t.labelStyle,!1,i));let w=x.getBBox();const b=t.padding/2;if(j(l.flowchart.htmlLabels)){const S=x.children[0],v=O(x),k=S.getElementsByTagName("img");if(k){const C=u.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...k].map(E=>new Promise(D=>{function y(){if(E.style.display="flex",E.style.flexDirection="column",C){const g=l.fontSize?l.fontSize:window.getComputedStyle(document.body).fontSize,m=parseInt(g,10)*5+"px";E.style.minWidth=m,E.style.maxWidth=m}else E.style.width="100%";D(E)}d(y,"setupImage"),setTimeout(()=>{E.complete&&y()}),E.addEventListener("error",y),E.addEventListener("load",y)})))}w=S.getBoundingClientRect(),v.attr("width",w.width),v.attr("height",w.height)}return r?c.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"):c.attr("transform","translate(0, "+-w.height/2+")"),t.centerLabel&&c.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),c.insert("rect",":first-child"),{shapeSvg:n,bbox:w,halfPadding:b,label:c}},"labelHelper"),T=d((e,t)=>{const a=t.node().getBBox();e.width=a.width,e.height=a.height},"updateNodeBounds");function U(e,t,a,i){return e.insert("polygon",":first-child").attr("points",i.map(function(l){return l.x+","+l.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+a/2+")")}d(U,"insertPolygonShape");var Lr=d(async(e,t)=>{t.useHtmlLabels||z().flowchart.htmlLabels||(t.centerLabel=!0);const{shapeSvg:i,bbox:l,halfPadding:s}=await A(e,t,"node "+t.classes,!0);L.info("Classes = ",t.classes);const r=i.insert("rect",":first-child");return r.attr("rx",t.rx).attr("ry",t.ry).attr("x",-l.width/2-s).attr("y",-l.height/2-s).attr("width",l.width+t.padding).attr("height",l.height+t.padding),T(t,r),t.intersect=function(n){return N.rect(t,n)},i},"note"),Sr=Lr,Rt=d(e=>e?" "+e:"","formatClass"),Y=d((e,t)=>`${t||"node default"}${Rt(e.classes)} ${Rt(e.class)}`,"getClassesFromNode"),At=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,Y(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=l+s,n=[{x:r/2,y:0},{x:r,y:-r/2},{x:r/2,y:-r},{x:0,y:-r/2}];L.info("Question main (Circle)");const c=U(a,r,r,n);return c.attr("style",t.style),T(t,c),t.intersect=function(u){return L.warn("Intersect called"),N.polygon(t,n,u)},a},"question"),kr=d((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=28,l=[{x:0,y:i/2},{x:i/2,y:0},{x:0,y:-i/2},{x:-i/2,y:0}];return a.insert("polygon",":first-child").attr("points",l.map(function(r){return r.x+","+r.y}).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),t.width=28,t.height=28,t.intersect=function(r){return N.circle(t,14,r)},a},"choice"),vr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,Y(t,void 0),!0),l=4,s=i.height+t.padding,r=s/l,n=i.width+2*r+t.padding,c=[{x:r,y:0},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:r,y:-s},{x:0,y:-s/2}],u=U(a,n,s,c);return u.attr("style",t.style),T(t,u),t.intersect=function(o){return N.polygon(t,c,o)},a},"hexagon"),_r=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,void 0,!0),l=2,s=i.height+2*t.padding,r=s/l,n=i.width+2*r+t.padding,c=pr(t.directions,i,t),u=U(a,n,s,c);return u.attr("style",t.style),T(t,u),t.intersect=function(o){return N.polygon(t,c,o)},a},"block_arrow"),Er=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,Y(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:-s/2,y:0},{x:l,y:0},{x:l,y:-s},{x:-s/2,y:-s},{x:0,y:-s/2}];return U(a,l,s,r).attr("style",t.style),t.width=l+s,t.height=s,t.intersect=function(c){return N.polygon(t,r,c)},a},"rect_left_inv_arrow"),Dr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,Y(t),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:-2*s/6,y:0},{x:l-s/6,y:0},{x:l+2*s/6,y:-s},{x:s/6,y:-s}],n=U(a,l,s,r);return n.attr("style",t.style),T(t,n),t.intersect=function(c){return N.polygon(t,r,c)},a},"lean_right"),Nr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,Y(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:2*s/6,y:0},{x:l+s/6,y:0},{x:l-2*s/6,y:-s},{x:-s/6,y:-s}],n=U(a,l,s,r);return n.attr("style",t.style),T(t,n),t.intersect=function(c){return N.polygon(t,r,c)},a},"lean_left"),Tr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,Y(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:-2*s/6,y:0},{x:l+2*s/6,y:0},{x:l-s/6,y:-s},{x:s/6,y:-s}],n=U(a,l,s,r);return n.attr("style",t.style),T(t,n),t.intersect=function(c){return N.polygon(t,r,c)},a},"trapezoid"),Cr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,Y(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:s/6,y:0},{x:l-s/6,y:0},{x:l+2*s/6,y:-s},{x:-2*s/6,y:-s}],n=U(a,l,s,r);return n.attr("style",t.style),T(t,n),t.intersect=function(c){return N.polygon(t,r,c)},a},"inv_trapezoid"),Br=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,Y(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:0,y:0},{x:l+s/2,y:0},{x:l,y:-s/2},{x:l+s/2,y:-s},{x:0,y:-s}],n=U(a,l,s,r);return n.attr("style",t.style),T(t,n),t.intersect=function(c){return N.polygon(t,r,c)},a},"rect_right_inv_arrow"),Ir=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,Y(t,void 0),!0),l=i.width+t.padding,s=l/2,r=s/(2.5+l/50),n=i.height+r+t.padding,c="M 0,"+r+" a "+s+","+r+" 0,0,0 "+l+" 0 a "+s+","+r+" 0,0,0 "+-l+" 0 l 0,"+n+" a "+s+","+r+" 0,0,0 "+l+" 0 l 0,"+-n,u=a.attr("label-offset-y",r).insert("path",":first-child").attr("style",t.style).attr("d",c).attr("transform","translate("+-l/2+","+-(n/2+r)+")");return T(t,u),t.intersect=function(o){const x=N.rect(t,o),w=x.x-t.x;if(s!=0&&(Math.abs(w)<t.width/2||Math.abs(w)==t.width/2&&Math.abs(x.y-t.y)>t.height/2-r)){let b=r*r*(1-w*w/(s*s));b!=0&&(b=Math.sqrt(b)),b=r-b,o.y-t.y>0&&(b=-b),x.y+=b}return x},a},"cylinder"),Or=d(async(e,t)=>{const{shapeSvg:a,bbox:i,halfPadding:l}=await A(e,t,"node "+t.classes+" "+t.class,!0),s=a.insert("rect",":first-child"),r=t.positioned?t.width:i.width+t.padding,n=t.positioned?t.height:i.height+t.padding,c=t.positioned?-r/2:-i.width/2-l,u=t.positioned?-n/2:-i.height/2-l;if(s.attr("class","basic label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",u).attr("width",r).attr("height",n),t.props){const o=new Set(Object.keys(t.props));t.props.borders&&(ht(s,t.props.borders,r,n),o.delete("borders")),o.forEach(x=>{L.warn(`Unknown node property ${x}`)})}return T(t,s),t.intersect=function(o){return N.rect(t,o)},a},"rect"),zr=d(async(e,t)=>{const{shapeSvg:a,bbox:i,halfPadding:l}=await A(e,t,"node "+t.classes,!0),s=a.insert("rect",":first-child"),r=t.positioned?t.width:i.width+t.padding,n=t.positioned?t.height:i.height+t.padding,c=t.positioned?-r/2:-i.width/2-l,u=t.positioned?-n/2:-i.height/2-l;if(s.attr("class","basic cluster composite label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",u).attr("width",r).attr("height",n),t.props){const o=new Set(Object.keys(t.props));t.props.borders&&(ht(s,t.props.borders,r,n),o.delete("borders")),o.forEach(x=>{L.warn(`Unknown node property ${x}`)})}return T(t,s),t.intersect=function(o){return N.rect(t,o)},a},"composite"),Rr=d(async(e,t)=>{const{shapeSvg:a}=await A(e,t,"label",!0);L.trace("Classes = ",t.class);const i=a.insert("rect",":first-child"),l=0,s=0;if(i.attr("width",l).attr("height",s),a.attr("class","label edgeLabel"),t.props){const r=new Set(Object.keys(t.props));t.props.borders&&(ht(i,t.props.borders,l,s),r.delete("borders")),r.forEach(n=>{L.warn(`Unknown node property ${n}`)})}return T(t,i),t.intersect=function(r){return N.rect(t,r)},a},"labelRect");function ht(e,t,a,i){const l=[],s=d(n=>{l.push(n,0)},"addBorder"),r=d(n=>{l.push(0,n)},"skipBorder");t.includes("t")?(L.debug("add top border"),s(a)):r(a),t.includes("r")?(L.debug("add right border"),s(i)):r(i),t.includes("b")?(L.debug("add bottom border"),s(a)):r(a),t.includes("l")?(L.debug("add left border"),s(i)):r(i),e.attr("stroke-dasharray",l.join(" "))}d(ht,"applyNodePropertyBorders");var Ar=d(async(e,t)=>{let a;t.classes?a="node "+t.classes:a="node default";const i=e.insert("g").attr("class",a).attr("id",t.domId||t.id),l=i.insert("rect",":first-child"),s=i.insert("line"),r=i.insert("g").attr("class","label"),n=t.labelText.flat?t.labelText.flat():t.labelText;let c="";typeof n=="object"?c=n[0]:c=n,L.info("Label text abc79",c,n,typeof n=="object");const u=r.node().appendChild(await K(c,t.labelStyle,!0,!0));let o={width:0,height:0};if(j(z().flowchart.htmlLabels)){const v=u.children[0],k=O(u);o=v.getBoundingClientRect(),k.attr("width",o.width),k.attr("height",o.height)}L.info("Text 2",n);const x=n.slice(1,n.length);let w=u.getBBox();const b=r.node().appendChild(await K(x.join?x.join("<br/>"):x,t.labelStyle,!0,!0));if(j(z().flowchart.htmlLabels)){const v=b.children[0],k=O(b);o=v.getBoundingClientRect(),k.attr("width",o.width),k.attr("height",o.height)}const S=t.padding/2;return O(b).attr("transform","translate( "+(o.width>w.width?0:(w.width-o.width)/2)+", "+(w.height+S+5)+")"),O(u).attr("transform","translate( "+(o.width<w.width?0:-(w.width-o.width)/2)+", 0)"),o=r.node().getBBox(),r.attr("transform","translate("+-o.width/2+", "+(-o.height/2-S+3)+")"),l.attr("class","outer title-state").attr("x",-o.width/2-S).attr("y",-o.height/2-S).attr("width",o.width+t.padding).attr("height",o.height+t.padding),s.attr("class","divider").attr("x1",-o.width/2-S).attr("x2",o.width/2+S).attr("y1",-o.height/2-S+w.height+S).attr("y2",-o.height/2-S+w.height+S),T(t,l),t.intersect=function(v){return N.rect(t,v)},i},"rectWithTitle"),Mr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,Y(t,void 0),!0),l=i.height+t.padding,s=i.width+l/4+t.padding,r=a.insert("rect",":first-child").attr("style",t.style).attr("rx",l/2).attr("ry",l/2).attr("x",-s/2).attr("y",-l/2).attr("width",s).attr("height",l);return T(t,r),t.intersect=function(n){return N.rect(t,n)},a},"stadium"),Fr=d(async(e,t)=>{const{shapeSvg:a,bbox:i,halfPadding:l}=await A(e,t,Y(t,void 0),!0),s=a.insert("circle",":first-child");return s.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l).attr("width",i.width+t.padding).attr("height",i.height+t.padding),L.info("Circle main"),T(t,s),t.intersect=function(r){return L.info("Circle intersect",t,i.width/2+l,r),N.circle(t,i.width/2+l,r)},a},"circle"),Wr=d(async(e,t)=>{const{shapeSvg:a,bbox:i,halfPadding:l}=await A(e,t,Y(t,void 0),!0),s=5,r=a.insert("g",":first-child"),n=r.insert("circle"),c=r.insert("circle");return r.attr("class",t.class),n.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l+s).attr("width",i.width+t.padding+s*2).attr("height",i.height+t.padding+s*2),c.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l).attr("width",i.width+t.padding).attr("height",i.height+t.padding),L.info("DoubleCircle main"),T(t,n),t.intersect=function(u){return L.info("DoubleCircle intersect",t,i.width/2+l+s,u),N.circle(t,i.width/2+l+s,u)},a},"doublecircle"),Pr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await A(e,t,Y(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:0,y:0},{x:l,y:0},{x:l,y:-s},{x:0,y:-s},{x:0,y:0},{x:-8,y:0},{x:l+8,y:0},{x:l+8,y:-s},{x:-8,y:-s},{x:-8,y:0}],n=U(a,l,s,r);return n.attr("style",t.style),T(t,n),t.intersect=function(c){return N.polygon(t,r,c)},a},"subroutine"),Yr=d((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=a.insert("circle",":first-child");return i.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),T(t,i),t.intersect=function(l){return N.circle(t,7,l)},a},"start"),Mt=d((e,t,a)=>{const i=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let l=70,s=10;a==="LR"&&(l=10,s=70);const r=i.append("rect").attr("x",-1*l/2).attr("y",-1*s/2).attr("width",l).attr("height",s).attr("class","fork-join");return T(t,r),t.height=t.height+t.padding/2,t.width=t.width+t.padding/2,t.intersect=function(n){return N.rect(t,n)},i},"forkJoin"),Hr=d((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=a.insert("circle",":first-child"),l=a.insert("circle",":first-child");return l.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),i.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),T(t,l),t.intersect=function(s){return N.circle(t,7,s)},a},"end"),Kr=d(async(e,t)=>{const a=t.padding/2,i=4,l=8;let s;t.classes?s="node "+t.classes:s="node default";const r=e.insert("g").attr("class",s).attr("id",t.domId||t.id),n=r.insert("rect",":first-child"),c=r.insert("line"),u=r.insert("line");let o=0,x=i;const w=r.insert("g").attr("class","label");let b=0;const S=t.classData.annotations?.[0],v=t.classData.annotations[0]?"«"+t.classData.annotations[0]+"»":"",k=w.node().appendChild(await K(v,t.labelStyle,!0,!0));let C=k.getBBox();if(j(z().flowchart.htmlLabels)){const _=k.children[0],h=O(k);C=_.getBoundingClientRect(),h.attr("width",C.width),h.attr("height",C.height)}t.classData.annotations[0]&&(x+=C.height+i,o+=C.width);let E=t.classData.label;t.classData.type!==void 0&&t.classData.type!==""&&(z().flowchart.htmlLabels?E+="&lt;"+t.classData.type+"&gt;":E+="<"+t.classData.type+">");const D=w.node().appendChild(await K(E,t.labelStyle,!0,!0));O(D).attr("class","classTitle");let y=D.getBBox();if(j(z().flowchart.htmlLabels)){const _=D.children[0],h=O(D);y=_.getBoundingClientRect(),h.attr("width",y.width),h.attr("height",y.height)}x+=y.height+i,y.width>o&&(o=y.width);const g=[];t.classData.members.forEach(async _=>{const h=_.getDisplayDetails();let W=h.displayText;z().flowchart.htmlLabels&&(W=W.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const p=w.node().appendChild(await K(W,h.cssStyle?h.cssStyle:t.labelStyle,!0,!0));let I=p.getBBox();if(j(z().flowchart.htmlLabels)){const Z=p.children[0],V=O(p);I=Z.getBoundingClientRect(),V.attr("width",I.width),V.attr("height",I.height)}I.width>o&&(o=I.width),x+=I.height+i,g.push(p)}),x+=l;const f=[];if(t.classData.methods.forEach(async _=>{const h=_.getDisplayDetails();let W=h.displayText;z().flowchart.htmlLabels&&(W=W.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const p=w.node().appendChild(await K(W,h.cssStyle?h.cssStyle:t.labelStyle,!0,!0));let I=p.getBBox();if(j(z().flowchart.htmlLabels)){const Z=p.children[0],V=O(p);I=Z.getBoundingClientRect(),V.attr("width",I.width),V.attr("height",I.height)}I.width>o&&(o=I.width),x+=I.height+i,f.push(p)}),x+=l,S){let _=(o-C.width)/2;O(k).attr("transform","translate( "+(-1*o/2+_)+", "+-1*x/2+")"),b=C.height+i}let m=(o-y.width)/2;return O(D).attr("transform","translate( "+(-1*o/2+m)+", "+(-1*x/2+b)+")"),b+=y.height+i,c.attr("class","divider").attr("x1",-o/2-a).attr("x2",o/2+a).attr("y1",-x/2-a+l+b).attr("y2",-x/2-a+l+b),b+=l,g.forEach(_=>{O(_).attr("transform","translate( "+-o/2+", "+(-1*x/2+b+l/2)+")");const h=_?.getBBox();b+=(h?.height??0)+i}),b+=l,u.attr("class","divider").attr("x1",-o/2-a).attr("x2",o/2+a).attr("y1",-x/2-a+l+b).attr("y2",-x/2-a+l+b),b+=l,f.forEach(_=>{O(_).attr("transform","translate( "+-o/2+", "+(-1*x/2+b)+")");const h=_?.getBBox();b+=(h?.height??0)+i}),n.attr("style",t.style).attr("class","outer title-state").attr("x",-o/2-a).attr("y",-(x/2)-a).attr("width",o+t.padding).attr("height",x+t.padding),T(t,n),t.intersect=function(_){return N.rect(t,_)},r},"class_box"),Ft={rhombus:At,composite:zr,question:At,rect:Or,labelRect:Rr,rectWithTitle:Ar,choice:kr,circle:Fr,doublecircle:Wr,stadium:Mr,hexagon:vr,block_arrow:_r,rect_left_inv_arrow:Er,lean_right:Dr,lean_left:Nr,trapezoid:Tr,inv_trapezoid:Cr,rect_right_inv_arrow:Br,cylinder:Ir,start:Yr,end:Hr,note:Sr,subroutine:Pr,fork:Mt,join:Mt,class_box:Kr},lt={},te=d(async(e,t,a)=>{let i,l;if(t.link){let s;z().securityLevel==="sandbox"?s="_top":t.linkTarget&&(s=t.linkTarget||"_blank"),i=e.insert("svg:a").attr("xlink:href",t.link).attr("target",s),l=await Ft[t.shape](i,t,a)}else l=await Ft[t.shape](e,t,a),i=l;return t.tooltip&&l.attr("title",t.tooltip),t.class&&l.attr("class","node default "+t.class),lt[t.id]=i,t.haveCallback&&lt[t.id].attr("class",lt[t.id].attr("class")+" clickable"),i},"insertNode"),Xr=d(e=>{const t=lt[e.id];L.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const a=8,i=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+i-e.width/2)+", "+(e.y-e.height/2-a)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),i},"positionNode");function Dt(e,t,a=!1){const i=e;let l="default";(i?.classes?.length||0)>0&&(l=(i?.classes??[]).join(" ")),l=l+" flowchart-label";let s=0,r="",n;switch(i.type){case"round":s=5,r="rect";break;case"composite":s=0,r="composite",n=0;break;case"square":r="rect";break;case"diamond":r="question";break;case"hexagon":r="hexagon";break;case"block_arrow":r="block_arrow";break;case"odd":r="rect_left_inv_arrow";break;case"lean_right":r="lean_right";break;case"lean_left":r="lean_left";break;case"trapezoid":r="trapezoid";break;case"inv_trapezoid":r="inv_trapezoid";break;case"rect_left_inv_arrow":r="rect_left_inv_arrow";break;case"circle":r="circle";break;case"ellipse":r="ellipse";break;case"stadium":r="stadium";break;case"subroutine":r="subroutine";break;case"cylinder":r="cylinder";break;case"group":r="rect";break;case"doublecircle":r="doublecircle";break;default:r="rect"}const c=Se(i?.styles??[]),u=i.label,o=i.size??{width:0,height:0,x:0,y:0};return{labelStyle:c.labelStyle,shape:r,labelText:u,rx:s,ry:s,class:l,style:c.style,id:i.id,directions:i.directions,width:o.width,height:o.height,x:o.x,y:o.y,positioned:a,intersect:void 0,type:i.type,padding:n??rt()?.block?.padding??0}}d(Dt,"getNodeFromBlock");async function ee(e,t,a){const i=Dt(t,a,!1);if(i.type==="group")return;const l=rt(),s=await te(e,i,{config:l}),r=s.node().getBBox(),n=a.getBlock(i.id);n.size={width:r.width,height:r.height,x:0,y:0,node:s},a.setBlock(n),s.remove()}d(ee,"calculateBlockSize");async function re(e,t,a){const i=Dt(t,a,!0);if(a.getBlock(i.id).type!=="space"){const s=rt();await te(e,i,{config:s}),t.intersect=i?.intersect,Xr(i)}}d(re,"insertBlockPositioned");async function dt(e,t,a,i){for(const l of t)await i(e,l,a),l.children&&await dt(e,l.children,a,i)}d(dt,"performOperations");async function ae(e,t,a){await dt(e,t,a,ee)}d(ae,"calculateBlockSizes");async function se(e,t,a){await dt(e,t,a,re)}d(se,"insertBlocks");async function ie(e,t,a,i,l){const s=new oe({multigraph:!0,compound:!0});s.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8});for(const r of a)r.size&&s.setNode(r.id,{width:r.size.width,height:r.size.height,intersect:r.intersect});for(const r of t)if(r.start&&r.end){const n=i.getBlock(r.start),c=i.getBlock(r.end);if(n?.size&&c?.size){const u=n.size,o=c.size,x=[{x:u.x,y:u.y},{x:u.x+(o.x-u.x)/2,y:u.y+(o.y-u.y)/2},{x:o.x,y:o.y}];gr(e,{v:r.start,w:r.end,name:r.id},{...r,arrowTypeEnd:r.arrowTypeEnd,arrowTypeStart:r.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"},void 0,"block",s,l),r.label&&(await cr(e,{...r,label:r.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:r.arrowTypeEnd,arrowTypeStart:r.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),or({...r,x:x[1].x,y:x[1].y},{originalPath:x}))}}}d(ie,"insertEdges");var Ur=d(function(e,t){return t.db.getClasses()},"getClasses"),jr=d(async function(e,t,a,i){const{securityLevel:l,block:s}=rt(),r=i.db;let n;l==="sandbox"&&(n=O("#i"+t));const c=l==="sandbox"?O(n.nodes()[0].contentDocument.body):O("body"),u=l==="sandbox"?c.select(`[id="${t}"]`):O(`[id="${t}"]`);ar(u,["point","circle","cross"],i.type,t);const x=r.getBlocks(),w=r.getBlocksFlat(),b=r.getEdges(),S=u.insert("g").attr("class","block");await ae(S,x,r);const v=jt(r);if(await se(S,x,r),await ie(S,b,w,r,t),v){const k=v,C=Math.max(1,Math.round(.125*(k.width/k.height))),E=k.height+C+10,D=k.width+10,{useMaxWidth:y}=s;de(u,E,D,!!y),L.debug("Here Bounds",v,k),u.attr("viewBox",`${k.x-5} ${k.y-5} ${k.width+10} ${k.height+10}`)}},"draw"),Vr={draw:jr,getClasses:Ur},Qr={parser:ve,db:Ke,renderer:Vr,styles:Ue};export{Qr as diagram};
