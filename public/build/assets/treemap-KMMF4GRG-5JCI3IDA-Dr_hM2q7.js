import{_ as a,aY as _e,a_ as F,b3 as Bd,D as ar,b4 as nv,aZ as or,bE as Po,j as Th,a5 as bo,bF as wt}from"./app-BQZQgfaL.js";import{C as ke,c as ne,f as N,A as Zs,m as I,D as J,q as ce,E as Fe,l as ys,h as w,F as ma,g as Le,v as Q,G as Oo,H as Ce,I as Ee,d as Cc,J as De,K as Ue,o as Se,e as iv,L as sv,M as av,N as te,O as fe,i as it,P as st,k as vs,Q as ea,R as Sc,S as Rh,T as Vd,U as Mo}from"./chunk-TGZYFRKZ-DbNMVLyI.js";var Ah=bo({"../../node_modules/.pnpm/vscode-jsonrpc@8.2.0/node_modules/vscode-jsonrpc/lib/common/ral.js"(r){Object.defineProperty(r,"__esModule",{value:!0});var e;function t(){if(e===void 0)throw new Error("No runtime abstraction layer installed");return e}a(t,"RAL"),(function(n){function i(s){if(s===void 0)throw new Error("No runtime abstraction layer provided");e=s}a(i,"install"),n.install=i})(t||(t={})),r.default=t}}),ov=bo({"../../node_modules/.pnpm/vscode-jsonrpc@8.2.0/node_modules/vscode-jsonrpc/lib/common/is.js"(r){Object.defineProperty(r,"__esModule",{value:!0}),r.stringArray=r.array=r.func=r.error=r.number=r.string=r.boolean=void 0;function e(c){return c===!0||c===!1}a(e,"boolean"),r.boolean=e;function t(c){return typeof c=="string"||c instanceof String}a(t,"string"),r.string=t;function n(c){return typeof c=="number"||c instanceof Number}a(n,"number"),r.number=n;function i(c){return c instanceof Error}a(i,"error"),r.error=i;function s(c){return typeof c=="function"}a(s,"func"),r.func=s;function o(c){return Array.isArray(c)}a(o,"array"),r.array=o;function l(c){return o(c)&&c.every(u=>t(u))}a(l,"stringArray"),r.stringArray=l}}),Eh=bo({"../../node_modules/.pnpm/vscode-jsonrpc@8.2.0/node_modules/vscode-jsonrpc/lib/common/events.js"(r){var s,o;Object.defineProperty(r,"__esModule",{value:!0}),r.Emitter=r.Event=void 0;var e=Ah(),t;(function(l){const c={dispose(){}};l.None=function(){return c}})(t||(r.Event=t={}));var n=(s=class{add(c,u=null,d){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(c),this._contexts.push(u),Array.isArray(d)&&d.push({dispose:a(()=>this.remove(c,u),"dispose")})}remove(c,u=null){if(!this._callbacks)return;let d=!1;for(let f=0,h=this._callbacks.length;f<h;f++)if(this._callbacks[f]===c)if(this._contexts[f]===u){this._callbacks.splice(f,1),this._contexts.splice(f,1);return}else d=!0;if(d)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...c){if(!this._callbacks)return[];const u=[],d=this._callbacks.slice(0),f=this._contexts.slice(0);for(let h=0,p=d.length;h<p;h++)try{u.push(d[h].apply(f[h],c))}catch(g){(0,e.default)().console.error(g)}return u}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}},a(s,"CallbackList"),s),i=(o=class{constructor(c){this._options=c}get event(){return this._event||(this._event=(c,u,d)=>{this._callbacks||(this._callbacks=new n),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(c,u);const f={dispose:a(()=>{this._callbacks&&(this._callbacks.remove(c,u),f.dispose=o._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))},"dispose")};return Array.isArray(d)&&d.push(f),f}),this._event}fire(c){this._callbacks&&this._callbacks.invoke.call(this._callbacks,c)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}},a(o,"Emitter"),o);r.Emitter=i,i._noop=function(){}}}),lv=bo({"../../node_modules/.pnpm/vscode-jsonrpc@8.2.0/node_modules/vscode-jsonrpc/lib/common/cancellation.js"(r){var c,u;Object.defineProperty(r,"__esModule",{value:!0}),r.CancellationTokenSource=r.CancellationToken=void 0;var e=Ah(),t=ov(),n=Eh(),i;(function(d){d.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:n.Event.None}),d.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:n.Event.None});function f(h){const p=h;return p&&(p===d.None||p===d.Cancelled||t.boolean(p.isCancellationRequested)&&!!p.onCancellationRequested)}a(f,"is"),d.is=f})(i||(r.CancellationToken=i={}));var s=Object.freeze(function(d,f){const h=(0,e.default)().timer.setTimeout(d.bind(f),0);return{dispose(){h.dispose()}}}),o=(c=class{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?s:(this._emitter||(this._emitter=new n.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}},a(c,"MutableToken"),c),l=(u=class{get token(){return this._token||(this._token=new o),this._token}cancel(){this._token?this._token.cancel():this._token=i.Cancelled}dispose(){this._token?this._token instanceof o&&this._token.dispose():this._token=i.None}},a(u,"CancellationTokenSource"),u);r.CancellationTokenSource=l}}),kh={};ar(kh,{AbstractAstReflection:()=>$c,AbstractCstNode:()=>ed,AbstractLangiumParser:()=>rd,AbstractParserErrorMessageProvider:()=>Dg,AbstractThreadedAsyncParser:()=>lT,AstUtils:()=>nu,BiMap:()=>wo,Cancellation:()=>G,CompositeCstNodeImpl:()=>cl,ContextCache:()=>ml,CstNodeBuilder:()=>bg,CstUtils:()=>Ic,DEFAULT_TOKENIZE_OPTIONS:()=>Rd,DONE_RESULT:()=>oe,DatatypeSymbol:()=>Io,DefaultAstNodeDescriptionProvider:()=>uy,DefaultAstNodeLocator:()=>fy,DefaultAsyncParser:()=>wy,DefaultCommentProvider:()=>Ny,DefaultConfigurationProvider:()=>hy,DefaultDocumentBuilder:()=>py,DefaultDocumentValidator:()=>cy,DefaultHydrator:()=>Ly,DefaultIndexManager:()=>my,DefaultJsonSerializer:()=>ay,DefaultLangiumDocumentFactory:()=>Jg,DefaultLangiumDocuments:()=>Qg,DefaultLexer:()=>Ad,DefaultLexerErrorMessageProvider:()=>yy,DefaultLinker:()=>Zg,DefaultNameProvider:()=>ey,DefaultReferenceDescriptionProvider:()=>dy,DefaultReferences:()=>ty,DefaultScopeComputation:()=>ry,DefaultScopeProvider:()=>sy,DefaultServiceRegistry:()=>oy,DefaultTokenBuilder:()=>dl,DefaultValueConverter:()=>ud,DefaultWorkspaceLock:()=>_y,DefaultWorkspaceManager:()=>gy,Deferred:()=>ft,Disposable:()=>Ft,DisposableCache:()=>pl,DocumentCache:()=>iy,DocumentState:()=>B,DocumentValidator:()=>we,EMPTY_SCOPE:()=>iT,EMPTY_STREAM:()=>Fo,EmptyFileSystem:()=>mt,EmptyFileSystemProvider:()=>Oy,ErrorWithLocation:()=>Uo,GrammarAST:()=>_h,GrammarUtils:()=>Dc,IndentationAwareLexer:()=>uT,IndentationAwareTokenBuilder:()=>by,JSDocDocumentationProvider:()=>$y,LangiumCompletionParser:()=>Fg,LangiumParser:()=>Mg,LangiumParserErrorMessageProvider:()=>nd,LeafCstNodeImpl:()=>So,LexingMode:()=>Mt,MapScope:()=>ny,Module:()=>Ac,MultiMap:()=>Ss,OperationCancelled:()=>kt,ParserWorker:()=>cT,Reduction:()=>ra,RegExpUtils:()=>au,RootCstNodeImpl:()=>td,SimpleCache:()=>md,StreamImpl:()=>He,StreamScope:()=>yc,TextDocument:()=>$o,TreeStreamImpl:()=>Ts,URI:()=>ht,UriUtils:()=>qe,ValidationCategory:()=>ha,ValidationRegistry:()=>ly,ValueConverter:()=>We,WorkspaceCache:()=>gd,assertUnreachable:()=>Ct,createCompletionParser:()=>od,createDefaultCoreModule:()=>Je,createDefaultSharedCoreModule:()=>Qe,createGrammarConfig:()=>Su,createLangiumParser:()=>ld,createParser:()=>ul,delayNextTick:()=>fl,diagnosticData:()=>Ot,eagerLoad:()=>$d,getDiagnosticRange:()=>vd,indentationBuilderDefaultOptions:()=>kc,inject:()=>Z,interruptAndCheck:()=>ae,isAstNode:()=>Y,isAstNodeDescription:()=>xc,isAstNodeWithComment:()=>yd,isCompositeCstNode:()=>at,isIMultiModeLexerDefinition:()=>vl,isJSDoc:()=>kd,isLeafCstNode:()=>lr,isLinkingError:()=>rn,isNamed:()=>pd,isOperationCancelled:()=>fr,isReference:()=>me,isRootCstNode:()=>Do,isTokenTypeArray:()=>yl,isTokenTypeDictionary:()=>_o,loadGrammarFromJson:()=>Ze,parseJSDoc:()=>Ed,prepareLangiumParser:()=>cd,setInterruptionPeriod:()=>dd,startCancelableOperation:()=>hl,stream:()=>q,toDiagnosticData:()=>Td,toDiagnosticSeverity:()=>Qs});var Ic={};ar(Ic,{DefaultNameRegexp:()=>Lc,RangeComparison:()=>Ke,compareRange:()=>wc,findCommentNode:()=>Pc,findDeclarationNodeAtOffset:()=>Ih,findLeafNodeAtOffset:()=>Go,findLeafNodeBeforeOffset:()=>bc,flattenCst:()=>Sh,getInteriorNodes:()=>Nh,getNextNode:()=>xh,getPreviousNode:()=>Mc,getStartlineNode:()=>$h,inRange:()=>_c,isChildNode:()=>Nc,isCommentNode:()=>no,streamCst:()=>Rs,toDocumentSegment:()=>As,tokenToRange:()=>na});function Y(r){return typeof r=="object"&&r!==null&&typeof r.$type=="string"}a(Y,"isAstNode");function me(r){return typeof r=="object"&&r!==null&&typeof r.$refText=="string"}a(me,"isReference");function xc(r){return typeof r=="object"&&r!==null&&typeof r.name=="string"&&typeof r.type=="string"&&typeof r.path=="string"}a(xc,"isAstNodeDescription");function rn(r){return typeof r=="object"&&r!==null&&Y(r.container)&&me(r.reference)&&typeof r.message=="string"}a(rn,"isLinkingError");var sn,$c=(sn=class{constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,t){return Y(e)&&this.isSubtype(e.$type,t)}isSubtype(e,t){if(e===t)return!0;let n=this.subtypes[e];n||(n=this.subtypes[e]={});const i=n[t];if(i!==void 0)return i;{const s=this.computeIsSubtype(e,t);return n[t]=s,s}}getAllSubTypes(e){const t=this.allSubtypes[e];if(t)return t;{const n=this.getAllTypes(),i=[];for(const s of n)this.isSubtype(s,e)&&i.push(s);return this.allSubtypes[e]=i,i}}},a(sn,"AbstractAstReflection"),sn);function at(r){return typeof r=="object"&&r!==null&&Array.isArray(r.content)}a(at,"isCompositeCstNode");function lr(r){return typeof r=="object"&&r!==null&&typeof r.tokenType=="object"}a(lr,"isLeafCstNode");function Do(r){return at(r)&&typeof r.fullText=="string"}a(Do,"isRootCstNode");var pe,He=(pe=class{constructor(e,t){this.startFn=e,this.nextFn=t}iterator(){const e={state:this.startFn(),next:a(()=>this.nextFn(e.state),"next"),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){const e=this.iterator();let t=0,n=e.next();for(;!n.done;)t++,n=e.next();return t}toArray(){const e=[],t=this.iterator();let n;do n=t.next(),n.value!==void 0&&e.push(n.value);while(!n.done);return e}toSet(){return new Set(this)}toMap(e,t){const n=this.map(i=>[e?e(i):i,t?t(i):i]);return new Map(n)}toString(){return this.join()}concat(e){return new pe(()=>({first:this.startFn(),firstDone:!1,iterator:e[Symbol.iterator]()}),t=>{let n;if(!t.firstDone){do if(n=this.nextFn(t.first),!n.done)return n;while(!n.done);t.firstDone=!0}do if(n=t.iterator.next(),!n.done)return n;while(!n.done);return oe})}join(e=","){const t=this.iterator();let n="",i,s=!1;do i=t.next(),i.done||(s&&(n+=e),n+=Ch(i.value)),s=!0;while(!i.done);return n}indexOf(e,t=0){const n=this.iterator();let i=0,s=n.next();for(;!s.done;){if(i>=t&&s.value===e)return i;s=n.next(),i++}return-1}every(e){const t=this.iterator();let n=t.next();for(;!n.done;){if(!e(n.value))return!1;n=t.next()}return!0}some(e){const t=this.iterator();let n=t.next();for(;!n.done;){if(e(n.value))return!0;n=t.next()}return!1}forEach(e){const t=this.iterator();let n=0,i=t.next();for(;!i.done;)e(i.value,n),i=t.next(),n++}map(e){return new pe(this.startFn,t=>{const{done:n,value:i}=this.nextFn(t);return n?oe:{done:!1,value:e(i)}})}filter(e){return new pe(this.startFn,t=>{let n;do if(n=this.nextFn(t),!n.done&&e(n.value))return n;while(!n.done);return oe})}nonNullable(){return this.filter(e=>e!=null)}reduce(e,t){const n=this.iterator();let i=t,s=n.next();for(;!s.done;)i===void 0?i=s.value:i=e(i,s.value),s=n.next();return i}reduceRight(e,t){return this.recursiveReduce(this.iterator(),e,t)}recursiveReduce(e,t,n){const i=e.next();if(i.done)return n;const s=this.recursiveReduce(e,t,n);return s===void 0?i.value:t(s,i.value)}find(e){const t=this.iterator();let n=t.next();for(;!n.done;){if(e(n.value))return n.value;n=t.next()}}findIndex(e){const t=this.iterator();let n=0,i=t.next();for(;!i.done;){if(e(i.value))return n;i=t.next(),n++}return-1}includes(e){const t=this.iterator();let n=t.next();for(;!n.done;){if(n.value===e)return!0;n=t.next()}return!1}flatMap(e){return new pe(()=>({this:this.startFn()}),t=>{do{if(t.iterator){const s=t.iterator.next();if(s.done)t.iterator=void 0;else return s}const{done:n,value:i}=this.nextFn(t.this);if(!n){const s=e(i);if(ta(s))t.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}}while(t.iterator);return oe})}flat(e){if(e===void 0&&(e=1),e<=0)return this;const t=e>1?this.flat(e-1):this;return new pe(()=>({this:t.startFn()}),n=>{do{if(n.iterator){const o=n.iterator.next();if(o.done)n.iterator=void 0;else return o}const{done:i,value:s}=t.nextFn(n.this);if(!i)if(ta(s))n.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}while(n.iterator);return oe})}head(){const t=this.iterator().next();if(!t.done)return t.value}tail(e=1){return new pe(()=>{const t=this.startFn();for(let n=0;n<e;n++)if(this.nextFn(t).done)return t;return t},this.nextFn)}limit(e){return new pe(()=>({size:0,state:this.startFn()}),t=>(t.size++,t.size>e?oe:this.nextFn(t.state)))}distinct(e){return new pe(()=>({set:new Set,internalState:this.startFn()}),t=>{let n;do if(n=this.nextFn(t.internalState),!n.done){const i=e?e(n.value):n.value;if(!t.set.has(i))return t.set.add(i),n}while(!n.done);return oe})}exclude(e,t){const n=new Set;for(const i of e){const s=t?t(i):i;n.add(s)}return this.filter(i=>{const s=t?t(i):i;return!n.has(s)})}},a(pe,"StreamImpl"),pe);function Ch(r){return typeof r=="string"?r:typeof r>"u"?"undefined":typeof r.toString=="function"?r.toString():Object.prototype.toString.call(r)}a(Ch,"toString");function ta(r){return!!r&&typeof r[Symbol.iterator]=="function"}a(ta,"isIterable");var Fo=new He(()=>{},()=>oe),oe=Object.freeze({done:!0,value:void 0});function q(...r){if(r.length===1){const e=r[0];if(e instanceof He)return e;if(ta(e))return new He(()=>e[Symbol.iterator](),t=>t.next());if(typeof e.length=="number")return new He(()=>({index:0}),t=>t.index<e.length?{done:!1,value:e[t.index++]}:oe)}return r.length>1?new He(()=>({collIndex:0,arrIndex:0}),e=>{do{if(e.iterator){const t=e.iterator.next();if(!t.done)return t;e.iterator=void 0}if(e.array){if(e.arrIndex<e.array.length)return{done:!1,value:e.array[e.arrIndex++]};e.array=void 0,e.arrIndex=0}if(e.collIndex<r.length){const t=r[e.collIndex++];ta(t)?e.iterator=t[Symbol.iterator]():t&&typeof t.length=="number"&&(e.array=t)}}while(e.iterator||e.array||e.collIndex<r.length);return oe}):Fo}a(q,"stream");var an,Ts=(an=class extends He{constructor(e,t,n){super(()=>({iterators:n?.includeRoot?[[e][Symbol.iterator]()]:[t(e)[Symbol.iterator]()],pruned:!1}),i=>{for(i.pruned&&(i.iterators.pop(),i.pruned=!1);i.iterators.length>0;){const o=i.iterators[i.iterators.length-1].next();if(o.done)i.iterators.pop();else return i.iterators.push(t(o.value)[Symbol.iterator]()),o}return oe})}iterator(){const e={state:this.startFn(),next:a(()=>this.nextFn(e.state),"next"),prune:a(()=>{e.state.pruned=!0},"prune"),[Symbol.iterator]:()=>e};return e}},a(an,"TreeStreamImpl"),an),ra;(function(r){function e(s){return s.reduce((o,l)=>o+l,0)}a(e,"sum"),r.sum=e;function t(s){return s.reduce((o,l)=>o*l,0)}a(t,"product"),r.product=t;function n(s){return s.reduce((o,l)=>Math.min(o,l))}a(n,"min"),r.min=n;function i(s){return s.reduce((o,l)=>Math.max(o,l))}a(i,"max"),r.max=i})(ra||(ra={}));function Rs(r){return new Ts(r,e=>at(e)?e.content:[],{includeRoot:!0})}a(Rs,"streamCst");function Sh(r){return Rs(r).filter(lr)}a(Sh,"flattenCst");function Nc(r,e){for(;r.container;)if(r=r.container,r===e)return!0;return!1}a(Nc,"isChildNode");function na(r){return{start:{character:r.startColumn-1,line:r.startLine-1},end:{character:r.endColumn,line:r.endLine-1}}}a(na,"tokenToRange");function As(r){if(!r)return;const{offset:e,end:t,range:n}=r;return{range:n,offset:e,end:t,length:t-e}}a(As,"toDocumentSegment");var Ke;(function(r){r[r.Before=0]="Before",r[r.After=1]="After",r[r.OverlapFront=2]="OverlapFront",r[r.OverlapBack=3]="OverlapBack",r[r.Inside=4]="Inside",r[r.Outside=5]="Outside"})(Ke||(Ke={}));function wc(r,e){if(r.end.line<e.start.line||r.end.line===e.start.line&&r.end.character<=e.start.character)return Ke.Before;if(r.start.line>e.end.line||r.start.line===e.end.line&&r.start.character>=e.end.character)return Ke.After;const t=r.start.line>e.start.line||r.start.line===e.start.line&&r.start.character>=e.start.character,n=r.end.line<e.end.line||r.end.line===e.end.line&&r.end.character<=e.end.character;return t&&n?Ke.Inside:t?Ke.OverlapBack:n?Ke.OverlapFront:Ke.Outside}a(wc,"compareRange");function _c(r,e){return wc(r,e)>Ke.After}a(_c,"inRange");var Lc=/^[\w\p{L}]$/u;function Ih(r,e,t=Lc){if(r){if(e>0){const n=e-r.offset,i=r.text.charAt(n);t.test(i)||e--}return Go(r,e)}}a(Ih,"findDeclarationNodeAtOffset");function Pc(r,e){if(r){const t=Mc(r,!0);if(t&&no(t,e))return t;if(Do(r)){const n=r.content.findIndex(i=>!i.hidden);for(let i=n-1;i>=0;i--){const s=r.content[i];if(no(s,e))return s}}}}a(Pc,"findCommentNode");function no(r,e){return lr(r)&&e.includes(r.tokenType.name)}a(no,"isCommentNode");function Go(r,e){if(lr(r))return r;if(at(r)){const t=Oc(r,e,!1);if(t)return Go(t,e)}}a(Go,"findLeafNodeAtOffset");function bc(r,e){if(lr(r))return r;if(at(r)){const t=Oc(r,e,!0);if(t)return bc(t,e)}}a(bc,"findLeafNodeBeforeOffset");function Oc(r,e,t){let n=0,i=r.content.length-1,s;for(;n<=i;){const o=Math.floor((n+i)/2),l=r.content[o];if(l.offset<=e&&l.end>e)return l;l.end<=e?(s=t?l:void 0,n=o+1):i=o-1}return s}a(Oc,"binarySearch");function Mc(r,e=!0){for(;r.container;){const t=r.container;let n=t.content.indexOf(r);for(;n>0;){n--;const i=t.content[n];if(e||!i.hidden)return i}r=t}}a(Mc,"getPreviousNode");function xh(r,e=!0){for(;r.container;){const t=r.container;let n=t.content.indexOf(r);const i=t.content.length-1;for(;n<i;){n++;const s=t.content[n];if(e||!s.hidden)return s}r=t}}a(xh,"getNextNode");function $h(r){if(r.range.start.character===0)return r;const e=r.range.start.line;let t=r,n;for(;r.container;){const i=r.container,s=n??i.content.indexOf(r);if(s===0?(r=i,n=void 0):(n=s-1,r=i.content[n]),r.range.start.line!==e)break;t=r}return t}a($h,"getStartlineNode");function Nh(r,e){const t=wh(r,e);return t?t.parent.content.slice(t.a+1,t.b):[]}a(Nh,"getInteriorNodes");function wh(r,e){const t=Ml(r),n=Ml(e);let i;for(let s=0;s<t.length&&s<n.length;s++){const o=t[s],l=n[s];if(o.parent===l.parent)i={parent:o.parent,a:o.index,b:l.index};else break}return i}a(wh,"getCommonParent");function Ml(r){const e=[];for(;r.container;){const t=r.container,n=t.content.indexOf(r);e.push({parent:t,index:n}),r=t}return e.reverse()}a(Ml,"getParentChain");var Dc={};ar(Dc,{findAssignment:()=>vu,findNameAssignment:()=>el,findNodeForKeyword:()=>yu,findNodeForProperty:()=>Jo,findNodesForKeyword:()=>ep,findNodesForKeywordInternal:()=>Zo,findNodesForProperty:()=>gu,getActionAtElement:()=>Ru,getActionType:()=>Eu,getAllReachableRules:()=>Xo,getCrossReferenceTerminal:()=>pu,getEntryRule:()=>du,getExplicitRuleType:()=>$s,getHiddenRules:()=>fu,getRuleType:()=>ku,getRuleTypeName:()=>sp,getTypeName:()=>Aa,isArrayCardinality:()=>rp,isArrayOperator:()=>np,isCommentTerminal:()=>mu,isDataType:()=>ip,isDataTypeRule:()=>Ra,isOptionalCardinality:()=>tp,terminalRegex:()=>Ea});var on,Uo=(on=class extends Error{constructor(e,t){super(e?`${t} at ${e.range.start.line}:${e.range.start.character}`:t)}},a(on,"ErrorWithLocation"),on);function Ct(r){throw new Error("Error! The input value was not handled.")}a(Ct,"assertUnreachable");var _h={};ar(_h,{AbstractElement:()=>Tr,AbstractRule:()=>yr,AbstractType:()=>vr,Action:()=>Fr,Alternatives:()=>Gr,ArrayLiteral:()=>Rr,ArrayType:()=>Ar,Assignment:()=>Ur,BooleanLiteral:()=>Er,CharacterRange:()=>Br,Condition:()=>Ks,Conjunction:()=>kr,CrossReference:()=>Vr,Disjunction:()=>Cr,EndOfFile:()=>Wr,Grammar:()=>Sr,GrammarImport:()=>Hs,Group:()=>Kr,InferredType:()=>Ir,Interface:()=>xr,Keyword:()=>jr,LangiumGrammarAstReflection:()=>ru,LangiumGrammarTerminals:()=>cv,NamedArgument:()=>zs,NegatedToken:()=>Hr,Negation:()=>$r,NumberLiteral:()=>Nr,Parameter:()=>wr,ParameterReference:()=>_r,ParserRule:()=>Lr,ReferenceType:()=>Pr,RegexToken:()=>zr,ReturnType:()=>qs,RuleCall:()=>qr,SimpleType:()=>br,StringLiteral:()=>Or,TerminalAlternatives:()=>Yr,TerminalGroup:()=>Xr,TerminalRule:()=>Lt,TerminalRuleCall:()=>Jr,Type:()=>Mr,TypeAttribute:()=>Ys,TypeDefinition:()=>io,UnionType:()=>Dr,UnorderedGroup:()=>Qr,UntilToken:()=>Zr,ValueLiteral:()=>js,Wildcard:()=>en,isAbstractElement:()=>Bo,isAbstractRule:()=>Lh,isAbstractType:()=>Ph,isAction:()=>Tt,isAlternatives:()=>jo,isArrayLiteral:()=>Fh,isArrayType:()=>Gc,isAssignment:()=>ot,isBooleanLiteral:()=>Uc,isCharacterRange:()=>qc,isCondition:()=>bh,isConjunction:()=>Bc,isCrossReference:()=>ya,isDisjunction:()=>Vc,isEndOfFile:()=>Yc,isFeatureName:()=>Oh,isGrammar:()=>Gh,isGrammarImport:()=>Uh,isGroup:()=>cr,isInferredType:()=>Vo,isInterface:()=>Wo,isKeyword:()=>lt,isNamedArgument:()=>Bh,isNegatedToken:()=>Xc,isNegation:()=>Wc,isNumberLiteral:()=>Vh,isParameter:()=>Wh,isParameterReference:()=>Kc,isParserRule:()=>ge,isPrimitiveType:()=>Fc,isReferenceType:()=>jc,isRegexToken:()=>Jc,isReturnType:()=>Hc,isRuleCall:()=>ct,isSimpleType:()=>Ko,isStringLiteral:()=>Kh,isTerminalAlternatives:()=>Qc,isTerminalGroup:()=>Zc,isTerminalRule:()=>Ye,isTerminalRuleCall:()=>Ho,isType:()=>ga,isTypeAttribute:()=>jh,isTypeDefinition:()=>Mh,isUnionType:()=>zc,isUnorderedGroup:()=>zo,isUntilToken:()=>eu,isValueLiteral:()=>Dh,isWildcard:()=>tu,reflection:()=>L});var cv={ID:/\^?[_a-zA-Z][\w_]*/,STRING:/"(\\.|[^"\\])*"|'(\\.|[^'\\])*'/,NUMBER:/NaN|-?((\d*\.\d+|\d+)([Ee][+-]?\d+)?|Infinity)/,RegexLiteral:/\/(?![*+?])(?:[^\r\n\[/\\]|\\.|\[(?:[^\r\n\]\\]|\\.)*\])+\/[a-z]*/,WS:/\s+/,ML_COMMENT:/\/\*[\s\S]*?\*\//,SL_COMMENT:/\/\/[^\n\r]*/},yr="AbstractRule";function Lh(r){return L.isInstance(r,yr)}a(Lh,"isAbstractRule");var vr="AbstractType";function Ph(r){return L.isInstance(r,vr)}a(Ph,"isAbstractType");var Ks="Condition";function bh(r){return L.isInstance(r,Ks)}a(bh,"isCondition");function Oh(r){return Fc(r)||r==="current"||r==="entry"||r==="extends"||r==="false"||r==="fragment"||r==="grammar"||r==="hidden"||r==="import"||r==="interface"||r==="returns"||r==="terminal"||r==="true"||r==="type"||r==="infer"||r==="infers"||r==="with"||typeof r=="string"&&/\^?[_a-zA-Z][\w_]*/.test(r)}a(Oh,"isFeatureName");function Fc(r){return r==="string"||r==="number"||r==="boolean"||r==="Date"||r==="bigint"}a(Fc,"isPrimitiveType");var io="TypeDefinition";function Mh(r){return L.isInstance(r,io)}a(Mh,"isTypeDefinition");var js="ValueLiteral";function Dh(r){return L.isInstance(r,js)}a(Dh,"isValueLiteral");var Tr="AbstractElement";function Bo(r){return L.isInstance(r,Tr)}a(Bo,"isAbstractElement");var Rr="ArrayLiteral";function Fh(r){return L.isInstance(r,Rr)}a(Fh,"isArrayLiteral");var Ar="ArrayType";function Gc(r){return L.isInstance(r,Ar)}a(Gc,"isArrayType");var Er="BooleanLiteral";function Uc(r){return L.isInstance(r,Er)}a(Uc,"isBooleanLiteral");var kr="Conjunction";function Bc(r){return L.isInstance(r,kr)}a(Bc,"isConjunction");var Cr="Disjunction";function Vc(r){return L.isInstance(r,Cr)}a(Vc,"isDisjunction");var Sr="Grammar";function Gh(r){return L.isInstance(r,Sr)}a(Gh,"isGrammar");var Hs="GrammarImport";function Uh(r){return L.isInstance(r,Hs)}a(Uh,"isGrammarImport");var Ir="InferredType";function Vo(r){return L.isInstance(r,Ir)}a(Vo,"isInferredType");var xr="Interface";function Wo(r){return L.isInstance(r,xr)}a(Wo,"isInterface");var zs="NamedArgument";function Bh(r){return L.isInstance(r,zs)}a(Bh,"isNamedArgument");var $r="Negation";function Wc(r){return L.isInstance(r,$r)}a(Wc,"isNegation");var Nr="NumberLiteral";function Vh(r){return L.isInstance(r,Nr)}a(Vh,"isNumberLiteral");var wr="Parameter";function Wh(r){return L.isInstance(r,wr)}a(Wh,"isParameter");var _r="ParameterReference";function Kc(r){return L.isInstance(r,_r)}a(Kc,"isParameterReference");var Lr="ParserRule";function ge(r){return L.isInstance(r,Lr)}a(ge,"isParserRule");var Pr="ReferenceType";function jc(r){return L.isInstance(r,Pr)}a(jc,"isReferenceType");var qs="ReturnType";function Hc(r){return L.isInstance(r,qs)}a(Hc,"isReturnType");var br="SimpleType";function Ko(r){return L.isInstance(r,br)}a(Ko,"isSimpleType");var Or="StringLiteral";function Kh(r){return L.isInstance(r,Or)}a(Kh,"isStringLiteral");var Lt="TerminalRule";function Ye(r){return L.isInstance(r,Lt)}a(Ye,"isTerminalRule");var Mr="Type";function ga(r){return L.isInstance(r,Mr)}a(ga,"isType");var Ys="TypeAttribute";function jh(r){return L.isInstance(r,Ys)}a(jh,"isTypeAttribute");var Dr="UnionType";function zc(r){return L.isInstance(r,Dr)}a(zc,"isUnionType");var Fr="Action";function Tt(r){return L.isInstance(r,Fr)}a(Tt,"isAction");var Gr="Alternatives";function jo(r){return L.isInstance(r,Gr)}a(jo,"isAlternatives");var Ur="Assignment";function ot(r){return L.isInstance(r,Ur)}a(ot,"isAssignment");var Br="CharacterRange";function qc(r){return L.isInstance(r,Br)}a(qc,"isCharacterRange");var Vr="CrossReference";function ya(r){return L.isInstance(r,Vr)}a(ya,"isCrossReference");var Wr="EndOfFile";function Yc(r){return L.isInstance(r,Wr)}a(Yc,"isEndOfFile");var Kr="Group";function cr(r){return L.isInstance(r,Kr)}a(cr,"isGroup");var jr="Keyword";function lt(r){return L.isInstance(r,jr)}a(lt,"isKeyword");var Hr="NegatedToken";function Xc(r){return L.isInstance(r,Hr)}a(Xc,"isNegatedToken");var zr="RegexToken";function Jc(r){return L.isInstance(r,zr)}a(Jc,"isRegexToken");var qr="RuleCall";function ct(r){return L.isInstance(r,qr)}a(ct,"isRuleCall");var Yr="TerminalAlternatives";function Qc(r){return L.isInstance(r,Yr)}a(Qc,"isTerminalAlternatives");var Xr="TerminalGroup";function Zc(r){return L.isInstance(r,Xr)}a(Zc,"isTerminalGroup");var Jr="TerminalRuleCall";function Ho(r){return L.isInstance(r,Jr)}a(Ho,"isTerminalRuleCall");var Qr="UnorderedGroup";function zo(r){return L.isInstance(r,Qr)}a(zo,"isUnorderedGroup");var Zr="UntilToken";function eu(r){return L.isInstance(r,Zr)}a(eu,"isUntilToken");var en="Wildcard";function tu(r){return L.isInstance(r,en)}a(tu,"isWildcard");var ln,ru=(ln=class extends $c{getAllTypes(){return[Tr,yr,vr,Fr,Gr,Rr,Ar,Ur,Er,Br,Ks,kr,Vr,Cr,Wr,Sr,Hs,Kr,Ir,xr,jr,zs,Hr,$r,Nr,wr,_r,Lr,Pr,zr,qs,qr,br,Or,Yr,Xr,Lt,Jr,Mr,Ys,io,Dr,Qr,Zr,js,en]}computeIsSubtype(e,t){switch(e){case Fr:case Gr:case Ur:case Br:case Vr:case Wr:case Kr:case jr:case Hr:case zr:case qr:case Yr:case Xr:case Jr:case Qr:case Zr:case en:return this.isSubtype(Tr,t);case Rr:case Nr:case Or:return this.isSubtype(js,t);case Ar:case Pr:case br:case Dr:return this.isSubtype(io,t);case Er:return this.isSubtype(Ks,t)||this.isSubtype(js,t);case kr:case Cr:case $r:case _r:return this.isSubtype(Ks,t);case Ir:case xr:case Mr:return this.isSubtype(vr,t);case Lr:return this.isSubtype(yr,t)||this.isSubtype(vr,t);case Lt:return this.isSubtype(yr,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return vr;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return yr;case"Grammar:usedGrammars":return Sr;case"NamedArgument:parameter":case"ParameterReference:parameter":return wr;case"TerminalRuleCall:rule":return Lt;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case Tr:return{name:Tr,properties:[{name:"cardinality"},{name:"lookahead"}]};case Rr:return{name:Rr,properties:[{name:"elements",defaultValue:[]}]};case Ar:return{name:Ar,properties:[{name:"elementType"}]};case Er:return{name:Er,properties:[{name:"true",defaultValue:!1}]};case kr:return{name:kr,properties:[{name:"left"},{name:"right"}]};case Cr:return{name:Cr,properties:[{name:"left"},{name:"right"}]};case Sr:return{name:Sr,properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case Hs:return{name:Hs,properties:[{name:"path"}]};case Ir:return{name:Ir,properties:[{name:"name"}]};case xr:return{name:xr,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case zs:return{name:zs,properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case $r:return{name:$r,properties:[{name:"value"}]};case Nr:return{name:Nr,properties:[{name:"value"}]};case wr:return{name:wr,properties:[{name:"name"}]};case _r:return{name:_r,properties:[{name:"parameter"}]};case Lr:return{name:Lr,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case Pr:return{name:Pr,properties:[{name:"referenceType"}]};case qs:return{name:qs,properties:[{name:"name"}]};case br:return{name:br,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case Or:return{name:Or,properties:[{name:"value"}]};case Lt:return{name:Lt,properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case Mr:return{name:Mr,properties:[{name:"name"},{name:"type"}]};case Ys:return{name:Ys,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case Dr:return{name:Dr,properties:[{name:"types",defaultValue:[]}]};case Fr:return{name:Fr,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case Gr:return{name:Gr,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Ur:return{name:Ur,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case Br:return{name:Br,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case Vr:return{name:Vr,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case Wr:return{name:Wr,properties:[{name:"cardinality"},{name:"lookahead"}]};case Kr:return{name:Kr,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case jr:return{name:jr,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case Hr:return{name:Hr,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case zr:return{name:zr,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case qr:return{name:qr,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case Yr:return{name:Yr,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Xr:return{name:Xr,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Jr:return{name:Jr,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case Qr:return{name:Qr,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Zr:return{name:Zr,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case en:return{name:en,properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}},a(ln,"LangiumGrammarAstReflection"),ln),L=new ru,nu={};ar(nu,{assignMandatoryProperties:()=>iu,copyAstNode:()=>ao,findLocalReferences:()=>zh,findRootNode:()=>ia,getContainerOfType:()=>Is,getDocument:()=>Ge,hasContainerOfType:()=>Hh,linkContentToContainer:()=>qo,streamAllContents:()=>St,streamAst:()=>nt,streamContents:()=>va,streamReferences:()=>Ta});function qo(r){for(const[e,t]of Object.entries(r))e.startsWith("$")||(Array.isArray(t)?t.forEach((n,i)=>{Y(n)&&(n.$container=r,n.$containerProperty=e,n.$containerIndex=i)}):Y(t)&&(t.$container=r,t.$containerProperty=e))}a(qo,"linkContentToContainer");function Is(r,e){let t=r;for(;t;){if(e(t))return t;t=t.$container}}a(Is,"getContainerOfType");function Hh(r,e){let t=r;for(;t;){if(e(t))return!0;t=t.$container}return!1}a(Hh,"hasContainerOfType");function Ge(r){const t=ia(r).$document;if(!t)throw new Error("AST node has no document.");return t}a(Ge,"getDocument");function ia(r){for(;r.$container;)r=r.$container;return r}a(ia,"findRootNode");function va(r,e){if(!r)throw new Error("Node must be an AstNode.");const t=e?.range;return new He(()=>({keys:Object.keys(r),keyIndex:0,arrayIndex:0}),n=>{for(;n.keyIndex<n.keys.length;){const i=n.keys[n.keyIndex];if(!i.startsWith("$")){const s=r[i];if(Y(s)){if(n.keyIndex++,so(s,t))return{done:!1,value:s}}else if(Array.isArray(s)){for(;n.arrayIndex<s.length;){const o=n.arrayIndex++,l=s[o];if(Y(l)&&so(l,t))return{done:!1,value:l}}n.arrayIndex=0}}n.keyIndex++}return oe})}a(va,"streamContents");function St(r,e){if(!r)throw new Error("Root node must be an AstNode.");return new Ts(r,t=>va(t,e))}a(St,"streamAllContents");function nt(r,e){if(r){if(e?.range&&!so(r,e.range))return new Ts(r,()=>[])}else throw new Error("Root node must be an AstNode.");return new Ts(r,t=>va(t,e),{includeRoot:!0})}a(nt,"streamAst");function so(r,e){var t;if(!e)return!0;const n=(t=r.$cstNode)===null||t===void 0?void 0:t.range;return n?_c(n,e):!1}a(so,"isAstNodeInRange");function Ta(r){return new He(()=>({keys:Object.keys(r),keyIndex:0,arrayIndex:0}),e=>{for(;e.keyIndex<e.keys.length;){const t=e.keys[e.keyIndex];if(!t.startsWith("$")){const n=r[t];if(me(n))return e.keyIndex++,{done:!1,value:{reference:n,container:r,property:t}};if(Array.isArray(n)){for(;e.arrayIndex<n.length;){const i=e.arrayIndex++,s=n[i];if(me(s))return{done:!1,value:{reference:s,container:r,property:t,index:i}}}e.arrayIndex=0}}e.keyIndex++}return oe})}a(Ta,"streamReferences");function zh(r,e=Ge(r).parseResult.value){const t=[];return nt(e).forEach(n=>{Ta(n).forEach(i=>{i.reference.ref===r&&t.push(i.reference)})}),q(t)}a(zh,"findLocalReferences");function iu(r,e){const t=r.getTypeMetaData(e.$type),n=e;for(const i of t.properties)i.defaultValue!==void 0&&n[i.name]===void 0&&(n[i.name]=su(i.defaultValue))}a(iu,"assignMandatoryProperties");function su(r){return Array.isArray(r)?[...r.map(su)]:r}a(su,"copyDefaultValue");function ao(r,e){const t={$type:r.$type};for(const[n,i]of Object.entries(r))if(!n.startsWith("$"))if(Y(i))t[n]=ao(i,e);else if(me(i))t[n]=e(t,n,i.$refNode,i.$refText);else if(Array.isArray(i)){const s=[];for(const o of i)Y(o)?s.push(ao(o,e)):me(o)?s.push(e(t,n,o.$refNode,o.$refText)):s.push(o);t[n]=s}else t[n]=i;return qo(t),t}a(ao,"copyAstNode");var au={};ar(au,{NEWLINE_REGEXP:()=>Xh,escapeRegExp:()=>xs,getCaseInsensitivePattern:()=>lu,getTerminalParts:()=>Qh,isMultilineComment:()=>ou,isWhitespace:()=>sa,partialMatches:()=>cu,partialRegExp:()=>uu,whitespaceCharacters:()=>Zh});function _(r){return r.charCodeAt(0)}a(_,"cc");function Fa(r,e){Array.isArray(r)?r.forEach(function(t){e.push(t)}):e.push(r)}a(Fa,"insertToSet");function mr(r,e){if(r[e]===!0)throw"duplicate flag "+e;r[e],r[e]=!0}a(mr,"addFlag");function _t(r){if(r===void 0)throw Error("Internal Error - Should never get here!");return!0}a(_t,"ASSERT_EXISTS");function qh(){throw Error("Internal Error - Should never get here!")}a(qh,"ASSERT_NEVER_REACH_HERE");function Dl(r){return r.type==="Character"}a(Dl,"isCharacter");var oo=[];for(let r=_("0");r<=_("9");r++)oo.push(r);var lo=[_("_")].concat(oo);for(let r=_("a");r<=_("z");r++)lo.push(r);for(let r=_("A");r<=_("Z");r++)lo.push(r);var Wd=[_(" "),_("\f"),_(`
`),_("\r"),_("	"),_("\v"),_("	"),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_("\u2028"),_("\u2029"),_(" "),_(" "),_("　"),_("\uFEFF")],uv=/[0-9a-fA-F]/,$a=/[0-9]/,dv=/[1-9]/,cn,Yh=(cn=class{constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");const t=this.disjunction();this.consumeChar("/");const n={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":mr(n,"global");break;case"i":mr(n,"ignoreCase");break;case"m":mr(n,"multiLine");break;case"u":mr(n,"unicode");break;case"y":mr(n,"sticky");break}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:n,value:t,loc:this.loc(0)}}disjunction(){const e=[],t=this.idx;for(e.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(t)}}alternative(){const e=[],t=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(t)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){const e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let t;switch(this.popChar()){case"=":t="Lookahead";break;case"!":t="NegativeLookahead";break}_t(t);const n=this.disjunction();return this.consumeChar(")"),{type:t,value:n,loc:this.loc(e)}}return qh()}quantifier(e=!1){let t;const n=this.idx;switch(this.popChar()){case"*":t={atLeast:0,atMost:1/0};break;case"+":t={atLeast:1,atMost:1/0};break;case"?":t={atLeast:0,atMost:1};break;case"{":const i=this.integerIncludingZero();switch(this.popChar()){case"}":t={atLeast:i,atMost:i};break;case",":let s;this.isDigit()?(s=this.integerIncludingZero(),t={atLeast:i,atMost:s}):t={atLeast:i,atMost:1/0},this.consumeChar("}");break}if(e===!0&&t===void 0)return;_t(t);break}if(!(e===!0&&t===void 0)&&_t(t))return this.peekChar(0)==="?"?(this.consumeChar("?"),t.greedy=!1):t.greedy=!0,t.type="Quantifier",t.loc=this.loc(n),t}atom(){let e;const t=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group();break}if(e===void 0&&this.isPatternCharacter()&&(e=this.patternCharacter()),_t(e))return e.loc=this.loc(t),this.isQuantifier()&&(e.quantifier=this.quantifier()),e}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[_(`
`),_("\r"),_("\u2028"),_("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,t=!1;switch(this.popChar()){case"d":e=oo;break;case"D":e=oo,t=!0;break;case"s":e=Wd;break;case"S":e=Wd,t=!0;break;case"w":e=lo;break;case"W":e=lo,t=!0;break}if(_t(e))return{type:"Set",value:e,complement:t}}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=_("\f");break;case"n":e=_(`
`);break;case"r":e=_("\r");break;case"t":e=_("	");break;case"v":e=_("\v");break}if(_t(e))return{type:"Character",value:e}}controlLetterEscapeAtom(){this.consumeChar("c");const e=this.popChar();if(/[a-zA-Z]/.test(e)===!1)throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:_("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){const e=this.popChar();return{type:"Character",value:_(e)}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:const e=this.popChar();return{type:"Character",value:_(e)}}}characterClass(){const e=[];let t=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),t=!0);this.isClassAtom();){const n=this.classAtom();if(n.type,Dl(n)&&this.isRangeDash()){this.consumeChar("-");const i=this.classAtom();if(i.type,Dl(i)){if(i.value<n.value)throw Error("Range out of order in character class");e.push({from:n.value,to:i.value})}else Fa(n.value,e),e.push(_("-")),Fa(i.value,e)}else Fa(n.value,e)}return this.consumeChar("]"),{type:"Set",complement:t,value:e}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:_("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),e=!1;break;default:this.groupIdx++;break}const t=this.disjunction();this.consumeChar(")");const n={type:"Group",capturing:e,value:t};return e&&(n.idx=this.groupIdx),n}positiveInteger(){let e=this.popChar();if(dv.test(e)===!1)throw Error("Expecting a positive integer");for(;$a.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if($a.test(e)===!1)throw Error("Expecting an integer");for(;$a.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){const e=this.popChar();switch(e){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:_(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return $a.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){const e=this.saveState();try{return this.quantifier(!0)!==void 0}catch{return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let t="";for(let i=0;i<e;i++){const s=this.popChar();if(uv.test(s)===!1)throw Error("Expecting a HexDecimal digits");t+=s}return{type:"Character",value:parseInt(t,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){const e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(e!==void 0&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}},a(cn,"RegExpParser"),cn),un,Yo=(un=class{visitChildren(e){for(const t in e){const n=e[t];e.hasOwnProperty(t)&&(n.type!==void 0?this.visit(n):Array.isArray(n)&&n.forEach(i=>{this.visit(i)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e);break}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}},a(un,"BaseRegExpVisitor"),un),Xh=/\r?\n/gm,Jh=new Yh,dn,fv=(dn=class extends Yo{constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){const t=String.fromCharCode(e.value);if(!this.multiline&&t===`
`&&(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const n=xs(t);this.endRegexpStack.push(n),this.isStarting&&(this.startRegexp+=n)}}visitSet(e){if(!this.multiline){const t=this.regex.substring(e.loc.begin,e.loc.end),n=new RegExp(t);this.multiline=!!`
`.match(n)}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const t=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(t),this.isStarting&&(this.startRegexp+=t)}}visitChildren(e){e.type==="Group"&&e.quantifier||super.visitChildren(e)}},a(dn,"TerminalRegExpVisitor"),dn),Pt=new fv;function Qh(r){try{typeof r!="string"&&(r=r.source),r=`/${r}/`;const e=Jh.pattern(r),t=[];for(const n of e.value.value)Pt.reset(r),Pt.visit(n),t.push({start:Pt.startRegexp,end:Pt.endRegex});return t}catch{return[]}}a(Qh,"getTerminalParts");function ou(r){try{return typeof r=="string"&&(r=new RegExp(r)),r=r.toString(),Pt.reset(r),Pt.visit(Jh.pattern(r)),Pt.multiline}catch{return!1}}a(ou,"isMultilineComment");var Zh=`\f
\r	\v              \u2028\u2029  　\uFEFF`.split("");function sa(r){const e=typeof r=="string"?new RegExp(r):r;return Zh.some(t=>e.test(t))}a(sa,"isWhitespace");function xs(r){return r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}a(xs,"escapeRegExp");function lu(r){return Array.prototype.map.call(r,e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:xs(e)).join("")}a(lu,"getCaseInsensitivePattern");function cu(r,e){const t=uu(r),n=e.match(t);return!!n&&n[0].length>0}a(cu,"partialMatches");function uu(r){typeof r=="string"&&(r=new RegExp(r));const e=r,t=r.source;let n=0;function i(){let s="",o;function l(u){s+=t.substr(n,u),n+=u}a(l,"appendRaw");function c(u){s+="(?:"+t.substr(n,u)+"|$)",n+=u}for(a(c,"appendOptional");n<t.length;)switch(t[n]){case"\\":switch(t[n+1]){case"c":c(3);break;case"x":c(4);break;case"u":e.unicode?t[n+2]==="{"?c(t.indexOf("}",n)-n+1):c(6):c(2);break;case"p":case"P":e.unicode?c(t.indexOf("}",n)-n+1):c(2);break;case"k":c(t.indexOf(">",n)-n+1);break;default:c(2);break}break;case"[":o=/\[(?:\\.|.)*?\]/g,o.lastIndex=n,o=o.exec(t)||[],c(o[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":l(1);break;case"{":o=/\{\d+,?\d*\}/g,o.lastIndex=n,o=o.exec(t),o?l(o[0].length):c(1);break;case"(":if(t[n+1]==="?")switch(t[n+2]){case":":s+="(?:",n+=3,s+=i()+"|$)";break;case"=":s+="(?=",n+=3,s+=i()+")";break;case"!":o=n,n+=3,i(),s+=t.substr(o,n-o);break;case"<":switch(t[n+3]){case"=":case"!":o=n,n+=4,i(),s+=t.substr(o,n-o);break;default:l(t.indexOf(">",n)-n+1),s+=i()+"|$)";break}break}else l(1),s+=i()+"|$)";break;case")":return++n,s;default:c(1);break}return s}return a(i,"process"),new RegExp(i(),r.flags)}a(uu,"partialRegExp");function du(r){return r.rules.find(e=>ge(e)&&e.entry)}a(du,"getEntryRule");function fu(r){return r.rules.filter(e=>Ye(e)&&e.hidden)}a(fu,"getHiddenRules");function Xo(r,e){const t=new Set,n=du(r);if(!n)return new Set(r.rules);const i=[n].concat(fu(r));for(const o of i)hu(o,t,e);const s=new Set;for(const o of r.rules)(t.has(o.name)||Ye(o)&&o.hidden)&&s.add(o);return s}a(Xo,"getAllReachableRules");function hu(r,e,t){e.add(r.name),St(r).forEach(n=>{if(ct(n)||t&&Ho(n)){const i=n.rule.ref;i&&!e.has(i.name)&&hu(i,e,t)}})}a(hu,"ruleDfs");function pu(r){if(r.terminal)return r.terminal;if(r.type.ref){const e=el(r.type.ref);return e?.terminal}}a(pu,"getCrossReferenceTerminal");function mu(r){return r.hidden&&!sa(Ea(r))}a(mu,"isCommentTerminal");function gu(r,e){return!r||!e?[]:Qo(r,e,r.astNode,!0)}a(gu,"findNodesForProperty");function Jo(r,e,t){if(!r||!e)return;const n=Qo(r,e,r.astNode,!0);if(n.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,n.length-1)):t=0,n[t]}a(Jo,"findNodeForProperty");function Qo(r,e,t,n){if(!n){const i=Is(r.grammarSource,ot);if(i&&i.feature===e)return[r]}return at(r)&&r.astNode===t?r.content.flatMap(i=>Qo(i,e,t,!1)):[]}a(Qo,"findNodesForPropertyInternal");function ep(r,e){return r?Zo(r,e,r?.astNode):[]}a(ep,"findNodesForKeyword");function yu(r,e,t){if(!r)return;const n=Zo(r,e,r?.astNode);if(n.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,n.length-1)):t=0,n[t]}a(yu,"findNodeForKeyword");function Zo(r,e,t){if(r.astNode!==t)return[];if(lt(r.grammarSource)&&r.grammarSource.value===e)return[r];const n=Rs(r).iterator();let i;const s=[];do if(i=n.next(),!i.done){const o=i.value;o.astNode===t?lt(o.grammarSource)&&o.grammarSource.value===e&&s.push(o):n.prune()}while(!i.done);return s}a(Zo,"findNodesForKeywordInternal");function vu(r){var e;const t=r.astNode;for(;t===((e=r.container)===null||e===void 0?void 0:e.astNode);){const n=Is(r.grammarSource,ot);if(n)return n;r=r.container}}a(vu,"findAssignment");function el(r){let e=r;return Vo(e)&&(Tt(e.$container)?e=e.$container.$container:ge(e.$container)?e=e.$container:Ct(e.$container)),Tu(r,e,new Map)}a(el,"findNameAssignment");function Tu(r,e,t){var n;function i(s,o){let l;return Is(s,ot)||(l=Tu(o,o,t)),t.set(r,l),l}if(a(i,"go"),t.has(r))return t.get(r);t.set(r,void 0);for(const s of St(e)){if(ot(s)&&s.feature.toLowerCase()==="name")return t.set(r,s),s;if(ct(s)&&ge(s.rule.ref))return i(s,s.rule.ref);if(Ko(s)&&(!((n=s.typeRef)===null||n===void 0)&&n.ref))return i(s,s.typeRef.ref)}}a(Tu,"findNameAssignmentInternal");function Ru(r){const e=r.$container;if(cr(e)){const t=e.elements,n=t.indexOf(r);for(let i=n-1;i>=0;i--){const s=t[i];if(Tt(s))return s;{const o=St(t[i]).find(Tt);if(o)return o}}}if(Bo(e))return Ru(e)}a(Ru,"getActionAtElement");function tp(r,e){return r==="?"||r==="*"||cr(e)&&!!e.guardCondition}a(tp,"isOptionalCardinality");function rp(r){return r==="*"||r==="+"}a(rp,"isArrayCardinality");function np(r){return r==="+="}a(np,"isArrayOperator");function Ra(r){return Au(r,new Set)}a(Ra,"isDataTypeRule");function Au(r,e){if(e.has(r))return!0;e.add(r);for(const t of St(r))if(ct(t)){if(!t.rule.ref||ge(t.rule.ref)&&!Au(t.rule.ref,e))return!1}else{if(ot(t))return!1;if(Tt(t))return!1}return!!r.definition}a(Au,"isDataTypeRuleInternal");function ip(r){return co(r.type,new Set)}a(ip,"isDataType");function co(r,e){if(e.has(r))return!0;if(e.add(r),Gc(r))return!1;if(jc(r))return!1;if(zc(r))return r.types.every(t=>co(t,e));if(Ko(r)){if(r.primitiveType!==void 0)return!0;if(r.stringType!==void 0)return!0;if(r.typeRef!==void 0){const t=r.typeRef.ref;return ga(t)?co(t.type,e):!1}else return!1}else return!1}a(co,"isDataTypeInternal");function $s(r){if(r.inferredType)return r.inferredType.name;if(r.dataType)return r.dataType;if(r.returnType){const e=r.returnType.ref;if(e){if(ge(e))return e.name;if(Wo(e)||ga(e))return e.name}}}a($s,"getExplicitRuleType");function Aa(r){var e;if(ge(r))return Ra(r)?r.name:(e=$s(r))!==null&&e!==void 0?e:r.name;if(Wo(r)||ga(r)||Hc(r))return r.name;if(Tt(r)){const t=Eu(r);if(t)return t}else if(Vo(r))return r.name;throw new Error("Cannot get name of Unknown Type")}a(Aa,"getTypeName");function Eu(r){var e;if(r.inferredType)return r.inferredType.name;if(!((e=r.type)===null||e===void 0)&&e.ref)return Aa(r.type.ref)}a(Eu,"getActionType");function sp(r){var e,t,n;return Ye(r)?(t=(e=r.type)===null||e===void 0?void 0:e.name)!==null&&t!==void 0?t:"string":Ra(r)?r.name:(n=$s(r))!==null&&n!==void 0?n:r.name}a(sp,"getRuleTypeName");function ku(r){var e,t,n;return Ye(r)?(t=(e=r.type)===null||e===void 0?void 0:e.name)!==null&&t!==void 0?t:"string":(n=$s(r))!==null&&n!==void 0?n:r.name}a(ku,"getRuleType");function Ea(r){const e={s:!1,i:!1,u:!1},t=ur(r.definition,e),n=Object.entries(e).filter(([,i])=>i).map(([i])=>i).join("");return new RegExp(t,n)}a(Ea,"terminalRegex");var Cu=/[\s\S]/.source;function ur(r,e){if(Qc(r))return ap(r);if(Zc(r))return op(r);if(qc(r))return up(r);if(Ho(r)){const t=r.rule.ref;if(!t)throw new Error("Missing rule reference.");return ze(ur(t.definition),{cardinality:r.cardinality,lookahead:r.lookahead})}else{if(Xc(r))return cp(r);if(eu(r))return lp(r);if(Jc(r)){const t=r.regex.lastIndexOf("/"),n=r.regex.substring(1,t),i=r.regex.substring(t+1);return e&&(e.i=i.includes("i"),e.s=i.includes("s"),e.u=i.includes("u")),ze(n,{cardinality:r.cardinality,lookahead:r.lookahead,wrap:!1})}else{if(tu(r))return ze(Cu,{cardinality:r.cardinality,lookahead:r.lookahead});throw new Error(`Invalid terminal element: ${r?.$type}`)}}}a(ur,"abstractElementToRegex");function ap(r){return ze(r.elements.map(e=>ur(e)).join("|"),{cardinality:r.cardinality,lookahead:r.lookahead})}a(ap,"terminalAlternativesToRegex");function op(r){return ze(r.elements.map(e=>ur(e)).join(""),{cardinality:r.cardinality,lookahead:r.lookahead})}a(op,"terminalGroupToRegex");function lp(r){return ze(`${Cu}*?${ur(r.terminal)}`,{cardinality:r.cardinality,lookahead:r.lookahead})}a(lp,"untilTokenToRegex");function cp(r){return ze(`(?!${ur(r.terminal)})${Cu}*?`,{cardinality:r.cardinality,lookahead:r.lookahead})}a(cp,"negateTokenToRegex");function up(r){return r.right?ze(`[${Ga(r.left)}-${Ga(r.right)}]`,{cardinality:r.cardinality,lookahead:r.lookahead,wrap:!1}):ze(Ga(r.left),{cardinality:r.cardinality,lookahead:r.lookahead,wrap:!1})}a(up,"characterRangeToRegex");function Ga(r){return xs(r.value)}a(Ga,"keywordToRegex");function ze(r,e){var t;return(e.wrap!==!1||e.lookahead)&&(r=`(${(t=e.lookahead)!==null&&t!==void 0?t:""}${r})`),e.cardinality?`${r}${e.cardinality}`:r}a(ze,"withCardinality");function Su(r){const e=[],t=r.Grammar;for(const n of t.rules)Ye(n)&&mu(n)&&ou(Ea(n))&&e.push(n.name);return{multilineCommentRules:e,nameRegexp:Lc}}a(Su,"createGrammarConfig");function uo(r){console&&console.error&&console.error(`Error: ${r}`)}a(uo,"PRINT_ERROR");function Iu(r){console&&console.warn&&console.warn(`Warning: ${r}`)}a(Iu,"PRINT_WARNING");function xu(r){const e=new Date().getTime(),t=r();return{time:new Date().getTime()-e,value:t}}a(xu,"timer");function $u(r){function e(){}a(e,"FakeConstructor"),e.prototype=r;const t=new e;function n(){return typeof t.bar}return a(n,"fakeAccess"),n(),n(),r}a($u,"toFastProperties");function dp(r){return fp(r)?r.LABEL:r.name}a(dp,"tokenLabel");function fp(r){return Ce(r.LABEL)&&r.LABEL!==""}a(fp,"hasTokenLabel");var fn,Xe=(fn=class{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),N(this.definition,t=>{t.accept(e)})}},a(fn,"AbstractProduction"),fn),hn,ue=(hn=class extends Xe{constructor(e){super([]),this.idx=1,ke(this,Ue(e,t=>t!==void 0))}set definition(e){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(e){e.visit(this)}},a(hn,"NonTerminal"),hn),pn,Ns=(pn=class extends Xe{constructor(e){super(e.definition),this.orgText="",ke(this,Ue(e,t=>t!==void 0))}},a(pn,"Rule"),pn),mn,ye=(mn=class extends Xe{constructor(e){super(e.definition),this.ignoreAmbiguities=!1,ke(this,Ue(e,t=>t!==void 0))}},a(mn,"Alternative"),mn),gn,re=(gn=class extends Xe{constructor(e){super(e.definition),this.idx=1,ke(this,Ue(e,t=>t!==void 0))}},a(gn,"Option"),gn),yn,Ie=(yn=class extends Xe{constructor(e){super(e.definition),this.idx=1,ke(this,Ue(e,t=>t!==void 0))}},a(yn,"RepetitionMandatory"),yn),vn,xe=(vn=class extends Xe{constructor(e){super(e.definition),this.idx=1,ke(this,Ue(e,t=>t!==void 0))}},a(vn,"RepetitionMandatoryWithSeparator"),vn),Tn,K=(Tn=class extends Xe{constructor(e){super(e.definition),this.idx=1,ke(this,Ue(e,t=>t!==void 0))}},a(Tn,"Repetition"),Tn),Rn,ve=(Rn=class extends Xe{constructor(e){super(e.definition),this.idx=1,ke(this,Ue(e,t=>t!==void 0))}},a(Rn,"RepetitionWithSeparator"),Rn),An,Te=(An=class extends Xe{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,ke(this,Ue(e,t=>t!==void 0))}},a(An,"Alternation"),An),En,V=(En=class{constructor(e){this.idx=1,ke(this,Ue(e,t=>t!==void 0))}accept(e){e.visit(this)}},a(En,"Terminal"),En);function hp(r){return I(r,Xs)}a(hp,"serializeGrammar");function Xs(r){function e(t){return I(t,Xs)}if(a(e,"convertDefinition"),r instanceof ue){const t={type:"NonTerminal",name:r.nonTerminalName,idx:r.idx};return Ce(r.label)&&(t.label=r.label),t}else{if(r instanceof ye)return{type:"Alternative",definition:e(r.definition)};if(r instanceof re)return{type:"Option",idx:r.idx,definition:e(r.definition)};if(r instanceof Ie)return{type:"RepetitionMandatory",idx:r.idx,definition:e(r.definition)};if(r instanceof xe)return{type:"RepetitionMandatoryWithSeparator",idx:r.idx,separator:Xs(new V({terminalType:r.separator})),definition:e(r.definition)};if(r instanceof ve)return{type:"RepetitionWithSeparator",idx:r.idx,separator:Xs(new V({terminalType:r.separator})),definition:e(r.definition)};if(r instanceof K)return{type:"Repetition",idx:r.idx,definition:e(r.definition)};if(r instanceof Te)return{type:"Alternation",idx:r.idx,definition:e(r.definition)};if(r instanceof V){const t={type:"Terminal",name:r.terminalType.name,label:dp(r.terminalType),idx:r.idx};Ce(r.label)&&(t.terminalLabel=r.label);const n=r.terminalType.PATTERN;return r.terminalType.PATTERN&&(t.pattern=st(n)?n.source:n),t}else{if(r instanceof Ns)return{type:"Rule",name:r.name,orgText:r.orgText,definition:e(r.definition)};throw Error("non exhaustive match")}}}a(Xs,"serializeProduction");var kn,ws=(kn=class{visit(e){const t=e;switch(t.constructor){case ue:return this.visitNonTerminal(t);case ye:return this.visitAlternative(t);case re:return this.visitOption(t);case Ie:return this.visitRepetitionMandatory(t);case xe:return this.visitRepetitionMandatoryWithSeparator(t);case ve:return this.visitRepetitionWithSeparator(t);case K:return this.visitRepetition(t);case Te:return this.visitAlternation(t);case V:return this.visitTerminal(t);case Ns:return this.visitRule(t);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}},a(kn,"GAstVisitor"),kn);function pp(r){return r instanceof ye||r instanceof re||r instanceof K||r instanceof Ie||r instanceof xe||r instanceof ve||r instanceof V||r instanceof Ns}a(pp,"isSequenceProd");function aa(r,e=[]){return r instanceof re||r instanceof K||r instanceof ve?!0:r instanceof Te?Rh(r.definition,n=>aa(n,e)):r instanceof ue&&fe(e,r)?!1:r instanceof Xe?(r instanceof ue&&e.push(r),De(r.definition,n=>aa(n,e))):!1}a(aa,"isOptionalProd");function mp(r){return r instanceof Te}a(mp,"isBranchingProd");function Me(r){if(r instanceof ue)return"SUBRULE";if(r instanceof re)return"OPTION";if(r instanceof Te)return"OR";if(r instanceof Ie)return"AT_LEAST_ONE";if(r instanceof xe)return"AT_LEAST_ONE_SEP";if(r instanceof ve)return"MANY_SEP";if(r instanceof K)return"MANY";if(r instanceof V)return"CONSUME";throw Error("non exhaustive match")}a(Me,"getProductionDslName");var Cn,tl=(Cn=class{walk(e,t=[]){N(e.definition,(n,i)=>{const s=te(e.definition,i+1);if(n instanceof ue)this.walkProdRef(n,s,t);else if(n instanceof V)this.walkTerminal(n,s,t);else if(n instanceof ye)this.walkFlat(n,s,t);else if(n instanceof re)this.walkOption(n,s,t);else if(n instanceof Ie)this.walkAtLeastOne(n,s,t);else if(n instanceof xe)this.walkAtLeastOneSep(n,s,t);else if(n instanceof ve)this.walkManySep(n,s,t);else if(n instanceof K)this.walkMany(n,s,t);else if(n instanceof Te)this.walkOr(n,s,t);else throw Error("non exhaustive match")})}walkTerminal(e,t,n){}walkProdRef(e,t,n){}walkFlat(e,t,n){const i=t.concat(n);this.walk(e,i)}walkOption(e,t,n){const i=t.concat(n);this.walk(e,i)}walkAtLeastOne(e,t,n){const i=[new re({definition:e.definition})].concat(t,n);this.walk(e,i)}walkAtLeastOneSep(e,t,n){const i=Fl(e,t,n);this.walk(e,i)}walkMany(e,t,n){const i=[new re({definition:e.definition})].concat(t,n);this.walk(e,i)}walkManySep(e,t,n){const i=Fl(e,t,n);this.walk(e,i)}walkOr(e,t,n){const i=t.concat(n);N(e.definition,s=>{const o=new ye({definition:[s]});this.walk(o,i)})}},a(Cn,"RestWalker"),Cn);function Fl(r,e,t){return[new re({definition:[new V({terminalType:r.separator})].concat(r.definition)})].concat(e,t)}a(Fl,"restForRepetitionWithSeparator");function _s(r){if(r instanceof ue)return _s(r.referencedRule);if(r instanceof V)return vp(r);if(pp(r))return gp(r);if(mp(r))return yp(r);throw Error("non exhaustive match")}a(_s,"first");function gp(r){let e=[];const t=r.definition;let n=0,i=t.length>n,s,o=!0;for(;i&&o;)s=t[n],o=aa(s),e=e.concat(_s(s)),n=n+1,i=t.length>n;return Sc(e)}a(gp,"firstForSequence");function yp(r){const e=I(r.definition,t=>_s(t));return Sc(Le(e))}a(yp,"firstForBranching");function vp(r){return[r.terminalType]}a(vp,"firstForTerminal");var Tp="_~IN~_",Sn,hv=(Sn=class extends tl{constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,t,n){}walkProdRef(e,t,n){const i=Ap(e.referencedRule,e.idx)+this.topProd.name,s=t.concat(n),o=new ye({definition:s}),l=_s(o);this.follows[i]=l}},a(Sn,"ResyncFollowsWalker"),Sn);function Rp(r){const e={};return N(r,t=>{const n=new hv(t).startWalking();ke(e,n)}),e}a(Rp,"computeAllProdsFollows");function Ap(r,e){return r.name+e+Tp}a(Ap,"buildBetweenProdsFollowPrefix");var Ua={},pv=new Yh;function ka(r){const e=r.toString();if(Ua.hasOwnProperty(e))return Ua[e];{const t=pv.pattern(e);return Ua[e]=t,t}}a(ka,"getRegExpAst");function Ep(){Ua={}}a(Ep,"clearRegExpParserCache");var kp="Complement Sets are not supported for first char optimization",fo=`Unable to use "first char" lexer optimizations:
`;function Cp(r,e=!1){try{const t=ka(r);return ho(t.value,{},t.flags.ignoreCase)}catch(t){if(t.message===kp)e&&Iu(`${fo}	Unable to optimize: < ${r.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let n="";e&&(n=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),uo(`${fo}
	Failed parsing: < ${r.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+n)}}return[]}a(Cp,"getOptimizedStartCodesIndices");function ho(r,e,t){switch(r.type){case"Disjunction":for(let i=0;i<r.value.length;i++)ho(r.value[i],e,t);break;case"Alternative":const n=r.value;for(let i=0;i<n.length;i++){const s=n[i];switch(s.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}const o=s;switch(o.type){case"Character":Ms(o.value,e,t);break;case"Set":if(o.complement===!0)throw Error(kp);N(o.value,c=>{if(typeof c=="number")Ms(c,e,t);else{const u=c;if(t===!0)for(let d=u.from;d<=u.to;d++)Ms(d,e,t);else{for(let d=u.from;d<=u.to&&d<Fs;d++)Ms(d,e,t);if(u.to>=Fs){const d=u.from>=Fs?u.from:Fs,f=u.to,h=ut(d),p=ut(f);for(let g=h;g<=p;g++)e[g]=g}}}});break;case"Group":ho(o.value,e,t);break;default:throw Error("Non Exhaustive Match")}const l=o.quantifier!==void 0&&o.quantifier.atLeast===0;if(o.type==="Group"&&po(o)===!1||o.type!=="Group"&&l===!1)break}break;default:throw Error("non exhaustive match!")}return Q(e)}a(ho,"firstCharOptimizedIndices");function Ms(r,e,t){const n=ut(r);e[n]=n,t===!0&&Sp(r,e)}a(Ms,"addOptimizedIdxToResult");function Sp(r,e){const t=String.fromCharCode(r),n=t.toUpperCase();if(n!==t){const i=ut(n.charCodeAt(0));e[i]=i}else{const i=t.toLowerCase();if(i!==t){const s=ut(i.charCodeAt(0));e[s]=s}}}a(Sp,"handleIgnoreCase");function Gl(r,e){return vs(r.value,t=>{if(typeof t=="number")return fe(e,t);{const n=t;return vs(e,i=>n.from<=i&&i<=n.to)!==void 0}})}a(Gl,"findCode");function po(r){const e=r.quantifier;return e&&e.atLeast===0?!0:r.value?_e(r.value)?De(r.value,po):po(r.value):!1}a(po,"isWholeOptional");var In,mv=(In=class extends Yo{constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(this.found!==!0){switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}}visitCharacter(e){fe(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?Gl(e,this.targetCharCodes)===void 0&&(this.found=!0):Gl(e,this.targetCharCodes)!==void 0&&(this.found=!0)}},a(In,"CharCodeFinder"),In);function rl(r,e){if(e instanceof RegExp){const t=ka(e),n=new mv(r);return n.visit(t),n.found}else return vs(e,t=>fe(r,t.charCodeAt(0)))!==void 0}a(rl,"canMatchCharCode");var ir="PATTERN",Ds="defaultMode",Na="modes",Ip=typeof new RegExp("(?:)").sticky=="boolean";function xp(r,e){e=Cc(e,{useSticky:Ip,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:a((k,R)=>R(),"tracer")});const t=e.tracer;t("initCharCodeToOptimizedIndexMap",()=>{zp()});let n;t("Reject Lexer.NA",()=>{n=Oo(r,k=>k[ir]===le.NA)});let i=!1,s;t("Transform Patterns",()=>{i=!1,s=I(n,k=>{const R=k[ir];if(st(R)){const $=R.source;return $.length===1&&$!=="^"&&$!=="$"&&$!=="."&&!R.ignoreCase?$:$.length===2&&$[0]==="\\"&&!fe(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],$[1])?$[1]:e.useSticky?Bl(R):Ul(R)}else{if(or(R))return i=!0,{exec:R};if(typeof R=="object")return i=!0,R;if(typeof R=="string"){if(R.length===1)return R;{const $=R.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),U=new RegExp($);return e.useSticky?Bl(U):Ul(U)}}else throw Error("non exhaustive match")}})});let o,l,c,u,d;t("misc mapping",()=>{o=I(n,k=>k.tokenTypeIdx),l=I(n,k=>{const R=k.GROUP;if(R!==le.SKIPPED){if(Ce(R))return R;if(it(R))return!1;throw Error("non exhaustive match")}}),c=I(n,k=>{const R=k.LONGER_ALT;if(R)return _e(R)?I(R,U=>Vd(n,U)):[Vd(n,R)]}),u=I(n,k=>k.PUSH_MODE),d=I(n,k=>w(k,"POP_MODE"))});let f;t("Line Terminator Handling",()=>{const k=_u(e.lineTerminatorCharacters);f=I(n,R=>!1),e.positionTracking!=="onlyOffset"&&(f=I(n,R=>w(R,"LINE_BREAKS")?!!R.LINE_BREAKS:wu(R,k)===!1&&rl(k,R.PATTERN)))});let h,p,g,y;t("Misc Mapping #2",()=>{h=I(n,Nu),p=I(s,jp),g=ce(n,(k,R)=>{const $=R.GROUP;return Ce($)&&$!==le.SKIPPED&&(k[$]=[]),k},{}),y=I(s,(k,R)=>({pattern:s[R],longerAlt:c[R],canLineTerminator:f[R],isCustom:h[R],short:p[R],group:l[R],push:u[R],pop:d[R],tokenTypeIdx:o[R],tokenType:n[R]}))});let E=!0,T=[];return e.safeMode||t("First Char Optimization",()=>{T=ce(n,(k,R,$)=>{if(typeof R.PATTERN=="string"){const U=R.PATTERN.charCodeAt(0),ee=ut(U);Ba(k,ee,y[$])}else if(_e(R.START_CHARS_HINT)){let U;N(R.START_CHARS_HINT,ee=>{const Pe=typeof ee=="string"?ee.charCodeAt(0):ee,Re=ut(Pe);U!==Re&&(U=Re,Ba(k,Re,y[$]))})}else if(st(R.PATTERN))if(R.PATTERN.unicode)E=!1,e.ensureOptimizations&&uo(`${fo}	Unable to analyze < ${R.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{const U=Cp(R.PATTERN,e.ensureOptimizations);F(U)&&(E=!1),N(U,ee=>{Ba(k,ee,y[$])})}else e.ensureOptimizations&&uo(`${fo}	TokenType: <${R.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),E=!1;return k},[])}),{emptyGroups:g,patternIdxToConfig:y,charCodeToPatternIdxToConfig:T,hasCustom:i,canBeOptimized:E}}a(xp,"analyzeTokenTypes");function $p(r,e){let t=[];const n=wp(r);t=t.concat(n.errors);const i=_p(n.valid),s=i.valid;return t=t.concat(i.errors),t=t.concat(Np(s)),t=t.concat(Dp(s)),t=t.concat(Fp(s,e)),t=t.concat(Gp(s)),t}a($p,"validatePatterns");function Np(r){let e=[];const t=Se(r,n=>st(n[ir]));return e=e.concat(Lp(t)),e=e.concat(bp(t)),e=e.concat(Op(t)),e=e.concat(Mp(t)),e=e.concat(Pp(t)),e}a(Np,"validateRegExpPattern");function wp(r){const e=Se(r,i=>!w(i,ir)),t=I(e,i=>({message:"Token Type: ->"+i.name+"<- missing static 'PATTERN' property",type:j.MISSING_PATTERN,tokenTypes:[i]})),n=Mo(r,e);return{errors:t,valid:n}}a(wp,"findMissingPatterns");function _p(r){const e=Se(r,i=>{const s=i[ir];return!st(s)&&!or(s)&&!w(s,"exec")&&!Ce(s)}),t=I(e,i=>({message:"Token Type: ->"+i.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:j.INVALID_PATTERN,tokenTypes:[i]})),n=Mo(r,e);return{errors:t,valid:n}}a(_p,"findInvalidPatterns");var gv=/[^\\][$]/;function Lp(r){const i=class i extends Yo{constructor(){super(...arguments),this.found=!1}visitEndAnchor(o){this.found=!0}};a(i,"EndAnchorFinder");let e=i;const t=Se(r,s=>{const o=s.PATTERN;try{const l=ka(o),c=new e;return c.visit(l),c.found}catch{return gv.test(o.source)}});return I(t,s=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+s.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:j.EOI_ANCHOR_FOUND,tokenTypes:[s]}))}a(Lp,"findEndOfInputAnchor");function Pp(r){const e=Se(r,n=>n.PATTERN.test(""));return I(e,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' must not match an empty string",type:j.EMPTY_MATCH_PATTERN,tokenTypes:[n]}))}a(Pp,"findEmptyMatchRegExps");var yv=/[^\\[][\^]|^\^/;function bp(r){const i=class i extends Yo{constructor(){super(...arguments),this.found=!1}visitStartAnchor(o){this.found=!0}};a(i,"StartAnchorFinder");let e=i;const t=Se(r,s=>{const o=s.PATTERN;try{const l=ka(o),c=new e;return c.visit(l),c.found}catch{return yv.test(o.source)}});return I(t,s=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+s.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:j.SOI_ANCHOR_FOUND,tokenTypes:[s]}))}a(bp,"findStartOfInputAnchor");function Op(r){const e=Se(r,n=>{const i=n[ir];return i instanceof RegExp&&(i.multiline||i.global)});return I(e,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:j.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[n]}))}a(Op,"findUnsupportedFlags");function Mp(r){const e=[];let t=I(r,s=>ce(r,(o,l)=>(s.PATTERN.source===l.PATTERN.source&&!fe(e,l)&&l.PATTERN!==le.NA&&(e.push(l),o.push(l)),o),[]));t=ma(t);const n=Se(t,s=>s.length>1);return I(n,s=>{const o=I(s,c=>c.name);return{message:`The same RegExp pattern ->${Fe(s).PATTERN}<-has been used in all of the following Token Types: ${o.join(", ")} <-`,type:j.DUPLICATE_PATTERNS_FOUND,tokenTypes:s}})}a(Mp,"findDuplicatePatterns");function Dp(r){const e=Se(r,n=>{if(!w(n,"GROUP"))return!1;const i=n.GROUP;return i!==le.SKIPPED&&i!==le.NA&&!Ce(i)});return I(e,n=>({message:"Token Type: ->"+n.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:j.INVALID_GROUP_TYPE_FOUND,tokenTypes:[n]}))}a(Dp,"findInvalidGroupType");function Fp(r,e){const t=Se(r,i=>i.PUSH_MODE!==void 0&&!fe(e,i.PUSH_MODE));return I(t,i=>({message:`Token Type: ->${i.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${i.PUSH_MODE}<-which does not exist`,type:j.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[i]}))}a(Fp,"findModesThatDoNotExist");function Gp(r){const e=[],t=ce(r,(n,i,s)=>{const o=i.PATTERN;return o===le.NA||(Ce(o)?n.push({str:o,idx:s,tokenType:i}):st(o)&&Bp(o)&&n.push({str:o.source,idx:s,tokenType:i})),n},[]);return N(r,(n,i)=>{N(t,({str:s,idx:o,tokenType:l})=>{if(i<o&&Up(s,n.PATTERN)){const c=`Token: ->${l.name}<- can never be matched.
Because it appears AFTER the Token Type ->${n.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;e.push({message:c,type:j.UNREACHABLE_PATTERN,tokenTypes:[n,l]})}})}),e}a(Gp,"findUnreachablePatterns");function Up(r,e){if(st(e)){const t=e.exec(r);return t!==null&&t.index===0}else{if(or(e))return e(r,0,[],{});if(w(e,"exec"))return e.exec(r,0,[],{});if(typeof e=="string")return e===r;throw Error("non exhaustive match")}}a(Up,"testTokenType");function Bp(r){return vs([".","\\","[","]","|","^","$","(",")","?","*","+","{"],t=>r.source.indexOf(t)!==-1)===void 0}a(Bp,"noMetaChar");function Ul(r){const e=r.ignoreCase?"i":"";return new RegExp(`^(?:${r.source})`,e)}a(Ul,"addStartOfInput");function Bl(r){const e=r.ignoreCase?"iy":"y";return new RegExp(`${r.source}`,e)}a(Bl,"addStickyFlag");function Vp(r,e,t){const n=[];return w(r,Ds)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+Ds+`> property in its definition
`,type:j.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),w(r,Na)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+Na+`> property in its definition
`,type:j.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),w(r,Na)&&w(r,Ds)&&!w(r.modes,r.defaultMode)&&n.push({message:`A MultiMode Lexer cannot be initialized with a ${Ds}: <${r.defaultMode}>which does not exist
`,type:j.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),w(r,Na)&&N(r.modes,(i,s)=>{N(i,(o,l)=>{if(it(o))n.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${s}> at index: <${l}>
`,type:j.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if(w(o,"LONGER_ALT")){const c=_e(o.LONGER_ALT)?o.LONGER_ALT:[o.LONGER_ALT];N(c,u=>{!it(u)&&!fe(i,u)&&n.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${u.name}> on token <${o.name}> outside of mode <${s}>
`,type:j.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),n}a(Vp,"performRuntimeChecks");function Wp(r,e,t){const n=[];let i=!1;const s=ma(Le(Q(r.modes))),o=Oo(s,c=>c[ir]===le.NA),l=_u(t);return e&&N(o,c=>{const u=wu(c,l);if(u!==!1){const f={message:Hp(c,u),type:u.issue,tokenType:c};n.push(f)}else w(c,"LINE_BREAKS")?c.LINE_BREAKS===!0&&(i=!0):rl(l,c.PATTERN)&&(i=!0)}),e&&!i&&n.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:j.NO_LINE_BREAKS_FLAGS}),n}a(Wp,"performWarningRuntimeChecks");function Kp(r){const e={},t=Zs(r);return N(t,n=>{const i=r[n];if(_e(i))e[n]=[];else throw Error("non exhaustive match")}),e}a(Kp,"cloneEmptyGroups");function Nu(r){const e=r.PATTERN;if(st(e))return!1;if(or(e))return!0;if(w(e,"exec"))return!0;if(Ce(e))return!1;throw Error("non exhaustive match")}a(Nu,"isCustomPattern");function jp(r){return Ce(r)&&r.length===1?r.charCodeAt(0):!1}a(jp,"isShortPattern");var vv={test:a(function(r){const e=r.length;for(let t=this.lastIndex;t<e;t++){const n=r.charCodeAt(t);if(n===10)return this.lastIndex=t+1,!0;if(n===13)return r.charCodeAt(t+1)===10?this.lastIndex=t+2:this.lastIndex=t+1,!0}return!1},"test"),lastIndex:0};function wu(r,e){if(w(r,"LINE_BREAKS"))return!1;if(st(r.PATTERN)){try{rl(e,r.PATTERN)}catch(t){return{issue:j.IDENTIFY_TERMINATOR,errMsg:t.message}}return!1}else{if(Ce(r.PATTERN))return!1;if(Nu(r))return{issue:j.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}}a(wu,"checkLineBreaksIssues");function Hp(r,e){if(e.issue===j.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${r.name}> Token Type
	 Root cause: ${e.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(e.issue===j.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${r.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}a(Hp,"buildLineBreakIssueMessage");function _u(r){return I(r,t=>Ce(t)?t.charCodeAt(0):t)}a(_u,"getCharCodes");function Ba(r,e,t){r[e]===void 0?r[e]=[t]:r[e].push(t)}a(Ba,"addToMapOfArrays");var Fs=256,Va=[];function ut(r){return r<Fs?r:Va[r]}a(ut,"charCodeToOptimizedIndex");function zp(){if(F(Va)){Va=new Array(65536);for(let r=0;r<65536;r++)Va[r]=r>255?255+~~(r/255):r}}a(zp,"initCharCodeToOptimizedIndexMap");function Ls(r,e){const t=r.tokenTypeIdx;return t===e.tokenTypeIdx?!0:e.isParent===!0&&e.categoryMatchesMap[t]===!0}a(Ls,"tokenStructuredMatcher");function oa(r,e){return r.tokenTypeIdx===e.tokenTypeIdx}a(oa,"tokenStructuredMatcherNoCategories");var Kd=1,qp={};function Ps(r){const e=Yp(r);Xp(e),Qp(e),Jp(e),N(e,t=>{t.isParent=t.categoryMatches.length>0})}a(Ps,"augmentTokenTypes");function Yp(r){let e=ne(r),t=r,n=!0;for(;n;){t=ma(Le(I(t,s=>s.CATEGORIES)));const i=Mo(t,e);e=e.concat(i),F(i)?n=!1:t=i}return e}a(Yp,"expandCategories");function Xp(r){N(r,e=>{Pu(e)||(qp[Kd]=e,e.tokenTypeIdx=Kd++),Vl(e)&&!_e(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),Vl(e)||(e.CATEGORIES=[]),Zp(e)||(e.categoryMatches=[]),em(e)||(e.categoryMatchesMap={})})}a(Xp,"assignTokenDefaultProps");function Jp(r){N(r,e=>{e.categoryMatches=[],N(e.categoryMatchesMap,(t,n)=>{e.categoryMatches.push(qp[n].tokenTypeIdx)})})}a(Jp,"assignCategoriesTokensProp");function Qp(r){N(r,e=>{Lu([],e)})}a(Qp,"assignCategoriesMapProp");function Lu(r,e){N(r,t=>{e.categoryMatchesMap[t.tokenTypeIdx]=!0}),N(e.CATEGORIES,t=>{const n=r.concat(e);fe(n,t)||Lu(n,t)})}a(Lu,"singleAssignCategoriesToksMap");function Pu(r){return w(r,"tokenTypeIdx")}a(Pu,"hasShortKeyProperty");function Vl(r){return w(r,"CATEGORIES")}a(Vl,"hasCategoriesProperty");function Zp(r){return w(r,"categoryMatches")}a(Zp,"hasExtendingTokensTypesProperty");function em(r){return w(r,"categoryMatchesMap")}a(em,"hasExtendingTokensTypesMapProperty");function tm(r){return w(r,"tokenTypeIdx")}a(tm,"isTokenType");var Wl={buildUnableToPopLexerModeMessage(r){return`Unable to pop Lexer Mode after encountering Token ->${r.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(r,e,t,n,i){return`unexpected character: ->${r.charAt(e)}<- at offset: ${e}, skipped ${t} characters.`}},j;(function(r){r[r.MISSING_PATTERN=0]="MISSING_PATTERN",r[r.INVALID_PATTERN=1]="INVALID_PATTERN",r[r.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",r[r.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",r[r.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",r[r.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",r[r.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",r[r.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",r[r.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",r[r.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",r[r.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",r[r.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",r[r.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",r[r.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",r[r.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",r[r.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",r[r.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",r[r.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(j||(j={}));var Gs={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:Wl,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(Gs);var xn,le=(xn=class{constructor(e,t=Gs){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(i,s)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;const o=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${o}--> <${i}>`);const{time:l,value:c}=xu(s),u=l>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&u(`${o}<-- <${i}> time: ${l}ms`),this.traceInitIndent--,c}else return s()},typeof t=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=ke({},Gs,t);const n=this.config.traceInitPerf;n===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof n=="number"&&(this.traceInitMaxIdent=n,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let i,s=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===Gs.lineTerminatorsPattern)this.config.lineTerminatorsPattern=vv;else if(this.config.lineTerminatorCharacters===Gs.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(t.safeMode&&t.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),_e(e)?i={modes:{defaultMode:ne(e)},defaultMode:Ds}:(s=!1,i=ne(e))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(Vp(i,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(Wp(i,this.trackStartLines,this.config.lineTerminatorCharacters))})),i.modes=i.modes?i.modes:{},N(i.modes,(l,c)=>{i.modes[c]=Oo(l,u=>it(u))});const o=Zs(i.modes);if(N(i.modes,(l,c)=>{this.TRACE_INIT(`Mode: <${c}> processing`,()=>{if(this.modes.push(c),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat($p(l,o))}),F(this.lexerDefinitionErrors)){Ps(l);let u;this.TRACE_INIT("analyzeTokenTypes",()=>{u=xp(l,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:t.positionTracking,ensureOptimizations:t.ensureOptimizations,safeMode:t.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[c]=u.patternIdxToConfig,this.charCodeToPatternIdxToConfig[c]=u.charCodeToPatternIdxToConfig,this.emptyGroups=ke({},this.emptyGroups,u.emptyGroups),this.hasCustom=u.hasCustom||this.hasCustom,this.canModeBeOptimized[c]=u.canBeOptimized}})}),this.defaultMode=i.defaultMode,!F(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){const c=I(this.lexerDefinitionErrors,u=>u.message).join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+c)}N(this.lexerDefinitionWarning,l=>{Iu(l.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(Ip?(this.chopInput=Bd,this.match=this.matchWithTest):(this.updateLastIndex=J,this.match=this.matchWithExec),s&&(this.handleModes=J),this.trackStartLines===!1&&(this.computeNewColumn=Bd),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=J),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{const l=ce(this.canModeBeOptimized,(c,u,d)=>(u===!1&&c.push(d),c),[]);if(t.ensureOptimizations&&!F(l))throw Error(`Lexer Modes: < ${l.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{Ep()}),this.TRACE_INIT("toFastProperties",()=>{$u(this)})})}tokenize(e,t=this.defaultMode){if(!F(this.lexerDefinitionErrors)){const i=I(this.lexerDefinitionErrors,s=>s.message).join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+i)}return this.tokenizeInternal(e,t)}tokenizeInternal(e,t){let n,i,s,o,l,c,u,d,f,h,p,g,y,E,T;const k=e,R=k.length;let $=0,U=0;const ee=this.hasCustom?0:Math.floor(e.length/10),Pe=new Array(ee),Re=[];let Ve=this.trackStartLines?1:void 0,$e=this.trackStartLines?1:void 0;const be=Kp(this.emptyGroups),bs=this.trackStartLines,C=this.config.lineTerminatorsPattern;let v=0,x=[],S=[];const M=[],b=[];Object.freeze(b);let P;function he(){return x}a(he,"getPossiblePatternsSlow");function ie(se){const Ne=ut(se),pr=S[Ne];return pr===void 0?b:pr}a(ie,"getPossiblePatternsOptimized");const H=a(se=>{if(M.length===1&&se.tokenType.PUSH_MODE===void 0){const Ne=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(se);Re.push({offset:se.startOffset,line:se.startLine,column:se.startColumn,length:se.image.length,message:Ne})}else{M.pop();const Ne=ys(M);x=this.patternIdxToConfig[Ne],S=this.charCodeToPatternIdxToConfig[Ne],v=x.length;const pr=this.canModeBeOptimized[Ne]&&this.config.safeMode===!1;S&&pr?P=ie:P=he}},"pop_mode");function gt(se){M.push(se),S=this.charCodeToPatternIdxToConfig[se],x=this.patternIdxToConfig[se],v=x.length,v=x.length;const Ne=this.canModeBeOptimized[se]&&this.config.safeMode===!1;S&&Ne?P=ie:P=he}a(gt,"push_mode"),gt.call(this,t);let Oe;const Gd=this.config.recoveryEnabled;for(;$<R;){c=null;const se=k.charCodeAt($),Ne=P(se),pr=Ne.length;for(n=0;n<pr;n++){Oe=Ne[n];const Ae=Oe.pattern;u=null;const et=Oe.short;if(et!==!1?se===et&&(c=Ae):Oe.isCustom===!0?(T=Ae.exec(k,$,Pe,be),T!==null?(c=T[0],T.payload!==void 0&&(u=T.payload)):c=null):(this.updateLastIndex(Ae,$),c=this.match(Ae,e,$)),c!==null){if(l=Oe.longerAlt,l!==void 0){const yt=l.length;for(s=0;s<yt;s++){const tt=x[l[s]],Nt=tt.pattern;if(d=null,tt.isCustom===!0?(T=Nt.exec(k,$,Pe,be),T!==null?(o=T[0],T.payload!==void 0&&(d=T.payload)):o=null):(this.updateLastIndex(Nt,$),o=this.match(Nt,e,$)),o&&o.length>c.length){c=o,u=d,Oe=tt;break}}}break}}if(c!==null){if(f=c.length,h=Oe.group,h!==void 0&&(p=Oe.tokenTypeIdx,g=this.createTokenInstance(c,$,p,Oe.tokenType,Ve,$e,f),this.handlePayload(g,u),h===!1?U=this.addToken(Pe,U,g):be[h].push(g)),e=this.chopInput(e,f),$=$+f,$e=this.computeNewColumn($e,f),bs===!0&&Oe.canLineTerminator===!0){let Ae=0,et,yt;C.lastIndex=0;do et=C.test(c),et===!0&&(yt=C.lastIndex-1,Ae++);while(et===!0);Ae!==0&&(Ve=Ve+Ae,$e=f-yt,this.updateTokenEndLineColumnLocation(g,h,yt,Ae,Ve,$e,f))}this.handleModes(Oe,H,gt,g)}else{const Ae=$,et=Ve,yt=$e;let tt=Gd===!1;for(;tt===!1&&$<R;)for(e=this.chopInput(e,1),$++,i=0;i<v;i++){const Nt=x[i],El=Nt.pattern,Ud=Nt.short;if(Ud!==!1?k.charCodeAt($)===Ud&&(tt=!0):Nt.isCustom===!0?tt=El.exec(k,$,Pe,be)!==null:(this.updateLastIndex(El,$),tt=El.exec(e)!==null),tt===!0)break}if(y=$-Ae,$e=this.computeNewColumn($e,y),E=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(k,Ae,y,et,yt),Re.push({offset:Ae,line:et,column:yt,length:y,message:E}),Gd===!1)break}}return this.hasCustom||(Pe.length=U),{tokens:Pe,groups:be,errors:Re}}handleModes(e,t,n,i){if(e.pop===!0){const s=e.push;t(i),s!==void 0&&n.call(this,s)}else e.push!==void 0&&n.call(this,e.push)}chopInput(e,t){return e.substring(t)}updateLastIndex(e,t){e.lastIndex=t}updateTokenEndLineColumnLocation(e,t,n,i,s,o,l){let c,u;t!==void 0&&(c=n===l-1,u=c?-1:0,i===1&&c===!0||(e.endLine=s+u,e.endColumn=o-1+-u))}computeNewColumn(e,t){return e+t}createOffsetOnlyToken(e,t,n,i){return{image:e,startOffset:t,tokenTypeIdx:n,tokenType:i}}createStartOnlyToken(e,t,n,i,s,o){return{image:e,startOffset:t,startLine:s,startColumn:o,tokenTypeIdx:n,tokenType:i}}createFullToken(e,t,n,i,s,o,l){return{image:e,startOffset:t,endOffset:t+l-1,startLine:s,endLine:s,startColumn:o,endColumn:o+l-1,tokenTypeIdx:n,tokenType:i}}addTokenUsingPush(e,t,n){return e.push(n),t}addTokenUsingMemberAccess(e,t,n){return e[t]=n,t++,t}handlePayloadNoCustom(e,t){}handlePayloadWithCustom(e,t){t!==null&&(e.payload=t)}matchWithTest(e,t,n){return e.test(t)===!0?t.substring(n,e.lastIndex):null}matchWithExec(e,t){const n=e.exec(t);return n!==null?n[0]:null}},a(xn,"Lexer"),xn);le.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.";le.NA=/NOT_APPLICABLE/;function Dt(r){return bu(r)?r.LABEL:r.name}a(Dt,"tokenLabel");function bu(r){return Ce(r.LABEL)&&r.LABEL!==""}a(bu,"hasTokenLabel");var Tv="parent",jd="categories",Hd="label",zd="group",qd="push_mode",Yd="pop_mode",Xd="longer_alt",Jd="line_breaks",Qd="start_chars_hint";function nn(r){return rm(r)}a(nn,"createToken");function rm(r){const e=r.pattern,t={};if(t.name=r.name,it(e)||(t.PATTERN=e),w(r,Tv))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return w(r,jd)&&(t.CATEGORIES=r[jd]),Ps([t]),w(r,Hd)&&(t.LABEL=r[Hd]),w(r,zd)&&(t.GROUP=r[zd]),w(r,Yd)&&(t.POP_MODE=r[Yd]),w(r,qd)&&(t.PUSH_MODE=r[qd]),w(r,Xd)&&(t.LONGER_ALT=r[Xd]),w(r,Jd)&&(t.LINE_BREAKS=r[Jd]),w(r,Qd)&&(t.START_CHARS_HINT=r[Qd]),t}a(rm,"createTokenInternal");var Rt=nn({name:"EOF",pattern:le.NA});Ps([Rt]);function Ca(r,e,t,n,i,s,o,l){return{image:e,startOffset:t,endOffset:n,startLine:i,endLine:s,startColumn:o,endColumn:l,tokenTypeIdx:r.tokenTypeIdx,tokenType:r}}a(Ca,"createTokenInstance");function Ou(r,e){return Ls(r,e)}a(Ou,"tokenMatcher");var tn={buildMismatchTokenMessage({expected:r,actual:e,previous:t,ruleName:n}){return`Expecting ${bu(r)?`--> ${Dt(r)} <--`:`token of type --> ${r.name} <--`} but found --> '${e.image}' <--`},buildNotAllInputParsedMessage({firstRedundant:r,ruleName:e}){return"Redundant input, expecting EOF but found: "+r.image},buildNoViableAltMessage({expectedPathsPerAlt:r,actual:e,previous:t,customUserDescription:n,ruleName:i}){const s="Expecting: ",l=`
but found: '`+Fe(e).image+"'";if(n)return s+n+l;{const c=ce(r,(h,p)=>h.concat(p),[]),u=I(c,h=>`[${I(h,p=>Dt(p)).join(", ")}]`),f=`one of these possible Token sequences:
${I(u,(h,p)=>`  ${p+1}. ${h}`).join(`
`)}`;return s+f+l}},buildEarlyExitMessage({expectedIterationPaths:r,actual:e,customUserDescription:t,ruleName:n}){const i="Expecting: ",o=`
but found: '`+Fe(e).image+"'";if(t)return i+t+o;{const c=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${I(r,u=>`[${I(u,d=>Dt(d)).join(",")}]`).join(" ,")}>`;return i+c+o}}};Object.freeze(tn);var Rv={buildRuleNotFoundError(r,e){return"Invalid grammar, reference to a rule which is not defined: ->"+e.nonTerminalName+`<-
inside top level rule: ->`+r.name+"<-"}},bt={buildDuplicateFoundError(r,e){function t(d){return d instanceof V?d.terminalType.name:d instanceof ue?d.nonTerminalName:""}a(t,"getExtraProductionArgument");const n=r.name,i=Fe(e),s=i.idx,o=Me(i),l=t(i),c=s>0;let u=`->${o}${c?s:""}<- ${l?`with argument: ->${l}<-`:""}
                  appears more than once (${e.length} times) in the top level rule: ->${n}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return u=u.replace(/[ \t]+/g," "),u=u.replace(/\s\s+/g,`
`),u},buildNamespaceConflictError(r){return`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${r.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`},buildAlternationPrefixAmbiguityError(r){const e=I(r.prefixPath,i=>Dt(i)).join(", "),t=r.alternation.idx===0?"":r.alternation.idx;return`Ambiguous alternatives: <${r.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${t}> inside <${r.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(r){const e=I(r.prefixPath,i=>Dt(i)).join(", "),t=r.alternation.idx===0?"":r.alternation.idx;let n=`Ambiguous Alternatives Detected: <${r.ambiguityIndices.join(" ,")}> in <OR${t}> inside <${r.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return n=n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,n},buildEmptyRepetitionError(r){let e=Me(r.repetition);return r.repetition.idx!==0&&(e+=r.repetition.idx),`The repetition <${e}> within Rule <${r.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError(r){return"deprecated"},buildEmptyAlternationError(r){return`Ambiguous empty alternative: <${r.emptyChoiceIdx+1}> in <OR${r.alternation.idx}> inside <${r.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`},buildTooManyAlternativesError(r){return`An Alternation cannot have more than 256 alternatives:
<OR${r.alternation.idx}> inside <${r.topLevelRule.name}> Rule.
 has ${r.alternation.definition.length+1} alternatives.`},buildLeftRecursionError(r){const e=r.topLevelRule.name,t=I(r.leftRecursionPath,s=>s.name),n=`${e} --> ${t.concat([e]).join(" --> ")}`;return`Left Recursion found in grammar.
rule: <${e}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${n}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError(r){return"deprecated"},buildDuplicateRuleNameError(r){let e;return r.topLevelRule instanceof Ns?e=r.topLevelRule.name:e=r.topLevelRule,`Duplicate definition, rule: ->${e}<- is already defined in the grammar: ->${r.grammarName}<-`}};function nm(r,e){const t=new Av(r,e);return t.resolveRefs(),t.errors}a(nm,"resolveGrammar");var $n,Av=($n=class extends ws{constructor(e,t){super(),this.nameToTopRule=e,this.errMsgProvider=t,this.errors=[]}resolveRefs(){N(Q(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){const t=this.nameToTopRule[e.nonTerminalName];if(t)e.referencedRule=t;else{const n=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:n,type:de.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}},a($n,"GastRefResolverVisitor"),$n),Nn,Ev=(Nn=class extends tl{constructor(e,t){super(),this.topProd=e,this.path=t,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=ne(this.path.ruleStack).reverse(),this.occurrenceStack=ne(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,t=[]){this.found||super.walk(e,t)}walkProdRef(e,t,n){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){const i=t.concat(n);this.updateExpectedNext(),this.walk(e.referencedRule,i)}}updateExpectedNext(){F(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}},a(Nn,"AbstractNextPossibleTokensWalker"),Nn),wn,kv=(wn=class extends Ev{constructor(e,t){super(e,t),this.path=t,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,t,n){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){const i=t.concat(n),s=new ye({definition:i});this.possibleTokTypes=_s(s),this.found=!0}}},a(wn,"NextAfterTokenWalker"),wn),_n,nl=(_n=class extends tl{constructor(e,t){super(),this.topRule=e,this.occurrence=t,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}},a(_n,"AbstractNextTerminalAfterProductionWalker"),_n),Ln,Cv=(Ln=class extends nl{walkMany(e,t,n){if(e.idx===this.occurrence){const i=Fe(t.concat(n));this.result.isEndOfRule=i===void 0,i instanceof V&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkMany(e,t,n)}},a(Ln,"NextTerminalAfterManyWalker"),Ln),Pn,Zd=(Pn=class extends nl{walkManySep(e,t,n){if(e.idx===this.occurrence){const i=Fe(t.concat(n));this.result.isEndOfRule=i===void 0,i instanceof V&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkManySep(e,t,n)}},a(Pn,"NextTerminalAfterManySepWalker"),Pn),bn,Sv=(bn=class extends nl{walkAtLeastOne(e,t,n){if(e.idx===this.occurrence){const i=Fe(t.concat(n));this.result.isEndOfRule=i===void 0,i instanceof V&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOne(e,t,n)}},a(bn,"NextTerminalAfterAtLeastOneWalker"),bn),On,ef=(On=class extends nl{walkAtLeastOneSep(e,t,n){if(e.idx===this.occurrence){const i=Fe(t.concat(n));this.result.isEndOfRule=i===void 0,i instanceof V&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOneSep(e,t,n)}},a(On,"NextTerminalAfterAtLeastOneSepWalker"),On);function mo(r,e,t=[]){t=ne(t);let n=[],i=0;function s(l){return l.concat(te(r,i+1))}a(s,"remainingPathWith");function o(l){const c=mo(s(l),e,t);return n.concat(c)}for(a(o,"getAlternativesForProd");t.length<e&&i<r.length;){const l=r[i];if(l instanceof ye)return o(l.definition);if(l instanceof ue)return o(l.definition);if(l instanceof re)n=o(l.definition);else if(l instanceof Ie){const c=l.definition.concat([new K({definition:l.definition})]);return o(c)}else if(l instanceof xe){const c=[new ye({definition:l.definition}),new K({definition:[new V({terminalType:l.separator})].concat(l.definition)})];return o(c)}else if(l instanceof ve){const c=l.definition.concat([new K({definition:[new V({terminalType:l.separator})].concat(l.definition)})]);n=o(c)}else if(l instanceof K){const c=l.definition.concat([new K({definition:l.definition})]);n=o(c)}else{if(l instanceof Te)return N(l.definition,c=>{F(c.definition)===!1&&(n=o(c.definition))}),n;if(l instanceof V)t.push(l.terminalType);else throw Error("non exhaustive match")}i++}return n.push({partialPath:t,suffixDef:te(r,i)}),n}a(mo,"possiblePathsFrom");function Mu(r,e,t,n){const i="EXIT_NONE_TERMINAL",s=[i],o="EXIT_ALTERNATIVE";let l=!1;const c=e.length,u=c-n-1,d=[],f=[];for(f.push({idx:-1,def:r,ruleStack:[],occurrenceStack:[]});!F(f);){const h=f.pop();if(h===o){l&&ys(f).idx<=u&&f.pop();continue}const p=h.def,g=h.idx,y=h.ruleStack,E=h.occurrenceStack;if(F(p))continue;const T=p[0];if(T===i){const k={idx:g,def:te(p),ruleStack:ea(y),occurrenceStack:ea(E)};f.push(k)}else if(T instanceof V)if(g<c-1){const k=g+1,R=e[k];if(t(R,T.terminalType)){const $={idx:k,def:te(p),ruleStack:y,occurrenceStack:E};f.push($)}}else if(g===c-1)d.push({nextTokenType:T.terminalType,nextTokenOccurrence:T.idx,ruleStack:y,occurrenceStack:E}),l=!0;else throw Error("non exhaustive match");else if(T instanceof ue){const k=ne(y);k.push(T.nonTerminalName);const R=ne(E);R.push(T.idx);const $={idx:g,def:T.definition.concat(s,te(p)),ruleStack:k,occurrenceStack:R};f.push($)}else if(T instanceof re){const k={idx:g,def:te(p),ruleStack:y,occurrenceStack:E};f.push(k),f.push(o);const R={idx:g,def:T.definition.concat(te(p)),ruleStack:y,occurrenceStack:E};f.push(R)}else if(T instanceof Ie){const k=new K({definition:T.definition,idx:T.idx}),R=T.definition.concat([k],te(p)),$={idx:g,def:R,ruleStack:y,occurrenceStack:E};f.push($)}else if(T instanceof xe){const k=new V({terminalType:T.separator}),R=new K({definition:[k].concat(T.definition),idx:T.idx}),$=T.definition.concat([R],te(p)),U={idx:g,def:$,ruleStack:y,occurrenceStack:E};f.push(U)}else if(T instanceof ve){const k={idx:g,def:te(p),ruleStack:y,occurrenceStack:E};f.push(k),f.push(o);const R=new V({terminalType:T.separator}),$=new K({definition:[R].concat(T.definition),idx:T.idx}),U=T.definition.concat([$],te(p)),ee={idx:g,def:U,ruleStack:y,occurrenceStack:E};f.push(ee)}else if(T instanceof K){const k={idx:g,def:te(p),ruleStack:y,occurrenceStack:E};f.push(k),f.push(o);const R=new K({definition:T.definition,idx:T.idx}),$=T.definition.concat([R],te(p)),U={idx:g,def:$,ruleStack:y,occurrenceStack:E};f.push(U)}else if(T instanceof Te)for(let k=T.definition.length-1;k>=0;k--){const R=T.definition[k],$={idx:g,def:R.definition.concat(te(p)),ruleStack:y,occurrenceStack:E};f.push($),f.push(o)}else if(T instanceof ye)f.push({idx:g,def:T.definition.concat(te(p)),ruleStack:y,occurrenceStack:E});else if(T instanceof Ns)f.push(im(T,g,y,E));else throw Error("non exhaustive match")}return d}a(Mu,"nextPossibleTokensAfter");function im(r,e,t,n){const i=ne(t);i.push(r.name);const s=ne(n);return s.push(1),{idx:e,def:r.definition,ruleStack:i,occurrenceStack:s}}a(im,"expandTopLevelRule");var W;(function(r){r[r.OPTION=0]="OPTION",r[r.REPETITION=1]="REPETITION",r[r.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",r[r.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",r[r.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",r[r.ALTERNATION=5]="ALTERNATION"})(W||(W={}));function il(r){if(r instanceof re||r==="Option")return W.OPTION;if(r instanceof K||r==="Repetition")return W.REPETITION;if(r instanceof Ie||r==="RepetitionMandatory")return W.REPETITION_MANDATORY;if(r instanceof xe||r==="RepetitionMandatoryWithSeparator")return W.REPETITION_MANDATORY_WITH_SEPARATOR;if(r instanceof ve||r==="RepetitionWithSeparator")return W.REPETITION_WITH_SEPARATOR;if(r instanceof Te||r==="Alternation")return W.ALTERNATION;throw Error("non exhaustive match")}a(il,"getProdType");function Kl(r){const{occurrence:e,rule:t,prodType:n,maxLookahead:i}=r,s=il(n);return s===W.ALTERNATION?Sa(e,t,i):Ia(e,t,s,i)}a(Kl,"getLookaheadPaths");function sm(r,e,t,n,i,s){const o=Sa(r,e,t),l=Fu(o)?oa:Ls;return s(o,n,l,i)}a(sm,"buildLookaheadFuncForOr");function am(r,e,t,n,i,s){const o=Ia(r,e,i,t),l=Fu(o)?oa:Ls;return s(o[0],l,n)}a(am,"buildLookaheadFuncForOptionalProd");function om(r,e,t,n){const i=r.length,s=De(r,o=>De(o,l=>l.length===1));if(e)return function(o){const l=I(o,c=>c.GATE);for(let c=0;c<i;c++){const u=r[c],d=u.length,f=l[c];if(!(f!==void 0&&f.call(this)===!1))e:for(let h=0;h<d;h++){const p=u[h],g=p.length;for(let y=0;y<g;y++){const E=this.LA(y+1);if(t(E,p[y])===!1)continue e}return c}}};if(s&&!n){const o=I(r,c=>Le(c)),l=ce(o,(c,u,d)=>(N(u,f=>{w(c,f.tokenTypeIdx)||(c[f.tokenTypeIdx]=d),N(f.categoryMatches,h=>{w(c,h)||(c[h]=d)})}),c),{});return function(){const c=this.LA(1);return l[c.tokenTypeIdx]}}else return function(){for(let o=0;o<i;o++){const l=r[o],c=l.length;e:for(let u=0;u<c;u++){const d=l[u],f=d.length;for(let h=0;h<f;h++){const p=this.LA(h+1);if(t(p,d[h])===!1)continue e}return o}}}}a(om,"buildAlternativesLookAheadFunc");function lm(r,e,t){const n=De(r,s=>s.length===1),i=r.length;if(n&&!t){const s=Le(r);if(s.length===1&&F(s[0].categoryMatches)){const l=s[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===l}}else{const o=ce(s,(l,c,u)=>(l[c.tokenTypeIdx]=!0,N(c.categoryMatches,d=>{l[d]=!0}),l),[]);return function(){const l=this.LA(1);return o[l.tokenTypeIdx]===!0}}}else return function(){e:for(let s=0;s<i;s++){const o=r[s],l=o.length;for(let c=0;c<l;c++){const u=this.LA(c+1);if(e(u,o[c])===!1)continue e}return!0}return!1}}a(lm,"buildSingleAlternativeLookaheadFunction");var Mn,Iv=(Mn=class extends tl{constructor(e,t,n){super(),this.topProd=e,this.targetOccurrence=t,this.targetProdType=n}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,t,n,i){return e.idx===this.targetOccurrence&&this.targetProdType===t?(this.restDef=n.concat(i),!0):!1}walkOption(e,t,n){this.checkIsTarget(e,W.OPTION,t,n)||super.walkOption(e,t,n)}walkAtLeastOne(e,t,n){this.checkIsTarget(e,W.REPETITION_MANDATORY,t,n)||super.walkOption(e,t,n)}walkAtLeastOneSep(e,t,n){this.checkIsTarget(e,W.REPETITION_MANDATORY_WITH_SEPARATOR,t,n)||super.walkOption(e,t,n)}walkMany(e,t,n){this.checkIsTarget(e,W.REPETITION,t,n)||super.walkOption(e,t,n)}walkManySep(e,t,n){this.checkIsTarget(e,W.REPETITION_WITH_SEPARATOR,t,n)||super.walkOption(e,t,n)}},a(Mn,"RestDefinitionFinderWalker"),Mn),Dn,cm=(Dn=class extends ws{constructor(e,t,n){super(),this.targetOccurrence=e,this.targetProdType=t,this.targetRef=n,this.result=[]}checkIsTarget(e,t){e.idx===this.targetOccurrence&&this.targetProdType===t&&(this.targetRef===void 0||e===this.targetRef)&&(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,W.OPTION)}visitRepetition(e){this.checkIsTarget(e,W.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,W.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,W.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,W.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,W.ALTERNATION)}},a(Dn,"InsideDefinitionFinderVisitor"),Dn);function jl(r){const e=new Array(r);for(let t=0;t<r;t++)e[t]=[];return e}a(jl,"initializeArrayOfArrays");function Wa(r){let e=[""];for(let t=0;t<r.length;t++){const n=r[t],i=[];for(let s=0;s<e.length;s++){const o=e[s];i.push(o+"_"+n.tokenTypeIdx);for(let l=0;l<n.categoryMatches.length;l++){const c="_"+n.categoryMatches[l];i.push(o+c)}}e=i}return e}a(Wa,"pathToHashKeys");function um(r,e,t){for(let n=0;n<r.length;n++){if(n===t)continue;const i=r[n];for(let s=0;s<e.length;s++){const o=e[s];if(i[o]===!0)return!1}}return!0}a(um,"isUniquePrefixHash");function Du(r,e){const t=I(r,o=>mo([o],1)),n=jl(t.length),i=I(t,o=>{const l={};return N(o,c=>{const u=Wa(c.partialPath);N(u,d=>{l[d]=!0})}),l});let s=t;for(let o=1;o<=e;o++){const l=s;s=jl(l.length);for(let c=0;c<l.length;c++){const u=l[c];for(let d=0;d<u.length;d++){const f=u[d].partialPath,h=u[d].suffixDef,p=Wa(f);if(um(i,p,c)||F(h)||f.length===e){const y=n[c];if(go(y,f)===!1){y.push(f);for(let E=0;E<p.length;E++){const T=p[E];i[c][T]=!0}}}else{const y=mo(h,o+1,f);s[c]=s[c].concat(y),N(y,E=>{const T=Wa(E.partialPath);N(T,k=>{i[c][k]=!0})})}}}}return n}a(Du,"lookAheadSequenceFromAlternatives");function Sa(r,e,t,n){const i=new cm(r,W.ALTERNATION,n);return e.accept(i),Du(i.result,t)}a(Sa,"getLookaheadPathsForOr");function Ia(r,e,t,n){const i=new cm(r,t);e.accept(i);const s=i.result,l=new Iv(e,r,t).startWalking(),c=new ye({definition:s}),u=new ye({definition:l});return Du([c,u],n)}a(Ia,"getLookaheadPathsForOptionalProd");function go(r,e){e:for(let t=0;t<r.length;t++){const n=r[t];if(n.length===e.length){for(let i=0;i<n.length;i++){const s=e[i],o=n[i];if((s===o||o.categoryMatchesMap[s.tokenTypeIdx]!==void 0)===!1)continue e}return!0}}return!1}a(go,"containsPath");function dm(r,e){return r.length<e.length&&De(r,(t,n)=>{const i=e[n];return t===i||i.categoryMatchesMap[t.tokenTypeIdx]})}a(dm,"isStrictPrefixOfPath");function Fu(r){return De(r,e=>De(e,t=>De(t,n=>F(n.categoryMatches))))}a(Fu,"areTokenCategoriesNotUsed");function fm(r){const e=r.lookaheadStrategy.validate({rules:r.rules,tokenTypes:r.tokenTypes,grammarName:r.grammarName});return I(e,t=>Object.assign({type:de.CUSTOM_LOOKAHEAD_VALIDATION},t))}a(fm,"validateLookahead");function hm(r,e,t,n){const i=Ee(r,c=>pm(c,t)),s=Cm(r,e,t),o=Ee(r,c=>Rm(c,t)),l=Ee(r,c=>gm(c,r,n,t));return i.concat(s,o,l)}a(hm,"validateGrammar");function pm(r,e){const t=new xv;r.accept(t);const n=t.allProductions,i=av(n,mm),s=Ue(i,l=>l.length>1);return I(Q(s),l=>{const c=Fe(l),u=e.buildDuplicateFoundError(r,l),d=Me(c),f={message:u,type:de.DUPLICATE_PRODUCTIONS,ruleName:r.name,dslName:d,occurrence:c.idx},h=Gu(c);return h&&(f.parameter=h),f})}a(pm,"validateDuplicateProductions");function mm(r){return`${Me(r)}_#_${r.idx}_#_${Gu(r)}`}a(mm,"identifyProductionForDuplicates");function Gu(r){return r instanceof V?r.terminalType.name:r instanceof ue?r.nonTerminalName:""}a(Gu,"getExtraProductionArgument");var Fn,xv=(Fn=class extends ws{constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}},a(Fn,"OccurrenceValidationCollector"),Fn);function gm(r,e,t,n){const i=[];if(ce(e,(o,l)=>l.name===r.name?o+1:o,0)>1){const o=n.buildDuplicateRuleNameError({topLevelRule:r,grammarName:t});i.push({message:o,type:de.DUPLICATE_RULE_NAME,ruleName:r.name})}return i}a(gm,"validateRuleDoesNotAlreadyExist");function ym(r,e,t){const n=[];let i;return fe(e,r)||(i=`Invalid rule override, rule: ->${r}<- cannot be overridden in the grammar: ->${t}<-as it is not defined in any of the super grammars `,n.push({message:i,type:de.INVALID_RULE_OVERRIDE,ruleName:r})),n}a(ym,"validateRuleIsOverridden");function Uu(r,e,t,n=[]){const i=[],s=Js(e.definition);if(F(s))return[];{const o=r.name;fe(s,r)&&i.push({message:t.buildLeftRecursionError({topLevelRule:r,leftRecursionPath:n}),type:de.LEFT_RECURSION,ruleName:o});const c=Mo(s,n.concat([r])),u=Ee(c,d=>{const f=ne(n);return f.push(d),Uu(r,d,t,f)});return i.concat(u)}}a(Uu,"validateNoLeftRecursion");function Js(r){let e=[];if(F(r))return e;const t=Fe(r);if(t instanceof ue)e.push(t.referencedRule);else if(t instanceof ye||t instanceof re||t instanceof Ie||t instanceof xe||t instanceof ve||t instanceof K)e=e.concat(Js(t.definition));else if(t instanceof Te)e=Le(I(t.definition,s=>Js(s.definition)));else if(!(t instanceof V))throw Error("non exhaustive match");const n=aa(t),i=r.length>1;if(n&&i){const s=te(r);return e.concat(Js(s))}else return e}a(Js,"getFirstNoneTerminal");var Gn,Bu=(Gn=class extends ws{constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}},a(Gn,"OrCollector"),Gn);function vm(r,e){const t=new Bu;r.accept(t);const n=t.alternations;return Ee(n,s=>{const o=ea(s.definition);return Ee(o,(l,c)=>{const u=Mu([l],[],Ls,1);return F(u)?[{message:e.buildEmptyAlternationError({topLevelRule:r,alternation:s,emptyChoiceIdx:c}),type:de.NONE_LAST_EMPTY_ALT,ruleName:r.name,occurrence:s.idx,alternative:c+1}]:[]})})}a(vm,"validateEmptyOrAlternative");function Tm(r,e,t){const n=new Bu;r.accept(n);let i=n.alternations;return i=Oo(i,o=>o.ignoreAmbiguities===!0),Ee(i,o=>{const l=o.idx,c=o.maxLookahead||e,u=Sa(l,r,c,o),d=Em(u,o,r,t),f=km(u,o,r,t);return d.concat(f)})}a(Tm,"validateAmbiguousAlternationAlternatives");var Un,$v=(Un=class extends ws{constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}},a(Un,"RepetitionCollector"),Un);function Rm(r,e){const t=new Bu;r.accept(t);const n=t.alternations;return Ee(n,s=>s.definition.length>255?[{message:e.buildTooManyAlternativesError({topLevelRule:r,alternation:s}),type:de.TOO_MANY_ALTS,ruleName:r.name,occurrence:s.idx}]:[])}a(Rm,"validateTooManyAlts");function Am(r,e,t){const n=[];return N(r,i=>{const s=new $v;i.accept(s);const o=s.allProductions;N(o,l=>{const c=il(l),u=l.maxLookahead||e,d=l.idx,h=Ia(d,i,c,u)[0];if(F(Le(h))){const p=t.buildEmptyRepetitionError({topLevelRule:i,repetition:l});n.push({message:p,type:de.NO_NON_EMPTY_LOOKAHEAD,ruleName:i.name})}})}),n}a(Am,"validateSomeNonEmptyLookaheadPath");function Em(r,e,t,n){const i=[],s=ce(r,(l,c,u)=>(e.definition[u].ignoreAmbiguities===!0||N(c,d=>{const f=[u];N(r,(h,p)=>{u!==p&&go(h,d)&&e.definition[p].ignoreAmbiguities!==!0&&f.push(p)}),f.length>1&&!go(i,d)&&(i.push(d),l.push({alts:f,path:d}))}),l),[]);return I(s,l=>{const c=I(l.alts,d=>d+1);return{message:n.buildAlternationAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:c,prefixPath:l.path}),type:de.AMBIGUOUS_ALTS,ruleName:t.name,occurrence:e.idx,alternatives:l.alts}})}a(Em,"checkAlternativesAmbiguities");function km(r,e,t,n){const i=ce(r,(o,l,c)=>{const u=I(l,d=>({idx:c,path:d}));return o.concat(u)},[]);return ma(Ee(i,o=>{if(e.definition[o.idx].ignoreAmbiguities===!0)return[];const c=o.idx,u=o.path,d=Se(i,h=>e.definition[h.idx].ignoreAmbiguities!==!0&&h.idx<c&&dm(h.path,u));return I(d,h=>{const p=[h.idx+1,c+1],g=e.idx===0?"":e.idx;return{message:n.buildAlternationPrefixAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:p,prefixPath:h.path}),type:de.AMBIGUOUS_PREFIX_ALTS,ruleName:t.name,occurrence:g,alternatives:p}})}))}a(km,"checkPrefixAlternativesAmbiguities");function Cm(r,e,t){const n=[],i=I(e,s=>s.name);return N(r,s=>{const o=s.name;if(fe(i,o)){const l=t.buildNamespaceConflictError(s);n.push({message:l,type:de.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:o})}}),n}a(Cm,"checkTerminalAndNoneTerminalsNameSpace");function Sm(r){const e=Cc(r,{errMsgProvider:Rv}),t={};return N(r.rules,n=>{t[n.name]=n}),nm(t,e.errMsgProvider)}a(Sm,"resolveGrammar");function Im(r){return r=Cc(r,{errMsgProvider:bt}),hm(r.rules,r.tokenTypes,r.errMsgProvider,r.grammarName)}a(Im,"validateGrammar");var xm="MismatchedTokenException",$m="NoViableAltException",Nm="EarlyExitException",wm="NotAllInputParsedException",_m=[xm,$m,Nm,wm];Object.freeze(_m);function la(r){return fe(_m,r.name)}a(la,"isRecognitionException");var Bn,sl=(Bn=class extends Error{constructor(e,t){super(e),this.token=t,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}},a(Bn,"RecognitionException"),Bn),Vn,Lm=(Vn=class extends sl{constructor(e,t,n){super(e,t),this.previousToken=n,this.name=xm}},a(Vn,"MismatchedTokenException"),Vn),Wn,Nv=(Wn=class extends sl{constructor(e,t,n){super(e,t),this.previousToken=n,this.name=$m}},a(Wn,"NoViableAltException"),Wn),Kn,wv=(Kn=class extends sl{constructor(e,t){super(e,t),this.name=wm}},a(Kn,"NotAllInputParsedException"),Kn),jn,_v=(jn=class extends sl{constructor(e,t,n){super(e,t),this.previousToken=n,this.name=Nm}},a(jn,"EarlyExitException"),jn),kl={},Pm="InRuleRecoveryException",Hn,Lv=(Hn=class extends Error{constructor(e){super(e),this.name=Pm}},a(Hn,"InRuleRecoveryException"),Hn),zn,Pv=(zn=class{initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=w(e,"recoveryEnabled")?e.recoveryEnabled:dt.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=bm)}getTokenToInsert(e){const t=Ca(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return t.isInsertedInRecovery=!0,t}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,t,n,i){const s=this.findReSyncTokenType(),o=this.exportLexerState(),l=[];let c=!1;const u=this.LA(1);let d=this.LA(1);const f=a(()=>{const h=this.LA(0),p=this.errorMessageProvider.buildMismatchTokenMessage({expected:i,actual:u,previous:h,ruleName:this.getCurrRuleFullName()}),g=new Lm(p,u,this.LA(0));g.resyncedTokens=ea(l),this.SAVE_ERROR(g)},"generateErrorMessage");for(;!c;)if(this.tokenMatcher(d,i)){f();return}else if(n.call(this)){f(),e.apply(this,t);return}else this.tokenMatcher(d,s)?c=!0:(d=this.SKIP_TOKEN(),this.addToResyncTokens(d,l));this.importLexerState(o)}shouldInRepetitionRecoveryBeTried(e,t,n){return!(n===!1||this.tokenMatcher(this.LA(1),e)||this.isBackTracking()||this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,t)))}getFollowsForInRuleRecovery(e,t){const n=this.getCurrentGrammarPath(e,t);return this.getNextPossibleTokenTypes(n)}tryInRuleRecovery(e,t){if(this.canRecoverWithSingleTokenInsertion(e,t))return this.getTokenToInsert(e);if(this.canRecoverWithSingleTokenDeletion(e)){const n=this.SKIP_TOKEN();return this.consumeToken(),n}throw new Lv("sad sad panda")}canPerformInRuleRecovery(e,t){return this.canRecoverWithSingleTokenInsertion(e,t)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,t){if(!this.canTokenTypeBeInsertedInRecovery(e)||F(t))return!1;const n=this.LA(1);return vs(t,s=>this.tokenMatcher(n,s))!==void 0}canRecoverWithSingleTokenDeletion(e){return this.canTokenTypeBeDeletedInRecovery(e)?this.tokenMatcher(this.LA(2),e):!1}isInCurrentRuleReSyncSet(e){const t=this.getCurrFollowKey(),n=this.getFollowSetFromFollowKey(t);return fe(n,e)}findReSyncTokenType(){const e=this.flattenFollowSet();let t=this.LA(1),n=2;for(;;){const i=vs(e,s=>Ou(t,s));if(i!==void 0)return i;t=this.LA(n),n++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return kl;const e=this.getLastExplicitRuleShortName(),t=this.getLastExplicitRuleOccurrenceIndex(),n=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:t,inRule:this.shortRuleNameToFullName(n)}}buildFullFollowKeyStack(){const e=this.RULE_STACK,t=this.RULE_OCCURRENCE_STACK;return I(e,(n,i)=>i===0?kl:{ruleName:this.shortRuleNameToFullName(n),idxInCallingRule:t[i],inRule:this.shortRuleNameToFullName(e[i-1])})}flattenFollowSet(){const e=I(this.buildFullFollowKeyStack(),t=>this.getFollowSetFromFollowKey(t));return Le(e)}getFollowSetFromFollowKey(e){if(e===kl)return[Rt];const t=e.ruleName+e.idxInCallingRule+Tp+e.inRule;return this.resyncFollows[t]}addToResyncTokens(e,t){return this.tokenMatcher(e,Rt)||t.push(e),t}reSyncTo(e){const t=[];let n=this.LA(1);for(;this.tokenMatcher(n,e)===!1;)n=this.SKIP_TOKEN(),this.addToResyncTokens(n,t);return ea(t)}attemptInRepetitionRecovery(e,t,n,i,s,o,l){}getCurrentGrammarPath(e,t){const n=this.getHumanReadableRuleStack(),i=ne(this.RULE_OCCURRENCE_STACK);return{ruleStack:n,occurrenceStack:i,lastTok:e,lastTokOccurrence:t}}getHumanReadableRuleStack(){return I(this.RULE_STACK,e=>this.shortRuleNameToFullName(e))}},a(zn,"Recoverable"),zn);function bm(r,e,t,n,i,s,o){const l=this.getKeyForAutomaticLookahead(n,i);let c=this.firstAfterRepMap[l];if(c===void 0){const h=this.getCurrRuleFullName(),p=this.getGAstProductions()[h];c=new s(p,i).startWalking(),this.firstAfterRepMap[l]=c}let u=c.token,d=c.occurrence;const f=c.isEndOfRule;this.RULE_STACK.length===1&&f&&u===void 0&&(u=Rt,d=1),!(u===void 0||d===void 0)&&this.shouldInRepetitionRecoveryBeTried(u,d,o)&&this.tryInRepetitionRecovery(r,e,t,u)}a(bm,"attemptInRepetitionRecovery");var bv=4,It=8,Om=1<<It,Mm=2<<It,Hl=3<<It,zl=4<<It,ql=5<<It,Ka=6<<It;function ja(r,e,t){return t|e|r}a(ja,"getKeyForAutomaticLookahead");var qn,Vu=(qn=class{constructor(e){var t;this.maxLookahead=(t=e?.maxLookahead)!==null&&t!==void 0?t:dt.maxLookahead}validate(e){const t=this.validateNoLeftRecursion(e.rules);if(F(t)){const n=this.validateEmptyOrAlternatives(e.rules),i=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),s=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);return[...t,...n,...i,...s]}return t}validateNoLeftRecursion(e){return Ee(e,t=>Uu(t,t,bt))}validateEmptyOrAlternatives(e){return Ee(e,t=>vm(t,bt))}validateAmbiguousAlternationAlternatives(e,t){return Ee(e,n=>Tm(n,t,bt))}validateSomeNonEmptyLookaheadPath(e,t){return Am(e,t,bt)}buildLookaheadForAlternation(e){return sm(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,om)}buildLookaheadForOptional(e){return am(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,il(e.prodType),lm)}},a(qn,"LLkLookaheadStrategy"),qn),Yn,Ov=(Yn=class{initLooksAhead(e){this.dynamicTokensEnabled=w(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:dt.dynamicTokensEnabled,this.maxLookahead=w(e,"maxLookahead")?e.maxLookahead:dt.maxLookahead,this.lookaheadStrategy=w(e,"lookaheadStrategy")?e.lookaheadStrategy:new Vu({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){N(e,t=>{this.TRACE_INIT(`${t.name} Rule Lookahead`,()=>{const{alternation:n,repetition:i,option:s,repetitionMandatory:o,repetitionMandatoryWithSeparator:l,repetitionWithSeparator:c}=Dm(t);N(n,u=>{const d=u.idx===0?"":u.idx;this.TRACE_INIT(`${Me(u)}${d}`,()=>{const f=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:u.idx,rule:t,maxLookahead:u.maxLookahead||this.maxLookahead,hasPredicates:u.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),h=ja(this.fullRuleNameToShort[t.name],Om,u.idx);this.setLaFuncCache(h,f)})}),N(i,u=>{this.computeLookaheadFunc(t,u.idx,Hl,"Repetition",u.maxLookahead,Me(u))}),N(s,u=>{this.computeLookaheadFunc(t,u.idx,Mm,"Option",u.maxLookahead,Me(u))}),N(o,u=>{this.computeLookaheadFunc(t,u.idx,zl,"RepetitionMandatory",u.maxLookahead,Me(u))}),N(l,u=>{this.computeLookaheadFunc(t,u.idx,Ka,"RepetitionMandatoryWithSeparator",u.maxLookahead,Me(u))}),N(c,u=>{this.computeLookaheadFunc(t,u.idx,ql,"RepetitionWithSeparator",u.maxLookahead,Me(u))})})})}computeLookaheadFunc(e,t,n,i,s,o){this.TRACE_INIT(`${o}${t===0?"":t}`,()=>{const l=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:t,rule:e,maxLookahead:s||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:i}),c=ja(this.fullRuleNameToShort[e.name],n,t);this.setLaFuncCache(c,l)})}getKeyForAutomaticLookahead(e,t){const n=this.getLastExplicitRuleShortName();return ja(n,e,t)}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,t){this.lookAheadFuncsCache.set(e,t)}},a(Yn,"LooksAhead"),Yn),Xn,Mv=(Xn=class extends ws{constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}},a(Xn,"DslMethodsCollectorVisitor"),Xn),wa=new Mv;function Dm(r){wa.reset(),r.accept(wa);const e=wa.dslMethods;return wa.reset(),e}a(Dm,"collectMethods");function Yl(r,e){isNaN(r.startOffset)===!0?(r.startOffset=e.startOffset,r.endOffset=e.endOffset):r.endOffset<e.endOffset&&(r.endOffset=e.endOffset)}a(Yl,"setNodeLocationOnlyOffset");function Xl(r,e){isNaN(r.startOffset)===!0?(r.startOffset=e.startOffset,r.startColumn=e.startColumn,r.startLine=e.startLine,r.endOffset=e.endOffset,r.endColumn=e.endColumn,r.endLine=e.endLine):r.endOffset<e.endOffset&&(r.endOffset=e.endOffset,r.endColumn=e.endColumn,r.endLine=e.endLine)}a(Xl,"setNodeLocationFull");function Fm(r,e,t){r.children[t]===void 0?r.children[t]=[e]:r.children[t].push(e)}a(Fm,"addTerminalToCst");function Gm(r,e,t){r.children[e]===void 0?r.children[e]=[t]:r.children[e].push(t)}a(Gm,"addNoneTerminalToCst");var Dv="name";function Wu(r,e){Object.defineProperty(r,Dv,{enumerable:!1,configurable:!0,writable:!1,value:e})}a(Wu,"defineNameProp");function Um(r,e){const t=Zs(r),n=t.length;for(let i=0;i<n;i++){const s=t[i],o=r[s],l=o.length;for(let c=0;c<l;c++){const u=o[c];u.tokenTypeIdx===void 0&&this[u.name](u.children,e)}}}a(Um,"defaultVisit");function Bm(r,e){const t=a(function(){},"derivedConstructor");Wu(t,r+"BaseSemantics");const n={visit:a(function(i,s){if(_e(i)&&(i=i[0]),!it(i))return this[i.name](i.children,s)},"visit"),validateVisitor:a(function(){const i=Wm(this,e);if(!F(i)){const s=I(i,o=>o.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${s.join(`

`).replace(/\n/g,`
	`)}`)}},"validateVisitor")};return t.prototype=n,t.prototype.constructor=t,t._RULE_NAMES=e,t}a(Bm,"createBaseSemanticVisitorConstructor");function Vm(r,e,t){const n=a(function(){},"derivedConstructor");Wu(n,r+"BaseSemanticsWithDefaults");const i=Object.create(t.prototype);return N(e,s=>{i[s]=Um}),n.prototype=i,n.prototype.constructor=n,n}a(Vm,"createBaseVisitorConstructorWithDefaults");var Jl;(function(r){r[r.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",r[r.MISSING_METHOD=1]="MISSING_METHOD"})(Jl||(Jl={}));function Wm(r,e){return Km(r,e)}a(Wm,"validateVisitor");function Km(r,e){const t=Se(e,i=>or(r[i])===!1),n=I(t,i=>({msg:`Missing visitor method: <${i}> on ${r.constructor.name} CST Visitor.`,type:Jl.MISSING_METHOD,methodName:i}));return ma(n)}a(Km,"validateMissingCstMethods");var Jn,Fv=(Jn=class{initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=w(e,"nodeLocationTracking")?e.nodeLocationTracking:dt.nodeLocationTracking,!this.outputCst)this.cstInvocationStateUpdate=J,this.cstFinallyStateUpdate=J,this.cstPostTerminal=J,this.cstPostNonTerminal=J,this.cstPostRule=J;else if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Xl,this.setNodeLocationFromNode=Xl,this.cstPostRule=J,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=J,this.setNodeLocationFromNode=J,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Yl,this.setNodeLocationFromNode=Yl,this.cstPostRule=J,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=J,this.setNodeLocationFromNode=J,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=J,this.setNodeLocationFromNode=J,this.cstPostRule=J,this.setInitialNodeLocation=J;else throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`)}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){const t=this.LA(1);e.location={startOffset:t.startOffset,startLine:t.startLine,startColumn:t.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){const t={name:e,children:Object.create(null)};this.setInitialNodeLocation(t),this.CST_STACK.push(t)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){const t=this.LA(0),n=e.location;n.startOffset<=t.startOffset?(n.endOffset=t.endOffset,n.endLine=t.endLine,n.endColumn=t.endColumn):(n.startOffset=NaN,n.startLine=NaN,n.startColumn=NaN)}cstPostRuleOnlyOffset(e){const t=this.LA(0),n=e.location;n.startOffset<=t.startOffset?n.endOffset=t.endOffset:n.startOffset=NaN}cstPostTerminal(e,t){const n=this.CST_STACK[this.CST_STACK.length-1];Fm(n,t,e),this.setNodeLocationFromToken(n.location,t)}cstPostNonTerminal(e,t){const n=this.CST_STACK[this.CST_STACK.length-1];Gm(n,t,e),this.setNodeLocationFromNode(n.location,e.location)}getBaseCstVisitorConstructor(){if(it(this.baseCstVisitorConstructor)){const e=Bm(this.className,Zs(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if(it(this.baseCstVisitorWithDefaultsConstructor)){const e=Vm(this.className,Zs(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){const e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}},a(Jn,"TreeBuilder"),Jn),Qn,Gv=(Qn=class{initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):yo}LA(e){const t=this.currIdx+e;return t<0||this.tokVectorLength<=t?yo:this.tokVector[t]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}},a(Qn,"LexerAdapter"),Qn),Zn,Uv=(Zn=class{ACTION(e){return e.call(this)}consume(e,t,n){return this.consumeInternal(t,e,n)}subrule(e,t,n){return this.subruleInternal(t,e,n)}option(e,t){return this.optionInternal(t,e)}or(e,t){return this.orInternal(t,e)}many(e,t){return this.manyInternal(e,t)}atLeastOne(e,t){return this.atLeastOneInternal(e,t)}CONSUME(e,t){return this.consumeInternal(e,0,t)}CONSUME1(e,t){return this.consumeInternal(e,1,t)}CONSUME2(e,t){return this.consumeInternal(e,2,t)}CONSUME3(e,t){return this.consumeInternal(e,3,t)}CONSUME4(e,t){return this.consumeInternal(e,4,t)}CONSUME5(e,t){return this.consumeInternal(e,5,t)}CONSUME6(e,t){return this.consumeInternal(e,6,t)}CONSUME7(e,t){return this.consumeInternal(e,7,t)}CONSUME8(e,t){return this.consumeInternal(e,8,t)}CONSUME9(e,t){return this.consumeInternal(e,9,t)}SUBRULE(e,t){return this.subruleInternal(e,0,t)}SUBRULE1(e,t){return this.subruleInternal(e,1,t)}SUBRULE2(e,t){return this.subruleInternal(e,2,t)}SUBRULE3(e,t){return this.subruleInternal(e,3,t)}SUBRULE4(e,t){return this.subruleInternal(e,4,t)}SUBRULE5(e,t){return this.subruleInternal(e,5,t)}SUBRULE6(e,t){return this.subruleInternal(e,6,t)}SUBRULE7(e,t){return this.subruleInternal(e,7,t)}SUBRULE8(e,t){return this.subruleInternal(e,8,t)}SUBRULE9(e,t){return this.subruleInternal(e,9,t)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,t,n=vo){if(fe(this.definedRulesNames,e)){const o={message:bt.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),type:de.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(o)}this.definedRulesNames.push(e);const i=this.defineRule(e,t,n);return this[e]=i,i}OVERRIDE_RULE(e,t,n=vo){const i=ym(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(i);const s=this.defineRule(e,t,n);return this[e]=s,s}BACKTRACK(e,t){return function(){this.isBackTrackingStack.push(1);const n=this.saveRecogState();try{return e.apply(this,t),!0}catch(i){if(la(i))return!1;throw i}finally{this.reloadRecogState(n),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return hp(Q(this.gastProductionsCache))}},a(Zn,"RecognizerApi"),Zn),ei,Bv=(ei=class{initRecognizerEngine(e,t){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=oa,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},w(t,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if(_e(e)){if(F(e))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof e[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if(_e(e))this.tokensMap=ce(e,(s,o)=>(s[o.name]=o,s),{});else if(w(e,"modes")&&De(Le(Q(e.modes)),tm)){const s=Le(Q(e.modes)),o=Sc(s);this.tokensMap=ce(o,(l,c)=>(l[c.name]=c,l),{})}else if(nv(e))this.tokensMap=ne(e);else throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=Rt;const n=w(e,"modes")?Le(Q(e.modes)):Q(e),i=De(n,s=>F(s.categoryMatches));this.tokenMatcher=i?oa:Ls,Ps(Q(this.tokensMap))}defineRule(e,t,n){if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);const i=w(n,"resyncEnabled")?n.resyncEnabled:vo.resyncEnabled,s=w(n,"recoveryValueFunc")?n.recoveryValueFunc:vo.recoveryValueFunc,o=this.ruleShortNameIdx<<bv+It;this.ruleShortNameIdx++,this.shortRuleNameToFull[o]=e,this.fullRuleNameToShort[e]=o;let l;return this.outputCst===!0?l=a(function(...d){try{this.ruleInvocationStateUpdate(o,e,this.subruleIdx),t.apply(this,d);const f=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(f),f}catch(f){return this.invokeRuleCatch(f,i,s)}finally{this.ruleFinallyStateUpdate()}},"invokeRuleWithTry"):l=a(function(...d){try{return this.ruleInvocationStateUpdate(o,e,this.subruleIdx),t.apply(this,d)}catch(f){return this.invokeRuleCatch(f,i,s)}finally{this.ruleFinallyStateUpdate()}},"invokeRuleWithTryCst"),Object.assign(l,{ruleName:e,originalGrammarAction:t})}invokeRuleCatch(e,t,n){const i=this.RULE_STACK.length===1,s=t&&!this.isBackTracking()&&this.recoveryEnabled;if(la(e)){const o=e;if(s){const l=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(l))if(o.resyncedTokens=this.reSyncTo(l),this.outputCst){const c=this.CST_STACK[this.CST_STACK.length-1];return c.recoveredNode=!0,c}else return n(e);else{if(this.outputCst){const c=this.CST_STACK[this.CST_STACK.length-1];c.recoveredNode=!0,o.partialCstResult=c}throw o}}else{if(i)return this.moveToTerminatedState(),n(e);throw o}}else throw e}optionInternal(e,t){const n=this.getKeyForAutomaticLookahead(Mm,t);return this.optionInternalLogic(e,t,n)}optionInternalLogic(e,t,n){let i=this.getLaFuncFromCache(n),s;if(typeof e!="function"){s=e.DEF;const o=e.GATE;if(o!==void 0){const l=i;i=a(()=>o.call(this)&&l.call(this),"lookAheadFunc")}}else s=e;if(i.call(this)===!0)return s.call(this)}atLeastOneInternal(e,t){const n=this.getKeyForAutomaticLookahead(zl,e);return this.atLeastOneInternalLogic(e,t,n)}atLeastOneInternalLogic(e,t,n){let i=this.getLaFuncFromCache(n),s;if(typeof t!="function"){s=t.DEF;const o=t.GATE;if(o!==void 0){const l=i;i=a(()=>o.call(this)&&l.call(this),"lookAheadFunc")}}else s=t;if(i.call(this)===!0){let o=this.doSingleRepetition(s);for(;i.call(this)===!0&&o===!0;)o=this.doSingleRepetition(s)}else throw this.raiseEarlyExitException(e,W.REPETITION_MANDATORY,t.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,t],i,zl,e,Sv)}atLeastOneSepFirstInternal(e,t){const n=this.getKeyForAutomaticLookahead(Ka,e);this.atLeastOneSepFirstInternalLogic(e,t,n)}atLeastOneSepFirstInternalLogic(e,t,n){const i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){i.call(this);const l=a(()=>this.tokenMatcher(this.LA(1),s),"separatorLookAheadFunc");for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,l,i,ef],l,Ka,e,ef)}else throw this.raiseEarlyExitException(e,W.REPETITION_MANDATORY_WITH_SEPARATOR,t.ERR_MSG)}manyInternal(e,t){const n=this.getKeyForAutomaticLookahead(Hl,e);return this.manyInternalLogic(e,t,n)}manyInternalLogic(e,t,n){let i=this.getLaFuncFromCache(n),s;if(typeof t!="function"){s=t.DEF;const l=t.GATE;if(l!==void 0){const c=i;i=a(()=>l.call(this)&&c.call(this),"lookaheadFunction")}}else s=t;let o=!0;for(;i.call(this)===!0&&o===!0;)o=this.doSingleRepetition(s);this.attemptInRepetitionRecovery(this.manyInternal,[e,t],i,Hl,e,Cv,o)}manySepFirstInternal(e,t){const n=this.getKeyForAutomaticLookahead(ql,e);this.manySepFirstInternalLogic(e,t,n)}manySepFirstInternalLogic(e,t,n){const i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){i.call(this);const l=a(()=>this.tokenMatcher(this.LA(1),s),"separatorLookAheadFunc");for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,l,i,Zd],l,ql,e,Zd)}}repetitionSepSecondInternal(e,t,n,i,s){for(;n();)this.CONSUME(t),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,t,n,i,s],n,Ka,e,s)}doSingleRepetition(e){const t=this.getLexerPosition();return e.call(this),this.getLexerPosition()>t}orInternal(e,t){const n=this.getKeyForAutomaticLookahead(Om,t),i=_e(e)?e:e.DEF,o=this.getLaFuncFromCache(n).call(this,i);if(o!==void 0)return i[o].ALT.call(this);this.raiseNoAltException(t,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){const e=this.LA(1),t=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new wv(t,e))}}subruleInternal(e,t,n){let i;try{const s=n!==void 0?n.ARGS:void 0;return this.subruleIdx=t,i=e.apply(this,s),this.cstPostNonTerminal(i,n!==void 0&&n.LABEL!==void 0?n.LABEL:e.ruleName),i}catch(s){throw this.subruleInternalError(s,n,e.ruleName)}}subruleInternalError(e,t,n){throw la(e)&&e.partialCstResult!==void 0&&(this.cstPostNonTerminal(e.partialCstResult,t!==void 0&&t.LABEL!==void 0?t.LABEL:n),delete e.partialCstResult),e}consumeInternal(e,t,n){let i;try{const s=this.LA(1);this.tokenMatcher(s,e)===!0?(this.consumeToken(),i=s):this.consumeInternalError(e,s,n)}catch(s){i=this.consumeInternalRecovery(e,t,s)}return this.cstPostTerminal(n!==void 0&&n.LABEL!==void 0?n.LABEL:e.name,i),i}consumeInternalError(e,t,n){let i;const s=this.LA(0);throw n!==void 0&&n.ERR_MSG?i=n.ERR_MSG:i=this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:t,previous:s,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new Lm(i,t,s))}consumeInternalRecovery(e,t,n){if(this.recoveryEnabled&&n.name==="MismatchedTokenException"&&!this.isBackTracking()){const i=this.getFollowsForInRuleRecovery(e,t);try{return this.tryInRuleRecovery(e,i)}catch(s){throw s.name===Pm?n:s}}else throw n}saveRecogState(){const e=this.errors,t=ne(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:t,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,t,n){this.RULE_OCCURRENCE_STACK.push(n),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(t)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){const e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),Rt)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}},a(ei,"RecognizerEngine"),ei),ti,Vv=(ti=class{initErrorHandler(e){this._errors=[],this.errorMessageProvider=w(e,"errorMessageProvider")?e.errorMessageProvider:dt.errorMessageProvider}SAVE_ERROR(e){if(la(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:ne(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return ne(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,t,n){const i=this.getCurrRuleFullName(),s=this.getGAstProductions()[i],l=Ia(e,s,t,this.maxLookahead)[0],c=[];for(let d=1;d<=this.maxLookahead;d++)c.push(this.LA(d));const u=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:l,actual:c,previous:this.LA(0),customUserDescription:n,ruleName:i});throw this.SAVE_ERROR(new _v(u,this.LA(1),this.LA(0)))}raiseNoAltException(e,t){const n=this.getCurrRuleFullName(),i=this.getGAstProductions()[n],s=Sa(e,i,this.maxLookahead),o=[];for(let u=1;u<=this.maxLookahead;u++)o.push(this.LA(u));const l=this.LA(0),c=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:s,actual:o,previous:l,customUserDescription:t,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new Nv(c,this.LA(1),l))}},a(ti,"ErrorHandler"),ti),ri,Wv=(ri=class{initContentAssist(){}computeContentAssist(e,t){const n=this.gastProductionsCache[e];if(it(n))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return Mu([n],t,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){const t=Fe(e.ruleStack),i=this.getGAstProductions()[t];return new kv(i,e).startWalking()}},a(ri,"ContentAssist"),ri),al={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(al);var tf=!0,rf=Math.pow(2,It)-1,jm=nn({name:"RECORDING_PHASE_TOKEN",pattern:le.NA});Ps([jm]);var Hm=Ca(jm,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(Hm);var Kv={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}},ni,jv=(ni=class{initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let e=0;e<10;e++){const t=e>0?e:"";this[`CONSUME${t}`]=function(n,i){return this.consumeInternalRecord(n,e,i)},this[`SUBRULE${t}`]=function(n,i){return this.subruleInternalRecord(n,e,i)},this[`OPTION${t}`]=function(n){return this.optionInternalRecord(n,e)},this[`OR${t}`]=function(n){return this.orInternalRecord(n,e)},this[`MANY${t}`]=function(n){this.manyInternalRecord(e,n)},this[`MANY_SEP${t}`]=function(n){this.manySepFirstInternalRecord(e,n)},this[`AT_LEAST_ONE${t}`]=function(n){this.atLeastOneInternalRecord(e,n)},this[`AT_LEAST_ONE_SEP${t}`]=function(n){this.atLeastOneSepFirstInternalRecord(e,n)}}this.consume=function(e,t,n){return this.consumeInternalRecord(t,e,n)},this.subrule=function(e,t,n){return this.subruleInternalRecord(t,e,n)},this.option=function(e,t){return this.optionInternalRecord(t,e)},this.or=function(e,t){return this.orInternalRecord(t,e)},this.many=function(e,t){this.manyInternalRecord(e,t)},this.atLeastOne=function(e,t){this.atLeastOneInternalRecord(e,t)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{const e=this;for(let t=0;t<10;t++){const n=t>0?t:"";delete e[`CONSUME${n}`],delete e[`SUBRULE${n}`],delete e[`OPTION${n}`],delete e[`OR${n}`],delete e[`MANY${n}`],delete e[`MANY_SEP${n}`],delete e[`AT_LEAST_ONE${n}`],delete e[`AT_LEAST_ONE_SEP${n}`]}delete e.consume,delete e.subrule,delete e.option,delete e.or,delete e.many,delete e.atLeastOne,delete e.ACTION,delete e.BACKTRACK,delete e.LA})}ACTION_RECORD(e){}BACKTRACK_RECORD(e,t){return()=>!0}LA_RECORD(e){return yo}topLevelRuleRecord(e,t){try{const n=new Ns({definition:[],name:e});return n.name=e,this.recordingProdStack.push(n),t.call(this),this.recordingProdStack.pop(),n}catch(n){if(n.KNOWN_RECORDER_ERROR!==!0)try{n.message=n.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch{throw n}throw n}}optionInternalRecord(e,t){return gr.call(this,re,e,t)}atLeastOneInternalRecord(e,t){gr.call(this,Ie,t,e)}atLeastOneSepFirstInternalRecord(e,t){gr.call(this,xe,t,e,tf)}manyInternalRecord(e,t){gr.call(this,K,t,e)}manySepFirstInternalRecord(e,t){gr.call(this,ve,t,e,tf)}orInternalRecord(e,t){return zm.call(this,e,t)}subruleInternalRecord(e,t,n){if(ca(t),!e||w(e,"ruleName")===!1){const l=new Error(`<SUBRULE${Ql(t)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw l.KNOWN_RECORDER_ERROR=!0,l}const i=ys(this.recordingProdStack),s=e.ruleName,o=new ue({idx:t,nonTerminalName:s,label:n?.LABEL,referencedRule:void 0});return i.definition.push(o),this.outputCst?Kv:al}consumeInternalRecord(e,t,n){if(ca(t),!Pu(e)){const o=new Error(`<CONSUME${Ql(t)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw o.KNOWN_RECORDER_ERROR=!0,o}const i=ys(this.recordingProdStack),s=new V({idx:t,terminalType:e,label:n?.LABEL});return i.definition.push(s),Hm}},a(ni,"GastRecorder"),ni);function gr(r,e,t,n=!1){ca(t);const i=ys(this.recordingProdStack),s=or(e)?e:e.DEF,o=new r({definition:[],idx:t});return n&&(o.separator=e.SEP),w(e,"MAX_LOOKAHEAD")&&(o.maxLookahead=e.MAX_LOOKAHEAD),this.recordingProdStack.push(o),s.call(this),i.definition.push(o),this.recordingProdStack.pop(),al}a(gr,"recordProd");function zm(r,e){ca(e);const t=ys(this.recordingProdStack),n=_e(r)===!1,i=n===!1?r:r.DEF,s=new Te({definition:[],idx:e,ignoreAmbiguities:n&&r.IGNORE_AMBIGUITIES===!0});w(r,"MAX_LOOKAHEAD")&&(s.maxLookahead=r.MAX_LOOKAHEAD);const o=Rh(i,l=>or(l.GATE));return s.hasPredicates=o,t.definition.push(s),N(i,l=>{const c=new ye({definition:[]});s.definition.push(c),w(l,"IGNORE_AMBIGUITIES")?c.ignoreAmbiguities=l.IGNORE_AMBIGUITIES:w(l,"GATE")&&(c.ignoreAmbiguities=!0),this.recordingProdStack.push(c),l.ALT.call(this),this.recordingProdStack.pop()}),al}a(zm,"recordOrProd");function Ql(r){return r===0?"":`${r}`}a(Ql,"getIdxSuffix");function ca(r){if(r<0||r>rf){const e=new Error(`Invalid DSL Method idx value: <${r}>
	Idx value must be a none negative value smaller than ${rf+1}`);throw e.KNOWN_RECORDER_ERROR=!0,e}}a(ca,"assertMethodIdxIsValid");var ii,Hv=(ii=class{initPerformanceTracer(e){if(w(e,"traceInitPerf")){const t=e.traceInitPerf,n=typeof t=="number";this.traceInitMaxIdent=n?t:1/0,this.traceInitPerf=n?t>0:t}else this.traceInitMaxIdent=0,this.traceInitPerf=dt.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,t){if(this.traceInitPerf===!0){this.traceInitIndent++;const n=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${n}--> <${e}>`);const{time:i,value:s}=xu(t),o=i>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&o(`${n}<-- <${e}> time: ${i}ms`),this.traceInitIndent--,s}else return t()}},a(ii,"PerformanceTracer"),ii);function qm(r,e){e.forEach(t=>{const n=t.prototype;Object.getOwnPropertyNames(n).forEach(i=>{if(i==="constructor")return;const s=Object.getOwnPropertyDescriptor(n,i);s&&(s.get||s.set)?Object.defineProperty(r.prototype,i,s):r.prototype[i]=t.prototype[i]})})}a(qm,"applyMixins");var yo=Ca(Rt,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(yo);var dt=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:tn,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),vo=Object.freeze({recoveryValueFunc:a(()=>{},"recoveryValueFunc"),resyncEnabled:!0}),de;(function(r){r[r.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",r[r.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",r[r.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",r[r.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",r[r.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",r[r.LEFT_RECURSION=5]="LEFT_RECURSION",r[r.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",r[r.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",r[r.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",r[r.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",r[r.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",r[r.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",r[r.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",r[r.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(de||(de={}));function Zl(r=void 0){return function(){return r}}a(Zl,"EMPTY_ALT");var Gt,Ku=(Gt=class{static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let e;this.selfAnalysisDone=!0;const t=this.className;this.TRACE_INIT("toFastProps",()=>{$u(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),N(this.definedRulesNames,i=>{const o=this[i].originalGrammarAction;let l;this.TRACE_INIT(`${i} Rule`,()=>{l=this.topLevelRuleRecord(i,o)}),this.gastProductionsCache[i]=l})}finally{this.disableRecording()}});let n=[];if(this.TRACE_INIT("Grammar Resolving",()=>{n=Sm({rules:Q(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(n)}),this.TRACE_INIT("Grammar Validations",()=>{if(F(n)&&this.skipValidations===!1){const i=Im({rules:Q(this.gastProductionsCache),tokenTypes:Q(this.tokensMap),errMsgProvider:bt,grammarName:t}),s=fm({lookaheadStrategy:this.lookaheadStrategy,rules:Q(this.gastProductionsCache),tokenTypes:Q(this.tokensMap),grammarName:t});this.definitionErrors=this.definitionErrors.concat(i,s)}}),F(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{const i=Rp(Q(this.gastProductionsCache));this.resyncFollows=i}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var i,s;(s=(i=this.lookaheadStrategy).initialize)===null||s===void 0||s.call(i,{rules:Q(this.gastProductionsCache)}),this.preComputeLookaheadFunctions(Q(this.gastProductionsCache))})),!Gt.DEFER_DEFINITION_ERRORS_HANDLING&&!F(this.definitionErrors))throw e=I(this.definitionErrors,i=>i.message),new Error(`Parser Definition Errors detected:
 ${e.join(`
-------------------------------
`)}`)})}constructor(e,t){this.definitionErrors=[],this.selfAnalysisDone=!1;const n=this;if(n.initErrorHandler(t),n.initLexerAdapter(),n.initLooksAhead(t),n.initRecognizerEngine(e,t),n.initRecoverable(t),n.initTreeBuilder(t),n.initContentAssist(),n.initGastRecorder(t),n.initPerformanceTracer(t),w(t,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=w(t,"skipValidations")?t.skipValidations:dt.skipValidations}},a(Gt,"Parser"),Gt);Ku.DEFER_DEFINITION_ERRORS_HANDLING=!1;qm(Ku,[Pv,Ov,Fv,Gv,Bv,Uv,Vv,Wv,jv,Hv]);var si,zv=(si=class extends Ku{constructor(e,t=dt){const n=ne(t);n.outputCst=!1,super(e,n)}},a(si,"EmbeddedActionsParser"),si);function sr(r,e,t){return`${r.name}_${e}_${t}`}a(sr,"buildATNKey");var At=1,qv=2,Ym=4,Xm=5,xa=7,Yv=8,Xv=9,Jv=10,Qv=11,Jm=12,ai,ju=(ai=class{constructor(e){this.target=e}isEpsilon(){return!1}},a(ai,"AbstractTransition"),ai),oi,Hu=(oi=class extends ju{constructor(e,t){super(e),this.tokenType=t}},a(oi,"AtomTransition"),oi),li,Qm=(li=class extends ju{constructor(e){super(e)}isEpsilon(){return!0}},a(li,"EpsilonTransition"),li),ci,zu=(ci=class extends ju{constructor(e,t,n){super(e),this.rule=t,this.followState=n}isEpsilon(){return!0}},a(ci,"RuleTransition"),ci);function Zm(r){const e={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};eg(e,r);const t=r.length;for(let n=0;n<t;n++){const i=r[n],s=xt(e,i,i);s!==void 0&&dg(e,i,s)}return e}a(Zm,"createATN");function eg(r,e){const t=e.length;for(let n=0;n<t;n++){const i=e[n],s=X(r,i,void 0,{type:qv}),o=X(r,i,void 0,{type:xa});s.stop=o,r.ruleToStartState.set(i,s),r.ruleToStopState.set(i,o)}}a(eg,"createRuleStartAndStopATNStates");function qu(r,e,t){return t instanceof V?ol(r,e,t.terminalType,t):t instanceof ue?ug(r,e,t):t instanceof Te?sg(r,e,t):t instanceof re?ag(r,e,t):t instanceof K?tg(r,e,t):t instanceof ve?rg(r,e,t):t instanceof Ie?ng(r,e,t):t instanceof xe?ig(r,e,t):xt(r,e,t)}a(qu,"atom");function tg(r,e,t){const n=X(r,e,t,{type:Xm});pt(r,n);const i=dr(r,e,n,t,xt(r,e,t));return Xu(r,e,t,i)}a(tg,"repetition");function rg(r,e,t){const n=X(r,e,t,{type:Xm});pt(r,n);const i=dr(r,e,n,t,xt(r,e,t)),s=ol(r,e,t.separator,t);return Xu(r,e,t,i,s)}a(rg,"repetitionSep");function ng(r,e,t){const n=X(r,e,t,{type:Ym});pt(r,n);const i=dr(r,e,n,t,xt(r,e,t));return Yu(r,e,t,i)}a(ng,"repetitionMandatory");function ig(r,e,t){const n=X(r,e,t,{type:Ym});pt(r,n);const i=dr(r,e,n,t,xt(r,e,t)),s=ol(r,e,t.separator,t);return Yu(r,e,t,i,s)}a(ig,"repetitionMandatorySep");function sg(r,e,t){const n=X(r,e,t,{type:At});pt(r,n);const i=I(t.definition,o=>qu(r,e,o));return dr(r,e,n,t,...i)}a(sg,"alternation");function ag(r,e,t){const n=X(r,e,t,{type:At});pt(r,n);const i=dr(r,e,n,t,xt(r,e,t));return og(r,e,t,i)}a(ag,"option");function xt(r,e,t){const n=Se(I(t.definition,i=>qu(r,e,i)),i=>i!==void 0);return n.length===1?n[0]:n.length===0?void 0:cg(r,n)}a(xt,"block");function Yu(r,e,t,n,i){const s=n.left,o=n.right,l=X(r,e,t,{type:Qv});pt(r,l);const c=X(r,e,t,{type:Jm});return s.loopback=l,c.loopback=l,r.decisionMap[sr(e,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",t.idx)]=l,z(o,l),i===void 0?(z(l,s),z(l,c)):(z(l,c),z(l,i.left),z(i.right,s)),{left:s,right:c}}a(Yu,"plus");function Xu(r,e,t,n,i){const s=n.left,o=n.right,l=X(r,e,t,{type:Jv});pt(r,l);const c=X(r,e,t,{type:Jm}),u=X(r,e,t,{type:Xv});return l.loopback=u,c.loopback=u,z(l,s),z(l,c),z(o,u),i!==void 0?(z(u,c),z(u,i.left),z(i.right,s)):z(u,l),r.decisionMap[sr(e,i?"RepetitionWithSeparator":"Repetition",t.idx)]=l,{left:l,right:c}}a(Xu,"star");function og(r,e,t,n){const i=n.left,s=n.right;return z(i,s),r.decisionMap[sr(e,"Option",t.idx)]=i,n}a(og,"optional");function pt(r,e){return r.decisionStates.push(e),e.decision=r.decisionStates.length-1,e.decision}a(pt,"defineDecisionState");function dr(r,e,t,n,...i){const s=X(r,e,n,{type:Yv,start:t});t.end=s;for(const l of i)l!==void 0?(z(t,l.left),z(l.right,s)):z(t,s);const o={left:t,right:s};return r.decisionMap[sr(e,lg(n),n.idx)]=t,o}a(dr,"makeAlts");function lg(r){if(r instanceof Te)return"Alternation";if(r instanceof re)return"Option";if(r instanceof K)return"Repetition";if(r instanceof ve)return"RepetitionWithSeparator";if(r instanceof Ie)return"RepetitionMandatory";if(r instanceof xe)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}a(lg,"getProdType");function cg(r,e){const t=e.length;for(let s=0;s<t-1;s++){const o=e[s];let l;o.left.transitions.length===1&&(l=o.left.transitions[0]);const c=l instanceof zu,u=l,d=e[s+1].left;o.left.type===At&&o.right.type===At&&l!==void 0&&(c&&u.followState===o.right||l.target===o.right)?(c?u.followState=d:l.target=d,fg(r,o.right)):z(o.right,d)}const n=e[0],i=e[t-1];return{left:n.left,right:i.right}}a(cg,"makeBlock");function ol(r,e,t,n){const i=X(r,e,n,{type:At}),s=X(r,e,n,{type:At});return ll(i,new Hu(s,t)),{left:i,right:s}}a(ol,"tokenRef");function ug(r,e,t){const n=t.referencedRule,i=r.ruleToStartState.get(n),s=X(r,e,t,{type:At}),o=X(r,e,t,{type:At}),l=new zu(i,n,o);return ll(s,l),{left:s,right:o}}a(ug,"ruleRef");function dg(r,e,t){const n=r.ruleToStartState.get(e);z(n,t.left);const i=r.ruleToStopState.get(e);return z(t.right,i),{left:n,right:i}}a(dg,"buildRuleHandle");function z(r,e){const t=new Qm(e);ll(r,t)}a(z,"epsilon");function X(r,e,t,n){const i=Object.assign({atn:r,production:t,epsilonOnlyTransitions:!1,rule:e,transitions:[],nextTokenWithinRule:[],stateNumber:r.states.length},n);return r.states.push(i),i}a(X,"newState");function ll(r,e){r.transitions.length===0&&(r.epsilonOnlyTransitions=e.isEpsilon()),r.transitions.push(e)}a(ll,"addTransition");function fg(r,e){r.states.splice(r.states.indexOf(e),1)}a(fg,"removeState");var To={},ui,ec=(ui=class{constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){const t=Ju(e);t in this.map||(this.map[t]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return I(this.configs,e=>e.alt)}get key(){let e="";for(const t in this.map)e+=t+":";return e}},a(ui,"ATNConfigSet"),ui);function Ju(r,e=!0){return`${e?`a${r.alt}`:""}s${r.state.stateNumber}:${r.stack.map(t=>t.stateNumber.toString()).join("_")}`}a(Ju,"getATNConfigKey");function hg(r,e){const t={};return n=>{const i=n.toString();let s=t[i];return s!==void 0||(s={atnStartState:r,decision:e,states:{}},t[i]=s),s}}a(hg,"createDFACache");var di,pg=(di=class{constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,t){this.predicates[e]=t}toString(){let e="";const t=this.predicates.length;for(let n=0;n<t;n++)e+=this.predicates[n]===!0?"1":"0";return e}},a(di,"PredicateSet"),di),nf=new pg,fi,Zv=(fi=class extends Vu{constructor(e){var t;super(),this.logging=(t=e?.logging)!==null&&t!==void 0?t:n=>console.log(n)}initialize(e){this.atn=Zm(e.rules),this.dfas=mg(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){const{prodOccurrence:t,rule:n,hasPredicates:i,dynamicTokensEnabled:s}=e,o=this.dfas,l=this.logging,c=sr(n,"Alternation",t),d=this.atn.decisionMap[c].decision,f=I(Kl({maxLookahead:1,occurrence:t,prodType:"Alternation",rule:n}),h=>I(h,p=>p[0]));if(tc(f,!1)&&!s){const h=ce(f,(p,g,y)=>(N(g,E=>{E&&(p[E.tokenTypeIdx]=y,N(E.categoryMatches,T=>{p[T]=y}))}),p),{});return i?function(p){var g;const y=this.LA(1),E=h[y.tokenTypeIdx];if(p!==void 0&&E!==void 0){const T=(g=p[E])===null||g===void 0?void 0:g.GATE;if(T!==void 0&&T.call(this)===!1)return}return E}:function(){const p=this.LA(1);return h[p.tokenTypeIdx]}}else return i?function(h){const p=new pg,g=h===void 0?0:h.length;for(let E=0;E<g;E++){const T=h?.[E].GATE;p.set(E,T===void 0||T.call(this))}const y=Ha.call(this,o,d,p,l);return typeof y=="number"?y:void 0}:function(){const h=Ha.call(this,o,d,nf,l);return typeof h=="number"?h:void 0}}buildLookaheadForOptional(e){const{prodOccurrence:t,rule:n,prodType:i,dynamicTokensEnabled:s}=e,o=this.dfas,l=this.logging,c=sr(n,i,t),d=this.atn.decisionMap[c].decision,f=I(Kl({maxLookahead:1,occurrence:t,prodType:i,rule:n}),h=>I(h,p=>p[0]));if(tc(f)&&f[0][0]&&!s){const h=f[0],p=Le(h);if(p.length===1&&F(p[0].categoryMatches)){const y=p[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===y}}else{const g=ce(p,(y,E)=>(E!==void 0&&(y[E.tokenTypeIdx]=!0,N(E.categoryMatches,T=>{y[T]=!0})),y),{});return function(){const y=this.LA(1);return g[y.tokenTypeIdx]===!0}}}return function(){const h=Ha.call(this,o,d,nf,l);return typeof h=="object"?!1:h===0}}},a(fi,"LLStarLookaheadStrategy"),fi);function tc(r,e=!0){const t=new Set;for(const n of r){const i=new Set;for(const s of n){if(s===void 0){if(e)break;return!1}const o=[s.tokenTypeIdx].concat(s.categoryMatches);for(const l of o)if(t.has(l)){if(!i.has(l))return!1}else t.add(l),i.add(l)}}return!0}a(tc,"isLL1Sequence");function mg(r){const e=r.decisionStates.length,t=Array(e);for(let n=0;n<e;n++)t[n]=hg(r.decisionStates[n],n);return t}a(mg,"initATNSimulator");function Ha(r,e,t,n){const i=r[e](t);let s=i.start;if(s===void 0){const l=Ig(i.atnStartState);s=Zu(i,Qu(l)),i.start=s}return gg.apply(this,[i,s,t,n])}a(Ha,"adaptivePredict");function gg(r,e,t,n){let i=e,s=1;const o=[];let l=this.LA(s++);for(;;){let c=Eg(i,l);if(c===void 0&&(c=yg.apply(this,[r,i,l,s,t,n])),c===To)return Ag(o,i,l);if(c.isAcceptState===!0)return c.prediction;i=c,o.push(l),l=this.LA(s++)}}a(gg,"performLookahead");function yg(r,e,t,n,i,s){const o=kg(e.configs,t,i);if(o.size===0)return rc(r,e,t,To),To;let l=Qu(o);const c=Sg(o,i);if(c!==void 0)l.isAcceptState=!0,l.prediction=c,l.configs.uniqueAlt=c;else if(wg(o)){const u=iv(o.alts);l.isAcceptState=!0,l.prediction=u,l.configs.uniqueAlt=u,vg.apply(this,[r,n,o.alts,s])}return l=rc(r,e,t,l),l}a(yg,"computeLookaheadTarget");function vg(r,e,t,n){const i=[];for(let u=1;u<=e;u++)i.push(this.LA(u).tokenType);const s=r.atnStartState,o=s.rule,l=s.production,c=Tg({topLevelRule:o,ambiguityIndices:t,production:l,prefixPath:i});n(c)}a(vg,"reportLookaheadAmbiguity");function Tg(r){const e=I(r.prefixPath,i=>Dt(i)).join(", "),t=r.production.idx===0?"":r.production.idx;let n=`Ambiguous Alternatives Detected: <${r.ambiguityIndices.join(", ")}> in <${Rg(r.production)}${t}> inside <${r.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return n=n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,n}a(Tg,"buildAmbiguityError");function Rg(r){if(r instanceof ue)return"SUBRULE";if(r instanceof re)return"OPTION";if(r instanceof Te)return"OR";if(r instanceof Ie)return"AT_LEAST_ONE";if(r instanceof xe)return"AT_LEAST_ONE_SEP";if(r instanceof ve)return"MANY_SEP";if(r instanceof K)return"MANY";if(r instanceof V)return"CONSUME";throw Error("non exhaustive match")}a(Rg,"getProductionDslName");function Ag(r,e,t){const n=Ee(e.configs.elements,s=>s.state.transitions),i=sv(n.filter(s=>s instanceof Hu).map(s=>s.tokenType),s=>s.tokenTypeIdx);return{actualToken:t,possibleTokenTypes:i,tokenPath:r}}a(Ag,"buildAdaptivePredictError");function Eg(r,e){return r.edges[e.tokenTypeIdx]}a(Eg,"getExistingTargetState");function kg(r,e,t){const n=new ec,i=[];for(const o of r.elements){if(t.is(o.alt)===!1)continue;if(o.state.type===xa){i.push(o);continue}const l=o.state.transitions.length;for(let c=0;c<l;c++){const u=o.state.transitions[c],d=Cg(u,e);d!==void 0&&n.add({state:d,alt:o.alt,stack:o.stack})}}let s;if(i.length===0&&n.size===1&&(s=n),s===void 0){s=new ec;for(const o of n.elements)ua(o,s)}if(i.length>0&&!$g(s))for(const o of i)s.add(o);return s}a(kg,"computeReachSet");function Cg(r,e){if(r instanceof Hu&&Ou(e,r.tokenType))return r.target}a(Cg,"getReachableTarget");function Sg(r,e){let t;for(const n of r.elements)if(e.is(n.alt)===!0){if(t===void 0)t=n.alt;else if(t!==n.alt)return}return t}a(Sg,"getUniqueAlt");function Qu(r){return{configs:r,edges:{},isAcceptState:!1,prediction:-1}}a(Qu,"newDFAState");function rc(r,e,t,n){return n=Zu(r,n),e.edges[t.tokenTypeIdx]=n,n}a(rc,"addDFAEdge");function Zu(r,e){if(e===To)return e;const t=e.configs.key,n=r.states[t];return n!==void 0?n:(e.configs.finalize(),r.states[t]=e,e)}a(Zu,"addDFAState");function Ig(r){const e=new ec,t=r.transitions.length;for(let n=0;n<t;n++){const s={state:r.transitions[n].target,alt:n,stack:[]};ua(s,e)}return e}a(Ig,"computeStartState");function ua(r,e){const t=r.state;if(t.type===xa){if(r.stack.length>0){const i=[...r.stack],o={state:i.pop(),alt:r.alt,stack:i};ua(o,e)}else e.add(r);return}t.epsilonOnlyTransitions||e.add(r);const n=t.transitions.length;for(let i=0;i<n;i++){const s=t.transitions[i],o=xg(r,s);o!==void 0&&ua(o,e)}}a(ua,"closure");function xg(r,e){if(e instanceof Qm)return{state:e.target,alt:r.alt,stack:r.stack};if(e instanceof zu){const t=[...r.stack,e.followState];return{state:e.target,alt:r.alt,stack:t}}}a(xg,"getEpsilonTarget");function $g(r){for(const e of r.elements)if(e.state.type===xa)return!0;return!1}a($g,"hasConfigInRuleStopState");function Ng(r){for(const e of r.elements)if(e.state.type!==xa)return!1;return!0}a(Ng,"allConfigsInRuleStopStates");function wg(r){if(Ng(r))return!0;const e=_g(r.elements);return Lg(e)&&!Pg(e)}a(wg,"hasConflictTerminatingPrediction");function _g(r){const e=new Map;for(const t of r){const n=Ju(t,!1);let i=e.get(n);i===void 0&&(i={},e.set(n,i)),i[t.alt]=!0}return e}a(_g,"getConflictingAltSets");function Lg(r){for(const e of Array.from(r.values()))if(Object.keys(e).length>1)return!0;return!1}a(Lg,"hasConflictingAltSet");function Pg(r){for(const e of Array.from(r.values()))if(Object.keys(e).length===1)return!0;return!1}a(Pg,"hasStateAssociatedWithOneAlt");var sf;(function(r){function e(t){return typeof t=="string"}a(e,"is"),r.is=e})(sf||(sf={}));var nc;(function(r){function e(t){return typeof t=="string"}a(e,"is"),r.is=e})(nc||(nc={}));var af;(function(r){r.MIN_VALUE=-2147483648,r.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&r.MIN_VALUE<=t&&t<=r.MAX_VALUE}a(e,"is"),r.is=e})(af||(af={}));var Ro;(function(r){r.MIN_VALUE=0,r.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&r.MIN_VALUE<=t&&t<=r.MAX_VALUE}a(e,"is"),r.is=e})(Ro||(Ro={}));var D;(function(r){function e(n,i){return n===Number.MAX_VALUE&&(n=Ro.MAX_VALUE),i===Number.MAX_VALUE&&(i=Ro.MAX_VALUE),{line:n,character:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&m.uinteger(i.line)&&m.uinteger(i.character)}a(t,"is"),r.is=t})(D||(D={}));var O;(function(r){function e(n,i,s,o){if(m.uinteger(n)&&m.uinteger(i)&&m.uinteger(s)&&m.uinteger(o))return{start:D.create(n,i),end:D.create(s,o)};if(D.is(n)&&D.is(i))return{start:n,end:i};throw new Error(`Range#create called with invalid arguments[${n}, ${i}, ${s}, ${o}]`)}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&D.is(i.start)&&D.is(i.end)}a(t,"is"),r.is=t})(O||(O={}));var Ao;(function(r){function e(n,i){return{uri:n,range:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&O.is(i.range)&&(m.string(i.uri)||m.undefined(i.uri))}a(t,"is"),r.is=t})(Ao||(Ao={}));var of;(function(r){function e(n,i,s,o){return{targetUri:n,targetRange:i,targetSelectionRange:s,originSelectionRange:o}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&O.is(i.targetRange)&&m.string(i.targetUri)&&O.is(i.targetSelectionRange)&&(O.is(i.originSelectionRange)||m.undefined(i.originSelectionRange))}a(t,"is"),r.is=t})(of||(of={}));var ic;(function(r){function e(n,i,s,o){return{red:n,green:i,blue:s,alpha:o}}a(e,"create"),r.create=e;function t(n){const i=n;return m.objectLiteral(i)&&m.numberRange(i.red,0,1)&&m.numberRange(i.green,0,1)&&m.numberRange(i.blue,0,1)&&m.numberRange(i.alpha,0,1)}a(t,"is"),r.is=t})(ic||(ic={}));var lf;(function(r){function e(n,i){return{range:n,color:i}}a(e,"create"),r.create=e;function t(n){const i=n;return m.objectLiteral(i)&&O.is(i.range)&&ic.is(i.color)}a(t,"is"),r.is=t})(lf||(lf={}));var cf;(function(r){function e(n,i,s){return{label:n,textEdit:i,additionalTextEdits:s}}a(e,"create"),r.create=e;function t(n){const i=n;return m.objectLiteral(i)&&m.string(i.label)&&(m.undefined(i.textEdit)||ks.is(i))&&(m.undefined(i.additionalTextEdits)||m.typedArray(i.additionalTextEdits,ks.is))}a(t,"is"),r.is=t})(cf||(cf={}));var uf;(function(r){r.Comment="comment",r.Imports="imports",r.Region="region"})(uf||(uf={}));var df;(function(r){function e(n,i,s,o,l,c){const u={startLine:n,endLine:i};return m.defined(s)&&(u.startCharacter=s),m.defined(o)&&(u.endCharacter=o),m.defined(l)&&(u.kind=l),m.defined(c)&&(u.collapsedText=c),u}a(e,"create"),r.create=e;function t(n){const i=n;return m.objectLiteral(i)&&m.uinteger(i.startLine)&&m.uinteger(i.startLine)&&(m.undefined(i.startCharacter)||m.uinteger(i.startCharacter))&&(m.undefined(i.endCharacter)||m.uinteger(i.endCharacter))&&(m.undefined(i.kind)||m.string(i.kind))}a(t,"is"),r.is=t})(df||(df={}));var sc;(function(r){function e(n,i){return{location:n,message:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&Ao.is(i.location)&&m.string(i.message)}a(t,"is"),r.is=t})(sc||(sc={}));var ff;(function(r){r.Error=1,r.Warning=2,r.Information=3,r.Hint=4})(ff||(ff={}));var hf;(function(r){r.Unnecessary=1,r.Deprecated=2})(hf||(hf={}));var pf;(function(r){function e(t){const n=t;return m.objectLiteral(n)&&m.string(n.href)}a(e,"is"),r.is=e})(pf||(pf={}));var Eo;(function(r){function e(n,i,s,o,l,c){let u={range:n,message:i};return m.defined(s)&&(u.severity=s),m.defined(o)&&(u.code=o),m.defined(l)&&(u.source=l),m.defined(c)&&(u.relatedInformation=c),u}a(e,"create"),r.create=e;function t(n){var i;let s=n;return m.defined(s)&&O.is(s.range)&&m.string(s.message)&&(m.number(s.severity)||m.undefined(s.severity))&&(m.integer(s.code)||m.string(s.code)||m.undefined(s.code))&&(m.undefined(s.codeDescription)||m.string((i=s.codeDescription)===null||i===void 0?void 0:i.href))&&(m.string(s.source)||m.undefined(s.source))&&(m.undefined(s.relatedInformation)||m.typedArray(s.relatedInformation,sc.is))}a(t,"is"),r.is=t})(Eo||(Eo={}));var Es;(function(r){function e(n,i,...s){let o={title:n,command:i};return m.defined(s)&&s.length>0&&(o.arguments=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.string(i.title)&&m.string(i.command)}a(t,"is"),r.is=t})(Es||(Es={}));var ks;(function(r){function e(s,o){return{range:s,newText:o}}a(e,"replace"),r.replace=e;function t(s,o){return{range:{start:s,end:s},newText:o}}a(t,"insert"),r.insert=t;function n(s){return{range:s,newText:""}}a(n,"del"),r.del=n;function i(s){const o=s;return m.objectLiteral(o)&&m.string(o.newText)&&O.is(o.range)}a(i,"is"),r.is=i})(ks||(ks={}));var ac;(function(r){function e(n,i,s){const o={label:n};return i!==void 0&&(o.needsConfirmation=i),s!==void 0&&(o.description=s),o}a(e,"create"),r.create=e;function t(n){const i=n;return m.objectLiteral(i)&&m.string(i.label)&&(m.boolean(i.needsConfirmation)||i.needsConfirmation===void 0)&&(m.string(i.description)||i.description===void 0)}a(t,"is"),r.is=t})(ac||(ac={}));var Cs;(function(r){function e(t){const n=t;return m.string(n)}a(e,"is"),r.is=e})(Cs||(Cs={}));var mf;(function(r){function e(s,o,l){return{range:s,newText:o,annotationId:l}}a(e,"replace"),r.replace=e;function t(s,o,l){return{range:{start:s,end:s},newText:o,annotationId:l}}a(t,"insert"),r.insert=t;function n(s,o){return{range:s,newText:"",annotationId:o}}a(n,"del"),r.del=n;function i(s){const o=s;return ks.is(o)&&(ac.is(o.annotationId)||Cs.is(o.annotationId))}a(i,"is"),r.is=i})(mf||(mf={}));var oc;(function(r){function e(n,i){return{textDocument:n,edits:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&fc.is(i.textDocument)&&Array.isArray(i.edits)}a(t,"is"),r.is=t})(oc||(oc={}));var lc;(function(r){function e(n,i,s){let o={kind:"create",uri:n};return i!==void 0&&(i.overwrite!==void 0||i.ignoreIfExists!==void 0)&&(o.options=i),s!==void 0&&(o.annotationId=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return i&&i.kind==="create"&&m.string(i.uri)&&(i.options===void 0||(i.options.overwrite===void 0||m.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||m.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||Cs.is(i.annotationId))}a(t,"is"),r.is=t})(lc||(lc={}));var cc;(function(r){function e(n,i,s,o){let l={kind:"rename",oldUri:n,newUri:i};return s!==void 0&&(s.overwrite!==void 0||s.ignoreIfExists!==void 0)&&(l.options=s),o!==void 0&&(l.annotationId=o),l}a(e,"create"),r.create=e;function t(n){let i=n;return i&&i.kind==="rename"&&m.string(i.oldUri)&&m.string(i.newUri)&&(i.options===void 0||(i.options.overwrite===void 0||m.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||m.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||Cs.is(i.annotationId))}a(t,"is"),r.is=t})(cc||(cc={}));var uc;(function(r){function e(n,i,s){let o={kind:"delete",uri:n};return i!==void 0&&(i.recursive!==void 0||i.ignoreIfNotExists!==void 0)&&(o.options=i),s!==void 0&&(o.annotationId=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return i&&i.kind==="delete"&&m.string(i.uri)&&(i.options===void 0||(i.options.recursive===void 0||m.boolean(i.options.recursive))&&(i.options.ignoreIfNotExists===void 0||m.boolean(i.options.ignoreIfNotExists)))&&(i.annotationId===void 0||Cs.is(i.annotationId))}a(t,"is"),r.is=t})(uc||(uc={}));var dc;(function(r){function e(t){let n=t;return n&&(n.changes!==void 0||n.documentChanges!==void 0)&&(n.documentChanges===void 0||n.documentChanges.every(i=>m.string(i.kind)?lc.is(i)||cc.is(i)||uc.is(i):oc.is(i)))}a(e,"is"),r.is=e})(dc||(dc={}));var gf;(function(r){function e(n){return{uri:n}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.string(i.uri)}a(t,"is"),r.is=t})(gf||(gf={}));var yf;(function(r){function e(n,i){return{uri:n,version:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.string(i.uri)&&m.integer(i.version)}a(t,"is"),r.is=t})(yf||(yf={}));var fc;(function(r){function e(n,i){return{uri:n,version:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.string(i.uri)&&(i.version===null||m.integer(i.version))}a(t,"is"),r.is=t})(fc||(fc={}));var vf;(function(r){function e(n,i,s,o){return{uri:n,languageId:i,version:s,text:o}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.string(i.uri)&&m.string(i.languageId)&&m.integer(i.version)&&m.string(i.text)}a(t,"is"),r.is=t})(vf||(vf={}));var hc;(function(r){r.PlainText="plaintext",r.Markdown="markdown";function e(t){const n=t;return n===r.PlainText||n===r.Markdown}a(e,"is"),r.is=e})(hc||(hc={}));var da;(function(r){function e(t){const n=t;return m.objectLiteral(t)&&hc.is(n.kind)&&m.string(n.value)}a(e,"is"),r.is=e})(da||(da={}));var Tf;(function(r){r.Text=1,r.Method=2,r.Function=3,r.Constructor=4,r.Field=5,r.Variable=6,r.Class=7,r.Interface=8,r.Module=9,r.Property=10,r.Unit=11,r.Value=12,r.Enum=13,r.Keyword=14,r.Snippet=15,r.Color=16,r.File=17,r.Reference=18,r.Folder=19,r.EnumMember=20,r.Constant=21,r.Struct=22,r.Event=23,r.Operator=24,r.TypeParameter=25})(Tf||(Tf={}));var Rf;(function(r){r.PlainText=1,r.Snippet=2})(Rf||(Rf={}));var Af;(function(r){r.Deprecated=1})(Af||(Af={}));var Ef;(function(r){function e(n,i,s){return{newText:n,insert:i,replace:s}}a(e,"create"),r.create=e;function t(n){const i=n;return i&&m.string(i.newText)&&O.is(i.insert)&&O.is(i.replace)}a(t,"is"),r.is=t})(Ef||(Ef={}));var kf;(function(r){r.asIs=1,r.adjustIndentation=2})(kf||(kf={}));var Cf;(function(r){function e(t){const n=t;return n&&(m.string(n.detail)||n.detail===void 0)&&(m.string(n.description)||n.description===void 0)}a(e,"is"),r.is=e})(Cf||(Cf={}));var Sf;(function(r){function e(t){return{label:t}}a(e,"create"),r.create=e})(Sf||(Sf={}));var If;(function(r){function e(t,n){return{items:t||[],isIncomplete:!!n}}a(e,"create"),r.create=e})(If||(If={}));var ko;(function(r){function e(n){return n.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}a(e,"fromPlainText"),r.fromPlainText=e;function t(n){const i=n;return m.string(i)||m.objectLiteral(i)&&m.string(i.language)&&m.string(i.value)}a(t,"is"),r.is=t})(ko||(ko={}));var xf;(function(r){function e(t){let n=t;return!!n&&m.objectLiteral(n)&&(da.is(n.contents)||ko.is(n.contents)||m.typedArray(n.contents,ko.is))&&(t.range===void 0||O.is(t.range))}a(e,"is"),r.is=e})(xf||(xf={}));var $f;(function(r){function e(t,n){return n?{label:t,documentation:n}:{label:t}}a(e,"create"),r.create=e})($f||($f={}));var Nf;(function(r){function e(t,n,...i){let s={label:t};return m.defined(n)&&(s.documentation=n),m.defined(i)?s.parameters=i:s.parameters=[],s}a(e,"create"),r.create=e})(Nf||(Nf={}));var wf;(function(r){r.Text=1,r.Read=2,r.Write=3})(wf||(wf={}));var _f;(function(r){function e(t,n){let i={range:t};return m.number(n)&&(i.kind=n),i}a(e,"create"),r.create=e})(_f||(_f={}));var Lf;(function(r){r.File=1,r.Module=2,r.Namespace=3,r.Package=4,r.Class=5,r.Method=6,r.Property=7,r.Field=8,r.Constructor=9,r.Enum=10,r.Interface=11,r.Function=12,r.Variable=13,r.Constant=14,r.String=15,r.Number=16,r.Boolean=17,r.Array=18,r.Object=19,r.Key=20,r.Null=21,r.EnumMember=22,r.Struct=23,r.Event=24,r.Operator=25,r.TypeParameter=26})(Lf||(Lf={}));var Pf;(function(r){r.Deprecated=1})(Pf||(Pf={}));var bf;(function(r){function e(t,n,i,s,o){let l={name:t,kind:n,location:{uri:s,range:i}};return o&&(l.containerName=o),l}a(e,"create"),r.create=e})(bf||(bf={}));var Of;(function(r){function e(t,n,i,s){return s!==void 0?{name:t,kind:n,location:{uri:i,range:s}}:{name:t,kind:n,location:{uri:i}}}a(e,"create"),r.create=e})(Of||(Of={}));var Mf;(function(r){function e(n,i,s,o,l,c){let u={name:n,detail:i,kind:s,range:o,selectionRange:l};return c!==void 0&&(u.children=c),u}a(e,"create"),r.create=e;function t(n){let i=n;return i&&m.string(i.name)&&m.number(i.kind)&&O.is(i.range)&&O.is(i.selectionRange)&&(i.detail===void 0||m.string(i.detail))&&(i.deprecated===void 0||m.boolean(i.deprecated))&&(i.children===void 0||Array.isArray(i.children))&&(i.tags===void 0||Array.isArray(i.tags))}a(t,"is"),r.is=t})(Mf||(Mf={}));var Df;(function(r){r.Empty="",r.QuickFix="quickfix",r.Refactor="refactor",r.RefactorExtract="refactor.extract",r.RefactorInline="refactor.inline",r.RefactorRewrite="refactor.rewrite",r.Source="source",r.SourceOrganizeImports="source.organizeImports",r.SourceFixAll="source.fixAll"})(Df||(Df={}));var Co;(function(r){r.Invoked=1,r.Automatic=2})(Co||(Co={}));var Ff;(function(r){function e(n,i,s){let o={diagnostics:n};return i!=null&&(o.only=i),s!=null&&(o.triggerKind=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.typedArray(i.diagnostics,Eo.is)&&(i.only===void 0||m.typedArray(i.only,m.string))&&(i.triggerKind===void 0||i.triggerKind===Co.Invoked||i.triggerKind===Co.Automatic)}a(t,"is"),r.is=t})(Ff||(Ff={}));var Gf;(function(r){function e(n,i,s){let o={title:n},l=!0;return typeof i=="string"?(l=!1,o.kind=i):Es.is(i)?o.command=i:o.edit=i,l&&s!==void 0&&(o.kind=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return i&&m.string(i.title)&&(i.diagnostics===void 0||m.typedArray(i.diagnostics,Eo.is))&&(i.kind===void 0||m.string(i.kind))&&(i.edit!==void 0||i.command!==void 0)&&(i.command===void 0||Es.is(i.command))&&(i.isPreferred===void 0||m.boolean(i.isPreferred))&&(i.edit===void 0||dc.is(i.edit))}a(t,"is"),r.is=t})(Gf||(Gf={}));var Uf;(function(r){function e(n,i){let s={range:n};return m.defined(i)&&(s.data=i),s}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&O.is(i.range)&&(m.undefined(i.command)||Es.is(i.command))}a(t,"is"),r.is=t})(Uf||(Uf={}));var Bf;(function(r){function e(n,i){return{tabSize:n,insertSpaces:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.uinteger(i.tabSize)&&m.boolean(i.insertSpaces)}a(t,"is"),r.is=t})(Bf||(Bf={}));var Vf;(function(r){function e(n,i,s){return{range:n,target:i,data:s}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&O.is(i.range)&&(m.undefined(i.target)||m.string(i.target))}a(t,"is"),r.is=t})(Vf||(Vf={}));var Wf;(function(r){function e(n,i){return{range:n,parent:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&O.is(i.range)&&(i.parent===void 0||r.is(i.parent))}a(t,"is"),r.is=t})(Wf||(Wf={}));var Kf;(function(r){r.namespace="namespace",r.type="type",r.class="class",r.enum="enum",r.interface="interface",r.struct="struct",r.typeParameter="typeParameter",r.parameter="parameter",r.variable="variable",r.property="property",r.enumMember="enumMember",r.event="event",r.function="function",r.method="method",r.macro="macro",r.keyword="keyword",r.modifier="modifier",r.comment="comment",r.string="string",r.number="number",r.regexp="regexp",r.operator="operator",r.decorator="decorator"})(Kf||(Kf={}));var jf;(function(r){r.declaration="declaration",r.definition="definition",r.readonly="readonly",r.static="static",r.deprecated="deprecated",r.abstract="abstract",r.async="async",r.modification="modification",r.documentation="documentation",r.defaultLibrary="defaultLibrary"})(jf||(jf={}));var Hf;(function(r){function e(t){const n=t;return m.objectLiteral(n)&&(n.resultId===void 0||typeof n.resultId=="string")&&Array.isArray(n.data)&&(n.data.length===0||typeof n.data[0]=="number")}a(e,"is"),r.is=e})(Hf||(Hf={}));var zf;(function(r){function e(n,i){return{range:n,text:i}}a(e,"create"),r.create=e;function t(n){const i=n;return i!=null&&O.is(i.range)&&m.string(i.text)}a(t,"is"),r.is=t})(zf||(zf={}));var qf;(function(r){function e(n,i,s){return{range:n,variableName:i,caseSensitiveLookup:s}}a(e,"create"),r.create=e;function t(n){const i=n;return i!=null&&O.is(i.range)&&m.boolean(i.caseSensitiveLookup)&&(m.string(i.variableName)||i.variableName===void 0)}a(t,"is"),r.is=t})(qf||(qf={}));var Yf;(function(r){function e(n,i){return{range:n,expression:i}}a(e,"create"),r.create=e;function t(n){const i=n;return i!=null&&O.is(i.range)&&(m.string(i.expression)||i.expression===void 0)}a(t,"is"),r.is=t})(Yf||(Yf={}));var Xf;(function(r){function e(n,i){return{frameId:n,stoppedLocation:i}}a(e,"create"),r.create=e;function t(n){const i=n;return m.defined(i)&&O.is(n.stoppedLocation)}a(t,"is"),r.is=t})(Xf||(Xf={}));var pc;(function(r){r.Type=1,r.Parameter=2;function e(t){return t===1||t===2}a(e,"is"),r.is=e})(pc||(pc={}));var mc;(function(r){function e(n){return{value:n}}a(e,"create"),r.create=e;function t(n){const i=n;return m.objectLiteral(i)&&(i.tooltip===void 0||m.string(i.tooltip)||da.is(i.tooltip))&&(i.location===void 0||Ao.is(i.location))&&(i.command===void 0||Es.is(i.command))}a(t,"is"),r.is=t})(mc||(mc={}));var Jf;(function(r){function e(n,i,s){const o={position:n,label:i};return s!==void 0&&(o.kind=s),o}a(e,"create"),r.create=e;function t(n){const i=n;return m.objectLiteral(i)&&D.is(i.position)&&(m.string(i.label)||m.typedArray(i.label,mc.is))&&(i.kind===void 0||pc.is(i.kind))&&i.textEdits===void 0||m.typedArray(i.textEdits,ks.is)&&(i.tooltip===void 0||m.string(i.tooltip)||da.is(i.tooltip))&&(i.paddingLeft===void 0||m.boolean(i.paddingLeft))&&(i.paddingRight===void 0||m.boolean(i.paddingRight))}a(t,"is"),r.is=t})(Jf||(Jf={}));var Qf;(function(r){function e(t){return{kind:"snippet",value:t}}a(e,"createSnippet"),r.createSnippet=e})(Qf||(Qf={}));var Zf;(function(r){function e(t,n,i,s){return{insertText:t,filterText:n,range:i,command:s}}a(e,"create"),r.create=e})(Zf||(Zf={}));var eh;(function(r){function e(t){return{items:t}}a(e,"create"),r.create=e})(eh||(eh={}));var th;(function(r){r.Invoked=0,r.Automatic=1})(th||(th={}));var rh;(function(r){function e(t,n){return{range:t,text:n}}a(e,"create"),r.create=e})(rh||(rh={}));var nh;(function(r){function e(t,n){return{triggerKind:t,selectedCompletionInfo:n}}a(e,"create"),r.create=e})(nh||(nh={}));var ih;(function(r){function e(t){const n=t;return m.objectLiteral(n)&&nc.is(n.uri)&&m.string(n.name)}a(e,"is"),r.is=e})(ih||(ih={}));var sh;(function(r){function e(s,o,l,c){return new eT(s,o,l,c)}a(e,"create"),r.create=e;function t(s){let o=s;return!!(m.defined(o)&&m.string(o.uri)&&(m.undefined(o.languageId)||m.string(o.languageId))&&m.uinteger(o.lineCount)&&m.func(o.getText)&&m.func(o.positionAt)&&m.func(o.offsetAt))}a(t,"is"),r.is=t;function n(s,o){let l=s.getText(),c=i(o,(d,f)=>{let h=d.range.start.line-f.range.start.line;return h===0?d.range.start.character-f.range.start.character:h}),u=l.length;for(let d=c.length-1;d>=0;d--){let f=c[d],h=s.offsetAt(f.range.start),p=s.offsetAt(f.range.end);if(p<=u)l=l.substring(0,h)+f.newText+l.substring(p,l.length);else throw new Error("Overlapping edit");u=h}return l}a(n,"applyEdits"),r.applyEdits=n;function i(s,o){if(s.length<=1)return s;const l=s.length/2|0,c=s.slice(0,l),u=s.slice(l);i(c,o),i(u,o);let d=0,f=0,h=0;for(;d<c.length&&f<u.length;)o(c[d],u[f])<=0?s[h++]=c[d++]:s[h++]=u[f++];for(;d<c.length;)s[h++]=c[d++];for(;f<u.length;)s[h++]=u[f++];return s}a(i,"mergeSort")})(sh||(sh={}));var hi,eT=(hi=class{constructor(e,t,n,i){this._uri=e,this._languageId=t,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],t=this._content,n=!0;for(let i=0;i<t.length;i++){n&&(e.push(i),n=!1);let s=t.charAt(i);n=s==="\r"||s===`
`,s==="\r"&&i+1<t.length&&t.charAt(i+1)===`
`&&i++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,i=t.length;if(i===0)return D.create(0,e);for(;n<i;){let o=Math.floor((n+i)/2);t[o]>e?i=o:n=o+1}let s=n-1;return D.create(s,e-t[s])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],i=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,i),n)}get lineCount(){return this.getLineOffsets().length}},a(hi,"FullTextDocument"),hi),m;(function(r){const e=Object.prototype.toString;function t(p){return typeof p<"u"}a(t,"defined"),r.defined=t;function n(p){return typeof p>"u"}a(n,"undefined"),r.undefined=n;function i(p){return p===!0||p===!1}a(i,"boolean"),r.boolean=i;function s(p){return e.call(p)==="[object String]"}a(s,"string"),r.string=s;function o(p){return e.call(p)==="[object Number]"}a(o,"number"),r.number=o;function l(p,g,y){return e.call(p)==="[object Number]"&&g<=p&&p<=y}a(l,"numberRange"),r.numberRange=l;function c(p){return e.call(p)==="[object Number]"&&-2147483648<=p&&p<=2147483647}a(c,"integer"),r.integer=c;function u(p){return e.call(p)==="[object Number]"&&0<=p&&p<=2147483647}a(u,"uinteger"),r.uinteger=u;function d(p){return e.call(p)==="[object Function]"}a(d,"func"),r.func=d;function f(p){return p!==null&&typeof p=="object"}a(f,"objectLiteral"),r.objectLiteral=f;function h(p,g){return Array.isArray(p)&&p.every(g)}a(h,"typedArray"),r.typedArray=h})(m||(m={}));var pi,bg=(pi=class{constructor(){this.nodeStack=[]}get current(){var e;return(e=this.nodeStack[this.nodeStack.length-1])!==null&&e!==void 0?e:this.rootNode}buildRootNode(e){return this.rootNode=new td(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){const t=new cl;return t.grammarSource=e,t.root=this.rootNode,this.current.content.push(t),this.nodeStack.push(t),t}buildLeafNode(e,t){const n=new So(e.startOffset,e.image.length,na(e),e.tokenType,!t);return n.grammarSource=t,n.root=this.rootNode,this.current.content.push(n),n}removeNode(e){const t=e.container;if(t){const n=t.content.indexOf(e);n>=0&&t.content.splice(n,1)}}addHiddenNodes(e){const t=[];for(const s of e){const o=new So(s.startOffset,s.image.length,na(s),s.tokenType,!0);o.root=this.rootNode,t.push(o)}let n=this.current,i=!1;if(n.content.length>0){n.content.push(...t);return}for(;n.container;){const s=n.container.content.indexOf(n);if(s>0){n.container.content.splice(s,0,...t),i=!0;break}n=n.container}i||this.rootNode.content.unshift(...t)}construct(e){const t=this.current;typeof e.$type=="string"&&(this.current.astNode=e),e.$cstNode=t;const n=this.nodeStack.pop();n?.content.length===0&&this.removeNode(n)}},a(pi,"CstNodeBuilder"),pi),mi,ed=(mi=class{get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,t;const n=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)=="string"?this._astNode:(t=this.container)===null||t===void 0?void 0:t.astNode;if(!n)throw new Error("This node has no associated AST element");return n}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}},a(mi,"AbstractCstNode"),mi),gi,So=(gi=class extends ed{get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,t,n,i,s=!1){super(),this._hidden=s,this._offset=e,this._tokenType=i,this._length=t,this._range=n}},a(gi,"LeafCstNodeImpl"),gi),yi,cl=(yi=class extends ed{constructor(){super(...arguments),this.content=new tT(this)}get children(){return this.content}get offset(){var e,t;return(t=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&t!==void 0?t:0}get length(){return this.end-this.offset}get end(){var e,t;return(t=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&t!==void 0?t:0}get range(){const e=this.firstNonHiddenNode,t=this.lastNonHiddenNode;if(e&&t){if(this._rangeCache===void 0){const{range:n}=e,{range:i}=t;this._rangeCache={start:n.start,end:i.end.line<n.start.line?n.start:i.end}}return this._rangeCache}else return{start:D.create(0,0),end:D.create(0,0)}}get firstNonHiddenNode(){for(const e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){const t=this.content[e];if(!t.hidden)return t}return this.content[this.content.length-1]}},a(yi,"CompositeCstNodeImpl"),yi),Ut,tT=(Ut=class extends Array{constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,Ut.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,t,...n){return this.addParents(n),super.splice(e,t,...n)}addParents(e){for(const t of e)t.container=this.parent}},a(Ut,"CstNodeContainer"),Ut),vi,td=(vi=class extends cl{get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=e??""}},a(vi,"RootCstNodeImpl"),vi),Io=Symbol("Datatype");function za(r){return r.$type===Io}a(za,"isDataTypeNode");var ah="​",Og=a(r=>r.endsWith(ah)?r:r+ah,"withRuleSuffix"),Ti,rd=(Ti=class{constructor(e){this._unorderedGroups=new Map,this.allRules=new Map,this.lexer=e.parser.Lexer;const t=this.lexer.definition,n=e.LanguageMetaData.mode==="production";this.wrapper=new nT(t,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:n,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,t){this.wrapper.wrapOr(e,t)}optional(e,t){this.wrapper.wrapOption(e,t)}many(e,t){this.wrapper.wrapMany(e,t)}atLeastOne(e,t){this.wrapper.wrapAtLeastOne(e,t)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}},a(Ti,"AbstractLangiumParser"),Ti),Ri,Mg=(Ri=class extends rd{get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new bg,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,t){const n=this.computeRuleType(e),i=this.wrapper.DEFINE_RULE(Og(e.name),this.startImplementation(n,t).bind(this));return this.allRules.set(e.name,i),e.entry&&(this.mainRule=i),i}computeRuleType(e){if(!e.fragment){if(Ra(e))return Io;{const t=$s(e);return t??e.name}}}parse(e,t={}){this.nodeBuilder.buildRootNode(e);const n=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=n.tokens;const i=t.rule?this.allRules.get(t.rule):this.mainRule;if(!i)throw new Error(t.rule?`No rule found with name '${t.rule}'`:"No main rule available.");const s=i.call(this.wrapper,{});return this.nodeBuilder.addHiddenNodes(n.hidden),this.unorderedGroups.clear(),this.lexerResult=void 0,{value:s,lexerErrors:n.errors,lexerReport:n.report,parserErrors:this.wrapper.errors}}startImplementation(e,t){return n=>{const i=!this.isRecording()&&e!==void 0;if(i){const o={$type:e};this.stack.push(o),e===Io&&(o.value="")}let s;try{s=t(n)}catch{s=void 0}return s===void 0&&i&&(s=this.construct()),s}}extractHiddenTokens(e){const t=this.lexerResult.hidden;if(!t.length)return[];const n=e.startOffset;for(let i=0;i<t.length;i++)if(t[i].startOffset>n)return t.splice(0,i);return t.splice(0,t.length)}consume(e,t,n){const i=this.wrapper.wrapConsume(e,t);if(!this.isRecording()&&this.isValidToken(i)){const s=this.extractHiddenTokens(i);this.nodeBuilder.addHiddenNodes(s);const o=this.nodeBuilder.buildLeafNode(i,n),{assignment:l,isCrossRef:c}=this.getAssignment(n),u=this.current;if(l){const d=lt(n)?i.image:this.converter.convert(i.image,o);this.assign(l.operator,l.feature,d,o,c)}else if(za(u)){let d=i.image;lt(n)||(d=this.converter.convert(d,o).toString()),u.value+=d}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset=="number"&&!isNaN(e.endOffset)}subrule(e,t,n,i,s){let o;!this.isRecording()&&!n&&(o=this.nodeBuilder.buildCompositeNode(i));const l=this.wrapper.wrapSubrule(e,t,s);!this.isRecording()&&o&&o.length>0&&this.performSubruleAssignment(l,i,o)}performSubruleAssignment(e,t,n){const{assignment:i,isCrossRef:s}=this.getAssignment(t);if(i)this.assign(i.operator,i.feature,e,n,s);else if(!i){const o=this.current;if(za(o))o.value+=e.toString();else if(typeof e=="object"&&e){const c=this.assignWithoutOverride(e,o);this.stack.pop(),this.stack.push(c)}}}action(e,t){if(!this.isRecording()){let n=this.current;if(t.feature&&t.operator){n=this.construct(),this.nodeBuilder.removeNode(n.$cstNode),this.nodeBuilder.buildCompositeNode(t).content.push(n.$cstNode);const s={$type:e};this.stack.push(s),this.assign(t.operator,t.feature,n,n.$cstNode,!1)}else n.$type=e}}construct(){if(this.isRecording())return;const e=this.current;return qo(e),this.nodeBuilder.construct(e),this.stack.pop(),za(e)?this.converter.convert(e.value,e.$cstNode):(iu(this.astReflection,e),e)}getAssignment(e){if(!this.assignmentMap.has(e)){const t=Is(e,ot);this.assignmentMap.set(e,{assignment:t,isCrossRef:t?ya(t.terminal):!1})}return this.assignmentMap.get(e)}assign(e,t,n,i,s){const o=this.current;let l;switch(s&&typeof n=="string"?l=this.linker.buildReference(o,t,i,n):l=n,e){case"=":{o[t]=l;break}case"?=":{o[t]=!0;break}case"+=":Array.isArray(o[t])||(o[t]=[]),o[t].push(l)}}assignWithoutOverride(e,t){for(const[i,s]of Object.entries(t)){const o=e[i];o===void 0?e[i]=s:Array.isArray(o)&&Array.isArray(s)&&(s.push(...o),e[i]=s)}const n=e.$cstNode;return n&&(n.astNode=void 0,e.$cstNode=void 0),e}get definitionErrors(){return this.wrapper.definitionErrors}},a(Ri,"LangiumParser"),Ri),Ai,Dg=(Ai=class{buildMismatchTokenMessage(e){return tn.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return tn.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return tn.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return tn.buildEarlyExitMessage(e)}},a(Ai,"AbstractParserErrorMessageProvider"),Ai),Ei,nd=(Ei=class extends Dg{buildMismatchTokenMessage({expected:e,actual:t}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${t.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}},a(Ei,"LangiumParserErrorMessageProvider"),Ei),ki,Fg=(ki=class extends rd{constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();const t=this.lexer.tokenize(e,{mode:"partial"});return this.tokens=t.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,t){const n=this.wrapper.DEFINE_RULE(Og(e.name),this.startImplementation(t).bind(this));return this.allRules.set(e.name,n),e.entry&&(this.mainRule=n),n}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return t=>{const n=this.keepStackSize();try{e(t)}finally{this.resetStackSize(n)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){const e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,t,n){this.wrapper.wrapConsume(e,t),this.isRecording()||(this.lastElementStack=[...this.elementStack,n],this.nextTokenIndex=this.currIdx+1)}subrule(e,t,n,i,s){this.before(i),this.wrapper.wrapSubrule(e,t,s),this.after(i)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){const t=this.elementStack.lastIndexOf(e);t>=0&&this.elementStack.splice(t)}}get currIdx(){return this.wrapper.currIdx}},a(ki,"LangiumCompletionParser"),ki),rT={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new nd},Ci,nT=(Ci=class extends zv{constructor(e,t){const n=t&&"maxLookahead"in t;super(e,Object.assign(Object.assign(Object.assign({},rT),{lookaheadStrategy:n?new Vu({maxLookahead:t.maxLookahead}):new Zv({logging:t.skipValidations?()=>{}:void 0})}),t))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,t){return this.RULE(e,t)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,t){return this.consume(e,t)}wrapSubrule(e,t,n){return this.subrule(e,t,{ARGS:[n]})}wrapOr(e,t){this.or(e,t)}wrapOption(e,t){this.option(e,t)}wrapMany(e,t){this.many(e,t)}wrapAtLeastOne(e,t){this.atLeastOne(e,t)}},a(Ci,"ChevrotainWrapper"),Ci);function ul(r,e,t){return Gg({parser:e,tokens:t,ruleNames:new Map},r),e}a(ul,"createParser");function Gg(r,e){const t=Xo(e,!1),n=q(e.rules).filter(ge).filter(i=>t.has(i));for(const i of n){const s=Object.assign(Object.assign({},r),{consume:1,optional:1,subrule:1,many:1,or:1});r.parser.rule(i,Et(s,i.definition))}}a(Gg,"buildRules");function Et(r,e,t=!1){let n;if(lt(e))n=Hg(r,e);else if(Tt(e))n=Ug(r,e);else if(ot(e))n=Et(r,e.terminal);else if(ya(e))n=id(r,e);else if(ct(e))n=Bg(r,e);else if(jo(e))n=Wg(r,e);else if(zo(e))n=Kg(r,e);else if(cr(e))n=jg(r,e);else if(Yc(e)){const i=r.consume++;n=a(()=>r.parser.consume(i,Rt,e),"method")}else throw new Uo(e.$cstNode,`Unexpected element type: ${e.$type}`);return sd(r,t?void 0:fa(e),n,e.cardinality)}a(Et,"buildElement");function Ug(r,e){const t=Aa(e);return()=>r.parser.action(t,e)}a(Ug,"buildAction");function Bg(r,e){const t=e.rule.ref;if(ge(t)){const n=r.subrule++,i=t.fragment,s=e.arguments.length>0?Vg(t,e.arguments):()=>({});return o=>r.parser.subrule(n,ad(r,t),i,e,s(o))}else if(Ye(t)){const n=r.consume++,i=xo(r,t.name);return()=>r.parser.consume(n,i,e)}else if(t)Ct();else throw new Uo(e.$cstNode,`Undefined rule: ${e.rule.$refText}`)}a(Bg,"buildRuleCall");function Vg(r,e){const t=e.map(n=>je(n.value));return n=>{const i={};for(let s=0;s<t.length;s++){const o=r.parameters[s],l=t[s];i[o.name]=l(n)}return i}}a(Vg,"buildRuleCallPredicate");function je(r){if(Vc(r)){const e=je(r.left),t=je(r.right);return n=>e(n)||t(n)}else if(Bc(r)){const e=je(r.left),t=je(r.right);return n=>e(n)&&t(n)}else if(Wc(r)){const e=je(r.value);return t=>!e(t)}else if(Kc(r)){const e=r.parameter.ref.name;return t=>t!==void 0&&t[e]===!0}else if(Uc(r)){const e=!!r.true;return()=>e}Ct()}a(je,"buildPredicate");function Wg(r,e){if(e.elements.length===1)return Et(r,e.elements[0]);{const t=[];for(const i of e.elements){const s={ALT:Et(r,i,!0)},o=fa(i);o&&(s.GATE=je(o)),t.push(s)}const n=r.or++;return i=>r.parser.alternatives(n,t.map(s=>{const o={ALT:a(()=>s.ALT(i),"ALT")},l=s.GATE;return l&&(o.GATE=()=>l(i)),o}))}}a(Wg,"buildAlternatives");function Kg(r,e){if(e.elements.length===1)return Et(r,e.elements[0]);const t=[];for(const l of e.elements){const c={ALT:Et(r,l,!0)},u=fa(l);u&&(c.GATE=je(u)),t.push(c)}const n=r.or++,i=a((l,c)=>{const u=c.getRuleStack().join("-");return`uGroup_${l}_${u}`},"idFunc"),s=a(l=>r.parser.alternatives(n,t.map((c,u)=>{const d={ALT:a(()=>!0,"ALT")},f=r.parser;d.ALT=()=>{if(c.ALT(l),!f.isRecording()){const p=i(n,f);f.unorderedGroups.get(p)||f.unorderedGroups.set(p,[]);const g=f.unorderedGroups.get(p);typeof g?.[u]>"u"&&(g[u]=!0)}};const h=c.GATE;return h?d.GATE=()=>h(l):d.GATE=()=>{const p=f.unorderedGroups.get(i(n,f));return!p?.[u]},d})),"alternatives"),o=sd(r,fa(e),s,"*");return l=>{o(l),r.parser.isRecording()||r.parser.unorderedGroups.delete(i(n,r.parser))}}a(Kg,"buildUnorderedGroup");function jg(r,e){const t=e.elements.map(n=>Et(r,n));return n=>t.forEach(i=>i(n))}a(jg,"buildGroup");function fa(r){if(cr(r))return r.guardCondition}a(fa,"getGuardCondition");function id(r,e,t=e.terminal){if(t)if(ct(t)&&ge(t.rule.ref)){const n=t.rule.ref,i=r.subrule++;return s=>r.parser.subrule(i,ad(r,n),!1,e,s)}else if(ct(t)&&Ye(t.rule.ref)){const n=r.consume++,i=xo(r,t.rule.ref.name);return()=>r.parser.consume(n,i,e)}else if(lt(t)){const n=r.consume++,i=xo(r,t.value);return()=>r.parser.consume(n,i,e)}else throw new Error("Could not build cross reference parser");else{if(!e.type.ref)throw new Error("Could not resolve reference to type: "+e.type.$refText);const n=el(e.type.ref),i=n?.terminal;if(!i)throw new Error("Could not find name assignment for type: "+Aa(e.type.ref));return id(r,e,i)}}a(id,"buildCrossReference");function Hg(r,e){const t=r.consume++,n=r.tokens[e.value];if(!n)throw new Error("Could not find token for keyword: "+e.value);return()=>r.parser.consume(t,n,e)}a(Hg,"buildKeyword");function sd(r,e,t,n){const i=e&&je(e);if(!n)if(i){const s=r.or++;return o=>r.parser.alternatives(s,[{ALT:a(()=>t(o),"ALT"),GATE:a(()=>i(o),"GATE")},{ALT:Zl(),GATE:a(()=>!i(o),"GATE")}])}else return t;if(n==="*"){const s=r.many++;return o=>r.parser.many(s,{DEF:a(()=>t(o),"DEF"),GATE:i?()=>i(o):void 0})}else if(n==="+"){const s=r.many++;if(i){const o=r.or++;return l=>r.parser.alternatives(o,[{ALT:a(()=>r.parser.atLeastOne(s,{DEF:a(()=>t(l),"DEF")}),"ALT"),GATE:a(()=>i(l),"GATE")},{ALT:Zl(),GATE:a(()=>!i(l),"GATE")}])}else return o=>r.parser.atLeastOne(s,{DEF:a(()=>t(o),"DEF")})}else if(n==="?"){const s=r.optional++;return o=>r.parser.optional(s,{DEF:a(()=>t(o),"DEF"),GATE:i?()=>i(o):void 0})}else Ct()}a(sd,"wrap");function ad(r,e){const t=zg(r,e),n=r.parser.getRule(t);if(!n)throw new Error(`Rule "${t}" not found."`);return n}a(ad,"getRule");function zg(r,e){if(ge(e))return e.name;if(r.ruleNames.has(e))return r.ruleNames.get(e);{let t=e,n=t.$container,i=e.$type;for(;!ge(n);)(cr(n)||jo(n)||zo(n))&&(i=n.elements.indexOf(t).toString()+":"+i),t=n,n=n.$container;return i=n.name+":"+i,r.ruleNames.set(e,i),i}}a(zg,"getRuleName");function xo(r,e){const t=r.tokens[e];if(!t)throw new Error(`Token "${e}" not found."`);return t}a(xo,"getToken");function od(r){const e=r.Grammar,t=r.parser.Lexer,n=new Fg(r);return ul(e,n,t.definition),n.finalize(),n}a(od,"createCompletionParser");function ld(r){const e=cd(r);return e.finalize(),e}a(ld,"createLangiumParser");function cd(r){const e=r.Grammar,t=r.parser.Lexer,n=new Mg(r);return ul(e,n,t.definition)}a(cd,"prepareLangiumParser");var Si,dl=(Si=class{constructor(){this.diagnostics=[]}buildTokens(e,t){const n=q(Xo(e,!1)),i=this.buildTerminalTokens(n),s=this.buildKeywordTokens(n,i,t);return i.forEach(o=>{const l=o.PATTERN;typeof l=="object"&&l&&"test"in l&&sa(l)?s.unshift(o):s.push(o)}),s}flushLexingReport(e){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){const e=[...this.diagnostics];return this.diagnostics=[],e}buildTerminalTokens(e){return e.filter(Ye).filter(t=>!t.fragment).map(t=>this.buildTerminalToken(t)).toArray()}buildTerminalToken(e){const t=Ea(e),n=this.requiresCustomPattern(t)?this.regexPatternFunction(t):t,i={name:e.name,PATTERN:n};return typeof n=="function"&&(i.LINE_BREAKS=!0),e.hidden&&(i.GROUP=sa(t)?le.SKIPPED:"hidden"),i}requiresCustomPattern(e){return e.flags.includes("u")||e.flags.includes("s")?!0:!!(e.source.includes("?<=")||e.source.includes("?<!"))}regexPatternFunction(e){const t=new RegExp(e,e.flags+"y");return(n,i)=>(t.lastIndex=i,t.exec(n))}buildKeywordTokens(e,t,n){return e.filter(ge).flatMap(i=>St(i).filter(lt)).distinct(i=>i.value).toArray().sort((i,s)=>s.value.length-i.value.length).map(i=>this.buildKeywordToken(i,t,!!n?.caseInsensitive))}buildKeywordToken(e,t,n){const i=this.buildKeywordPattern(e,n),s={name:e.value,PATTERN:i,LONGER_ALT:this.findLongerAlt(e,t)};return typeof i=="function"&&(s.LINE_BREAKS=!0),s}buildKeywordPattern(e,t){return t?new RegExp(lu(e.value)):e.value}findLongerAlt(e,t){return t.reduce((n,i)=>{const s=i?.PATTERN;return s?.source&&cu("^"+s.source+"$",e.value)&&n.push(i),n},[])}},a(Si,"DefaultTokenBuilder"),Si),Ii,ud=(Ii=class{convert(e,t){let n=t.grammarSource;if(ya(n)&&(n=pu(n)),ct(n)){const i=n.rule.ref;if(!i)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(i,e,t)}return e}runConverter(e,t,n){var i;switch(e.name.toUpperCase()){case"INT":return We.convertInt(t);case"STRING":return We.convertString(t);case"ID":return We.convertID(t)}switch((i=ku(e))===null||i===void 0?void 0:i.toLowerCase()){case"number":return We.convertNumber(t);case"boolean":return We.convertBoolean(t);case"bigint":return We.convertBigint(t);case"date":return We.convertDate(t);default:return t}}},a(Ii,"DefaultValueConverter"),Ii),We;(function(r){function e(u){let d="";for(let f=1;f<u.length-1;f++){const h=u.charAt(f);if(h==="\\"){const p=u.charAt(++f);d+=t(p)}else d+=h}return d}a(e,"convertString"),r.convertString=e;function t(u){switch(u){case"b":return"\b";case"f":return"\f";case"n":return`
`;case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return u}}a(t,"convertEscapeCharacter");function n(u){return u.charAt(0)==="^"?u.substring(1):u}a(n,"convertID"),r.convertID=n;function i(u){return parseInt(u)}a(i,"convertInt"),r.convertInt=i;function s(u){return BigInt(u)}a(s,"convertBigint"),r.convertBigint=s;function o(u){return new Date(u)}a(o,"convertDate"),r.convertDate=o;function l(u){return Number(u)}a(l,"convertNumber"),r.convertNumber=l;function c(u){return u.toLowerCase()==="true"}a(c,"convertBoolean"),r.convertBoolean=c})(We||(We={}));var G={};Po(G,Th(lv()));function fl(){return new Promise(r=>{typeof setImmediate>"u"?setTimeout(r,0):setImmediate(r)})}a(fl,"delayNextTick");var qa=0,qg=10;function hl(){return qa=performance.now(),new G.CancellationTokenSource}a(hl,"startCancelableOperation");function dd(r){qg=r}a(dd,"setInterruptionPeriod");var kt=Symbol("OperationCancelled");function fr(r){return r===kt}a(fr,"isOperationCancelled");async function ae(r){if(r===G.CancellationToken.None)return;const e=performance.now();if(e-qa>=qg&&(qa=e,await fl(),qa=performance.now()),r.isCancellationRequested)throw kt}a(ae,"interruptAndCheck");var xi,ft=(xi=class{constructor(){this.promise=new Promise((e,t)=>{this.resolve=n=>(e(n),this),this.reject=n=>(t(n),this)})}},a(xi,"Deferred"),xi),vt,oh=(vt=class{constructor(e,t,n,i){this._uri=e,this._languageId=t,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){for(const n of e)if(vt.isIncremental(n)){const i=hd(n.range),s=this.offsetAt(i.start),o=this.offsetAt(i.end);this._content=this._content.substring(0,s)+n.text+this._content.substring(o,this._content.length);const l=Math.max(i.start.line,0),c=Math.max(i.end.line,0);let u=this._lineOffsets;const d=gc(n.text,!1,s);if(c-l===d.length)for(let h=0,p=d.length;h<p;h++)u[h+l+1]=d[h];else d.length<1e4?u.splice(l+1,c-l,...d):this._lineOffsets=u=u.slice(0,l+1).concat(d,u.slice(c+1));const f=n.text.length-(o-s);if(f!==0)for(let h=l+1+d.length,p=u.length;h<p;h++)u[h]=u[h]+f}else if(vt.isFull(n))this._content=n.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=t}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=gc(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let n=0,i=t.length;if(i===0)return{line:0,character:e};for(;n<i;){const o=Math.floor((n+i)/2);t[o]>e?i=o:n=o+1}const s=n-1;return e=this.ensureBeforeEOL(e,t[s]),{line:s,character:e-t[s]}}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;const n=t[e.line];if(e.character<=0)return n;const i=e.line+1<t.length?t[e.line+1]:this._content.length,s=Math.min(n+e.character,i);return this.ensureBeforeEOL(s,n)}ensureBeforeEOL(e,t){for(;e>t&&fd(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range!==void 0&&(t.rangeLength===void 0||typeof t.rangeLength=="number")}static isFull(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range===void 0&&t.rangeLength===void 0}},a(vt,"FullTextDocument"),vt),$o;(function(r){function e(i,s,o,l){return new oh(i,s,o,l)}a(e,"create"),r.create=e;function t(i,s,o){if(i instanceof oh)return i.update(s,o),i;throw new Error("TextDocument.update: document must be created by TextDocument.create")}a(t,"update"),r.update=t;function n(i,s){const o=i.getText(),l=No(s.map(Yg),(d,f)=>{const h=d.range.start.line-f.range.start.line;return h===0?d.range.start.character-f.range.start.character:h});let c=0;const u=[];for(const d of l){const f=i.offsetAt(d.range.start);if(f<c)throw new Error("Overlapping edit");f>c&&u.push(o.substring(c,f)),d.newText.length&&u.push(d.newText),c=i.offsetAt(d.range.end)}return u.push(o.substr(c)),u.join("")}a(n,"applyEdits"),r.applyEdits=n})($o||($o={}));function No(r,e){if(r.length<=1)return r;const t=r.length/2|0,n=r.slice(0,t),i=r.slice(t);No(n,e),No(i,e);let s=0,o=0,l=0;for(;s<n.length&&o<i.length;)e(n[s],i[o])<=0?r[l++]=n[s++]:r[l++]=i[o++];for(;s<n.length;)r[l++]=n[s++];for(;o<i.length;)r[l++]=i[o++];return r}a(No,"mergeSort");function gc(r,e,t=0){const n=e?[t]:[];for(let i=0;i<r.length;i++){const s=r.charCodeAt(i);fd(s)&&(s===13&&i+1<r.length&&r.charCodeAt(i+1)===10&&i++,n.push(t+i+1))}return n}a(gc,"computeLineOffsets");function fd(r){return r===13||r===10}a(fd,"isEOL");function hd(r){const e=r.start,t=r.end;return e.line>t.line||e.line===t.line&&e.character>t.character?{start:t,end:e}:r}a(hd,"getWellformedRange");function Yg(r){const e=hd(r.range);return e!==r.range?{newText:r.newText,range:e}:r}a(Yg,"getWellformedEdit");var Xg;(()=>{var r={470:i=>{function s(c){if(typeof c!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(c))}a(s,"e");function o(c,u){for(var d,f="",h=0,p=-1,g=0,y=0;y<=c.length;++y){if(y<c.length)d=c.charCodeAt(y);else{if(d===47)break;d=47}if(d===47){if(!(p===y-1||g===1))if(p!==y-1&&g===2){if(f.length<2||h!==2||f.charCodeAt(f.length-1)!==46||f.charCodeAt(f.length-2)!==46){if(f.length>2){var E=f.lastIndexOf("/");if(E!==f.length-1){E===-1?(f="",h=0):h=(f=f.slice(0,E)).length-1-f.lastIndexOf("/"),p=y,g=0;continue}}else if(f.length===2||f.length===1){f="",h=0,p=y,g=0;continue}}u&&(f.length>0?f+="/..":f="..",h=2)}else f.length>0?f+="/"+c.slice(p+1,y):f=c.slice(p+1,y),h=y-p-1;p=y,g=0}else d===46&&g!==-1?++g:g=-1}return f}a(o,"r");var l={resolve:a(function(){for(var c,u="",d=!1,f=arguments.length-1;f>=-1&&!d;f--){var h;f>=0?h=arguments[f]:(c===void 0&&(c=process.cwd()),h=c),s(h),h.length!==0&&(u=h+"/"+u,d=h.charCodeAt(0)===47)}return u=o(u,!d),d?u.length>0?"/"+u:"/":u.length>0?u:"."},"resolve"),normalize:a(function(c){if(s(c),c.length===0)return".";var u=c.charCodeAt(0)===47,d=c.charCodeAt(c.length-1)===47;return(c=o(c,!u)).length!==0||u||(c="."),c.length>0&&d&&(c+="/"),u?"/"+c:c},"normalize"),isAbsolute:a(function(c){return s(c),c.length>0&&c.charCodeAt(0)===47},"isAbsolute"),join:a(function(){if(arguments.length===0)return".";for(var c,u=0;u<arguments.length;++u){var d=arguments[u];s(d),d.length>0&&(c===void 0?c=d:c+="/"+d)}return c===void 0?".":l.normalize(c)},"join"),relative:a(function(c,u){if(s(c),s(u),c===u||(c=l.resolve(c))===(u=l.resolve(u)))return"";for(var d=1;d<c.length&&c.charCodeAt(d)===47;++d);for(var f=c.length,h=f-d,p=1;p<u.length&&u.charCodeAt(p)===47;++p);for(var g=u.length-p,y=h<g?h:g,E=-1,T=0;T<=y;++T){if(T===y){if(g>y){if(u.charCodeAt(p+T)===47)return u.slice(p+T+1);if(T===0)return u.slice(p+T)}else h>y&&(c.charCodeAt(d+T)===47?E=T:T===0&&(E=0));break}var k=c.charCodeAt(d+T);if(k!==u.charCodeAt(p+T))break;k===47&&(E=T)}var R="";for(T=d+E+1;T<=f;++T)T!==f&&c.charCodeAt(T)!==47||(R.length===0?R+="..":R+="/..");return R.length>0?R+u.slice(p+E):(p+=E,u.charCodeAt(p)===47&&++p,u.slice(p))},"relative"),_makeLong:a(function(c){return c},"_makeLong"),dirname:a(function(c){if(s(c),c.length===0)return".";for(var u=c.charCodeAt(0),d=u===47,f=-1,h=!0,p=c.length-1;p>=1;--p)if((u=c.charCodeAt(p))===47){if(!h){f=p;break}}else h=!1;return f===-1?d?"/":".":d&&f===1?"//":c.slice(0,f)},"dirname"),basename:a(function(c,u){if(u!==void 0&&typeof u!="string")throw new TypeError('"ext" argument must be a string');s(c);var d,f=0,h=-1,p=!0;if(u!==void 0&&u.length>0&&u.length<=c.length){if(u.length===c.length&&u===c)return"";var g=u.length-1,y=-1;for(d=c.length-1;d>=0;--d){var E=c.charCodeAt(d);if(E===47){if(!p){f=d+1;break}}else y===-1&&(p=!1,y=d+1),g>=0&&(E===u.charCodeAt(g)?--g==-1&&(h=d):(g=-1,h=y))}return f===h?h=y:h===-1&&(h=c.length),c.slice(f,h)}for(d=c.length-1;d>=0;--d)if(c.charCodeAt(d)===47){if(!p){f=d+1;break}}else h===-1&&(p=!1,h=d+1);return h===-1?"":c.slice(f,h)},"basename"),extname:a(function(c){s(c);for(var u=-1,d=0,f=-1,h=!0,p=0,g=c.length-1;g>=0;--g){var y=c.charCodeAt(g);if(y!==47)f===-1&&(h=!1,f=g+1),y===46?u===-1?u=g:p!==1&&(p=1):u!==-1&&(p=-1);else if(!h){d=g+1;break}}return u===-1||f===-1||p===0||p===1&&u===f-1&&u===d+1?"":c.slice(u,f)},"extname"),format:a(function(c){if(c===null||typeof c!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof c);return(function(u,d){var f=d.dir||d.root,h=d.base||(d.name||"")+(d.ext||"");return f?f===d.root?f+h:f+"/"+h:h})(0,c)},"format"),parse:a(function(c){s(c);var u={root:"",dir:"",base:"",ext:"",name:""};if(c.length===0)return u;var d,f=c.charCodeAt(0),h=f===47;h?(u.root="/",d=1):d=0;for(var p=-1,g=0,y=-1,E=!0,T=c.length-1,k=0;T>=d;--T)if((f=c.charCodeAt(T))!==47)y===-1&&(E=!1,y=T+1),f===46?p===-1?p=T:k!==1&&(k=1):p!==-1&&(k=-1);else if(!E){g=T+1;break}return p===-1||y===-1||k===0||k===1&&p===y-1&&p===g+1?y!==-1&&(u.base=u.name=g===0&&h?c.slice(1,y):c.slice(g,y)):(g===0&&h?(u.name=c.slice(1,p),u.base=c.slice(1,y)):(u.name=c.slice(g,p),u.base=c.slice(g,y)),u.ext=c.slice(p,y)),g>0?u.dir=c.slice(0,g-1):h&&(u.dir="/"),u},"parse"),sep:"/",delimiter:":",win32:null,posix:null};l.posix=l,i.exports=l}},e={};function t(i){var s=e[i];if(s!==void 0)return s.exports;var o=e[i]={exports:{}};return r[i](o,o.exports,t),o.exports}a(t,"r"),t.d=(i,s)=>{for(var o in s)t.o(s,o)&&!t.o(i,o)&&Object.defineProperty(i,o,{enumerable:!0,get:s[o]})},t.o=(i,s)=>Object.prototype.hasOwnProperty.call(i,s),t.r=i=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var n={};(()=>{let i;t.r(n),t.d(n,{URI:a(()=>h,"URI"),Utils:a(()=>$e,"Utils")}),typeof process=="object"?i=process.platform==="win32":typeof navigator=="object"&&(i=navigator.userAgent.indexOf("Windows")>=0);const s=/^\w[\w\d+.-]*$/,o=/^\//,l=/^\/\//;function c(C,v){if(!C.scheme&&v)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${C.authority}", path: "${C.path}", query: "${C.query}", fragment: "${C.fragment}"}`);if(C.scheme&&!s.test(C.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(C.path){if(C.authority){if(!o.test(C.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(l.test(C.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}a(c,"s");const u="",d="/",f=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,be=class be{static isUri(v){return v instanceof be||!!v&&typeof v.authority=="string"&&typeof v.fragment=="string"&&typeof v.path=="string"&&typeof v.query=="string"&&typeof v.scheme=="string"&&typeof v.fsPath=="string"&&typeof v.with=="function"&&typeof v.toString=="function"}scheme;authority;path;query;fragment;constructor(v,x,S,M,b,P=!1){typeof v=="object"?(this.scheme=v.scheme||u,this.authority=v.authority||u,this.path=v.path||u,this.query=v.query||u,this.fragment=v.fragment||u):(this.scheme=(function(he,ie){return he||ie?he:"file"})(v,P),this.authority=x||u,this.path=(function(he,ie){switch(he){case"https":case"http":case"file":ie?ie[0]!==d&&(ie=d+ie):ie=d}return ie})(this.scheme,S||u),this.query=M||u,this.fragment=b||u,c(this,P))}get fsPath(){return k(this,!1)}with(v){if(!v)return this;let{scheme:x,authority:S,path:M,query:b,fragment:P}=v;return x===void 0?x=this.scheme:x===null&&(x=u),S===void 0?S=this.authority:S===null&&(S=u),M===void 0?M=this.path:M===null&&(M=u),b===void 0?b=this.query:b===null&&(b=u),P===void 0?P=this.fragment:P===null&&(P=u),x===this.scheme&&S===this.authority&&M===this.path&&b===this.query&&P===this.fragment?this:new g(x,S,M,b,P)}static parse(v,x=!1){const S=f.exec(v);return S?new g(S[2]||u,ee(S[4]||u),ee(S[5]||u),ee(S[7]||u),ee(S[9]||u),x):new g(u,u,u,u,u)}static file(v){let x=u;if(i&&(v=v.replace(/\\/g,d)),v[0]===d&&v[1]===d){const S=v.indexOf(d,2);S===-1?(x=v.substring(2),v=d):(x=v.substring(2,S),v=v.substring(S)||d)}return new g("file",x,v,u,u)}static from(v){const x=new g(v.scheme,v.authority,v.path,v.query,v.fragment);return c(x,!0),x}toString(v=!1){return R(this,v)}toJSON(){return this}static revive(v){if(v){if(v instanceof be)return v;{const x=new g(v);return x._formatted=v.external,x._fsPath=v._sep===p?v.fsPath:null,x}}return v}};a(be,"f");let h=be;const p=i?1:void 0,bs=class bs extends h{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=k(this,!1)),this._fsPath}toString(v=!1){return v?R(this,!0):(this._formatted||(this._formatted=R(this,!1)),this._formatted)}toJSON(){const v={$mid:1};return this._fsPath&&(v.fsPath=this._fsPath,v._sep=p),this._formatted&&(v.external=this._formatted),this.path&&(v.path=this.path),this.scheme&&(v.scheme=this.scheme),this.authority&&(v.authority=this.authority),this.query&&(v.query=this.query),this.fragment&&(v.fragment=this.fragment),v}};a(bs,"l");let g=bs;const y={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function E(C,v,x){let S,M=-1;for(let b=0;b<C.length;b++){const P=C.charCodeAt(b);if(P>=97&&P<=122||P>=65&&P<=90||P>=48&&P<=57||P===45||P===46||P===95||P===126||v&&P===47||x&&P===91||x&&P===93||x&&P===58)M!==-1&&(S+=encodeURIComponent(C.substring(M,b)),M=-1),S!==void 0&&(S+=C.charAt(b));else{S===void 0&&(S=C.substr(0,b));const he=y[P];he!==void 0?(M!==-1&&(S+=encodeURIComponent(C.substring(M,b)),M=-1),S+=he):M===-1&&(M=b)}}return M!==-1&&(S+=encodeURIComponent(C.substring(M))),S!==void 0?S:C}a(E,"d");function T(C){let v;for(let x=0;x<C.length;x++){const S=C.charCodeAt(x);S===35||S===63?(v===void 0&&(v=C.substr(0,x)),v+=y[S]):v!==void 0&&(v+=C[x])}return v!==void 0?v:C}a(T,"p");function k(C,v){let x;return x=C.authority&&C.path.length>1&&C.scheme==="file"?`//${C.authority}${C.path}`:C.path.charCodeAt(0)===47&&(C.path.charCodeAt(1)>=65&&C.path.charCodeAt(1)<=90||C.path.charCodeAt(1)>=97&&C.path.charCodeAt(1)<=122)&&C.path.charCodeAt(2)===58?v?C.path.substr(1):C.path[1].toLowerCase()+C.path.substr(2):C.path,i&&(x=x.replace(/\//g,"\\")),x}a(k,"m");function R(C,v){const x=v?T:E;let S="",{scheme:M,authority:b,path:P,query:he,fragment:ie}=C;if(M&&(S+=M,S+=":"),(b||M==="file")&&(S+=d,S+=d),b){let H=b.indexOf("@");if(H!==-1){const gt=b.substr(0,H);b=b.substr(H+1),H=gt.lastIndexOf(":"),H===-1?S+=x(gt,!1,!1):(S+=x(gt.substr(0,H),!1,!1),S+=":",S+=x(gt.substr(H+1),!1,!0)),S+="@"}b=b.toLowerCase(),H=b.lastIndexOf(":"),H===-1?S+=x(b,!1,!0):(S+=x(b.substr(0,H),!1,!0),S+=b.substr(H))}if(P){if(P.length>=3&&P.charCodeAt(0)===47&&P.charCodeAt(2)===58){const H=P.charCodeAt(1);H>=65&&H<=90&&(P=`/${String.fromCharCode(H+32)}:${P.substr(3)}`)}else if(P.length>=2&&P.charCodeAt(1)===58){const H=P.charCodeAt(0);H>=65&&H<=90&&(P=`${String.fromCharCode(H+32)}:${P.substr(2)}`)}S+=x(P,!0,!1)}return he&&(S+="?",S+=x(he,!1,!1)),ie&&(S+="#",S+=v?ie:E(ie,!1,!1)),S}a(R,"y");function $(C){try{return decodeURIComponent(C)}catch{return C.length>3?C.substr(0,3)+$(C.substr(3)):C}}a($,"v");const U=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ee(C){return C.match(U)?C.replace(U,v=>$(v)):C}a(ee,"C");var Pe=t(470);const Re=Pe.posix||Pe,Ve="/";var $e;(function(C){C.joinPath=function(v,...x){return v.with({path:Re.join(v.path,...x)})},C.resolvePath=function(v,...x){let S=v.path,M=!1;S[0]!==Ve&&(S=Ve+S,M=!0);let b=Re.resolve(S,...x);return M&&b[0]===Ve&&!v.authority&&(b=b.substring(1)),v.with({path:b})},C.dirname=function(v){if(v.path.length===0||v.path===Ve)return v;let x=Re.dirname(v.path);return x.length===1&&x.charCodeAt(0)===46&&(x=""),v.with({path:x})},C.basename=function(v){return Re.basename(v.path)},C.extname=function(v){return Re.extname(v.path)}})($e||($e={}))})(),Xg=n})();var{URI:ht,Utils:Os}=Xg,qe;(function(r){r.basename=Os.basename,r.dirname=Os.dirname,r.extname=Os.extname,r.joinPath=Os.joinPath,r.resolvePath=Os.resolvePath;function e(i,s){return i?.toString()===s?.toString()}a(e,"equals"),r.equals=e;function t(i,s){const o=typeof i=="string"?i:i.path,l=typeof s=="string"?s:s.path,c=o.split("/").filter(p=>p.length>0),u=l.split("/").filter(p=>p.length>0);let d=0;for(;d<c.length&&c[d]===u[d];d++);const f="../".repeat(c.length-d),h=u.slice(d).join("/");return f+h}a(t,"relative"),r.relative=t;function n(i){return ht.parse(i.toString()).toString()}a(n,"normalize"),r.normalize=n})(qe||(qe={}));var B;(function(r){r[r.Changed=0]="Changed",r[r.Parsed=1]="Parsed",r[r.IndexedContent=2]="IndexedContent",r[r.ComputedScopes=3]="ComputedScopes",r[r.Linked=4]="Linked",r[r.IndexedReferences=5]="IndexedReferences",r[r.Validated=6]="Validated"})(B||(B={}));var $i,Jg=($i=class{constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,t=G.CancellationToken.None){const n=await this.fileSystemProvider.readFile(e);return this.createAsync(e,n,t)}fromTextDocument(e,t,n){return t=t??ht.parse(e.uri),G.CancellationToken.is(n)?this.createAsync(t,e,n):this.create(t,e,n)}fromString(e,t,n){return G.CancellationToken.is(n)?this.createAsync(t,e,n):this.create(t,e,n)}fromModel(e,t){return this.create(t,{$model:e})}create(e,t,n){if(typeof t=="string"){const i=this.parse(e,t,n);return this.createLangiumDocument(i,e,void 0,t)}else if("$model"in t){const i={value:t.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(i,e)}else{const i=this.parse(e,t.getText(),n);return this.createLangiumDocument(i,e,t)}}async createAsync(e,t,n){if(typeof t=="string"){const i=await this.parseAsync(e,t,n);return this.createLangiumDocument(i,e,void 0,t)}else{const i=await this.parseAsync(e,t.getText(),n);return this.createLangiumDocument(i,e,t)}}createLangiumDocument(e,t,n,i){let s;if(n)s={parseResult:e,uri:t,state:B.Parsed,references:[],textDocument:n};else{const o=this.createTextDocumentGetter(t,i);s={parseResult:e,uri:t,state:B.Parsed,references:[],get textDocument(){return o()}}}return e.value.$document=s,s}async update(e,t){var n,i;const s=(n=e.parseResult.value.$cstNode)===null||n===void 0?void 0:n.root.fullText,o=(i=this.textDocuments)===null||i===void 0?void 0:i.get(e.uri.toString()),l=o?o.getText():await this.fileSystemProvider.readFile(e.uri);if(o)Object.defineProperty(e,"textDocument",{value:o});else{const c=this.createTextDocumentGetter(e.uri,l);Object.defineProperty(e,"textDocument",{get:c})}return s!==l&&(e.parseResult=await this.parseAsync(e.uri,l,t),e.parseResult.value.$document=e),e.state=B.Parsed,e}parse(e,t,n){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(t,n)}parseAsync(e,t,n){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(t,n)}createTextDocumentGetter(e,t){const n=this.serviceRegistry;let i;return()=>i??(i=$o.create(e.toString(),n.getServices(e).LanguageMetaData.languageId,0,t??""))}},a($i,"DefaultLangiumDocumentFactory"),$i),Ni,Qg=(Ni=class{constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.serviceRegistry=e.ServiceRegistry}get all(){return q(this.documentMap.values())}addDocument(e){const t=e.uri.toString();if(this.documentMap.has(t))throw new Error(`A document with the URI '${t}' is already present.`);this.documentMap.set(t,e)}getDocument(e){const t=e.toString();return this.documentMap.get(t)}async getOrCreateDocument(e,t){let n=this.getDocument(e);return n||(n=await this.langiumDocumentFactory.fromUri(e,t),this.addDocument(n),n)}createDocument(e,t,n){if(n)return this.langiumDocumentFactory.fromString(t,e,n).then(i=>(this.addDocument(i),i));{const i=this.langiumDocumentFactory.fromString(t,e);return this.addDocument(i),i}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){const t=e.toString(),n=this.documentMap.get(t);return n&&(this.serviceRegistry.getServices(e).references.Linker.unlink(n),n.state=B.Changed,n.precomputedScopes=void 0,n.diagnostics=void 0),n}deleteDocument(e){const t=e.toString(),n=this.documentMap.get(t);return n&&(n.state=B.Changed,this.documentMap.delete(t)),n}},a(Ni,"DefaultLangiumDocuments"),Ni),Cl=Symbol("ref_resolving"),wi,Zg=(wi=class{constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,t=G.CancellationToken.None){for(const n of nt(e.parseResult.value))await ae(t),Ta(n).forEach(i=>this.doLink(i,e))}doLink(e,t){var n;const i=e.reference;if(i._ref===void 0){i._ref=Cl;try{const s=this.getCandidate(e);if(rn(s))i._ref=s;else if(i._nodeDescription=s,this.langiumDocuments().hasDocument(s.documentUri)){const o=this.loadAstNode(s);i._ref=o??this.createLinkingError(e,s)}else i._ref=void 0}catch(s){console.error(`An error occurred while resolving reference to '${i.$refText}':`,s);const o=(n=s.message)!==null&&n!==void 0?n:String(s);i._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${i.$refText}': ${o}`})}t.references.push(i)}}unlink(e){for(const t of e.references)delete t._ref,delete t._nodeDescription;e.references=[]}getCandidate(e){const n=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return n??this.createLinkingError(e)}buildReference(e,t,n,i){const s=this,o={$refNode:n,$refText:i,get ref(){var l;if(Y(this._ref))return this._ref;if(xc(this._nodeDescription)){const c=s.loadAstNode(this._nodeDescription);this._ref=c??s.createLinkingError({reference:o,container:e,property:t},this._nodeDescription)}else if(this._ref===void 0){this._ref=Cl;const c=ia(e).$document,u=s.getLinkedNode({reference:o,container:e,property:t});if(u.error&&c&&c.state<B.ComputedScopes)return this._ref=void 0;this._ref=(l=u.node)!==null&&l!==void 0?l:u.error,this._nodeDescription=u.descr,c?.references.push(this)}else if(this._ref===Cl)throw new Error(`Cyclic reference resolution detected: ${s.astNodeLocator.getAstNodePath(e)}/${t} (symbol '${i}')`);return Y(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return rn(this._ref)?this._ref:void 0}};return o}getLinkedNode(e){var t;try{const n=this.getCandidate(e);if(rn(n))return{error:n};const i=this.loadAstNode(n);return i?{node:i,descr:n}:{descr:n,error:this.createLinkingError(e,n)}}catch(n){console.error(`An error occurred while resolving reference to '${e.reference.$refText}':`,n);const i=(t=n.message)!==null&&t!==void 0?t:String(n);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${i}`})}}}loadAstNode(e){if(e.node)return e.node;const t=this.langiumDocuments().getDocument(e.documentUri);if(t)return this.astNodeLocator.getAstNode(t.parseResult.value,e.path)}createLinkingError(e,t){const n=ia(e.container).$document;n&&n.state<B.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${n.uri}).`);const i=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${i} named '${e.reference.$refText}'.`,targetDescription:t})}},a(wi,"DefaultLinker"),wi);function pd(r){return typeof r.name=="string"}a(pd,"isNamed");var _i,ey=(_i=class{getName(e){if(pd(e))return e.name}getNameNode(e){return Jo(e.$cstNode,"name")}},a(_i,"DefaultNameProvider"),_i),Li,ty=(Li=class{constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){const t=vu(e),n=e.astNode;if(t&&n){const i=n[t.feature];if(me(i))return i.ref;if(Array.isArray(i)){for(const s of i)if(me(s)&&s.$refNode&&s.$refNode.offset<=e.offset&&s.$refNode.end>=e.end)return s.ref}}if(n){const i=this.nameProvider.getNameNode(n);if(i&&(i===e||Nc(e,i)))return n}}}findDeclarationNode(e){const t=this.findDeclaration(e);if(t?.$cstNode){const n=this.nameProvider.getNameNode(t);return n??t.$cstNode}}findReferences(e,t){const n=[];if(t.includeDeclaration){const s=this.getReferenceToSelf(e);s&&n.push(s)}let i=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return t.documentUri&&(i=i.filter(s=>qe.equals(s.sourceUri,t.documentUri))),n.push(...i),q(n)}getReferenceToSelf(e){const t=this.nameProvider.getNameNode(e);if(t){const n=Ge(e),i=this.nodeLocator.getAstNodePath(e);return{sourceUri:n.uri,sourcePath:i,targetUri:n.uri,targetPath:i,segment:As(t),local:!0}}}},a(Li,"DefaultReferences"),Li),Pi,Ss=(Pi=class{constructor(e){if(this.map=new Map,e)for(const[t,n]of e)this.add(t,n)}get size(){return ra.sum(q(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,t){if(t===void 0)return this.map.delete(e);{const n=this.map.get(e);if(n){const i=n.indexOf(t);if(i>=0)return n.length===1?this.map.delete(e):n.splice(i,1),!0}return!1}}get(e){var t;return(t=this.map.get(e))!==null&&t!==void 0?t:[]}has(e,t){if(t===void 0)return this.map.has(e);{const n=this.map.get(e);return n?n.indexOf(t)>=0:!1}}add(e,t){return this.map.has(e)?this.map.get(e).push(t):this.map.set(e,[t]),this}addAll(e,t){return this.map.has(e)?this.map.get(e).push(...t):this.map.set(e,Array.from(t)),this}forEach(e){this.map.forEach((t,n)=>t.forEach(i=>e(i,n,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return q(this.map.entries()).flatMap(([e,t])=>t.map(n=>[e,n]))}keys(){return q(this.map.keys())}values(){return q(this.map.values()).flat()}entriesGroupedByKey(){return q(this.map.entries())}},a(Pi,"MultiMap"),Pi),bi,wo=(bi=class{get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(const[t,n]of e)this.set(t,n)}clear(){this.map.clear(),this.inverse.clear()}set(e,t){return this.map.set(e,t),this.inverse.set(t,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){const t=this.map.get(e);return t!==void 0?(this.map.delete(e),this.inverse.delete(t),!0):!1}},a(bi,"BiMap"),bi),Oi,ry=(Oi=class{constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,t=G.CancellationToken.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,t)}async computeExportsForNode(e,t,n=va,i=G.CancellationToken.None){const s=[];this.exportNode(e,s,t);for(const o of n(e))await ae(i),this.exportNode(o,s,t);return s}exportNode(e,t,n){const i=this.nameProvider.getName(e);i&&t.push(this.descriptions.createDescription(e,i,n))}async computeLocalScopes(e,t=G.CancellationToken.None){const n=e.parseResult.value,i=new Ss;for(const s of St(n))await ae(t),this.processNode(s,e,i);return i}processNode(e,t,n){const i=e.$container;if(i){const s=this.nameProvider.getName(e);s&&n.add(i,this.descriptions.createDescription(e,s,t))}}},a(Oi,"DefaultScopeComputation"),Oi),Mi,yc=(Mi=class{constructor(e,t,n){var i;this.elements=e,this.outerScope=t,this.caseInsensitive=(i=n?.caseInsensitive)!==null&&i!==void 0?i:!1}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){const t=this.caseInsensitive?this.elements.find(n=>n.name.toLowerCase()===e.toLowerCase()):this.elements.find(n=>n.name===e);if(t)return t;if(this.outerScope)return this.outerScope.getElement(e)}},a(Mi,"StreamScope"),Mi),Di,ny=(Di=class{constructor(e,t,n){var i;this.elements=new Map,this.caseInsensitive=(i=n?.caseInsensitive)!==null&&i!==void 0?i:!1;for(const s of e){const o=this.caseInsensitive?s.name.toLowerCase():s.name;this.elements.set(o,s)}this.outerScope=t}getElement(e){const t=this.caseInsensitive?e.toLowerCase():e,n=this.elements.get(t);if(n)return n;if(this.outerScope)return this.outerScope.getElement(e)}getAllElements(){let e=q(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}},a(Di,"MapScope"),Di),iT={getElement(){},getAllElements(){return Fo}},Fi,pl=(Fi=class{constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}},a(Fi,"DisposableCache"),Fi),Gi,md=(Gi=class extends pl{constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,t){this.throwIfDisposed(),this.cache.set(e,t)}get(e,t){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(t){const n=t();return this.cache.set(e,n),n}else return}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}},a(Gi,"SimpleCache"),Gi),Ui,ml=(Ui=class extends pl{constructor(e){super(),this.cache=new Map,this.converter=e??(t=>t)}has(e,t){return this.throwIfDisposed(),this.cacheForContext(e).has(t)}set(e,t,n){this.throwIfDisposed(),this.cacheForContext(e).set(t,n)}get(e,t,n){this.throwIfDisposed();const i=this.cacheForContext(e);if(i.has(t))return i.get(t);if(n){const s=n();return i.set(t,s),s}else return}delete(e,t){return this.throwIfDisposed(),this.cacheForContext(e).delete(t)}clear(e){if(this.throwIfDisposed(),e){const t=this.converter(e);this.cache.delete(t)}else this.cache.clear()}cacheForContext(e){const t=this.converter(e);let n=this.cache.get(t);return n||(n=new Map,this.cache.set(t,n)),n}},a(Ui,"ContextCache"),Ui),Bi,iy=(Bi=class extends ml{constructor(e,t){super(n=>n.toString()),t?(this.toDispose.push(e.workspace.DocumentBuilder.onDocumentPhase(t,n=>{this.clear(n.uri.toString())})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{for(const s of i)this.clear(s)}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{const s=n.concat(i);for(const o of s)this.clear(o)}))}},a(Bi,"DocumentCache"),Bi),Vi,gd=(Vi=class extends md{constructor(e,t){super(),t?(this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(t,()=>{this.clear()})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{i.length>0&&this.clear()}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}},a(Vi,"WorkspaceCache"),Vi),Wi,sy=(Wi=class{constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new gd(e.shared)}getScope(e){const t=[],n=this.reflection.getReferenceType(e),i=Ge(e.container).precomputedScopes;if(i){let o=e.container;do{const l=i.get(o);l.length>0&&t.push(q(l).filter(c=>this.reflection.isSubtype(c.type,n))),o=o.$container}while(o)}let s=this.getGlobalScope(n,e);for(let o=t.length-1;o>=0;o--)s=this.createScope(t[o],s);return s}createScope(e,t,n){return new yc(q(e),t,n)}createScopeForNodes(e,t,n){const i=q(e).map(s=>{const o=this.nameProvider.getName(s);if(o)return this.descriptions.createDescription(s,o)}).nonNullable();return new yc(i,t,n)}getGlobalScope(e,t){return this.globalScopeCache.get(e,()=>new ny(this.indexManager.allElements(e)))}},a(Wi,"DefaultScopeProvider"),Wi);function yd(r){return typeof r.$comment=="string"}a(yd,"isAstNodeWithComment");function vc(r){return typeof r=="object"&&!!r&&("$ref"in r||"$error"in r)}a(vc,"isIntermediateReference");var Ki,ay=(Ki=class{constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,t){const n=t??{},i=t?.replacer,s=a((l,c)=>this.replacer(l,c,n),"defaultReplacer"),o=i?(l,c)=>i(l,c,s):s;try{return this.currentDocument=Ge(e),JSON.stringify(e,o,t?.space)}finally{this.currentDocument=void 0}}deserialize(e,t){const n=t??{},i=JSON.parse(e);return this.linkNode(i,i,n),i}replacer(e,t,{refText:n,sourceText:i,textRegions:s,comments:o,uriConverter:l}){var c,u,d,f;if(!this.ignoreProperties.has(e))if(me(t)){const h=t.ref,p=n?t.$refText:void 0;if(h){const g=Ge(h);let y="";this.currentDocument&&this.currentDocument!==g&&(l?y=l(g.uri,t):y=g.uri.toString());const E=this.astNodeLocator.getAstNodePath(h);return{$ref:`${y}#${E}`,$refText:p}}else return{$error:(u=(c=t.error)===null||c===void 0?void 0:c.message)!==null&&u!==void 0?u:"Could not resolve reference",$refText:p}}else if(Y(t)){let h;if(s&&(h=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},t)),(!e||t.$document)&&h?.$textRegion&&(h.$textRegion.documentURI=(d=this.currentDocument)===null||d===void 0?void 0:d.uri.toString())),i&&!e&&(h??(h=Object.assign({},t)),h.$sourceText=(f=t.$cstNode)===null||f===void 0?void 0:f.text),o){h??(h=Object.assign({},t));const p=this.commentProvider.getComment(t);p&&(h.$comment=p.replace(/\r/g,""))}return h??t}else return t}addAstNodeRegionWithAssignmentsTo(e){const t=a(n=>({offset:n.offset,end:n.end,length:n.length,range:n.range}),"createDocumentSegment");if(e.$cstNode){const n=e.$textRegion=t(e.$cstNode),i=n.assignments={};return Object.keys(e).filter(s=>!s.startsWith("$")).forEach(s=>{const o=gu(e.$cstNode,s).map(t);o.length!==0&&(i[s]=o)}),e}}linkNode(e,t,n,i,s,o){for(const[c,u]of Object.entries(e))if(Array.isArray(u))for(let d=0;d<u.length;d++){const f=u[d];vc(f)?u[d]=this.reviveReference(e,c,t,f,n):Y(f)&&this.linkNode(f,t,n,e,c,d)}else vc(u)?e[c]=this.reviveReference(e,c,t,u,n):Y(u)&&this.linkNode(u,t,n,e,c);const l=e;l.$container=i,l.$containerProperty=s,l.$containerIndex=o}reviveReference(e,t,n,i,s){let o=i.$refText,l=i.$error;if(i.$ref){const c=this.getRefNode(n,i.$ref,s.uriConverter);if(Y(c))return o||(o=this.nameProvider.getName(c)),{$refText:o??"",ref:c};l=c}if(l){const c={$refText:o??""};return c.error={container:e,property:t,message:l,reference:c},c}else return}getRefNode(e,t,n){try{const i=t.indexOf("#");if(i===0){const c=this.astNodeLocator.getAstNode(e,t.substring(1));return c||"Could not resolve path: "+t}if(i<0){const c=n?n(t):ht.parse(t),u=this.langiumDocuments.getDocument(c);return u?u.parseResult.value:"Could not find document for URI: "+t}const s=n?n(t.substring(0,i)):ht.parse(t.substring(0,i)),o=this.langiumDocuments.getDocument(s);if(!o)return"Could not find document for URI: "+t;if(i===t.length-1)return o.parseResult.value;const l=this.astNodeLocator.getAstNode(o.parseResult.value,t.substring(i+1));return l||"Could not resolve URI: "+t}catch(i){return String(i)}}},a(Ki,"DefaultJsonSerializer"),Ki),ji,oy=(ji=class{get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map,this.fileExtensionMap=new Map,this.textDocuments=e?.workspace.TextDocuments}register(e){const t=e.LanguageMetaData;for(const n of t.fileExtensions)this.fileExtensionMap.has(n)&&console.warn(`The file extension ${n} is used by multiple languages. It is now assigned to '${t.languageId}'.`),this.fileExtensionMap.set(n,e);this.languageIdMap.set(t.languageId,e),this.languageIdMap.size===1?this.singleton=e:this.singleton=void 0}getServices(e){var t,n;if(this.singleton!==void 0)return this.singleton;if(this.languageIdMap.size===0)throw new Error("The service registry is empty. Use `register` to register the services of a language.");const i=(n=(t=this.textDocuments)===null||t===void 0?void 0:t.get(e))===null||n===void 0?void 0:n.languageId;if(i!==void 0){const l=this.languageIdMap.get(i);if(l)return l}const s=qe.extname(e),o=this.fileExtensionMap.get(s);if(!o)throw i?new Error(`The service registry contains no services for the extension '${s}' for language '${i}'.`):new Error(`The service registry contains no services for the extension '${s}'.`);return o}hasServices(e){try{return this.getServices(e),!0}catch{return!1}}get all(){return Array.from(this.languageIdMap.values())}},a(ji,"DefaultServiceRegistry"),ji);function Ot(r){return{code:r}}a(Ot,"diagnosticData");var ha;(function(r){r.all=["fast","slow","built-in"]})(ha||(ha={}));var Hi,ly=(Hi=class{constructor(e){this.entries=new Ss,this.entriesBefore=[],this.entriesAfter=[],this.reflection=e.shared.AstReflection}register(e,t=this,n="fast"){if(n==="built-in")throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(const[i,s]of Object.entries(e)){const o=s;if(Array.isArray(o))for(const l of o){const c={check:this.wrapValidationException(l,t),category:n};this.addEntry(i,c)}else if(typeof o=="function"){const l={check:this.wrapValidationException(o,t),category:n};this.addEntry(i,l)}else Ct()}}wrapValidationException(e,t){return async(n,i,s)=>{await this.handleException(()=>e.call(t,n,i,s),"An error occurred during validation",i,n)}}async handleException(e,t,n,i){try{await e()}catch(s){if(fr(s))throw s;console.error(`${t}:`,s),s instanceof Error&&s.stack&&console.error(s.stack);const o=s instanceof Error?s.message:String(s);n("error",`${t}: ${o}`,{node:i})}}addEntry(e,t){if(e==="AstNode"){this.entries.add("AstNode",t);return}for(const n of this.reflection.getAllSubTypes(e))this.entries.add(n,t)}getChecks(e,t){let n=q(this.entries.get(e)).concat(this.entries.get("AstNode"));return t&&(n=n.filter(i=>t.includes(i.category))),n.map(i=>i.check)}registerBeforeDocument(e,t=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",t))}registerAfterDocument(e,t=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",t))}wrapPreparationException(e,t,n){return async(i,s,o,l)=>{await this.handleException(()=>e.call(n,i,s,o,l),t,s,i)}}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}},a(Hi,"ValidationRegistry"),Hi),zi,cy=(zi=class{constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,t={},n=G.CancellationToken.None){const i=e.parseResult,s=[];if(await ae(n),(!t.categories||t.categories.includes("built-in"))&&(this.processLexingErrors(i,s,t),t.stopAfterLexingErrors&&s.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===we.LexingError})||(this.processParsingErrors(i,s,t),t.stopAfterParsingErrors&&s.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===we.ParsingError}))||(this.processLinkingErrors(e,s,t),t.stopAfterLinkingErrors&&s.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===we.LinkingError}))))return s;try{s.push(...await this.validateAst(i.value,t,n))}catch(o){if(fr(o))throw o;console.error("An error occurred during validation:",o)}return await ae(n),s}processLexingErrors(e,t,n){var i,s,o;const l=[...e.lexerErrors,...(s=(i=e.lexerReport)===null||i===void 0?void 0:i.diagnostics)!==null&&s!==void 0?s:[]];for(const c of l){const u=(o=c.severity)!==null&&o!==void 0?o:"error",d={severity:Qs(u),range:{start:{line:c.line-1,character:c.column-1},end:{line:c.line-1,character:c.column+c.length-1}},message:c.message,data:Td(u),source:this.getSource()};t.push(d)}}processParsingErrors(e,t,n){for(const i of e.parserErrors){let s;if(isNaN(i.token.startOffset)){if("previousToken"in i){const o=i.previousToken;if(isNaN(o.startOffset)){const l={line:0,character:0};s={start:l,end:l}}else{const l={line:o.endLine-1,character:o.endColumn};s={start:l,end:l}}}}else s=na(i.token);if(s){const o={severity:Qs("error"),range:s,message:i.message,data:Ot(we.ParsingError),source:this.getSource()};t.push(o)}}}processLinkingErrors(e,t,n){for(const i of e.references){const s=i.error;if(s){const o={node:s.container,property:s.property,index:s.index,data:{code:we.LinkingError,containerType:s.container.$type,property:s.property,refText:s.reference.$refText}};t.push(this.toDiagnostic("error",s.message,o))}}}async validateAst(e,t,n=G.CancellationToken.None){const i=[],s=a((o,l,c)=>{i.push(this.toDiagnostic(o,l,c))},"acceptor");return await this.validateAstBefore(e,t,s,n),await this.validateAstNodes(e,t,s,n),await this.validateAstAfter(e,t,s,n),i}async validateAstBefore(e,t,n,i=G.CancellationToken.None){var s;const o=this.validationRegistry.checksBefore;for(const l of o)await ae(i),await l(e,n,(s=t.categories)!==null&&s!==void 0?s:[],i)}async validateAstNodes(e,t,n,i=G.CancellationToken.None){await Promise.all(nt(e).map(async s=>{await ae(i);const o=this.validationRegistry.getChecks(s.$type,t.categories);for(const l of o)await l(s,n,i)}))}async validateAstAfter(e,t,n,i=G.CancellationToken.None){var s;const o=this.validationRegistry.checksAfter;for(const l of o)await ae(i),await l(e,n,(s=t.categories)!==null&&s!==void 0?s:[],i)}toDiagnostic(e,t,n){return{message:t,range:vd(n),severity:Qs(e),code:n.code,codeDescription:n.codeDescription,tags:n.tags,relatedInformation:n.relatedInformation,data:n.data,source:this.getSource()}}getSource(){return this.metadata.languageId}},a(zi,"DefaultDocumentValidator"),zi);function vd(r){if(r.range)return r.range;let e;return typeof r.property=="string"?e=Jo(r.node.$cstNode,r.property,r.index):typeof r.keyword=="string"&&(e=yu(r.node.$cstNode,r.keyword,r.index)),e??(e=r.node.$cstNode),e?e.range:{start:{line:0,character:0},end:{line:0,character:0}}}a(vd,"getDiagnosticRange");function Qs(r){switch(r){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+r)}}a(Qs,"toDiagnosticSeverity");function Td(r){switch(r){case"error":return Ot(we.LexingError);case"warning":return Ot(we.LexingWarning);case"info":return Ot(we.LexingInfo);case"hint":return Ot(we.LexingHint);default:throw new Error("Invalid diagnostic severity: "+r)}}a(Td,"toDiagnosticData");var we;(function(r){r.LexingError="lexing-error",r.LexingWarning="lexing-warning",r.LexingInfo="lexing-info",r.LexingHint="lexing-hint",r.ParsingError="parsing-error",r.LinkingError="linking-error"})(we||(we={}));var qi,uy=(qi=class{constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,t,n){const i=n??Ge(e);t??(t=this.nameProvider.getName(e));const s=this.astNodeLocator.getAstNodePath(e);if(!t)throw new Error(`Node at path ${s} has no name.`);let o;const l=a(()=>{var c;return o??(o=As((c=this.nameProvider.getNameNode(e))!==null&&c!==void 0?c:e.$cstNode))},"nameSegmentGetter");return{node:e,name:t,get nameSegment(){return l()},selectionSegment:As(e.$cstNode),type:e.$type,documentUri:i.uri,path:s}}},a(qi,"DefaultAstNodeDescriptionProvider"),qi),Yi,dy=(Yi=class{constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,t=G.CancellationToken.None){const n=[],i=e.parseResult.value;for(const s of nt(i))await ae(t),Ta(s).filter(o=>!rn(o)).forEach(o=>{const l=this.createDescription(o);l&&n.push(l)});return n}createDescription(e){const t=e.reference.$nodeDescription,n=e.reference.$refNode;if(!t||!n)return;const i=Ge(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:t.documentUri,targetPath:t.path,segment:As(n),local:qe.equals(t.documentUri,i)}}},a(Yi,"DefaultReferenceDescriptionProvider"),Yi),Xi,fy=(Xi=class{constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){const t=this.getAstNodePath(e.$container),n=this.getPathSegment(e);return t+this.segmentSeparator+n}return""}getPathSegment({$containerProperty:e,$containerIndex:t}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return t!==void 0?e+this.indexSeparator+t:e}getAstNode(e,t){return t.split(this.segmentSeparator).reduce((i,s)=>{if(!i||s.length===0)return i;const o=s.indexOf(this.indexSeparator);if(o>0){const l=s.substring(0,o),c=parseInt(s.substring(o+1)),u=i[l];return u?.[c]}return i[s]},e)}},a(Xi,"DefaultAstNodeLocator"),Xi),gl={};Po(gl,Th(Eh()));var Ji,hy=(Ji=class{constructor(e){this._ready=new ft,this.settings={},this.workspaceConfig=!1,this.onConfigurationSectionUpdateEmitter=new gl.Emitter,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var t,n;this.workspaceConfig=(n=(t=e.capabilities.workspace)===null||t===void 0?void 0:t.configuration)!==null&&n!==void 0?n:!1}async initialized(e){if(this.workspaceConfig){if(e.register){const t=this.serviceRegistry.all;e.register({section:t.map(n=>this.toSectionName(n.LanguageMetaData.languageId))})}if(e.fetchConfiguration){const t=this.serviceRegistry.all.map(i=>({section:this.toSectionName(i.LanguageMetaData.languageId)})),n=await e.fetchConfiguration(t);t.forEach((i,s)=>{this.updateSectionConfiguration(i.section,n[s])})}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(t=>{const n=e.settings[t];this.updateSectionConfiguration(t,n),this.onConfigurationSectionUpdateEmitter.fire({section:t,configuration:n})})}updateSectionConfiguration(e,t){this.settings[e]=t}async getConfiguration(e,t){await this.ready;const n=this.toSectionName(e);if(this.settings[n])return this.settings[n][t]}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}},a(Ji,"DefaultConfigurationProvider"),Ji),Ft;(function(r){function e(t){return{dispose:a(async()=>await t(),"dispose")}}a(e,"create"),r.create=e})(Ft||(Ft={}));var Qi,py=(Qi=class{constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new Ss,this.documentPhaseListeners=new Ss,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=B.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.textDocuments=e.workspace.TextDocuments,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,t={},n=G.CancellationToken.None){var i,s;for(const o of e){const l=o.uri.toString();if(o.state===B.Validated){if(typeof t.validation=="boolean"&&t.validation)o.state=B.IndexedReferences,o.diagnostics=void 0,this.buildState.delete(l);else if(typeof t.validation=="object"){const c=this.buildState.get(l),u=(i=c?.result)===null||i===void 0?void 0:i.validationChecks;if(u){const f=((s=t.validation.categories)!==null&&s!==void 0?s:ha.all).filter(h=>!u.includes(h));f.length>0&&(this.buildState.set(l,{completed:!1,options:{validation:Object.assign(Object.assign({},t.validation),{categories:f})},result:c.result}),o.state=B.IndexedReferences)}}}else this.buildState.delete(l)}this.currentState=B.Changed,await this.emitUpdate(e.map(o=>o.uri),[]),await this.buildDocuments(e,t,n)}async update(e,t,n=G.CancellationToken.None){this.currentState=B.Changed;for(const o of t)this.langiumDocuments.deleteDocument(o),this.buildState.delete(o.toString()),this.indexManager.remove(o);for(const o of e){if(!this.langiumDocuments.invalidateDocument(o)){const c=this.langiumDocumentFactory.fromModel({$type:"INVALID"},o);c.state=B.Changed,this.langiumDocuments.addDocument(c)}this.buildState.delete(o.toString())}const i=q(e).concat(t).map(o=>o.toString()).toSet();this.langiumDocuments.all.filter(o=>!i.has(o.uri.toString())&&this.shouldRelink(o,i)).forEach(o=>{this.serviceRegistry.getServices(o.uri).references.Linker.unlink(o),o.state=Math.min(o.state,B.ComputedScopes),o.diagnostics=void 0}),await this.emitUpdate(e,t),await ae(n);const s=this.sortDocuments(this.langiumDocuments.all.filter(o=>{var l;return o.state<B.Linked||!(!((l=this.buildState.get(o.uri.toString()))===null||l===void 0)&&l.completed)}).toArray());await this.buildDocuments(s,this.updateBuildOptions,n)}async emitUpdate(e,t){await Promise.all(this.updateListeners.map(n=>n(e,t)))}sortDocuments(e){let t=0,n=e.length-1;for(;t<n;){for(;t<e.length&&this.hasTextDocument(e[t]);)t++;for(;n>=0&&!this.hasTextDocument(e[n]);)n--;t<n&&([e[t],e[n]]=[e[n],e[t]])}return e}hasTextDocument(e){var t;return!!(!((t=this.textDocuments)===null||t===void 0)&&t.get(e.uri))}shouldRelink(e,t){return e.references.some(n=>n.error!==void 0)?!0:this.indexManager.isAffected(e,t)}onUpdate(e){return this.updateListeners.push(e),Ft.create(()=>{const t=this.updateListeners.indexOf(e);t>=0&&this.updateListeners.splice(t,1)})}async buildDocuments(e,t,n){this.prepareBuild(e,t),await this.runCancelable(e,B.Parsed,n,s=>this.langiumDocumentFactory.update(s,n)),await this.runCancelable(e,B.IndexedContent,n,s=>this.indexManager.updateContent(s,n)),await this.runCancelable(e,B.ComputedScopes,n,async s=>{const o=this.serviceRegistry.getServices(s.uri).references.ScopeComputation;s.precomputedScopes=await o.computeLocalScopes(s,n)}),await this.runCancelable(e,B.Linked,n,s=>this.serviceRegistry.getServices(s.uri).references.Linker.link(s,n)),await this.runCancelable(e,B.IndexedReferences,n,s=>this.indexManager.updateReferences(s,n));const i=e.filter(s=>this.shouldValidate(s));await this.runCancelable(i,B.Validated,n,s=>this.validate(s,n));for(const s of e){const o=this.buildState.get(s.uri.toString());o&&(o.completed=!0)}}prepareBuild(e,t){for(const n of e){const i=n.uri.toString(),s=this.buildState.get(i);(!s||s.completed)&&this.buildState.set(i,{completed:!1,options:t,result:s?.result})}}async runCancelable(e,t,n,i){const s=e.filter(l=>l.state<t);for(const l of s)await ae(n),await i(l),l.state=t,await this.notifyDocumentPhase(l,t,n);const o=e.filter(l=>l.state===t);await this.notifyBuildPhase(o,t,n),this.currentState=t}onBuildPhase(e,t){return this.buildPhaseListeners.add(e,t),Ft.create(()=>{this.buildPhaseListeners.delete(e,t)})}onDocumentPhase(e,t){return this.documentPhaseListeners.add(e,t),Ft.create(()=>{this.documentPhaseListeners.delete(e,t)})}waitUntil(e,t,n){let i;if(t&&"path"in t?i=t:n=t,n??(n=G.CancellationToken.None),i){const s=this.langiumDocuments.getDocument(i);if(s&&s.state>e)return Promise.resolve(i)}return this.currentState>=e?Promise.resolve(void 0):n.isCancellationRequested?Promise.reject(kt):new Promise((s,o)=>{const l=this.onBuildPhase(e,()=>{if(l.dispose(),c.dispose(),i){const u=this.langiumDocuments.getDocument(i);s(u?.uri)}else s(void 0)}),c=n.onCancellationRequested(()=>{l.dispose(),c.dispose(),o(kt)})})}async notifyDocumentPhase(e,t,n){const s=this.documentPhaseListeners.get(t).slice();for(const o of s)try{await o(e,n)}catch(l){if(!fr(l))throw l}}async notifyBuildPhase(e,t,n){if(e.length===0)return;const s=this.buildPhaseListeners.get(t).slice();for(const o of s)await ae(n),await o(e,n)}shouldValidate(e){return!!this.getBuildOptions(e).validation}async validate(e,t){var n,i;const s=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,o=this.getBuildOptions(e).validation,l=typeof o=="object"?o:void 0,c=await s.validateDocument(e,l,t);e.diagnostics?e.diagnostics.push(...c):e.diagnostics=c;const u=this.buildState.get(e.uri.toString());if(u){(n=u.result)!==null&&n!==void 0||(u.result={});const d=(i=l?.categories)!==null&&i!==void 0?i:ha.all;u.result.validationChecks?u.result.validationChecks.push(...d):u.result.validationChecks=[...d]}}getBuildOptions(e){var t,n;return(n=(t=this.buildState.get(e.uri.toString()))===null||t===void 0?void 0:t.options)!==null&&n!==void 0?n:{}}},a(Qi,"DefaultDocumentBuilder"),Qi),Zi,my=(Zi=class{constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new ml,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,t){const n=Ge(e).uri,i=[];return this.referenceIndex.forEach(s=>{s.forEach(o=>{qe.equals(o.targetUri,n)&&o.targetPath===t&&i.push(o)})}),q(i)}allElements(e,t){let n=q(this.symbolIndex.keys());return t&&(n=n.filter(i=>!t||t.has(i))),n.map(i=>this.getFileDescriptions(i,e)).flat()}getFileDescriptions(e,t){var n;return t?this.symbolByTypeIndex.get(e,t,()=>{var s;return((s=this.symbolIndex.get(e))!==null&&s!==void 0?s:[]).filter(l=>this.astReflection.isSubtype(l.type,t))}):(n=this.symbolIndex.get(e))!==null&&n!==void 0?n:[]}remove(e){const t=e.toString();this.symbolIndex.delete(t),this.symbolByTypeIndex.clear(t),this.referenceIndex.delete(t)}async updateContent(e,t=G.CancellationToken.None){const i=await this.serviceRegistry.getServices(e.uri).references.ScopeComputation.computeExports(e,t),s=e.uri.toString();this.symbolIndex.set(s,i),this.symbolByTypeIndex.clear(s)}async updateReferences(e,t=G.CancellationToken.None){const i=await this.serviceRegistry.getServices(e.uri).workspace.ReferenceDescriptionProvider.createDescriptions(e,t);this.referenceIndex.set(e.uri.toString(),i)}isAffected(e,t){const n=this.referenceIndex.get(e.uri.toString());return n?n.some(i=>!i.local&&t.has(i.targetUri.toString())):!1}},a(Zi,"DefaultIndexManager"),Zi),es,gy=(es=class{constructor(e){this.initialBuildOptions={},this._ready=new ft,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var t;this.folders=(t=e.workspaceFolders)!==null&&t!==void 0?t:void 0}initialized(e){return this.mutex.write(t=>{var n;return this.initializeWorkspace((n=this.folders)!==null&&n!==void 0?n:[],t)})}async initializeWorkspace(e,t=G.CancellationToken.None){const n=await this.performStartup(e);await ae(t),await this.documentBuilder.build(n,this.initialBuildOptions,t)}async performStartup(e){const t=this.serviceRegistry.all.flatMap(s=>s.LanguageMetaData.fileExtensions),n=[],i=a(s=>{n.push(s),this.langiumDocuments.hasDocument(s.uri)||this.langiumDocuments.addDocument(s)},"collector");return await this.loadAdditionalDocuments(e,i),await Promise.all(e.map(s=>[s,this.getRootFolder(s)]).map(async s=>this.traverseFolder(...s,t,i))),this._ready.resolve(),n}loadAdditionalDocuments(e,t){return Promise.resolve()}getRootFolder(e){return ht.parse(e.uri)}async traverseFolder(e,t,n,i){const s=await this.fileSystemProvider.readDirectory(t);await Promise.all(s.map(async o=>{if(this.includeEntry(e,o,n)){if(o.isDirectory)await this.traverseFolder(e,o.uri,n,i);else if(o.isFile){const l=await this.langiumDocuments.getOrCreateDocument(o.uri);i(l)}}}))}includeEntry(e,t,n){const i=qe.basename(t.uri);if(i.startsWith("."))return!1;if(t.isDirectory)return i!=="node_modules"&&i!=="out";if(t.isFile){const s=qe.extname(t.uri);return n.includes(s)}return!1}},a(es,"DefaultWorkspaceManager"),es),ts,yy=(ts=class{buildUnexpectedCharactersMessage(e,t,n,i,s){return Wl.buildUnexpectedCharactersMessage(e,t,n,i,s)}buildUnableToPopLexerModeMessage(e){return Wl.buildUnableToPopLexerModeMessage(e)}},a(ts,"DefaultLexerErrorMessageProvider"),ts),Rd={mode:"full"},rs,Ad=(rs=class{constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider,this.tokenBuilder=e.parser.TokenBuilder;const t=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(t);const n=_o(t)?Object.values(t):t,i=e.LanguageMetaData.mode==="production";this.chevrotainLexer=new le(n,{positionTracking:"full",skipValidations:i,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,t=Rd){var n,i,s;const o=this.chevrotainLexer.tokenize(e);return{tokens:o.tokens,errors:o.errors,hidden:(n=o.groups.hidden)!==null&&n!==void 0?n:[],report:(s=(i=this.tokenBuilder).flushLexingReport)===null||s===void 0?void 0:s.call(i,e)}}toTokenTypeDictionary(e){if(_o(e))return e;const t=vl(e)?Object.values(e.modes).flat():e,n={};return t.forEach(i=>n[i.name]=i),n}},a(rs,"DefaultLexer"),rs);function yl(r){return Array.isArray(r)&&(r.length===0||"name"in r[0])}a(yl,"isTokenTypeArray");function vl(r){return r&&"modes"in r&&"defaultMode"in r}a(vl,"isIMultiModeLexerDefinition");function _o(r){return!yl(r)&&!vl(r)}a(_o,"isTokenTypeDictionary");function Ed(r,e,t){let n,i;typeof r=="string"?(i=e,n=t):(i=r.range.start,n=e),i||(i=D.create(0,0));const s=Cd(r),o=Tl(n),l=vy({lines:s,position:i,options:o});return Ay({index:0,tokens:l,position:i})}a(Ed,"parseJSDoc");function kd(r,e){const t=Tl(e),n=Cd(r);if(n.length===0)return!1;const i=n[0],s=n[n.length-1],o=t.start,l=t.end;return!!o?.exec(i)&&!!l?.exec(s)}a(kd,"isJSDoc");function Cd(r){let e="";return typeof r=="string"?e=r:e=r.text,e.split(Xh)}a(Cd,"getLines");var lh=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,sT=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function vy(r){var e,t,n;const i=[];let s=r.position.line,o=r.position.character;for(let l=0;l<r.lines.length;l++){const c=l===0,u=l===r.lines.length-1;let d=r.lines[l],f=0;if(c&&r.options.start){const p=(e=r.options.start)===null||e===void 0?void 0:e.exec(d);p&&(f=p.index+p[0].length)}else{const p=(t=r.options.line)===null||t===void 0?void 0:t.exec(d);p&&(f=p.index+p[0].length)}if(u){const p=(n=r.options.end)===null||n===void 0?void 0:n.exec(d);p&&(d=d.substring(0,p.index))}if(d=d.substring(0,Ry(d)),Lo(d,f)>=d.length){if(i.length>0){const p=D.create(s,o);i.push({type:"break",content:"",range:O.create(p,p)})}}else{lh.lastIndex=f;const p=lh.exec(d);if(p){const g=p[0],y=p[1],E=D.create(s,o+f),T=D.create(s,o+f+g.length);i.push({type:"tag",content:y,range:O.create(E,T)}),f+=g.length,f=Lo(d,f)}if(f<d.length){const g=d.substring(f),y=Array.from(g.matchAll(sT));i.push(...Ty(y,g,s,o+f))}}s++,o=0}return i.length>0&&i[i.length-1].type==="break"?i.slice(0,-1):i}a(vy,"tokenize");function Ty(r,e,t,n){const i=[];if(r.length===0){const s=D.create(t,n),o=D.create(t,n+e.length);i.push({type:"text",content:e,range:O.create(s,o)})}else{let s=0;for(const l of r){const c=l.index,u=e.substring(s,c);u.length>0&&i.push({type:"text",content:e.substring(s,c),range:O.create(D.create(t,s+n),D.create(t,c+n))});let d=u.length+1;const f=l[1];if(i.push({type:"inline-tag",content:f,range:O.create(D.create(t,s+d+n),D.create(t,s+d+f.length+n))}),d+=f.length,l.length===4){d+=l[2].length;const h=l[3];i.push({type:"text",content:h,range:O.create(D.create(t,s+d+n),D.create(t,s+d+h.length+n))})}else i.push({type:"text",content:"",range:O.create(D.create(t,s+d+n),D.create(t,s+d+n))});s=c+l[0].length}const o=e.substring(s);o.length>0&&i.push({type:"text",content:o,range:O.create(D.create(t,s+n),D.create(t,s+n+o.length))})}return i}a(Ty,"buildInlineTokens");var aT=/\S/,oT=/\s*$/;function Lo(r,e){const t=r.substring(e).match(aT);return t?e+t.index:r.length}a(Lo,"skipWhitespace");function Ry(r){const e=r.match(oT);if(e&&typeof e.index=="number")return e.index}a(Ry,"lastCharacter");function Ay(r){var e,t,n,i;const s=D.create(r.position.line,r.position.character);if(r.tokens.length===0)return new ch([],O.create(s,s));const o=[];for(;r.index<r.tokens.length;){const u=Ey(r,o[o.length-1]);u&&o.push(u)}const l=(t=(e=o[0])===null||e===void 0?void 0:e.range.start)!==null&&t!==void 0?t:s,c=(i=(n=o[o.length-1])===null||n===void 0?void 0:n.range.end)!==null&&i!==void 0?i:s;return new ch(o,O.create(l,c))}a(Ay,"parseJSDocComment");function Ey(r,e){const t=r.tokens[r.index];if(t.type==="tag")return Id(r,!1);if(t.type==="text"||t.type==="inline-tag")return Sd(r);ky(t,e),r.index++}a(Ey,"parseJSDocElement");function ky(r,e){if(e){const t=new xy("",r.range);"inlines"in e?e.inlines.push(t):e.content.inlines.push(t)}}a(ky,"appendEmptyLine");function Sd(r){let e=r.tokens[r.index];const t=e;let n=e;const i=[];for(;e&&e.type!=="break"&&e.type!=="tag";)i.push(Cy(r)),n=e,e=r.tokens[r.index];return new Tc(i,O.create(t.range.start,n.range.end))}a(Sd,"parseJSDocText");function Cy(r){return r.tokens[r.index].type==="inline-tag"?Id(r,!0):xd(r)}a(Cy,"parseJSDocInline");function Id(r,e){const t=r.tokens[r.index++],n=t.content.substring(1),i=r.tokens[r.index];if(i?.type==="text")if(e){const s=xd(r);return new Sl(n,new Tc([s],s.range),e,O.create(t.range.start,s.range.end))}else{const s=Sd(r);return new Sl(n,s,e,O.create(t.range.start,s.range.end))}else{const s=t.range;return new Sl(n,new Tc([],s),e,s)}}a(Id,"parseJSDocTag");function xd(r){const e=r.tokens[r.index++];return new xy(e.content,e.range)}a(xd,"parseJSDocLine");function Tl(r){if(!r)return Tl({start:"/**",end:"*/",line:"*"});const{start:e,end:t,line:n}=r;return{start:Ya(e,!0),end:Ya(t,!1),line:Ya(n,!0)}}a(Tl,"normalizeOptions");function Ya(r,e){if(typeof r=="string"||typeof r=="object"){const t=typeof r=="string"?xs(r):r.source;return e?new RegExp(`^\\s*${t}`):new RegExp(`\\s*${t}\\s*$`)}else return r}a(Ya,"normalizeOption");var ns,ch=(ns=class{constructor(e,t){this.elements=e,this.range=t}getTag(e){return this.getAllTags().find(t=>t.name===e)}getTags(e){return this.getAllTags().filter(t=>t.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(const t of this.elements)if(e.length===0)e=t.toString();else{const n=t.toString();e+=Rc(e)+n}return e.trim()}toMarkdown(e){let t="";for(const n of this.elements)if(t.length===0)t=n.toMarkdown(e);else{const i=n.toMarkdown(e);t+=Rc(t)+i}return t.trim()}},a(ns,"JSDocCommentImpl"),ns),is,Sl=(is=class{constructor(e,t,n,i){this.name=e,this.content=t,this.inline=n,this.range=i}toString(){let e=`@${this.name}`;const t=this.content.toString();return this.content.inlines.length===1?e=`${e} ${t}`:this.content.inlines.length>1&&(e=`${e}
${t}`),this.inline?`{${e}}`:e}toMarkdown(e){var t,n;return(n=(t=e?.renderTag)===null||t===void 0?void 0:t.call(e,this))!==null&&n!==void 0?n:this.toMarkdownDefault(e)}toMarkdownDefault(e){const t=this.content.toMarkdown(e);if(this.inline){const s=Sy(this.name,t,e??{});if(typeof s=="string")return s}let n="";e?.tag==="italic"||e?.tag===void 0?n="*":e?.tag==="bold"?n="**":e?.tag==="bold-italic"&&(n="***");let i=`${n}@${this.name}${n}`;return this.content.inlines.length===1?i=`${i} — ${t}`:this.content.inlines.length>1&&(i=`${i}
${t}`),this.inline?`{${i}}`:i}},a(is,"JSDocTagImpl"),is);function Sy(r,e,t){var n,i;if(r==="linkplain"||r==="linkcode"||r==="link"){const s=e.indexOf(" ");let o=e;if(s>0){const c=Lo(e,s);o=e.substring(c),e=e.substring(0,s)}return(r==="linkcode"||r==="link"&&t.link==="code")&&(o=`\`${o}\``),(i=(n=t.renderLink)===null||n===void 0?void 0:n.call(t,e,o))!==null&&i!==void 0?i:Iy(e,o)}}a(Sy,"renderInlineTag");function Iy(r,e){try{return ht.parse(r,!0),`[${e}](${r})`}catch{return r}}a(Iy,"renderLinkDefault");var ss,Tc=(ss=class{constructor(e,t){this.inlines=e,this.range=t}toString(){let e="";for(let t=0;t<this.inlines.length;t++){const n=this.inlines[t],i=this.inlines[t+1];e+=n.toString(),i&&i.range.start.line>n.range.start.line&&(e+=`
`)}return e}toMarkdown(e){let t="";for(let n=0;n<this.inlines.length;n++){const i=this.inlines[n],s=this.inlines[n+1];t+=i.toMarkdown(e),s&&s.range.start.line>i.range.start.line&&(t+=`
`)}return t}},a(ss,"JSDocTextImpl"),ss),as,xy=(as=class{constructor(e,t){this.text=e,this.range=t}toString(){return this.text}toMarkdown(){return this.text}},a(as,"JSDocLineImpl"),as);function Rc(r){return r.endsWith(`
`)?`
`:`

`}a(Rc,"fillNewlines");var os,$y=(os=class{constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){const t=this.commentProvider.getComment(e);if(t&&kd(t))return Ed(t).toMarkdown({renderLink:a((i,s)=>this.documentationLinkRenderer(e,i,s),"renderLink"),renderTag:a(i=>this.documentationTagRenderer(e,i),"renderTag")})}documentationLinkRenderer(e,t,n){var i;const s=(i=this.findNameInPrecomputedScopes(e,t))!==null&&i!==void 0?i:this.findNameInGlobalScope(e,t);if(s&&s.nameSegment){const o=s.nameSegment.range.start.line+1,l=s.nameSegment.range.start.character+1,c=s.documentUri.with({fragment:`L${o},${l}`});return`[${n}](${c.toString()})`}else return}documentationTagRenderer(e,t){}findNameInPrecomputedScopes(e,t){const i=Ge(e).precomputedScopes;if(!i)return;let s=e;do{const l=i.get(s).find(c=>c.name===t);if(l)return l;s=s.$container}while(s)}findNameInGlobalScope(e,t){return this.indexManager.allElements().find(i=>i.name===t)}},a(os,"JSDocDocumentationProvider"),os),ls,Ny=(ls=class{constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var t;return yd(e)?e.$comment:(t=Pc(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||t===void 0?void 0:t.text}},a(ls,"DefaultCommentProvider"),ls),cs,wy=(cs=class{constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,t){return Promise.resolve(this.syncParser.parse(e))}},a(cs,"DefaultAsyncParser"),cs),us,lT=(us=class{constructor(e){this.threadCount=8,this.terminationDelay=200,this.workerPool=[],this.queue=[],this.hydrator=e.serializer.Hydrator}initializeWorkers(){for(;this.workerPool.length<this.threadCount;){const e=this.createWorker();e.onReady(()=>{if(this.queue.length>0){const t=this.queue.shift();t&&(e.lock(),t.resolve(e))}}),this.workerPool.push(e)}}async parse(e,t){const n=await this.acquireParserWorker(t),i=new ft;let s;const o=t.onCancellationRequested(()=>{s=setTimeout(()=>{this.terminateWorker(n)},this.terminationDelay)});return n.parse(e).then(l=>{const c=this.hydrator.hydrate(l);i.resolve(c)}).catch(l=>{i.reject(l)}).finally(()=>{o.dispose(),clearTimeout(s)}),i.promise}terminateWorker(e){e.terminate();const t=this.workerPool.indexOf(e);t>=0&&this.workerPool.splice(t,1)}async acquireParserWorker(e){this.initializeWorkers();for(const n of this.workerPool)if(n.ready)return n.lock(),n;const t=new ft;return e.onCancellationRequested(()=>{const n=this.queue.indexOf(t);n>=0&&this.queue.splice(n,1),t.reject(kt)}),this.queue.push(t),t.promise}},a(us,"AbstractThreadedAsyncParser"),us),ds,cT=(ds=class{get ready(){return this._ready}get onReady(){return this.onReadyEmitter.event}constructor(e,t,n,i){this.onReadyEmitter=new gl.Emitter,this.deferred=new ft,this._ready=!0,this._parsing=!1,this.sendMessage=e,this._terminate=i,t(s=>{const o=s;this.deferred.resolve(o),this.unlock()}),n(s=>{this.deferred.reject(s),this.unlock()})}terminate(){this.deferred.reject(kt),this._terminate()}lock(){this._ready=!1}unlock(){this._parsing=!1,this._ready=!0,this.onReadyEmitter.fire()}parse(e){if(this._parsing)throw new Error("Parser worker is busy");return this._parsing=!0,this.deferred=new ft,this.sendMessage(e),this.deferred.promise}},a(ds,"ParserWorker"),ds),fs,_y=(fs=class{constructor(){this.previousTokenSource=new G.CancellationTokenSource,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();const t=hl();return this.previousTokenSource=t,this.enqueue(this.writeQueue,e,t.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,t,n=G.CancellationToken.None){const i=new ft,s={action:t,deferred:i,cancellationToken:n};return e.push(s),this.performNextOperation(),i.promise}async performNextOperation(){if(!this.done)return;const e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else if(this.readQueue.length>0)e.push(...this.readQueue.splice(0,this.readQueue.length));else return;this.done=!1,await Promise.all(e.map(async({action:t,deferred:n,cancellationToken:i})=>{try{const s=await Promise.resolve().then(()=>t(i));n.resolve(s)}catch(s){fr(s)?n.resolve(void 0):n.reject(s)}})),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}},a(fs,"DefaultWorkspaceLock"),fs),hs,Ly=(hs=class{constructor(e){this.grammarElementIdMap=new wo,this.tokenTypeIdMap=new wo,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):void 0,parserErrors:e.parserErrors.map(t=>Object.assign(Object.assign({},t),{message:t.message})),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){const t=new Map,n=new Map;for(const i of nt(e))t.set(i,{});if(e.$cstNode)for(const i of Rs(e.$cstNode))n.set(i,{});return{astNodes:t,cstNodes:n}}dehydrateAstNode(e,t){const n=t.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,e.$cstNode!==void 0&&(n.$cstNode=this.dehydrateCstNode(e.$cstNode,t));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const o=[];n[i]=o;for(const l of s)Y(l)?o.push(this.dehydrateAstNode(l,t)):me(l)?o.push(this.dehydrateReference(l,t)):o.push(l)}else Y(s)?n[i]=this.dehydrateAstNode(s,t):me(s)?n[i]=this.dehydrateReference(s,t):s!==void 0&&(n[i]=s);return n}dehydrateReference(e,t){const n={};return n.$refText=e.$refText,e.$refNode&&(n.$refNode=t.cstNodes.get(e.$refNode)),n}dehydrateCstNode(e,t){const n=t.cstNodes.get(e);return Do(e)?n.fullText=e.fullText:n.grammarSource=this.getGrammarElementId(e.grammarSource),n.hidden=e.hidden,n.astNode=t.astNodes.get(e.astNode),at(e)?n.content=e.content.map(i=>this.dehydrateCstNode(i,t)):lr(e)&&(n.tokenType=e.tokenType.name,n.offset=e.offset,n.length=e.length,n.startLine=e.range.start.line,n.startColumn=e.range.start.character,n.endLine=e.range.end.line,n.endColumn=e.range.end.character),n}hydrate(e){const t=e.value,n=this.createHydrationContext(t);return"$cstNode"in t&&this.hydrateCstNode(t.$cstNode,n),{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(t,n)}}createHydrationContext(e){const t=new Map,n=new Map;for(const s of nt(e))t.set(s,{});let i;if(e.$cstNode)for(const s of Rs(e.$cstNode)){let o;"fullText"in s?(o=new td(s.fullText),i=o):"content"in s?o=new cl:"tokenType"in s&&(o=this.hydrateCstLeafNode(s)),o&&(n.set(s,o),o.root=i)}return{astNodes:t,cstNodes:n}}hydrateAstNode(e,t){const n=t.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,e.$cstNode&&(n.$cstNode=t.cstNodes.get(e.$cstNode));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const o=[];n[i]=o;for(const l of s)Y(l)?o.push(this.setParent(this.hydrateAstNode(l,t),n)):me(l)?o.push(this.hydrateReference(l,n,i,t)):o.push(l)}else Y(s)?n[i]=this.setParent(this.hydrateAstNode(s,t),n):me(s)?n[i]=this.hydrateReference(s,n,i,t):s!==void 0&&(n[i]=s);return n}setParent(e,t){return e.$container=t,e}hydrateReference(e,t,n,i){return this.linker.buildReference(t,n,i.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,t,n=0){const i=t.cstNodes.get(e);if(typeof e.grammarSource=="number"&&(i.grammarSource=this.getGrammarElement(e.grammarSource)),i.astNode=t.astNodes.get(e.astNode),at(i))for(const s of e.content){const o=this.hydrateCstNode(s,t,n++);i.content.push(o)}return i}hydrateCstLeafNode(e){const t=this.getTokenType(e.tokenType),n=e.offset,i=e.length,s=e.startLine,o=e.startColumn,l=e.endLine,c=e.endColumn,u=e.hidden;return new So(n,i,{start:{line:s,character:o},end:{line:l,character:c}},t,u)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(e)return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.getKey(e)}createGrammarElementIdMap(){let e=0;for(const t of nt(this.grammar))Bo(t)&&this.grammarElementIdMap.set(t,e++)}},a(hs,"DefaultHydrator"),hs);function Je(r){return{documentation:{CommentProvider:a(e=>new Ny(e),"CommentProvider"),DocumentationProvider:a(e=>new $y(e),"DocumentationProvider")},parser:{AsyncParser:a(e=>new wy(e),"AsyncParser"),GrammarConfig:a(e=>Su(e),"GrammarConfig"),LangiumParser:a(e=>ld(e),"LangiumParser"),CompletionParser:a(e=>od(e),"CompletionParser"),ValueConverter:a(()=>new ud,"ValueConverter"),TokenBuilder:a(()=>new dl,"TokenBuilder"),Lexer:a(e=>new Ad(e),"Lexer"),ParserErrorMessageProvider:a(()=>new nd,"ParserErrorMessageProvider"),LexerErrorMessageProvider:a(()=>new yy,"LexerErrorMessageProvider")},workspace:{AstNodeLocator:a(()=>new fy,"AstNodeLocator"),AstNodeDescriptionProvider:a(e=>new uy(e),"AstNodeDescriptionProvider"),ReferenceDescriptionProvider:a(e=>new dy(e),"ReferenceDescriptionProvider")},references:{Linker:a(e=>new Zg(e),"Linker"),NameProvider:a(()=>new ey,"NameProvider"),ScopeProvider:a(e=>new sy(e),"ScopeProvider"),ScopeComputation:a(e=>new ry(e),"ScopeComputation"),References:a(e=>new ty(e),"References")},serializer:{Hydrator:a(e=>new Ly(e),"Hydrator"),JsonSerializer:a(e=>new ay(e),"JsonSerializer")},validation:{DocumentValidator:a(e=>new cy(e),"DocumentValidator"),ValidationRegistry:a(e=>new ly(e),"ValidationRegistry")},shared:a(()=>r.shared,"shared")}}a(Je,"createDefaultCoreModule");function Qe(r){return{ServiceRegistry:a(e=>new oy(e),"ServiceRegistry"),workspace:{LangiumDocuments:a(e=>new Qg(e),"LangiumDocuments"),LangiumDocumentFactory:a(e=>new Jg(e),"LangiumDocumentFactory"),DocumentBuilder:a(e=>new py(e),"DocumentBuilder"),IndexManager:a(e=>new my(e),"IndexManager"),WorkspaceManager:a(e=>new gy(e),"WorkspaceManager"),FileSystemProvider:a(e=>r.fileSystemProvider(e),"FileSystemProvider"),WorkspaceLock:a(()=>new _y,"WorkspaceLock"),ConfigurationProvider:a(e=>new hy(e),"ConfigurationProvider")}}}a(Qe,"createDefaultSharedCoreModule");var Ac;(function(r){r.merge=(e,t)=>pa(pa({},e),t)})(Ac||(Ac={}));function Z(r,e,t,n,i,s,o,l,c){const u=[r,e,t,n,i,s,o,l,c].reduce(pa,{});return Nd(u)}a(Z,"inject");var Py=Symbol("isProxy");function $d(r){if(r&&r[Py])for(const e of Object.values(r))$d(e);return r}a($d,"eagerLoad");function Nd(r,e){const t=new Proxy({},{deleteProperty:a(()=>!1,"deleteProperty"),set:a(()=>{throw new Error("Cannot set property on injected service container")},"set"),get:a((n,i)=>i===Py?!0:Ec(n,i,r,e||t),"get"),getOwnPropertyDescriptor:a((n,i)=>(Ec(n,i,r,e||t),Object.getOwnPropertyDescriptor(n,i)),"getOwnPropertyDescriptor"),has:a((n,i)=>i in r,"has"),ownKeys:a(()=>[...Object.getOwnPropertyNames(r)],"ownKeys")});return t}a(Nd,"_inject");var uh=Symbol();function Ec(r,e,t,n){if(e in r){if(r[e]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:r[e]});if(r[e]===uh)throw new Error('Cycle detected. Please make "'+String(e)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');return r[e]}else if(e in t){const i=t[e];r[e]=uh;try{r[e]=typeof i=="function"?i(n):Nd(i,n)}catch(s){throw r[e]=s instanceof Error?s:void 0,s}return r[e]}else return}a(Ec,"_resolve");function pa(r,e){if(e){for(const[t,n]of Object.entries(e))if(n!==void 0){const i=r[t];i!==null&&n!==null&&typeof i=="object"&&typeof n=="object"?r[t]=pa(i,n):r[t]=n}}return r}a(pa,"_merge");var kc={indentTokenName:"INDENT",dedentTokenName:"DEDENT",whitespaceTokenName:"WS",ignoreIndentationDelimiters:[]},Mt;(function(r){r.REGULAR="indentation-sensitive",r.IGNORE_INDENTATION="ignore-indentation"})(Mt||(Mt={}));var ps,by=(ps=class extends dl{constructor(e=kc){super(),this.indentationStack=[0],this.whitespaceRegExp=/[ \t]+/y,this.options=Object.assign(Object.assign({},kc),e),this.indentTokenType=nn({name:this.options.indentTokenName,pattern:this.indentMatcher.bind(this),line_breaks:!1}),this.dedentTokenType=nn({name:this.options.dedentTokenName,pattern:this.dedentMatcher.bind(this),line_breaks:!1})}buildTokens(e,t){const n=super.buildTokens(e,t);if(!yl(n))throw new Error("Invalid tokens built by default builder");const{indentTokenName:i,dedentTokenName:s,whitespaceTokenName:o,ignoreIndentationDelimiters:l}=this.options;let c,u,d;const f=[];for(const h of n){for(const[p,g]of l)h.name===p?h.PUSH_MODE=Mt.IGNORE_INDENTATION:h.name===g&&(h.POP_MODE=!0);h.name===s?c=h:h.name===i?u=h:h.name===o?d=h:f.push(h)}if(!c||!u||!d)throw new Error("Some indentation/whitespace tokens not found!");return l.length>0?{modes:{[Mt.REGULAR]:[c,u,...f,d],[Mt.IGNORE_INDENTATION]:[...f,d]},defaultMode:Mt.REGULAR}:[c,u,d,...f]}flushLexingReport(e){const t=super.flushLexingReport(e);return Object.assign(Object.assign({},t),{remainingDedents:this.flushRemainingDedents(e)})}isStartOfLine(e,t){return t===0||`\r
`.includes(e[t-1])}matchWhitespace(e,t,n,i){var s;this.whitespaceRegExp.lastIndex=t;const o=this.whitespaceRegExp.exec(e);return{currIndentLevel:(s=o?.[0].length)!==null&&s!==void 0?s:0,prevIndentLevel:this.indentationStack.at(-1),match:o}}createIndentationTokenInstance(e,t,n,i){const s=this.getLineNumber(t,i);return Ca(e,n,i,i+n.length,s,s,1,n.length)}getLineNumber(e,t){return e.substring(0,t).split(/\r\n|\r|\n/).length}indentMatcher(e,t,n,i){if(!this.isStartOfLine(e,t))return null;const{currIndentLevel:s,prevIndentLevel:o,match:l}=this.matchWhitespace(e,t,n,i);return s<=o?null:(this.indentationStack.push(s),l)}dedentMatcher(e,t,n,i){var s,o,l,c;if(!this.isStartOfLine(e,t))return null;const{currIndentLevel:u,prevIndentLevel:d,match:f}=this.matchWhitespace(e,t,n,i);if(u>=d)return null;const h=this.indentationStack.lastIndexOf(u);if(h===-1)return this.diagnostics.push({severity:"error",message:`Invalid dedent level ${u} at offset: ${t}. Current indentation stack: ${this.indentationStack}`,offset:t,length:(o=(s=f?.[0])===null||s===void 0?void 0:s.length)!==null&&o!==void 0?o:0,line:this.getLineNumber(e,t),column:1}),null;const p=this.indentationStack.length-h-1,g=(c=(l=e.substring(0,t).match(/[\r\n]+$/))===null||l===void 0?void 0:l[0].length)!==null&&c!==void 0?c:1;for(let y=0;y<p;y++){const E=this.createIndentationTokenInstance(this.dedentTokenType,e,"",t-(g-1));n.push(E),this.indentationStack.pop()}return null}buildTerminalToken(e){const t=super.buildTerminalToken(e),{indentTokenName:n,dedentTokenName:i,whitespaceTokenName:s}=this.options;return t.name===n?this.indentTokenType:t.name===i?this.dedentTokenType:t.name===s?nn({name:s,pattern:this.whitespaceRegExp,group:le.SKIPPED}):t}flushRemainingDedents(e){const t=[];for(;this.indentationStack.length>1;)t.push(this.createIndentationTokenInstance(this.dedentTokenType,e,"",e.length)),this.indentationStack.pop();return this.indentationStack=[0],t}},a(ps,"IndentationAwareTokenBuilder"),ps),ms,uT=(ms=class extends Ad{constructor(e){if(super(e),e.parser.TokenBuilder instanceof by)this.indentationTokenBuilder=e.parser.TokenBuilder;else throw new Error("IndentationAwareLexer requires an accompanying IndentationAwareTokenBuilder")}tokenize(e,t=Rd){const n=super.tokenize(e),i=n.report;t?.mode==="full"&&n.tokens.push(...i.remainingDedents),i.remainingDedents=[];const{indentTokenType:s,dedentTokenType:o}=this.indentationTokenBuilder,l=s.tokenTypeIdx,c=o.tokenTypeIdx,u=[],d=n.tokens.length-1;for(let f=0;f<d;f++){const h=n.tokens[f],p=n.tokens[f+1];if(h.tokenTypeIdx===l&&p.tokenTypeIdx===c){f++;continue}u.push(h)}return d>=0&&u.push(n.tokens[d]),n.tokens=u,n}},a(ms,"IndentationAwareLexer"),ms),wd={};ar(wd,{AstUtils:()=>nu,BiMap:()=>wo,Cancellation:()=>G,ContextCache:()=>ml,CstUtils:()=>Ic,DONE_RESULT:()=>oe,Deferred:()=>ft,Disposable:()=>Ft,DisposableCache:()=>pl,DocumentCache:()=>iy,EMPTY_STREAM:()=>Fo,ErrorWithLocation:()=>Uo,GrammarUtils:()=>Dc,MultiMap:()=>Ss,OperationCancelled:()=>kt,Reduction:()=>ra,RegExpUtils:()=>au,SimpleCache:()=>md,StreamImpl:()=>He,TreeStreamImpl:()=>Ts,URI:()=>ht,UriUtils:()=>qe,WorkspaceCache:()=>gd,assertUnreachable:()=>Ct,delayNextTick:()=>fl,interruptAndCheck:()=>ae,isOperationCancelled:()=>fr,loadGrammarFromJson:()=>Ze,setInterruptionPeriod:()=>dd,startCancelableOperation:()=>hl,stream:()=>q});Po(wd,gl);var gs,Oy=(gs=class{readFile(){throw new Error("No file system is available.")}async readDirectory(){return[]}},a(gs,"EmptyFileSystemProvider"),gs),mt={fileSystemProvider:a(()=>new Oy,"fileSystemProvider")},dT={Grammar:a(()=>{},"Grammar"),LanguageMetaData:a(()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"}),"LanguageMetaData")},fT={AstReflection:a(()=>new ru,"AstReflection")};function My(){const r=Z(Qe(mt),fT),e=Z(Je({shared:r}),dT);return r.ServiceRegistry.register(e),e}a(My,"createMinimalGrammarServices");function Ze(r){var e;const t=My(),n=t.serializer.JsonSerializer.deserialize(r);return t.shared.workspace.LangiumDocumentFactory.fromModel(n,ht.parse(`memory://${(e=n.name)!==null&&e!==void 0?e:"grammar"}.langium`)),n}a(Ze,"loadGrammarFromJson");Po(kh,wd);var hT=Object.defineProperty,A=a((r,e)=>hT(r,"name",{value:e,configurable:!0}),"__name"),dh="Statement",Xa="Architecture";function Dy(r){return Be.isInstance(r,Xa)}a(Dy,"isArchitecture");A(Dy,"isArchitecture");var _a="Axis",Us="Branch";function Fy(r){return Be.isInstance(r,Us)}a(Fy,"isBranch");A(Fy,"isBranch");var La="Checkout",Pa="CherryPicking",Il="ClassDefStatement",Bs="Commit";function Gy(r){return Be.isInstance(r,Bs)}a(Gy,"isCommit");A(Gy,"isCommit");var xl="Curve",$l="Edge",Nl="Entry",Vs="GitGraph";function Uy(r){return Be.isInstance(r,Vs)}a(Uy,"isGitGraph");A(Uy,"isGitGraph");var wl="Group",Ja="Info";function By(r){return Be.isInstance(r,Ja)}a(By,"isInfo");A(By,"isInfo");var ba="Item",_l="Junction",Ws="Merge";function Vy(r){return Be.isInstance(r,Ws)}a(Vy,"isMerge");A(Vy,"isMerge");var Ll="Option",Qa="Packet";function Wy(r){return Be.isInstance(r,Qa)}a(Wy,"isPacket");A(Wy,"isPacket");var Za="PacketBlock";function Ky(r){return Be.isInstance(r,Za)}a(Ky,"isPacketBlock");A(Ky,"isPacketBlock");var eo="Pie";function jy(r){return Be.isInstance(r,eo)}a(jy,"isPie");A(jy,"isPie");var to="PieSection";function Hy(r){return Be.isInstance(r,to)}a(Hy,"isPieSection");A(Hy,"isPieSection");var Pl="Radar",bl="Service",ro="Treemap";function zy(r){return Be.isInstance(r,ro)}a(zy,"isTreemap");A(zy,"isTreemap");var Ol="TreemapRow",Oa="Direction",Ma="Leaf",Da="Section",Bt,qy=(Bt=class extends $c{getAllTypes(){return[Xa,_a,Us,La,Pa,Il,Bs,xl,Oa,$l,Nl,Vs,wl,Ja,ba,_l,Ma,Ws,Ll,Qa,Za,eo,to,Pl,Da,bl,dh,ro,Ol]}computeIsSubtype(e,t){switch(e){case Us:case La:case Pa:case Bs:case Ws:return this.isSubtype(dh,t);case Oa:return this.isSubtype(Vs,t);case Ma:case Da:return this.isSubtype(ba,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Entry:axis":return _a;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case Xa:return{name:Xa,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case _a:return{name:_a,properties:[{name:"label"},{name:"name"}]};case Us:return{name:Us,properties:[{name:"name"},{name:"order"}]};case La:return{name:La,properties:[{name:"branch"}]};case Pa:return{name:Pa,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case Il:return{name:Il,properties:[{name:"className"},{name:"styleText"}]};case Bs:return{name:Bs,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case xl:return{name:xl,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]};case $l:return{name:$l,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case Nl:return{name:Nl,properties:[{name:"axis"},{name:"value"}]};case Vs:return{name:Vs,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case wl:return{name:wl,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case Ja:return{name:Ja,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case ba:return{name:ba,properties:[{name:"classSelector"},{name:"name"}]};case _l:return{name:_l,properties:[{name:"id"},{name:"in"}]};case Ws:return{name:Ws,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case Ll:return{name:Ll,properties:[{name:"name"},{name:"value",defaultValue:!1}]};case Qa:return{name:Qa,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case Za:return{name:Za,properties:[{name:"bits"},{name:"end"},{name:"label"},{name:"start"}]};case eo:return{name:eo,properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case to:return{name:to,properties:[{name:"label"},{name:"value"}]};case Pl:return{name:Pl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]};case bl:return{name:bl,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case ro:return{name:ro,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"},{name:"TreemapRows",defaultValue:[]}]};case Ol:return{name:Ol,properties:[{name:"indent"},{name:"item"}]};case Oa:return{name:Oa,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};case Ma:return{name:Ma,properties:[{name:"classSelector"},{name:"name"},{name:"value"}]};case Da:return{name:Da,properties:[{name:"classSelector"},{name:"name"}]};default:return{name:e,properties:[]}}}},a(Bt,"MermaidAstReflection"),A(Bt,"MermaidAstReflection"),Bt),Be=new qy,fh,pT=A(()=>fh??(fh=Ze(`{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@7"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"InfoGrammar"),hh,mT=A(()=>hh??(hh=Ze(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"packet"},{"$type":"Keyword","value":"packet-beta"}]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}],"cardinality":"?"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"+"},{"$type":"Assignment","feature":"bits","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]}]},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@9"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"PacketGrammar"),ph,gT=A(()=>ph??(ph=Ze(`{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"FLOAT_PIE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/-?[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT_PIE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/-?(0|[1-9][0-9]*)(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER_PIE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@2"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@3"}}]},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@11"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@12"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"PieGrammar"),mh,yT=A(()=>mh??(mh=Ze(`{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@18"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@19"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"ArchitectureGrammar"),gh,vT=A(()=>gh??(gh=Ze(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@14"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"REFERENCE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"GitGraphGrammar"),yh,TT=A(()=>yh??(yh=Ze(`{"$type":"Grammar","isDeclared":true,"name":"Radar","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@2"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@16"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"interfaces":[{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@2"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"definesHiddenTokens":false,"hiddenTokens":[],"types":[],"usedGrammars":[]}`)),"RadarGrammar"),vh,RT=A(()=>vh??(vh=Ze(`{"$type":"Grammar","isDeclared":true,"name":"Treemap","rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"ParserRule","entry":true,"name":"Treemap","returnType":{"$ref":"#/interfaces@4"},"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"TreemapRows","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"TREEMAP_KEYWORD","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"treemap-beta"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"treemap"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"CLASS_DEF","definition":{"$type":"RegexToken","regex":"/classDef\\\\s+([a-zA-Z_][a-zA-Z0-9_]+)(?:\\\\s+([^;\\\\r\\\\n]*))?(?:;)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STYLE_SEPARATOR","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":":::"}},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"SEPARATOR","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":":"}},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"COMMA","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":","}},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WS","definition":{"$type":"RegexToken","regex":"/[ \\\\t]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"ML_COMMENT","definition":{"$type":"RegexToken","regex":"/\\\\%\\\\%[^\\\\n]*/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"NL","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false},{"$type":"ParserRule","name":"TreemapRow","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"indent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"item","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"ClassDef","dataType":"string","definition":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Item","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Section","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]},{"$type":"Assignment","feature":"classSelector","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Leaf","returnType":{"$ref":"#/interfaces@2"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]},{"$type":"Assignment","feature":"classSelector","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INDENTATION","definition":{"$type":"RegexToken","regex":"/[ \\\\t]{1,}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID2","definition":{"$type":"RegexToken","regex":"/[a-zA-Z_][a-zA-Z0-9_]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER2","definition":{"$type":"RegexToken","regex":"/[0-9_\\\\.\\\\,]+/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"MyNumber","dataType":"number","definition":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"STRING2","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"interfaces":[{"$type":"Interface","name":"Item","attributes":[{"$type":"TypeAttribute","name":"name","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false},{"$type":"TypeAttribute","name":"classSelector","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]},{"$type":"Interface","name":"Section","superTypes":[{"$ref":"#/interfaces@0"}],"attributes":[]},{"$type":"Interface","name":"Leaf","superTypes":[{"$ref":"#/interfaces@0"}],"attributes":[{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}]},{"$type":"Interface","name":"ClassDefStatement","attributes":[{"$type":"TypeAttribute","name":"className","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false},{"$type":"TypeAttribute","name":"styleText","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false}],"superTypes":[]},{"$type":"Interface","name":"Treemap","attributes":[{"$type":"TypeAttribute","name":"TreemapRows","type":{"$type":"ArrayType","elementType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@14"}}},"isOptional":false},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[],"$comment":"/**\\n * Treemap grammar for Langium\\n * Converted from mindmap grammar\\n *\\n * The ML_COMMENT and NL hidden terminals handle whitespace, comments, and newlines\\n * before the treemap keyword, allowing for empty lines and comments before the\\n * treemap declaration.\\n */"}`)),"TreemapGrammar"),AT={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},ET={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},kT={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},CT={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},ST={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},IT={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},xT={languageId:"treemap",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},hr={AstReflection:A(()=>new qy,"AstReflection")},$T={Grammar:A(()=>pT(),"Grammar"),LanguageMetaData:A(()=>AT,"LanguageMetaData"),parser:{}},NT={Grammar:A(()=>mT(),"Grammar"),LanguageMetaData:A(()=>ET,"LanguageMetaData"),parser:{}},wT={Grammar:A(()=>gT(),"Grammar"),LanguageMetaData:A(()=>kT,"LanguageMetaData"),parser:{}},_T={Grammar:A(()=>yT(),"Grammar"),LanguageMetaData:A(()=>CT,"LanguageMetaData"),parser:{}},LT={Grammar:A(()=>vT(),"Grammar"),LanguageMetaData:A(()=>ST,"LanguageMetaData"),parser:{}},PT={Grammar:A(()=>TT(),"Grammar"),LanguageMetaData:A(()=>IT,"LanguageMetaData"),parser:{}},bT={Grammar:A(()=>RT(),"Grammar"),LanguageMetaData:A(()=>xT,"LanguageMetaData"),parser:{}},OT=/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,MT=/accTitle[\t ]*:([^\n\r]*)/,DT=/title([\t ][^\n\r]*|)/,FT={ACC_DESCR:OT,ACC_TITLE:MT,TITLE:DT},Vt,Rl=(Vt=class extends ud{runConverter(e,t,n){let i=this.runCommonConverter(e,t,n);return i===void 0&&(i=this.runCustomConverter(e,t,n)),i===void 0?super.runConverter(e,t,n):i}runCommonConverter(e,t,n){const i=FT[e.name];if(i===void 0)return;const s=i.exec(t);if(s!==null){if(s[1]!==void 0)return s[1].trim().replace(/[\t ]{2,}/gm," ");if(s[2]!==void 0)return s[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,`
`)}}},a(Vt,"AbstractMermaidValueConverter"),A(Vt,"AbstractMermaidValueConverter"),Vt),Wt,Al=(Wt=class extends Rl{runCustomConverter(e,t,n){}},a(Wt,"CommonValueConverter"),A(Wt,"CommonValueConverter"),Wt),Kt,$t=(Kt=class extends dl{constructor(e){super(),this.keywords=new Set(e)}buildKeywordTokens(e,t,n){const i=super.buildKeywordTokens(e,t,n);return i.forEach(s=>{this.keywords.has(s.name)&&s.PATTERN!==void 0&&(s.PATTERN=new RegExp(s.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),i}},a(Kt,"AbstractMermaidTokenBuilder"),A(Kt,"AbstractMermaidTokenBuilder"),Kt),jt;jt=class extends $t{},a(jt,"CommonTokenBuilder"),A(jt,"CommonTokenBuilder");var rt={},GT={info:A(async()=>{const{createInfoServices:r}=await wt(async()=>{const{createInfoServices:t}=await Promise.resolve().then(()=>ZT);return{createInfoServices:t}},void 0),e=r().Info.parser.LangiumParser;rt.info=e},"info"),packet:A(async()=>{const{createPacketServices:r}=await wt(async()=>{const{createPacketServices:t}=await Promise.resolve().then(()=>eR);return{createPacketServices:t}},void 0),e=r().Packet.parser.LangiumParser;rt.packet=e},"packet"),pie:A(async()=>{const{createPieServices:r}=await wt(async()=>{const{createPieServices:t}=await Promise.resolve().then(()=>tR);return{createPieServices:t}},void 0),e=r().Pie.parser.LangiumParser;rt.pie=e},"pie"),architecture:A(async()=>{const{createArchitectureServices:r}=await wt(async()=>{const{createArchitectureServices:t}=await Promise.resolve().then(()=>rR);return{createArchitectureServices:t}},void 0),e=r().Architecture.parser.LangiumParser;rt.architecture=e},"architecture"),gitGraph:A(async()=>{const{createGitGraphServices:r}=await wt(async()=>{const{createGitGraphServices:t}=await Promise.resolve().then(()=>nR);return{createGitGraphServices:t}},void 0),e=r().GitGraph.parser.LangiumParser;rt.gitGraph=e},"gitGraph"),radar:A(async()=>{const{createRadarServices:r}=await wt(async()=>{const{createRadarServices:t}=await Promise.resolve().then(()=>iR);return{createRadarServices:t}},void 0),e=r().Radar.parser.LangiumParser;rt.radar=e},"radar"),treemap:A(async()=>{const{createTreemapServices:r}=await wt(async()=>{const{createTreemapServices:t}=await Promise.resolve().then(()=>sR);return{createTreemapServices:t}},void 0),e=r().Treemap.parser.LangiumParser;rt.treemap=e},"treemap")};async function Yy(r,e){const t=GT[r];if(!t)throw new Error(`Unknown diagram type: ${r}`);rt[r]||await t();const i=rt[r].parse(e);if(i.lexerErrors.length>0||i.parserErrors.length>0)throw new UT(i);return i.value}a(Yy,"parse");A(Yy,"parse");var Ht,UT=(Ht=class extends Error{constructor(e){const t=e.lexerErrors.map(i=>i.message).join(`
`),n=e.parserErrors.map(i=>i.message).join(`
`);super(`Parsing failed: ${t} ${n}`),this.result=e}},a(Ht,"MermaidParseError"),A(Ht,"MermaidParseError"),Ht),zt,BT=(zt=class extends $t{constructor(){super(["pie","showData"])}},a(zt,"PieTokenBuilder"),A(zt,"PieTokenBuilder"),zt),qt,VT=(qt=class extends Rl{runCustomConverter(e,t,n){if(e.name==="PIE_SECTION_LABEL")return t.replace(/"/g,"").trim()}},a(qt,"PieValueConverter"),A(qt,"PieValueConverter"),qt),Xy={parser:{TokenBuilder:A(()=>new BT,"TokenBuilder"),ValueConverter:A(()=>new VT,"ValueConverter")}};function _d(r=mt){const e=Z(Qe(r),hr),t=Z(Je({shared:e}),wT,Xy);return e.ServiceRegistry.register(t),{shared:e,Pie:t}}a(_d,"createPieServices");A(_d,"createPieServices");var Yt,WT=(Yt=class extends $t{constructor(){super(["architecture"])}},a(Yt,"ArchitectureTokenBuilder"),A(Yt,"ArchitectureTokenBuilder"),Yt),Xt,KT=(Xt=class extends Rl{runCustomConverter(e,t,n){if(e.name==="ARCH_ICON")return t.replace(/[()]/g,"").trim();if(e.name==="ARCH_TEXT_ICON")return t.replace(/["()]/g,"");if(e.name==="ARCH_TITLE")return t.replace(/[[\]]/g,"").trim()}},a(Xt,"ArchitectureValueConverter"),A(Xt,"ArchitectureValueConverter"),Xt),Jy={parser:{TokenBuilder:A(()=>new WT,"TokenBuilder"),ValueConverter:A(()=>new KT,"ValueConverter")}};function Ld(r=mt){const e=Z(Qe(r),hr),t=Z(Je({shared:e}),_T,Jy);return e.ServiceRegistry.register(t),{shared:e,Architecture:t}}a(Ld,"createArchitectureServices");A(Ld,"createArchitectureServices");var Jt,jT=(Jt=class extends $t{constructor(){super(["gitGraph"])}},a(Jt,"GitGraphTokenBuilder"),A(Jt,"GitGraphTokenBuilder"),Jt),Qy={parser:{TokenBuilder:A(()=>new jT,"TokenBuilder"),ValueConverter:A(()=>new Al,"ValueConverter")}};function Pd(r=mt){const e=Z(Qe(r),hr),t=Z(Je({shared:e}),LT,Qy);return e.ServiceRegistry.register(t),{shared:e,GitGraph:t}}a(Pd,"createGitGraphServices");A(Pd,"createGitGraphServices");var Qt,HT=(Qt=class extends $t{constructor(){super(["radar-beta"])}},a(Qt,"RadarTokenBuilder"),A(Qt,"RadarTokenBuilder"),Qt),Zy={parser:{TokenBuilder:A(()=>new HT,"TokenBuilder"),ValueConverter:A(()=>new Al,"ValueConverter")}};function bd(r=mt){const e=Z(Qe(r),hr),t=Z(Je({shared:e}),PT,Zy);return e.ServiceRegistry.register(t),{shared:e,Radar:t}}a(bd,"createRadarServices");A(bd,"createRadarServices");var Zt,zT=(Zt=class extends $t{constructor(){super(["treemap"])}},a(Zt,"TreemapTokenBuilder"),A(Zt,"TreemapTokenBuilder"),Zt),qT=/classDef\s+([A-Z_a-z]\w+)(?:\s+([^\n\r;]*))?;?/,er,YT=(er=class extends Rl{runCustomConverter(e,t,n){if(e.name==="NUMBER2")return parseFloat(t.replace(/,/g,""));if(e.name==="SEPARATOR")return t.substring(1,t.length-1);if(e.name==="STRING2")return t.substring(1,t.length-1);if(e.name==="INDENTATION")return t.length;if(e.name==="ClassDef"){if(typeof t!="string")return t;const i=qT.exec(t);if(i)return{$type:"ClassDefStatement",className:i[1],styleText:i[2]||void 0}}}},a(er,"TreemapValueConverter"),A(er,"TreemapValueConverter"),er);function Od(r){const e=r.validation.TreemapValidator,t=r.validation.ValidationRegistry;if(t){const n={Treemap:e.checkSingleRoot.bind(e)};t.register(n,e)}}a(Od,"registerValidationChecks");A(Od,"registerValidationChecks");var tr,XT=(tr=class{checkSingleRoot(e,t){let n;for(const i of e.TreemapRows)i.item&&(n===void 0&&i.indent===void 0?n=0:i.indent===void 0?t("error","Multiple root nodes are not allowed in a treemap.",{node:i,property:"item"}):n!==void 0&&n>=parseInt(i.indent,10)&&t("error","Multiple root nodes are not allowed in a treemap.",{node:i,property:"item"}))}},a(tr,"TreemapValidator"),A(tr,"TreemapValidator"),tr),ev={parser:{TokenBuilder:A(()=>new zT,"TokenBuilder"),ValueConverter:A(()=>new YT,"ValueConverter")},validation:{TreemapValidator:A(()=>new XT,"TreemapValidator")}};function Md(r=mt){const e=Z(Qe(r),hr),t=Z(Je({shared:e}),bT,ev);return e.ServiceRegistry.register(t),Od(t),{shared:e,Treemap:t}}a(Md,"createTreemapServices");A(Md,"createTreemapServices");var rr,JT=(rr=class extends $t{constructor(){super(["info","showInfo"])}},a(rr,"InfoTokenBuilder"),A(rr,"InfoTokenBuilder"),rr),tv={parser:{TokenBuilder:A(()=>new JT,"TokenBuilder"),ValueConverter:A(()=>new Al,"ValueConverter")}};function Dd(r=mt){const e=Z(Qe(r),hr),t=Z(Je({shared:e}),$T,tv);return e.ServiceRegistry.register(t),{shared:e,Info:t}}a(Dd,"createInfoServices");A(Dd,"createInfoServices");var nr,QT=(nr=class extends $t{constructor(){super(["packet"])}},a(nr,"PacketTokenBuilder"),A(nr,"PacketTokenBuilder"),nr),rv={parser:{TokenBuilder:A(()=>new QT,"TokenBuilder"),ValueConverter:A(()=>new Al,"ValueConverter")}};function Fd(r=mt){const e=Z(Qe(r),hr),t=Z(Je({shared:e}),NT,rv);return e.ServiceRegistry.register(t),{shared:e,Packet:t}}a(Fd,"createPacketServices");A(Fd,"createPacketServices");const ZT=Object.freeze(Object.defineProperty({__proto__:null,InfoModule:tv,createInfoServices:Dd},Symbol.toStringTag,{value:"Module"})),eR=Object.freeze(Object.defineProperty({__proto__:null,PacketModule:rv,createPacketServices:Fd},Symbol.toStringTag,{value:"Module"})),tR=Object.freeze(Object.defineProperty({__proto__:null,PieModule:Xy,createPieServices:_d},Symbol.toStringTag,{value:"Module"})),rR=Object.freeze(Object.defineProperty({__proto__:null,ArchitectureModule:Jy,createArchitectureServices:Ld},Symbol.toStringTag,{value:"Module"})),nR=Object.freeze(Object.defineProperty({__proto__:null,GitGraphModule:Qy,createGitGraphServices:Pd},Symbol.toStringTag,{value:"Module"})),iR=Object.freeze(Object.defineProperty({__proto__:null,RadarModule:Zy,createRadarServices:bd},Symbol.toStringTag,{value:"Module"})),sR=Object.freeze(Object.defineProperty({__proto__:null,TreemapModule:ev,createTreemapServices:Md},Symbol.toStringTag,{value:"Module"}));export{Yy as p};
