import{_ as v}from"./app-BQZQgfaL.js";function $e(t){"@babel/helpers - typeof";return $e=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$e(t)}v($e,"_typeof");function Xt(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}v(Xt,"_classCallCheck");function di(t,e){for(var r=0;r<e.length;r++){var a=e[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}v(di,"_defineProperties");function Yt(t,e,r){return e&&di(t.prototype,e),r&&di(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}v(Yt,"_createClass");function Ki(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}v(Ki,"_defineProperty$1");function We(t,e){return Gs(t)||Ws(t,e)||In(t,e)||Us()}v(We,"_slicedToArray");function Gi(t){return Ks(t)||Hs(t)||In(t)||$s()}v(Gi,"_toConsumableArray");function Ks(t){if(Array.isArray(t))return xn(t)}v(Ks,"_arrayWithoutHoles");function Gs(t){if(Array.isArray(t))return t}v(Gs,"_arrayWithHoles");function Hs(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}v(Hs,"_iterableToArray");function Ws(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var a=[],n=!0,i=!1,o,s;try{for(r=r.call(t);!(n=(o=r.next()).done)&&(a.push(o.value),!(e&&a.length===e));n=!0);}catch(u){i=!0,s=u}finally{try{!n&&r.return!=null&&r.return()}finally{if(i)throw s}}return a}}v(Ws,"_iterableToArrayLimit");function In(t,e){if(t){if(typeof t=="string")return xn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xn(t,e)}}v(In,"_unsupportedIterableToArray");function xn(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,a=new Array(e);r<e;r++)a[r]=t[r];return a}v(xn,"_arrayLikeToArray");function $s(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}v($s,"_nonIterableSpread");function Us(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}v(Us,"_nonIterableRest");function mt(t,e){var r=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=In(t))||e&&t&&typeof t.length=="number"){r&&(t=r);var a=0,n=v(function(){},"F");return{s:n,n:v(function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},"n"),e:v(function(u){throw u},"e"),f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i=!0,o=!1,s;return{s:v(function(){r=r.call(t)},"s"),n:v(function(){var u=r.next();return i=u.done,u},"n"),e:v(function(u){o=!0,s=u},"e"),f:v(function(){try{!i&&r.return!=null&&r.return()}finally{if(o)throw s}},"f")}}v(mt,"_createForOfIteratorHelper");var _e=typeof window>"u"?null:window,Mo=_e?_e.navigator:null;_e&&_e.document;var jv=$e(""),_s=$e({}),ec=$e(function(){}),tc=typeof HTMLElement>"u"?"undefined":$e(HTMLElement),Ia=v(function(e){return e&&e.instanceString&&Ge(e.instanceString)?e.instanceString():null},"instanceStr"),fe=v(function(e){return e!=null&&$e(e)==jv},"string"),Ge=v(function(e){return e!=null&&$e(e)===ec},"fn"),Oe=v(function(e){return!Et(e)&&(Array.isArray?Array.isArray(e):e!=null&&e instanceof Array)},"array"),ke=v(function(e){return e!=null&&$e(e)===_s&&!Oe(e)&&e.constructor===Object},"plainObject"),rc=v(function(e){return e!=null&&$e(e)===_s},"object"),ae=v(function(e){return e!=null&&$e(e)===$e(1)&&!isNaN(e)},"number"),ac=v(function(e){return ae(e)&&Math.floor(e)===e},"integer"),En=v(function(e){if(tc!=="undefined")return e!=null&&e instanceof HTMLElement},"htmlElement"),Et=v(function(e){return Ma(e)||Xs(e)},"elementOrCollection"),Ma=v(function(e){return Ia(e)==="collection"&&e._private.single},"element"),Xs=v(function(e){return Ia(e)==="collection"&&!e._private.single},"collection"),Hi=v(function(e){return Ia(e)==="core"},"core"),Ys=v(function(e){return Ia(e)==="stylesheet"},"stylesheet"),nc=v(function(e){return Ia(e)==="event"},"event"),nr=v(function(e){return e==null?!0:!!(e===""||e.match(/^\s+$/))},"emptyString"),ic=v(function(e){return typeof HTMLElement>"u"?!1:e instanceof HTMLElement},"domElement"),oc=v(function(e){return ke(e)&&ae(e.x1)&&ae(e.x2)&&ae(e.y1)&&ae(e.y2)},"boundingBox"),sc=v(function(e){return rc(e)&&Ge(e.then)},"promise"),lc=v(function(){return Mo&&Mo.userAgent.match(/msie|trident|edge/i)},"ms"),xa=v(function(e,r){r||(r=v(function(){if(arguments.length===1)return arguments[0];if(arguments.length===0)return"undefined";for(var i=[],o=0;o<arguments.length;o++)i.push(arguments[o]);return i.join("$")},"keyFn"));var a=v(function n(){var i=this,o=arguments,s,u=r.apply(i,o),l=n.cache;return(s=l[u])||(s=l[u]=e.apply(i,o)),s},"memoizedFn");return a.cache={},a},"memoize"),Wi=xa(function(t){return t.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),Mn=xa(function(t){return t.replace(/(-\w)/g,function(e){return e[1].toUpperCase()})}),Zs=xa(function(t,e){return t+e[0].toUpperCase()+e.substring(1)},function(t,e){return t+"$"+e}),Oo=v(function(e){return nr(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},"capitalize"),Xe="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",uc="rgb[a]?\\(("+Xe+"[%]?)\\s*,\\s*("+Xe+"[%]?)\\s*,\\s*("+Xe+"[%]?)(?:\\s*,\\s*("+Xe+"))?\\)",vc="rgb[a]?\\((?:"+Xe+"[%]?)\\s*,\\s*(?:"+Xe+"[%]?)\\s*,\\s*(?:"+Xe+"[%]?)(?:\\s*,\\s*(?:"+Xe+"))?\\)",cc="hsl[a]?\\(("+Xe+")\\s*,\\s*("+Xe+"[%])\\s*,\\s*("+Xe+"[%])(?:\\s*,\\s*("+Xe+"))?\\)",fc="hsl[a]?\\((?:"+Xe+")\\s*,\\s*(?:"+Xe+"[%])\\s*,\\s*(?:"+Xe+"[%])(?:\\s*,\\s*(?:"+Xe+"))?\\)",dc="\\#[0-9a-fA-F]{3}",hc="\\#[0-9a-fA-F]{6}",Qs=v(function(e,r){return e<r?-1:e>r?1:0},"ascending"),gc=v(function(e,r){return-1*Qs(e,r)},"descending"),he=Object.assign!=null?Object.assign.bind(Object):function(t){for(var e=arguments,r=1;r<e.length;r++){var a=e[r];if(a!=null)for(var n=Object.keys(a),i=0;i<n.length;i++){var o=n[i];t[o]=a[o]}}return t},pc=v(function(e){if(!(!(e.length===4||e.length===7)||e[0]!=="#")){var r=e.length===4,a,n,i,o=16;return r?(a=parseInt(e[1]+e[1],o),n=parseInt(e[2]+e[2],o),i=parseInt(e[3]+e[3],o)):(a=parseInt(e[1]+e[2],o),n=parseInt(e[3]+e[4],o),i=parseInt(e[5]+e[6],o)),[a,n,i]}},"hex2tuple"),yc=v(function(e){var r,a,n,i,o,s,u,l;function c(h,m,y){return y<0&&(y+=1),y>1&&(y-=1),y<1/6?h+(m-h)*6*y:y<1/2?m:y<2/3?h+(m-h)*(2/3-y)*6:h}v(c,"hue2rgb");var f=new RegExp("^"+cc+"$").exec(e);if(f){if(a=parseInt(f[1]),a<0?a=(360- -1*a%360)%360:a>360&&(a=a%360),a/=360,n=parseFloat(f[2]),n<0||n>100||(n=n/100,i=parseFloat(f[3]),i<0||i>100)||(i=i/100,o=f[4],o!==void 0&&(o=parseFloat(o),o<0||o>1)))return;if(n===0)s=u=l=Math.round(i*255);else{var d=i<.5?i*(1+n):i+n-i*n,g=2*i-d;s=Math.round(255*c(g,d,a+1/3)),u=Math.round(255*c(g,d,a)),l=Math.round(255*c(g,d,a-1/3))}r=[s,u,l,o]}return r},"hsl2tuple"),mc=v(function(e){var r,a=new RegExp("^"+uc+"$").exec(e);if(a){r=[];for(var n=[],i=1;i<=3;i++){var o=a[i];if(o[o.length-1]==="%"&&(n[i]=!0),o=parseFloat(o),n[i]&&(o=o/100*255),o<0||o>255)return;r.push(Math.floor(o))}var s=n[1]||n[2]||n[3],u=n[1]&&n[2]&&n[3];if(s&&!u)return;var l=a[4];if(l!==void 0){if(l=parseFloat(l),l<0||l>1)return;r.push(l)}}return r},"rgb2tuple"),bc=v(function(e){return wc[e.toLowerCase()]},"colorname2tuple"),Js=v(function(e){return(Oe(e)?e:null)||bc(e)||pc(e)||mc(e)||yc(e)},"color2tuple"),wc={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},js=v(function(e){for(var r=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var o=a[i];if(ke(o))throw Error("Tried to set map with object key");i<a.length-1?(r[o]==null&&(r[o]={}),r=r[o]):r[o]=e.value}},"setMap"),el=v(function(e){for(var r=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var o=a[i];if(ke(o))throw Error("Tried to get map with object key");if(r=r[o],r==null)return r}return r},"getMap");function tl(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}v(tl,"isObject");var xr=tl,va=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function rl(t,e){return e={exports:{}},t(e,e.exports),e.exports}v(rl,"createCommonjsModule");var xc=typeof va=="object"&&va&&va.Object===Object&&va,Ec=xc,Cc=typeof self=="object"&&self&&self.Object===Object&&self,Tc=Ec||Cc||Function("return this")(),On=Tc,Sc=v(function(){return On.Date.now()},"now"),ti=Sc,Dc=/\s/;function al(t){for(var e=t.length;e--&&Dc.test(t.charAt(e)););return e}v(al,"trimmedEndIndex");var Pc=al,kc=/^\s+/;function nl(t){return t&&t.slice(0,Pc(t)+1).replace(kc,"")}v(nl,"baseTrim");var Bc=nl,Ac=On.Symbol,Ur=Ac,il=Object.prototype,Rc=il.hasOwnProperty,Lc=il.toString,ia=Ur?Ur.toStringTag:void 0;function ol(t){var e=Rc.call(t,ia),r=t[ia];try{t[ia]=void 0;var a=!0}catch{}var n=Lc.call(t);return a&&(e?t[ia]=r:delete t[ia]),n}v(ol,"getRawTag");var Ic=ol,Mc=Object.prototype,Oc=Mc.toString;function sl(t){return Oc.call(t)}v(sl,"objectToString");var Fc=sl,Nc="[object Null]",zc="[object Undefined]",Fo=Ur?Ur.toStringTag:void 0;function ll(t){return t==null?t===void 0?zc:Nc:Fo&&Fo in Object(t)?Ic(t):Fc(t)}v(ll,"baseGetTag");var ul=ll;function vl(t){return t!=null&&typeof t=="object"}v(vl,"isObjectLike");var Vc=vl,qc="[object Symbol]";function cl(t){return typeof t=="symbol"||Vc(t)&&ul(t)==qc}v(cl,"isSymbol");var Oa=cl,No=NaN,Kc=/^[-+]0x[0-9a-f]+$/i,Gc=/^0b[01]+$/i,Hc=/^0o[0-7]+$/i,Wc=parseInt;function fl(t){if(typeof t=="number")return t;if(Oa(t))return No;if(xr(t)){var e=typeof t.valueOf=="function"?t.valueOf():t;t=xr(e)?e+"":e}if(typeof t!="string")return t===0?t:+t;t=Bc(t);var r=Gc.test(t);return r||Hc.test(t)?Wc(t.slice(2),r?2:8):Kc.test(t)?No:+t}v(fl,"toNumber");var zo=fl,$c="Expected a function",Uc=Math.max,_c=Math.min;function dl(t,e,r){var a,n,i,o,s,u,l=0,c=!1,f=!1,d=!0;if(typeof t!="function")throw new TypeError($c);e=zo(e)||0,xr(r)&&(c=!!r.leading,f="maxWait"in r,i=f?Uc(zo(r.maxWait)||0,e):i,d="trailing"in r?!!r.trailing:d);function g(C){var E=a,D=n;return a=n=void 0,l=C,o=t.apply(D,E),o}v(g,"invokeFunc");function h(C){return l=C,s=setTimeout(p,e),c?g(C):o}v(h,"leadingEdge");function m(C){var E=C-u,D=C-l,T=e-E;return f?_c(T,i-D):T}v(m,"remainingWait");function y(C){var E=C-u,D=C-l;return u===void 0||E>=e||E<0||f&&D>=i}v(y,"shouldInvoke");function p(){var C=ti();if(y(C))return b(C);s=setTimeout(p,m(C))}v(p,"timerExpired");function b(C){return s=void 0,d&&a?g(C):(a=n=void 0,o)}v(b,"trailingEdge");function w(){s!==void 0&&clearTimeout(s),l=0,a=u=n=s=void 0}v(w,"cancel");function x(){return s===void 0?o:b(ti())}v(x,"flush");function S(){var C=ti(),E=y(C);if(a=arguments,n=this,u=C,E){if(s===void 0)return h(u);if(f)return clearTimeout(s),s=setTimeout(p,e),g(u)}return s===void 0&&(s=setTimeout(p,e)),o}return v(S,"debounced"),S.cancel=w,S.flush=x,S}v(dl,"debounce");var Fa=dl,ri=_e?_e.performance:null,hl=ri&&ri.now?function(){return ri.now()}:function(){return Date.now()},Xc=(function(){if(_e){if(_e.requestAnimationFrame)return function(t){_e.requestAnimationFrame(t)};if(_e.mozRequestAnimationFrame)return function(t){_e.mozRequestAnimationFrame(t)};if(_e.webkitRequestAnimationFrame)return function(t){_e.webkitRequestAnimationFrame(t)};if(_e.msRequestAnimationFrame)return function(t){_e.msRequestAnimationFrame(t)}}return function(t){t&&setTimeout(function(){t(hl())},1e3/60)}})(),Cn=v(function(e){return Xc(e)},"requestAnimationFrame"),$t=hl,zr=9261,gl=65599,ca=5381,pl=v(function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:zr,a=r,n;n=e.next(),!n.done;)a=a*gl+n.value|0;return a},"hashIterableInts"),Ea=v(function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:zr;return r*gl+e|0},"hashInt"),Ca=v(function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ca;return(r<<5)+r+e|0},"hashIntAlt"),Yc=v(function(e,r){return e*2097152+r},"combineHashes"),Jt=v(function(e){return e[0]*2097152+e[1]},"combineHashesArray"),Za=v(function(e,r){return[Ea(e[0],r[0]),Ca(e[1],r[1])]},"hashArrays"),Zc=v(function(e,r){var a={value:0,done:!1},n=0,i=e.length,o={next:v(function(){return n<i?a.value=e[n++]:a.done=!0,a},"next")};return pl(o,r)},"hashIntsArray"),ir=v(function(e,r){var a={value:0,done:!1},n=0,i=e.length,o={next:v(function(){return n<i?a.value=e.charCodeAt(n++):a.done=!0,a},"next")};return pl(o,r)},"hashString"),yl=v(function(){return Qc(arguments)},"hashStrings"),Qc=v(function(e){for(var r,a=0;a<e.length;a++){var n=e[a];a===0?r=ir(n):r=ir(n,r)}return r},"hashStringsArray"),Vo=!0,Jc=console.warn!=null,jc=console.trace!=null,$i=Number.MAX_SAFE_INTEGER||9007199254740991,ml=v(function(){return!0},"trueify"),Tn=v(function(){return!1},"falsify"),qo=v(function(){return 0},"zeroify"),Ui=v(function(){},"noop"),Ke=v(function(e){throw new Error(e)},"error"),bl=v(function(e){if(e!==void 0)Vo=!!e;else return Vo},"warnings"),Re=v(function(e){bl()&&(Jc?console.warn(e):(console.log(e),jc&&console.trace()))},"warn"),ef=v(function(e){return he({},e)},"clone"),Nt=v(function(e){return e==null?e:Oe(e)?e.slice():ke(e)?ef(e):e},"copy"),tf=v(function(e){return e.slice()},"copyArray"),wl=v(function(e,r){for(r=e="";e++<36;r+=e*51&52?(e^15?8^Math.random()*(e^20?16:4):4).toString(16):"-");return r},"uuid"),rf={},xl=v(function(){return rf},"staticEmptyObject"),et=v(function(e){var r=Object.keys(e);return function(a){for(var n={},i=0;i<r.length;i++){var o=r[i],s=a?.[o];n[o]=s===void 0?e[o]:s}return n}},"defaults"),or=v(function(e,r,a){for(var n=e.length-1;n>=0&&!(e[n]===r&&(e.splice(n,1),a));n--);},"removeFromArray"),_i=v(function(e){e.splice(0,e.length)},"clearArray"),af=v(function(e,r){for(var a=0;a<r.length;a++){var n=r[a];e.push(n)}},"push"),At=v(function(e,r,a){return a&&(r=Zs(a,r)),e[r]},"getPrefixedProperty"),er=v(function(e,r,a,n){a&&(r=Zs(a,r)),e[r]=n},"setPrefixedProperty"),nf=(function(){function t(){Xt(this,t),this._obj={}}return v(t,"ObjectMap"),Yt(t,[{key:"set",value:v(function(r,a){return this._obj[r]=a,this},"set")},{key:"delete",value:v(function(r){return this._obj[r]=void 0,this},"_delete")},{key:"clear",value:v(function(){this._obj={}},"clear")},{key:"has",value:v(function(r){return this._obj[r]!==void 0},"has")},{key:"get",value:v(function(r){return this._obj[r]},"get")}]),t})(),zt=typeof Map<"u"?Map:nf,of="undefined",sf=(function(){function t(e){if(Xt(this,t),this._obj=Object.create(null),this.size=0,e!=null){var r;e.instanceString!=null&&e.instanceString()===this.instanceString()?r=e.toArray():r=e;for(var a=0;a<r.length;a++)this.add(r[a])}}return v(t,"ObjectSet"),Yt(t,[{key:"instanceString",value:v(function(){return"set"},"instanceString")},{key:"add",value:v(function(r){var a=this._obj;a[r]!==1&&(a[r]=1,this.size++)},"add")},{key:"delete",value:v(function(r){var a=this._obj;a[r]===1&&(a[r]=0,this.size--)},"_delete")},{key:"clear",value:v(function(){this._obj=Object.create(null)},"clear")},{key:"has",value:v(function(r){return this._obj[r]===1},"has")},{key:"toArray",value:v(function(){var r=this;return Object.keys(this._obj).filter(function(a){return r.has(a)})},"toArray")},{key:"forEach",value:v(function(r,a){return this.toArray().forEach(r,a)},"forEach")}]),t})(),Qr=(typeof Set>"u"?"undefined":$e(Set))!==of?Set:sf,Fn=v(function(e,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(e===void 0||r===void 0||!Hi(e)){Ke("An element must have a core reference and parameters set");return}var n=r.group;if(n==null&&(r.data&&r.data.source!=null&&r.data.target!=null?n="edges":n="nodes"),n!=="nodes"&&n!=="edges"){Ke("An element must be of type `nodes` or `edges`; you specified `"+n+"`");return}this.length=1,this[0]=this;var i=this._private={cy:e,single:!0,data:r.data||{},position:r.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:n,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!r.selected,selectable:r.selectable===void 0?!0:!!r.selectable,locked:!!r.locked,grabbed:!1,grabbable:r.grabbable===void 0?!0:!!r.grabbable,pannable:r.pannable===void 0?n==="edges":!!r.pannable,active:!1,classes:new Qr,animation:{current:[],queue:[]},rscratch:{},scratch:r.scratch||{},edges:[],children:[],parent:r.parent&&r.parent.isNode()?r.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(i.position.x==null&&(i.position.x=0),i.position.y==null&&(i.position.y=0),r.renderedPosition){var o=r.renderedPosition,s=e.pan(),u=e.zoom();i.position={x:(o.x-s.x)/u,y:(o.y-s.y)/u}}var l=[];Oe(r.classes)?l=r.classes:fe(r.classes)&&(l=r.classes.split(/\s+/));for(var c=0,f=l.length;c<f;c++){var d=l[c];!d||d===""||i.classes.add(d)}this.createEmitter();var g=r.style||r.css;g&&(Re("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(g)),(a===void 0||a)&&this.restore()},"Element"),Ko=v(function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},v(function(a,n,i){var o;ke(a)&&!Et(a)&&(o=a,a=o.roots||o.root,n=o.visit,i=o.directed),i=arguments.length===2&&!Ge(n)?n:i,n=Ge(n)?n:function(){};for(var s=this._private.cy,u=a=fe(a)?this.filter(a):a,l=[],c=[],f={},d={},g={},h=0,m,y=this.byGroup(),p=y.nodes,b=y.edges,w=0;w<u.length;w++){var x=u[w],S=x.id();x.isNode()&&(l.unshift(x),e.bfs&&(g[S]=!0,c.push(x)),d[S]=0)}for(var C=v(function(){var L=e.bfs?l.shift():l.pop(),R=L.id();if(e.dfs){if(g[R])return"continue";g[R]=!0,c.push(L)}var M=d[R],I=f[R],O=I!=null?I.source():null,F=I!=null?I.target():null,K=I==null?void 0:L.same(O)?F[0]:O[0],$=void 0;if($=n(L,I,K,h++,M),$===!0)return m=L,"break";if($===!1)return"break";for(var q=L.connectedEdges().filter(function(Q){return(!i||Q.source().same(L))&&b.has(Q)}),G=0;G<q.length;G++){var X=q[G],Z=X.connectedNodes().filter(function(Q){return!Q.same(L)&&p.has(Q)}),J=Z.id();Z.length!==0&&!g[J]&&(Z=Z[0],l.push(Z),e.bfs&&(g[J]=!0,c.push(Z)),f[J]=X,d[J]=d[R]+1)}},"_loop");l.length!==0;){var E=C();if(E!=="continue"&&E==="break")break}for(var D=s.collection(),T=0;T<c.length;T++){var A=c[T],B=f[A.id()];B!=null&&D.push(B),D.push(A)}return{path:s.collection(D),found:s.collection(m)}},"searchFn")},"defineSearch"),Ta={breadthFirstSearch:Ko({bfs:!0}),depthFirstSearch:Ko({dfs:!0})};Ta.bfs=Ta.breadthFirstSearch;Ta.dfs=Ta.depthFirstSearch;var lf=rl(function(t,e){(function(){var r,a,n,i,o,s,u,l,c,f,d,g,h,m,y;n=Math.floor,f=Math.min,a=v(function(p,b){return p<b?-1:p>b?1:0},"defaultCmp"),c=v(function(p,b,w,x,S){var C;if(w==null&&(w=0),S==null&&(S=a),w<0)throw new Error("lo must be non-negative");for(x==null&&(x=p.length);w<x;)C=n((w+x)/2),S(b,p[C])<0?x=C:w=C+1;return[].splice.apply(p,[w,w-w].concat(b)),b},"insort"),s=v(function(p,b,w){return w==null&&(w=a),p.push(b),m(p,0,p.length-1,w)},"heappush"),o=v(function(p,b){var w,x;return b==null&&(b=a),w=p.pop(),p.length?(x=p[0],p[0]=w,y(p,0,b)):x=w,x},"heappop"),l=v(function(p,b,w){var x;return w==null&&(w=a),x=p[0],p[0]=b,y(p,0,w),x},"heapreplace"),u=v(function(p,b,w){var x;return w==null&&(w=a),p.length&&w(p[0],b)<0&&(x=[p[0],b],b=x[0],p[0]=x[1],y(p,0,w)),b},"heappushpop"),i=v(function(p,b){var w,x,S,C,E,D;for(b==null&&(b=a),C=(function(){D=[];for(var T=0,A=n(p.length/2);0<=A?T<A:T>A;0<=A?T++:T--)D.push(T);return D}).apply(this).reverse(),E=[],x=0,S=C.length;x<S;x++)w=C[x],E.push(y(p,w,b));return E},"heapify"),h=v(function(p,b,w){var x;if(w==null&&(w=a),x=p.indexOf(b),x!==-1)return m(p,0,x,w),y(p,x,w)},"updateItem"),d=v(function(p,b,w){var x,S,C,E,D;if(w==null&&(w=a),S=p.slice(0,b),!S.length)return S;for(i(S,w),D=p.slice(b),C=0,E=D.length;C<E;C++)x=D[C],u(S,x,w);return S.sort(w).reverse()},"nlargest"),g=v(function(p,b,w){var x,S,C,E,D,T,A,B,k;if(w==null&&(w=a),b*10<=p.length){if(C=p.slice(0,b).sort(w),!C.length)return C;for(S=C[C.length-1],A=p.slice(b),E=0,T=A.length;E<T;E++)x=A[E],w(x,S)<0&&(c(C,x,0,null,w),C.pop(),S=C[C.length-1]);return C}for(i(p,w),k=[],D=0,B=f(b,p.length);0<=B?D<B:D>B;0<=B?++D:--D)k.push(o(p,w));return k},"nsmallest"),m=v(function(p,b,w,x){var S,C,E;for(x==null&&(x=a),S=p[w];w>b;){if(E=w-1>>1,C=p[E],x(S,C)<0){p[w]=C,w=E;continue}break}return p[w]=S},"_siftdown"),y=v(function(p,b,w){var x,S,C,E,D;for(w==null&&(w=a),S=p.length,D=b,C=p[b],x=2*b+1;x<S;)E=x+1,E<S&&!(w(p[x],p[E])<0)&&(x=E),p[b]=p[x],b=x,x=2*b+1;return p[b]=C,m(p,D,b,w)},"_siftup"),r=(function(){p.push=s,p.pop=o,p.replace=l,p.pushpop=u,p.heapify=i,p.updateItem=h,p.nlargest=d,p.nsmallest=g;function p(b){this.cmp=b??a,this.nodes=[]}return v(p,"Heap"),p.prototype.push=function(b){return s(this.nodes,b,this.cmp)},p.prototype.pop=function(){return o(this.nodes,this.cmp)},p.prototype.peek=function(){return this.nodes[0]},p.prototype.contains=function(b){return this.nodes.indexOf(b)!==-1},p.prototype.replace=function(b){return l(this.nodes,b,this.cmp)},p.prototype.pushpop=function(b){return u(this.nodes,b,this.cmp)},p.prototype.heapify=function(){return i(this.nodes,this.cmp)},p.prototype.updateItem=function(b){return h(this.nodes,b,this.cmp)},p.prototype.clear=function(){return this.nodes=[]},p.prototype.empty=function(){return this.nodes.length===0},p.prototype.size=function(){return this.nodes.length},p.prototype.clone=function(){var b;return b=new p,b.nodes=this.nodes.slice(0),b},p.prototype.toArray=function(){return this.nodes.slice(0)},p.prototype.insert=p.prototype.push,p.prototype.top=p.prototype.peek,p.prototype.front=p.prototype.peek,p.prototype.has=p.prototype.contains,p.prototype.copy=p.prototype.clone,p})(),(function(p,b){return t.exports=b()})(this,function(){return r})}).call(va)}),Na=lf,uf=et({root:null,weight:v(function(e){return 1},"weight"),directed:!1}),vf={dijkstra:v(function(e){if(!ke(e)){var r=arguments;e={root:r[0],weight:r[1],directed:r[2]}}var a=uf(e),n=a.root,i=a.weight,o=a.directed,s=this,u=i,l=fe(n)?this.filter(n)[0]:n[0],c={},f={},d={},g=this.byGroup(),h=g.nodes,m=g.edges;m.unmergeBy(function(M){return M.isLoop()});for(var y=v(function(I){return c[I.id()]},"getDist"),p=v(function(I,O){c[I.id()]=O,b.updateItem(I)},"setDist"),b=new Na(function(M,I){return y(M)-y(I)}),w=0;w<h.length;w++){var x=h[w];c[x.id()]=x.same(l)?0:1/0,b.push(x)}for(var S=v(function(I,O){for(var F=(o?I.edgesTo(O):I.edgesWith(O)).intersect(m),K=1/0,$,q=0;q<F.length;q++){var G=F[q],X=u(G);(X<K||!$)&&(K=X,$=G)}return{edge:$,dist:K}},"distBetween");b.size()>0;){var C=b.pop(),E=y(C),D=C.id();if(d[D]=E,E!==1/0)for(var T=C.neighborhood().intersect(h),A=0;A<T.length;A++){var B=T[A],k=B.id(),L=S(C,B),R=E+L.dist;R<y(B)&&(p(B,R),f[k]={node:C,edge:L.edge})}}return{distanceTo:v(function(I){var O=fe(I)?h.filter(I)[0]:I[0];return d[O.id()]},"distanceTo"),pathTo:v(function(I){var O=fe(I)?h.filter(I)[0]:I[0],F=[],K=O,$=K.id();if(O.length>0)for(F.unshift(O);f[$];){var q=f[$];F.unshift(q.edge),F.unshift(q.node),K=q.node,$=K.id()}return s.spawn(F)},"pathTo")}},"dijkstra")},cf={kruskal:v(function(e){e=e||function(w){return 1};for(var r=this.byGroup(),a=r.nodes,n=r.edges,i=a.length,o=new Array(i),s=a,u=v(function(x){for(var S=0;S<o.length;S++){var C=o[S];if(C.has(x))return S}},"findSetIndex"),l=0;l<i;l++)o[l]=this.spawn(a[l]);for(var c=n.sort(function(w,x){return e(w)-e(x)}),f=0;f<c.length;f++){var d=c[f],g=d.source()[0],h=d.target()[0],m=u(g),y=u(h),p=o[m],b=o[y];m!==y&&(s.merge(d),p.merge(b),o.splice(y,1))}return s},"kruskal")},ff=et({root:null,goal:null,weight:v(function(e){return 1},"weight"),heuristic:v(function(e){return 0},"heuristic"),directed:!1}),df={aStar:v(function(e){var r=this.cy(),a=ff(e),n=a.root,i=a.goal,o=a.heuristic,s=a.directed,u=a.weight;n=r.collection(n)[0],i=r.collection(i)[0];var l=n.id(),c=i.id(),f={},d={},g={},h=new Na(function($,q){return d[$.id()]-d[q.id()]}),m=new Qr,y={},p={},b=v(function(q,G){h.push(q),m.add(G)},"addToOpenSet"),w,x,S=v(function(){w=h.pop(),x=w.id(),m.delete(x)},"popFromOpenSet"),C=v(function(q){return m.has(q)},"isInOpenSet");b(n,l),f[l]=0,d[l]=o(n);for(var E=0;h.size()>0;){if(S(),E++,x===c){for(var D=[],T=i,A=c,B=p[A];D.unshift(T),B!=null&&D.unshift(B),T=y[A],T!=null;)A=T.id(),B=p[A];return{found:!0,distance:f[x],path:this.spawn(D),steps:E}}g[x]=!0;for(var k=w._private.edges,L=0;L<k.length;L++){var R=k[L];if(this.hasElementWithId(R.id())&&!(s&&R.data("source")!==x)){var M=R.source(),I=R.target(),O=M.id()!==x?M:I,F=O.id();if(this.hasElementWithId(F)&&!g[F]){var K=f[x]+u(R);if(!C(F)){f[F]=K,d[F]=K+o(O),b(O,F),y[F]=w,p[F]=R;continue}K<f[F]&&(f[F]=K,d[F]=K+o(O),y[F]=w,p[F]=R)}}}}return{found:!1,distance:void 0,path:void 0,steps:E}},"aStar")},hf=et({weight:v(function(e){return 1},"weight"),directed:!1}),gf={floydWarshall:v(function(e){for(var r=this.cy(),a=hf(e),n=a.weight,i=a.directed,o=n,s=this.byGroup(),u=s.nodes,l=s.edges,c=u.length,f=c*c,d=v(function(X){return u.indexOf(X)},"indexOf"),g=v(function(X){return u[X]},"atIndex"),h=new Array(f),m=0;m<f;m++){var y=m%c,p=(m-y)/c;p===y?h[m]=0:h[m]=1/0}for(var b=new Array(f),w=new Array(f),x=0;x<l.length;x++){var S=l[x],C=S.source()[0],E=S.target()[0];if(C!==E){var D=d(C),T=d(E),A=D*c+T,B=o(S);if(h[A]>B&&(h[A]=B,b[A]=T,w[A]=S),!i){var k=T*c+D;!i&&h[k]>B&&(h[k]=B,b[k]=D,w[k]=S)}}}for(var L=0;L<c;L++)for(var R=0;R<c;R++)for(var M=R*c+L,I=0;I<c;I++){var O=R*c+I,F=L*c+I;h[M]+h[F]<h[O]&&(h[O]=h[M]+h[F],b[O]=b[M])}var K=v(function(X){return(fe(X)?r.filter(X):X)[0]},"getArgEle"),$=v(function(X){return d(K(X))},"indexOfArgEle"),q={distance:v(function(X,Z){var J=$(X),Q=$(Z);return h[J*c+Q]},"distance"),path:v(function(X,Z){var J=$(X),Q=$(Z),ee=g(J);if(J===Q)return ee.collection();if(b[J*c+Q]==null)return r.collection();var re=r.collection(),W=J,N;for(re.merge(ee);J!==Q;)W=J,J=b[J*c+Q],N=w[W*c+J],re.merge(N),re.merge(g(J));return re},"path")};return q},"floydWarshall")},pf=et({weight:v(function(e){return 1},"weight"),directed:!1,root:null}),yf={bellmanFord:v(function(e){var r=this,a=pf(e),n=a.weight,i=a.directed,o=a.root,s=n,u=this,l=this.cy(),c=this.byGroup(),f=c.edges,d=c.nodes,g=d.length,h=new zt,m=!1,y=[];o=l.collection(o)[0],f.unmergeBy(function(Ie){return Ie.isLoop()});for(var p=f.length,b=v(function(ve){var le=h.get(ve.id());return le||(le={},h.set(ve.id(),le)),le},"getInfo"),w=v(function(ve){return(fe(ve)?l.$(ve):ve)[0]},"getNodeFromTo"),x=v(function(ve){return b(w(ve)).dist},"distanceTo"),S=v(function(ve){for(var le=arguments.length>1&&arguments[1]!==void 0?arguments[1]:o,ye=w(ve),me=[],ge=ye;;){if(ge==null)return r.spawn();var be=b(ge),Ce=be.edge,De=be.pred;if(me.unshift(ge[0]),ge.same(le)&&me.length>0)break;Ce!=null&&me.unshift(Ce),ge=De}return u.spawn(me)},"pathTo"),C=0;C<g;C++){var E=d[C],D=b(E);E.same(o)?D.dist=0:D.dist=1/0,D.pred=null,D.edge=null}for(var T=!1,A=v(function(ve,le,ye,me,ge,be){var Ce=me.dist+be;Ce<ge.dist&&!ye.same(me.edge)&&(ge.dist=Ce,ge.pred=ve,ge.edge=ye,T=!0)},"checkForEdgeReplacement"),B=1;B<g;B++){T=!1;for(var k=0;k<p;k++){var L=f[k],R=L.source(),M=L.target(),I=s(L),O=b(R),F=b(M);A(R,M,L,O,F,I),i||A(M,R,L,F,O,I)}if(!T)break}if(T)for(var K=[],$=0;$<p;$++){var q=f[$],G=q.source(),X=q.target(),Z=s(q),J=b(G).dist,Q=b(X).dist;if(J+Z<Q||!i&&Q+Z<J)if(m||(Re("Graph contains a negative weight cycle for Bellman-Ford"),m=!0),e.findNegativeWeightCycles!==!1){var ee=[];J+Z<Q&&ee.push(G),!i&&Q+Z<J&&ee.push(X);for(var re=ee.length,W=0;W<re;W++){var N=ee[W],U=[N];U.push(b(N).edge);for(var te=b(N).pred;U.indexOf(te)===-1;)U.push(te),U.push(b(te).edge),te=b(te).pred;U=U.slice(U.indexOf(te));for(var oe=U[0].id(),ue=0,Se=2;Se<U.length;Se+=2)U[Se].id()<oe&&(oe=U[Se].id(),ue=Se);U=U.slice(ue).concat(U.slice(0,ue)),U.push(U[0]);var Le=U.map(function(Ie){return Ie.id()}).join(",");K.indexOf(Le)===-1&&(y.push(u.spawn(U)),K.push(Le))}}else break}return{distanceTo:x,pathTo:S,hasNegativeWeightCycle:m,negativeWeightCycles:y}},"bellmanFord")},mf=Math.sqrt(2),bf=v(function(e,r,a){a.length===0&&Ke("Karger-Stein must be run on a connected (sub)graph");for(var n=a[e],i=n[1],o=n[2],s=r[i],u=r[o],l=a,c=l.length-1;c>=0;c--){var f=l[c],d=f[1],g=f[2];(r[d]===s&&r[g]===u||r[d]===u&&r[g]===s)&&l.splice(c,1)}for(var h=0;h<l.length;h++){var m=l[h];m[1]===u?(l[h]=m.slice(),l[h][1]=s):m[2]===u&&(l[h]=m.slice(),l[h][2]=s)}for(var y=0;y<r.length;y++)r[y]===u&&(r[y]=s);return l},"collapse"),ai=v(function(e,r,a,n){for(;a>n;){var i=Math.floor(Math.random()*r.length);r=bf(i,e,r),a--}return r},"contractUntil"),wf={kargerStein:v(function(){var e=this,r=this.byGroup(),a=r.nodes,n=r.edges;n.unmergeBy(function(F){return F.isLoop()});var i=a.length,o=n.length,s=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2)),u=Math.floor(i/mf);if(i<2){Ke("At least 2 nodes are required for Karger-Stein algorithm");return}for(var l=[],c=0;c<o;c++){var f=n[c];l.push([c,a.indexOf(f.source()),a.indexOf(f.target())])}for(var d=1/0,g=[],h=new Array(i),m=new Array(i),y=new Array(i),p=v(function(K,$){for(var q=0;q<i;q++)$[q]=K[q]},"copyNodesMap"),b=0;b<=s;b++){for(var w=0;w<i;w++)m[w]=w;var x=ai(m,l.slice(),i,u),S=x.slice();p(m,y);var C=ai(m,x,u,2),E=ai(y,S,u,2);C.length<=E.length&&C.length<d?(d=C.length,g=C,p(m,h)):E.length<=C.length&&E.length<d&&(d=E.length,g=E,p(y,h))}for(var D=this.spawn(g.map(function(F){return n[F[0]]})),T=this.spawn(),A=this.spawn(),B=h[0],k=0;k<h.length;k++){var L=h[k],R=a[k];L===B?T.merge(R):A.merge(R)}var M=v(function(K){var $=e.spawn();return K.forEach(function(q){$.merge(q),q.connectedEdges().forEach(function(G){e.contains(G)&&!D.contains(G)&&$.merge(G)})}),$},"constructComponent"),I=[M(T),M(A)],O={cut:D,components:I,partition1:T,partition2:A};return O},"kargerStein")},xf=v(function(e){return{x:e.x,y:e.y}},"copyPosition"),Nn=v(function(e,r,a){return{x:e.x*r+a.x,y:e.y*r+a.y}},"modelToRenderedPosition"),El=v(function(e,r,a){return{x:(e.x-a.x)/r,y:(e.y-a.y)/r}},"renderedToModelPosition"),Vr=v(function(e){return{x:e[0],y:e[1]}},"array2point"),Ef=v(function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=1/0,i=r;i<a;i++){var o=e[i];isFinite(o)&&(n=Math.min(o,n))}return n},"min"),Cf=v(function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=-1/0,i=r;i<a;i++){var o=e[i];isFinite(o)&&(n=Math.max(o,n))}return n},"max"),Tf=v(function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=0,i=0,o=r;o<a;o++){var s=e[o];isFinite(s)&&(n+=s,i++)}return n/i},"mean"),Sf=v(function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,o=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0;n?e=e.slice(r,a):(a<e.length&&e.splice(a,e.length-a),r>0&&e.splice(0,r));for(var s=0,u=e.length-1;u>=0;u--){var l=e[u];o?isFinite(l)||(e[u]=-1/0,s++):e.splice(u,1)}i&&e.sort(function(d,g){return d-g});var c=e.length,f=Math.floor(c/2);return c%2!==0?e[f+1+s]:(e[f-1+s]+e[f+s])/2},"median"),Df=v(function(e){return Math.PI*e/180},"deg2rad"),Qa=v(function(e,r){return Math.atan2(r,e)-Math.PI/2},"getAngleFromDisp"),Xi=Math.log2||function(t){return Math.log(t)/Math.log(2)},Cl=v(function(e){return e>0?1:e<0?-1:0},"signum"),Er=v(function(e,r){return Math.sqrt(hr(e,r))},"dist"),hr=v(function(e,r){var a=r.x-e.x,n=r.y-e.y;return a*a+n*n},"sqdist"),Pf=v(function(e){for(var r=e.length,a=0,n=0;n<r;n++)a+=e[n];for(var i=0;i<r;i++)e[i]=e[i]/a;return e},"inPlaceSumNormalize"),je=v(function(e,r,a,n){return(1-n)*(1-n)*e+2*(1-n)*n*r+n*n*a},"qbezierAt"),Kr=v(function(e,r,a,n){return{x:je(e.x,r.x,a.x,n),y:je(e.y,r.y,a.y,n)}},"qbezierPtAt"),kf=v(function(e,r,a,n){var i={x:r.x-e.x,y:r.y-e.y},o=Er(e,r),s={x:i.x/o,y:i.y/o};return a=a??0,n=n??a*o,{x:e.x+s.x*n,y:e.y+s.y*n}},"lineAt"),Sa=v(function(e,r,a){return Math.max(e,Math.min(a,r))},"bound"),bt=v(function(e){if(e==null)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(e.x1!=null&&e.y1!=null){if(e.x2!=null&&e.y2!=null&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(e.w!=null&&e.h!=null&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},"makeBoundingBox"),Bf=v(function(e){return{x1:e.x1,x2:e.x2,w:e.w,y1:e.y1,y2:e.y2,h:e.h}},"copyBoundingBox"),Af=v(function(e){e.x1=1/0,e.y1=1/0,e.x2=-1/0,e.y2=-1/0,e.w=0,e.h=0},"clearBoundingBox"),Rf=v(function(e,r,a){return{x1:e.x1+r,x2:e.x2+r,y1:e.y1+a,y2:e.y2+a,w:e.w,h:e.h}},"shiftBoundingBox"),Tl=v(function(e,r){e.x1=Math.min(e.x1,r.x1),e.x2=Math.max(e.x2,r.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,r.y1),e.y2=Math.max(e.y2,r.y2),e.h=e.y2-e.y1},"updateBoundingBox"),Lf=v(function(e,r,a){e.x1=Math.min(e.x1,r),e.x2=Math.max(e.x2,r),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,a),e.y2=Math.max(e.y2,a),e.h=e.y2-e.y1},"expandBoundingBoxByPoint"),ln=v(function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.x1-=r,e.x2+=r,e.y1-=r,e.y2+=r,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},"expandBoundingBox"),un=v(function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[0],a,n,i,o;if(r.length===1)a=n=i=o=r[0];else if(r.length===2)a=i=r[0],o=n=r[1];else if(r.length===4){var s=We(r,4);a=s[0],n=s[1],i=s[2],o=s[3]}return e.x1-=o,e.x2+=n,e.y1-=a,e.y2+=i,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},"expandBoundingBoxSides"),Go=v(function(e,r){e.x1=r.x1,e.y1=r.y1,e.x2=r.x2,e.y2=r.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},"assignBoundingBox"),Yi=v(function(e,r){return!(e.x1>r.x2||r.x1>e.x2||e.x2<r.x1||r.x2<e.x1||e.y2<r.y1||r.y2<e.y1||e.y1>r.y2||r.y1>e.y2)},"boundingBoxesIntersect"),_r=v(function(e,r,a){return e.x1<=r&&r<=e.x2&&e.y1<=a&&a<=e.y2},"inBoundingBox"),If=v(function(e,r){return _r(e,r.x,r.y)},"pointInBoundingBox"),Sl=v(function(e,r){return _r(e,r.x1,r.y1)&&_r(e,r.x2,r.y2)},"boundingBoxInBoundingBox"),Dl=v(function(e,r,a,n,i,o,s){var u=arguments.length>7&&arguments[7]!==void 0?arguments[7]:"auto",l=u==="auto"?Cr(i,o):u,c=i/2,f=o/2;l=Math.min(l,c,f);var d=l!==c,g=l!==f,h;if(d){var m=a-c+l-s,y=n-f-s,p=a+c-l+s,b=y;if(h=tr(e,r,a,n,m,y,p,b,!1),h.length>0)return h}if(g){var w=a+c+s,x=n-f+l-s,S=w,C=n+f-l+s;if(h=tr(e,r,a,n,w,x,S,C,!1),h.length>0)return h}if(d){var E=a-c+l-s,D=n+f+s,T=a+c-l+s,A=D;if(h=tr(e,r,a,n,E,D,T,A,!1),h.length>0)return h}if(g){var B=a-c-s,k=n-f+l-s,L=B,R=n+f-l+s;if(h=tr(e,r,a,n,B,k,L,R,!1),h.length>0)return h}var M;{var I=a-c+l,O=n-f+l;if(M=fa(e,r,a,n,I,O,l+s),M.length>0&&M[0]<=I&&M[1]<=O)return[M[0],M[1]]}{var F=a+c-l,K=n-f+l;if(M=fa(e,r,a,n,F,K,l+s),M.length>0&&M[0]>=F&&M[1]<=K)return[M[0],M[1]]}{var $=a+c-l,q=n+f-l;if(M=fa(e,r,a,n,$,q,l+s),M.length>0&&M[0]>=$&&M[1]>=q)return[M[0],M[1]]}{var G=a-c+l,X=n+f-l;if(M=fa(e,r,a,n,G,X,l+s),M.length>0&&M[0]<=G&&M[1]>=X)return[M[0],M[1]]}return[]},"roundRectangleIntersectLine"),Mf=v(function(e,r,a,n,i,o,s){var u=s,l=Math.min(a,i),c=Math.max(a,i),f=Math.min(n,o),d=Math.max(n,o);return l-u<=e&&e<=c+u&&f-u<=r&&r<=d+u},"inLineVicinity"),Of=v(function(e,r,a,n,i,o,s,u,l){var c={x1:Math.min(a,s,i)-l,x2:Math.max(a,s,i)+l,y1:Math.min(n,u,o)-l,y2:Math.max(n,u,o)+l};return!(e<c.x1||e>c.x2||r<c.y1||r>c.y2)},"inBezierVicinity"),Ff=v(function(e,r,a,n){a-=n;var i=r*r-4*e*a;if(i<0)return[];var o=Math.sqrt(i),s=2*e,u=(-r+o)/s,l=(-r-o)/s;return[u,l]},"solveQuadratic"),Nf=v(function(e,r,a,n,i){var o=1e-5;e===0&&(e=o),r/=e,a/=e,n/=e;var s,u,l,c,f,d,g,h;if(u=(3*a-r*r)/9,l=-(27*n)+r*(9*a-2*(r*r)),l/=54,s=u*u*u+l*l,i[1]=0,g=r/3,s>0){f=l+Math.sqrt(s),f=f<0?-Math.pow(-f,1/3):Math.pow(f,1/3),d=l-Math.sqrt(s),d=d<0?-Math.pow(-d,1/3):Math.pow(d,1/3),i[0]=-g+f+d,g+=(f+d)/2,i[4]=i[2]=-g,g=Math.sqrt(3)*(-d+f)/2,i[3]=g,i[5]=-g;return}if(i[5]=i[3]=0,s===0){h=l<0?-Math.pow(-l,1/3):Math.pow(l,1/3),i[0]=-g+2*h,i[4]=i[2]=-(h+g);return}u=-u,c=u*u*u,c=Math.acos(l/Math.sqrt(c)),h=2*Math.sqrt(u),i[0]=-g+h*Math.cos(c/3),i[2]=-g+h*Math.cos((c+2*Math.PI)/3),i[4]=-g+h*Math.cos((c+4*Math.PI)/3)},"solveCubic"),zf=v(function(e,r,a,n,i,o,s,u){var l=1*a*a-4*a*i+2*a*s+4*i*i-4*i*s+s*s+n*n-4*n*o+2*n*u+4*o*o-4*o*u+u*u,c=9*a*i-3*a*a-3*a*s-6*i*i+3*i*s+9*n*o-3*n*n-3*n*u-6*o*o+3*o*u,f=3*a*a-6*a*i+a*s-a*e+2*i*i+2*i*e-s*e+3*n*n-6*n*o+n*u-n*r+2*o*o+2*o*r-u*r,d=1*a*i-a*a+a*e-i*e+n*o-n*n+n*r-o*r,g=[];Nf(l,c,f,d,g);for(var h=1e-7,m=[],y=0;y<6;y+=2)Math.abs(g[y+1])<h&&g[y]>=0&&g[y]<=1&&m.push(g[y]);m.push(1),m.push(0);for(var p=-1,b,w,x,S=0;S<m.length;S++)b=Math.pow(1-m[S],2)*a+2*(1-m[S])*m[S]*i+m[S]*m[S]*s,w=Math.pow(1-m[S],2)*n+2*(1-m[S])*m[S]*o+m[S]*m[S]*u,x=Math.pow(b-e,2)+Math.pow(w-r,2),p>=0?x<p&&(p=x):p=x;return p},"sqdistToQuadraticBezier"),Vf=v(function(e,r,a,n,i,o){var s=[e-a,r-n],u=[i-a,o-n],l=u[0]*u[0]+u[1]*u[1],c=s[0]*s[0]+s[1]*s[1],f=s[0]*u[0]+s[1]*u[1],d=f*f/l;return f<0?c:d>l?(e-i)*(e-i)+(r-o)*(r-o):c-d},"sqdistToFiniteLine"),yt=v(function(e,r,a){for(var n,i,o,s,u,l=0,c=0;c<a.length/2;c++)if(n=a[c*2],i=a[c*2+1],c+1<a.length/2?(o=a[(c+1)*2],s=a[(c+1)*2+1]):(o=a[(c+1-a.length/2)*2],s=a[(c+1-a.length/2)*2+1]),!(n==e&&o==e))if(n>=e&&e>=o||n<=e&&e<=o)u=(e-n)/(o-n)*(s-i)+i,u>r&&l++;else continue;return l%2!==0},"pointInsidePolygonPoints"),Ut=v(function(e,r,a,n,i,o,s,u,l){var c=new Array(a.length),f;u[0]!=null?(f=Math.atan(u[1]/u[0]),u[0]<0?f=f+Math.PI/2:f=-f-Math.PI/2):f=u;for(var d=Math.cos(-f),g=Math.sin(-f),h=0;h<c.length/2;h++)c[h*2]=o/2*(a[h*2]*d-a[h*2+1]*g),c[h*2+1]=s/2*(a[h*2+1]*d+a[h*2]*g),c[h*2]+=n,c[h*2+1]+=i;var m;if(l>0){var y=Dn(c,-l);m=Sn(y)}else m=c;return yt(e,r,m)},"pointInsidePolygon"),qf=v(function(e,r,a,n,i,o,s,u){for(var l=new Array(a.length*2),c=0;c<u.length;c++){var f=u[c];l[c*4+0]=f.startX,l[c*4+1]=f.startY,l[c*4+2]=f.stopX,l[c*4+3]=f.stopY;var d=Math.pow(f.cx-e,2)+Math.pow(f.cy-r,2);if(d<=Math.pow(f.radius,2))return!0}return yt(e,r,l)},"pointInsideRoundPolygon"),Sn=v(function(e){for(var r=new Array(e.length/2),a,n,i,o,s,u,l,c,f=0;f<e.length/4;f++){a=e[f*4],n=e[f*4+1],i=e[f*4+2],o=e[f*4+3],f<e.length/4-1?(s=e[(f+1)*4],u=e[(f+1)*4+1],l=e[(f+1)*4+2],c=e[(f+1)*4+3]):(s=e[0],u=e[1],l=e[2],c=e[3]);var d=tr(a,n,i,o,s,u,l,c,!0);r[f*2]=d[0],r[f*2+1]=d[1]}return r},"joinLines"),Dn=v(function(e,r){for(var a=new Array(e.length*2),n,i,o,s,u=0;u<e.length/2;u++){n=e[u*2],i=e[u*2+1],u<e.length/2-1?(o=e[(u+1)*2],s=e[(u+1)*2+1]):(o=e[0],s=e[1]);var l=s-i,c=-(o-n),f=Math.sqrt(l*l+c*c),d=l/f,g=c/f;a[u*4]=n+d*r,a[u*4+1]=i+g*r,a[u*4+2]=o+d*r,a[u*4+3]=s+g*r}return a},"expandPolygon"),Kf=v(function(e,r,a,n,i,o){var s=a-e,u=n-r;s/=i,u/=o;var l=Math.sqrt(s*s+u*u),c=l-1;if(c<0)return[];var f=c/l;return[(a-e)*f+e,(n-r)*f+r]},"intersectLineEllipse"),wr=v(function(e,r,a,n,i,o,s){return e-=i,r-=o,e/=a/2+s,r/=n/2+s,e*e+r*r<=1},"checkInEllipse"),fa=v(function(e,r,a,n,i,o,s){var u=[a-e,n-r],l=[e-i,r-o],c=u[0]*u[0]+u[1]*u[1],f=2*(l[0]*u[0]+l[1]*u[1]),d=l[0]*l[0]+l[1]*l[1]-s*s,g=f*f-4*c*d;if(g<0)return[];var h=(-f+Math.sqrt(g))/(2*c),m=(-f-Math.sqrt(g))/(2*c),y=Math.min(h,m),p=Math.max(h,m),b=[];if(y>=0&&y<=1&&b.push(y),p>=0&&p<=1&&b.push(p),b.length===0)return[];var w=b[0]*u[0]+e,x=b[0]*u[1]+r;if(b.length>1){if(b[0]==b[1])return[w,x];var S=b[1]*u[0]+e,C=b[1]*u[1]+r;return[w,x,S,C]}else return[w,x]},"intersectLineCircle"),ni=v(function(e,r,a){return r<=e&&e<=a||a<=e&&e<=r?e:e<=r&&r<=a||a<=r&&r<=e?r:a},"midOfThree"),tr=v(function(e,r,a,n,i,o,s,u,l){var c=e-i,f=a-e,d=s-i,g=r-o,h=n-r,m=u-o,y=d*g-m*c,p=f*g-h*c,b=m*f-d*h;if(b!==0){var w=y/b,x=p/b,S=.001,C=0-S,E=1+S;return C<=w&&w<=E&&C<=x&&x<=E?[e+w*f,r+w*h]:l?[e+w*f,r+w*h]:[]}else return y===0||p===0?ni(e,a,s)===s?[s,u]:ni(e,a,i)===i?[i,o]:ni(i,s,a)===a?[a,n]:[]:[]},"finiteLinesIntersect"),Da=v(function(e,r,a,n,i,o,s,u){var l=[],c,f=new Array(a.length),d=!0;o==null&&(d=!1);var g;if(d){for(var h=0;h<f.length/2;h++)f[h*2]=a[h*2]*o+n,f[h*2+1]=a[h*2+1]*s+i;if(u>0){var m=Dn(f,-u);g=Sn(m)}else g=f}else g=a;for(var y,p,b,w,x=0;x<g.length/2;x++)y=g[x*2],p=g[x*2+1],x<g.length/2-1?(b=g[(x+1)*2],w=g[(x+1)*2+1]):(b=g[0],w=g[1]),c=tr(e,r,n,i,y,p,b,w),c.length!==0&&l.push(c[0],c[1]);return l},"polygonIntersectLine"),Gf=v(function(e,r,a,n,i,o,s,u,l){var c=[],f,d=new Array(a.length*2);l.forEach(function(b,w){w===0?(d[d.length-2]=b.startX,d[d.length-1]=b.startY):(d[w*4-2]=b.startX,d[w*4-1]=b.startY),d[w*4]=b.stopX,d[w*4+1]=b.stopY,f=fa(e,r,n,i,b.cx,b.cy,b.radius),f.length!==0&&c.push(f[0],f[1])});for(var g=0;g<d.length/4;g++)f=tr(e,r,n,i,d[g*4],d[g*4+1],d[g*4+2],d[g*4+3],!1),f.length!==0&&c.push(f[0],f[1]);if(c.length>2){for(var h=[c[0],c[1]],m=Math.pow(h[0]-e,2)+Math.pow(h[1]-r,2),y=1;y<c.length/2;y++){var p=Math.pow(c[y*2]-e,2)+Math.pow(c[y*2+1]-r,2);p<=m&&(h[0]=c[y*2],h[1]=c[y*2+1],m=p)}return h}return c},"roundPolygonIntersectLine"),Ja=v(function(e,r,a){var n=[e[0]-r[0],e[1]-r[1]],i=Math.sqrt(n[0]*n[0]+n[1]*n[1]),o=(i-a)/i;return o<0&&(o=1e-5),[r[0]+o*n[0],r[1]+o*n[1]]},"shortenIntersection"),dt=v(function(e,r){var a=hi(e,r);return a=Pl(a),a},"generateUnitNgonPointsFitToSquare"),Pl=v(function(e){for(var r,a,n=e.length/2,i=1/0,o=1/0,s=-1/0,u=-1/0,l=0;l<n;l++)r=e[2*l],a=e[2*l+1],i=Math.min(i,r),s=Math.max(s,r),o=Math.min(o,a),u=Math.max(u,a);for(var c=2/(s-i),f=2/(u-o),d=0;d<n;d++)r=e[2*d]=e[2*d]*c,a=e[2*d+1]=e[2*d+1]*f,i=Math.min(i,r),s=Math.max(s,r),o=Math.min(o,a),u=Math.max(u,a);if(o<-1)for(var g=0;g<n;g++)a=e[2*g+1]=e[2*g+1]+(-1-o);return e},"fitPolygonToSquare"),hi=v(function(e,r){var a=1/e*2*Math.PI,n=e%2===0?Math.PI/2+a/2:Math.PI/2;n+=r;for(var i=new Array(e*2),o,s=0;s<e;s++)o=s*a+n,i[2*s]=Math.cos(o),i[2*s+1]=Math.sin(-o);return i},"generateUnitNgonPoints"),Cr=v(function(e,r){return Math.min(e/4,r/4,8)},"getRoundRectangleRadius"),kl=v(function(e,r){return Math.min(e/10,r/10,8)},"getRoundPolygonRadius"),Zi=v(function(){return 8},"getCutRectangleCornerLength"),Hf=v(function(e,r,a){return[e-2*r+a,2*(r-e),e]},"bezierPtsToQuadCoeff"),gi=v(function(e,r){return{heightOffset:Math.min(15,.05*r),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}},"getBarrelCurveConstants"),Wf=et({dampingFactor:.8,precision:1e-6,iterations:200,weight:v(function(e){return 1},"weight")}),$f={pageRank:v(function(e){for(var r=Wf(e),a=r.dampingFactor,n=r.precision,i=r.iterations,o=r.weight,s=this._private.cy,u=this.byGroup(),l=u.nodes,c=u.edges,f=l.length,d=f*f,g=c.length,h=new Array(d),m=new Array(f),y=(1-a)/f,p=0;p<f;p++){for(var b=0;b<f;b++){var w=p*f+b;h[w]=0}m[p]=0}for(var x=0;x<g;x++){var S=c[x],C=S.data("source"),E=S.data("target");if(C!==E){var D=l.indexOfId(C),T=l.indexOfId(E),A=o(S),B=T*f+D;h[B]+=A,m[D]+=A}}for(var k=1/f+y,L=0;L<f;L++)if(m[L]===0)for(var R=0;R<f;R++){var M=R*f+L;h[M]=k}else for(var I=0;I<f;I++){var O=I*f+L;h[O]=h[O]/m[L]+y}for(var F=new Array(f),K=new Array(f),$,q=0;q<f;q++)F[q]=1;for(var G=0;G<i;G++){for(var X=0;X<f;X++)K[X]=0;for(var Z=0;Z<f;Z++)for(var J=0;J<f;J++){var Q=Z*f+J;K[Z]+=h[Q]*F[J]}Pf(K),$=F,F=K,K=$;for(var ee=0,re=0;re<f;re++){var W=$[re]-F[re];ee+=W*W}if(ee<n)break}var N={rank:v(function(te){return te=s.collection(te)[0],F[l.indexOf(te)]},"rank")};return N},"pageRank")},Ho=et({root:null,weight:v(function(e){return 1},"weight"),directed:!1,alpha:0}),Gr={degreeCentralityNormalized:v(function(e){e=Ho(e);var r=this.cy(),a=this.nodes(),n=a.length;if(e.directed){for(var c={},f={},d=0,g=0,h=0;h<n;h++){var m=a[h],y=m.id();e.root=m;var p=this.degreeCentrality(e);d<p.indegree&&(d=p.indegree),g<p.outdegree&&(g=p.outdegree),c[y]=p.indegree,f[y]=p.outdegree}return{indegree:v(function(w){return d==0?0:(fe(w)&&(w=r.filter(w)),c[w.id()]/d)},"indegree"),outdegree:v(function(w){return g===0?0:(fe(w)&&(w=r.filter(w)),f[w.id()]/g)},"outdegree")}}else{for(var i={},o=0,s=0;s<n;s++){var u=a[s];e.root=u;var l=this.degreeCentrality(e);o<l.degree&&(o=l.degree),i[u.id()]=l.degree}return{degree:v(function(w){return o===0?0:(fe(w)&&(w=r.filter(w)),i[w.id()]/o)},"degree")}}},"degreeCentralityNormalized"),degreeCentrality:v(function(e){e=Ho(e);var r=this.cy(),a=this,n=e,i=n.root,o=n.weight,s=n.directed,u=n.alpha;if(i=r.collection(i)[0],s){for(var g=i.connectedEdges(),h=g.filter(function(C){return C.target().same(i)&&a.has(C)}),m=g.filter(function(C){return C.source().same(i)&&a.has(C)}),y=h.length,p=m.length,b=0,w=0,x=0;x<h.length;x++)b+=o(h[x]);for(var S=0;S<m.length;S++)w+=o(m[S]);return{indegree:Math.pow(y,1-u)*Math.pow(b,u),outdegree:Math.pow(p,1-u)*Math.pow(w,u)}}else{for(var l=i.connectedEdges().intersection(a),c=l.length,f=0,d=0;d<l.length;d++)f+=o(l[d]);return{degree:Math.pow(c,1-u)*Math.pow(f,u)}}},"degreeCentrality")};Gr.dc=Gr.degreeCentrality;Gr.dcn=Gr.degreeCentralityNormalised=Gr.degreeCentralityNormalized;var Wo=et({harmonic:!0,weight:v(function(){return 1},"weight"),directed:!1,root:null}),Hr={closenessCentralityNormalized:v(function(e){for(var r=Wo(e),a=r.harmonic,n=r.weight,i=r.directed,o=this.cy(),s={},u=0,l=this.nodes(),c=this.floydWarshall({weight:n,directed:i}),f=0;f<l.length;f++){for(var d=0,g=l[f],h=0;h<l.length;h++)if(f!==h){var m=c.distance(g,l[h]);a?d+=1/m:d+=m}a||(d=1/d),u<d&&(u=d),s[g.id()]=d}return{closeness:v(function(p){return u==0?0:(fe(p)?p=o.filter(p)[0].id():p=p.id(),s[p]/u)},"closeness")}},"closenessCentralityNormalized"),closenessCentrality:v(function(e){var r=Wo(e),a=r.root,n=r.weight,i=r.directed,o=r.harmonic;a=this.filter(a)[0];for(var s=this.dijkstra({root:a,weight:n,directed:i}),u=0,l=this.nodes(),c=0;c<l.length;c++){var f=l[c];if(!f.same(a)){var d=s.distanceTo(f);o?u+=1/d:u+=d}}return o?u:1/u},"closenessCentrality")};Hr.cc=Hr.closenessCentrality;Hr.ccn=Hr.closenessCentralityNormalised=Hr.closenessCentralityNormalized;var Uf=et({weight:null,directed:!1}),pi={betweennessCentrality:v(function(e){for(var r=Uf(e),a=r.directed,n=r.weight,i=n!=null,o=this.cy(),s=this.nodes(),u={},l={},c=0,f={set:v(function(w,x){l[w]=x,x>c&&(c=x)},"set"),get:v(function(w){return l[w]},"get")},d=0;d<s.length;d++){var g=s[d],h=g.id();a?u[h]=g.outgoers().nodes():u[h]=g.openNeighborhood().nodes(),f.set(h,0)}for(var m=v(function(w){for(var x=s[w].id(),S=[],C={},E={},D={},T=new Na(function(J,Q){return D[J]-D[Q]}),A=0;A<s.length;A++){var B=s[A].id();C[B]=[],E[B]=0,D[B]=1/0}for(E[x]=1,D[x]=0,T.push(x);!T.empty();){var k=T.pop();if(S.push(k),i)for(var L=0;L<u[k].length;L++){var R=u[k][L],M=o.getElementById(k),I=void 0;M.edgesTo(R).length>0?I=M.edgesTo(R)[0]:I=R.edgesTo(M)[0];var O=n(I);R=R.id(),D[R]>D[k]+O&&(D[R]=D[k]+O,T.nodes.indexOf(R)<0?T.push(R):T.updateItem(R),E[R]=0,C[R]=[]),D[R]==D[k]+O&&(E[R]=E[R]+E[k],C[R].push(k))}else for(var F=0;F<u[k].length;F++){var K=u[k][F].id();D[K]==1/0&&(T.push(K),D[K]=D[k]+1),D[K]==D[k]+1&&(E[K]=E[K]+E[k],C[K].push(k))}}for(var $={},q=0;q<s.length;q++)$[s[q].id()]=0;for(;S.length>0;){for(var G=S.pop(),X=0;X<C[G].length;X++){var Z=C[G][X];$[Z]=$[Z]+E[Z]/E[G]*(1+$[G])}G!=s[w].id()&&f.set(G,f.get(G)+$[G])}},"_loop"),y=0;y<s.length;y++)m(y);var p={betweenness:v(function(w){var x=o.collection(w).id();return f.get(x)},"betweenness"),betweennessNormalized:v(function(w){if(c==0)return 0;var x=o.collection(w).id();return f.get(x)/c},"betweennessNormalized")};return p.betweennessNormalised=p.betweennessNormalized,p},"betweennessCentrality")};pi.bc=pi.betweennessCentrality;var _f=et({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(t){return 1}]}),Xf=v(function(e){return _f(e)},"setOptions"),Yf=v(function(e,r){for(var a=0,n=0;n<r.length;n++)a+=r[n](e);return a},"getSimilarity"),Zf=v(function(e,r,a){for(var n=0;n<r;n++)e[n*r+n]=a},"addLoops"),Bl=v(function(e,r){for(var a,n=0;n<r;n++){a=0;for(var i=0;i<r;i++)a+=e[i*r+n];for(var o=0;o<r;o++)e[o*r+n]=e[o*r+n]/a}},"normalize"),Qf=v(function(e,r,a){for(var n=new Array(a*a),i=0;i<a;i++){for(var o=0;o<a;o++)n[i*a+o]=0;for(var s=0;s<a;s++)for(var u=0;u<a;u++)n[i*a+u]+=e[i*a+s]*r[s*a+u]}return n},"mmult"),Jf=v(function(e,r,a){for(var n=e.slice(0),i=1;i<a;i++)e=Qf(e,n,r);return e},"expand"),jf=v(function(e,r,a){for(var n=new Array(r*r),i=0;i<r*r;i++)n[i]=Math.pow(e[i],a);return Bl(n,r),n},"inflate"),ed=v(function(e,r,a,n){for(var i=0;i<a;i++){var o=Math.round(e[i]*Math.pow(10,n))/Math.pow(10,n),s=Math.round(r[i]*Math.pow(10,n))/Math.pow(10,n);if(o!==s)return!1}return!0},"hasConverged"),td=v(function(e,r,a,n){for(var i=[],o=0;o<r;o++){for(var s=[],u=0;u<r;u++)Math.round(e[o*r+u]*1e3)/1e3>0&&s.push(a[u]);s.length!==0&&i.push(n.collection(s))}return i},"assign"),rd=v(function(e,r){for(var a=0;a<e.length;a++)if(!r[a]||e[a].id()!==r[a].id())return!1;return!0},"isDuplicate"),ad=v(function(e){for(var r=0;r<e.length;r++)for(var a=0;a<e.length;a++)r!=a&&rd(e[r],e[a])&&e.splice(a,1);return e},"removeDuplicates"),$o=v(function(e){for(var r=this.nodes(),a=this.edges(),n=this.cy(),i=Xf(e),o={},s=0;s<r.length;s++)o[r[s].id()]=s;for(var u=r.length,l=u*u,c=new Array(l),f,d=0;d<l;d++)c[d]=0;for(var g=0;g<a.length;g++){var h=a[g],m=o[h.source().id()],y=o[h.target().id()],p=Yf(h,i.attributes);c[m*u+y]+=p,c[y*u+m]+=p}Zf(c,u,i.multFactor),Bl(c,u);for(var b=!0,w=0;b&&w<i.maxIterations;)b=!1,f=Jf(c,u,i.expandFactor),c=jf(f,u,i.inflateFactor),ed(c,f,l,4)||(b=!0),w++;var x=td(c,u,r,n);return x=ad(x),x},"markovClustering"),nd={markovClustering:$o,mcl:$o},id=v(function(e){return e},"identity"),Al=v(function(e,r){return Math.abs(r-e)},"absDiff"),Uo=v(function(e,r,a){return e+Al(r,a)},"addAbsDiff"),_o=v(function(e,r,a){return e+Math.pow(a-r,2)},"addSquaredDiff"),od=v(function(e){return Math.sqrt(e)},"sqrt"),sd=v(function(e,r,a){return Math.max(e,Al(r,a))},"maxAbsDiff"),oa=v(function(e,r,a,n,i){for(var o=arguments.length>5&&arguments[5]!==void 0?arguments[5]:id,s=n,u,l,c=0;c<e;c++)u=r(c),l=a(c),s=i(s,u,l);return o(s)},"getDistance"),Xr={euclidean:v(function(e,r,a){return e>=2?oa(e,r,a,0,_o,od):oa(e,r,a,0,Uo)},"euclidean"),squaredEuclidean:v(function(e,r,a){return oa(e,r,a,0,_o)},"squaredEuclidean"),manhattan:v(function(e,r,a){return oa(e,r,a,0,Uo)},"manhattan"),max:v(function(e,r,a){return oa(e,r,a,-1/0,sd)},"max")};Xr["squared-euclidean"]=Xr.squaredEuclidean;Xr.squaredeuclidean=Xr.squaredEuclidean;function za(t,e,r,a,n,i){var o;return Ge(t)?o=t:o=Xr[t]||Xr.euclidean,e===0&&Ge(t)?o(n,i):o(e,r,a,n,i)}v(za,"clusteringDistance");var ld=et({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),Qi=v(function(e){return ld(e)},"setOptions"),Pn=v(function(e,r,a,n,i){var o=i!=="kMedoids",s=o?function(f){return a[f]}:function(f){return n[f](a)},u=v(function(d){return n[d](r)},"getQ"),l=a,c=r;return za(e,n.length,s,u,l,c)},"getDist"),ii=v(function(e,r,a){for(var n=a.length,i=new Array(n),o=new Array(n),s=new Array(r),u=null,l=0;l<n;l++)i[l]=e.min(a[l]).value,o[l]=e.max(a[l]).value;for(var c=0;c<r;c++){u=[];for(var f=0;f<n;f++)u[f]=Math.random()*(o[f]-i[f])+i[f];s[c]=u}return s},"randomCentroids"),Rl=v(function(e,r,a,n,i){for(var o=1/0,s=0,u=0;u<r.length;u++){var l=Pn(a,e,r[u],n,i);l<o&&(o=l,s=u)}return s},"classify"),Ll=v(function(e,r,a){for(var n=[],i=null,o=0;o<r.length;o++)i=r[o],a[i.id()]===e&&n.push(i);return n},"buildCluster"),ud=v(function(e,r,a){return Math.abs(r-e)<=a},"haveValuesConverged"),vd=v(function(e,r,a){for(var n=0;n<e.length;n++)for(var i=0;i<e[n].length;i++){var o=Math.abs(e[n][i]-r[n][i]);if(o>a)return!1}return!0},"haveMatricesConverged"),cd=v(function(e,r,a){for(var n=0;n<a;n++)if(e===r[n])return!0;return!1},"seenBefore"),Xo=v(function(e,r){var a=new Array(r);if(e.length<50)for(var n=0;n<r;n++){for(var i=e[Math.floor(Math.random()*e.length)];cd(i,a,n);)i=e[Math.floor(Math.random()*e.length)];a[n]=i}else for(var o=0;o<r;o++)a[o]=e[Math.floor(Math.random()*e.length)];return a},"randomMedoids"),Yo=v(function(e,r,a){for(var n=0,i=0;i<r.length;i++)n+=Pn("manhattan",r[i],e,a,"kMedoids");return n},"findCost"),fd=v(function(e){var r=this.cy(),a=this.nodes(),n=null,i=Qi(e),o=new Array(i.k),s={},u;i.testMode?typeof i.testCentroids=="number"?(i.testCentroids,u=ii(a,i.k,i.attributes)):$e(i.testCentroids)==="object"?u=i.testCentroids:u=ii(a,i.k,i.attributes):u=ii(a,i.k,i.attributes);for(var l=!0,c=0;l&&c<i.maxIterations;){for(var f=0;f<a.length;f++)n=a[f],s[n.id()]=Rl(n,u,i.distance,i.attributes,"kMeans");l=!1;for(var d=0;d<i.k;d++){var g=Ll(d,a,s);if(g.length!==0){for(var h=i.attributes.length,m=u[d],y=new Array(h),p=new Array(h),b=0;b<h;b++){p[b]=0;for(var w=0;w<g.length;w++)n=g[w],p[b]+=i.attributes[b](n);y[b]=p[b]/g.length,ud(y[b],m[b],i.sensitivityThreshold)||(l=!0)}u[d]=y,o[d]=r.collection(g)}}c++}return o},"kMeans"),dd=v(function(e){var r=this.cy(),a=this.nodes(),n=null,i=Qi(e),o=new Array(i.k),s,u={},l,c=new Array(i.k);i.testMode?typeof i.testCentroids=="number"||($e(i.testCentroids)==="object"?s=i.testCentroids:s=Xo(a,i.k)):s=Xo(a,i.k);for(var f=!0,d=0;f&&d<i.maxIterations;){for(var g=0;g<a.length;g++)n=a[g],u[n.id()]=Rl(n,s,i.distance,i.attributes,"kMedoids");f=!1;for(var h=0;h<s.length;h++){var m=Ll(h,a,u);if(m.length!==0){c[h]=Yo(s[h],m,i.attributes);for(var y=0;y<m.length;y++)l=Yo(m[y],m,i.attributes),l<c[h]&&(c[h]=l,s[h]=m[y],f=!0);o[h]=r.collection(m)}}d++}return o},"kMedoids"),hd=v(function(e,r,a,n,i){for(var o,s,u=0;u<r.length;u++)for(var l=0;l<e.length;l++)n[u][l]=Math.pow(a[u][l],i.m);for(var c=0;c<e.length;c++)for(var f=0;f<i.attributes.length;f++){o=0,s=0;for(var d=0;d<r.length;d++)o+=n[d][c]*i.attributes[f](r[d]),s+=n[d][c];e[c][f]=o/s}},"updateCentroids"),gd=v(function(e,r,a,n,i){for(var o=0;o<e.length;o++)r[o]=e[o].slice();for(var s,u,l,c=2/(i.m-1),f=0;f<a.length;f++)for(var d=0;d<n.length;d++){s=0;for(var g=0;g<a.length;g++)u=Pn(i.distance,n[d],a[f],i.attributes,"cmeans"),l=Pn(i.distance,n[d],a[g],i.attributes,"cmeans"),s+=Math.pow(u/l,c);e[d][f]=1/s}},"updateMembership"),pd=v(function(e,r,a,n){for(var i=new Array(a.k),o=0;o<i.length;o++)i[o]=[];for(var s,u,l=0;l<r.length;l++){s=-1/0,u=-1;for(var c=0;c<r[0].length;c++)r[l][c]>s&&(s=r[l][c],u=c);i[u].push(e[l])}for(var f=0;f<i.length;f++)i[f]=n.collection(i[f]);return i},"assign"),Zo=v(function(e){var r=this.cy(),a=this.nodes(),n=Qi(e),i,o,s,u,l;u=new Array(a.length);for(var c=0;c<a.length;c++)u[c]=new Array(n.k);s=new Array(a.length);for(var f=0;f<a.length;f++)s[f]=new Array(n.k);for(var d=0;d<a.length;d++){for(var g=0,h=0;h<n.k;h++)s[d][h]=Math.random(),g+=s[d][h];for(var m=0;m<n.k;m++)s[d][m]=s[d][m]/g}o=new Array(n.k);for(var y=0;y<n.k;y++)o[y]=new Array(n.attributes.length);l=new Array(a.length);for(var p=0;p<a.length;p++)l[p]=new Array(n.k);for(var b=!0,w=0;b&&w<n.maxIterations;)b=!1,hd(o,a,s,l,n),gd(s,u,o,a,n),vd(s,u,n.sensitivityThreshold)||(b=!0),w++;return i=pd(a,s,n,r),{clusters:i,degreeOfMembership:s}},"fuzzyCMeans"),yd={kMeans:fd,kMedoids:dd,fuzzyCMeans:Zo,fcm:Zo},md=et({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),bd={single:"min",complete:"max"},wd=v(function(e){var r=md(e),a=bd[r.linkage];return a!=null&&(r.linkage=a),r},"setOptions"),Qo=v(function(e,r,a,n,i){for(var o=0,s=1/0,u,l=i.attributes,c=v(function(T,A){return za(i.distance,l.length,function(B){return l[B](T)},function(B){return l[B](A)},T,A)},"getDist"),f=0;f<e.length;f++){var d=e[f].key,g=a[d][n[d]];g<s&&(o=d,s=g)}if(i.mode==="threshold"&&s>=i.threshold||i.mode==="dendrogram"&&e.length===1)return!1;var h=r[o],m=r[n[o]],y;i.mode==="dendrogram"?y={left:h,right:m,key:h.key}:y={value:h.value.concat(m.value),key:h.key},e[h.index]=y,e.splice(m.index,1),r[h.key]=y;for(var p=0;p<e.length;p++){var b=e[p];h.key===b.key?u=1/0:i.linkage==="min"?(u=a[h.key][b.key],a[h.key][b.key]>a[m.key][b.key]&&(u=a[m.key][b.key])):i.linkage==="max"?(u=a[h.key][b.key],a[h.key][b.key]<a[m.key][b.key]&&(u=a[m.key][b.key])):i.linkage==="mean"?u=(a[h.key][b.key]*h.size+a[m.key][b.key]*m.size)/(h.size+m.size):i.mode==="dendrogram"?u=c(b.value,h.value):u=c(b.value[0],h.value[0]),a[h.key][b.key]=a[b.key][h.key]=u}for(var w=0;w<e.length;w++){var x=e[w].key;if(n[x]===h.key||n[x]===m.key){for(var S=x,C=0;C<e.length;C++){var E=e[C].key;a[x][E]<a[x][S]&&(S=E)}n[x]=S}e[w].index=w}return h.key=m.key=h.index=m.index=null,!0},"mergeClosest"),ja=v(function t(e,r,a){e&&(e.value?r.push(e.value):(e.left&&t(e.left,r),e.right&&t(e.right,r)))},"getAllChildren"),xd=v(function t(e,r){if(!e)return"";if(e.left&&e.right){var a=t(e.left,r),n=t(e.right,r),i=r.add({group:"nodes",data:{id:a+","+n}});return r.add({group:"edges",data:{source:a,target:i.id()}}),r.add({group:"edges",data:{source:n,target:i.id()}}),i.id()}else if(e.value)return e.value.id()},"buildDendrogram"),Ed=v(function t(e,r,a){if(!e)return[];var n=[],i=[],o=[];return r===0?(e.left&&ja(e.left,n),e.right&&ja(e.right,i),o=n.concat(i),[a.collection(o)]):r===1?e.value?[a.collection(e.value)]:(e.left&&ja(e.left,n),e.right&&ja(e.right,i),[a.collection(n),a.collection(i)]):e.value?[a.collection(e.value)]:(e.left&&(n=t(e.left,r-1,a)),e.right&&(i=t(e.right,r-1,a)),n.concat(i))},"buildClustersFromTree"),Jo=v(function(e){for(var r=this.cy(),a=this.nodes(),n=wd(e),i=n.attributes,o=v(function(w,x){return za(n.distance,i.length,function(S){return i[S](w)},function(S){return i[S](x)},w,x)},"getDist"),s=[],u=[],l=[],c=[],f=0;f<a.length;f++){var d={value:n.mode==="dendrogram"?a[f]:[a[f]],key:f,index:f};s[f]=d,c[f]=d,u[f]=[],l[f]=0}for(var g=0;g<s.length;g++)for(var h=0;h<=g;h++){var m=void 0;n.mode==="dendrogram"?m=g===h?1/0:o(s[g].value,s[h].value):m=g===h?1/0:o(s[g].value[0],s[h].value[0]),u[g][h]=m,u[h][g]=m,m<u[g][l[g]]&&(l[g]=h)}for(var y=Qo(s,c,u,l,n);y;)y=Qo(s,c,u,l,n);var p;return n.mode==="dendrogram"?(p=Ed(s[0],n.dendrogramDepth,r),n.addDendrogram&&xd(s[0],r)):(p=new Array(s.length),s.forEach(function(b,w){b.key=b.index=null,p[w]=r.collection(b.value)})),p},"hierarchicalClustering"),Cd={hierarchicalClustering:Jo,hca:Jo},Td=et({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),Sd=v(function(e){var r=e.damping,a=e.preference;.5<=r&&r<1||Ke("Damping must range on [0.5, 1).  Got: ".concat(r));var n=["median","mean","min","max"];return n.some(function(i){return i===a})||ae(a)||Ke("Preference must be one of [".concat(n.map(function(i){return"'".concat(i,"'")}).join(", "),"] or a number.  Got: ").concat(a)),Td(e)},"setOptions"),Dd=v(function(e,r,a,n){var i=v(function(s,u){return n[u](s)},"attr");return-za(e,n.length,function(o){return i(r,o)},function(o){return i(a,o)},r,a)},"getSimilarity"),Pd=v(function(e,r){var a=null;return r==="median"?a=Sf(e):r==="mean"?a=Tf(e):r==="min"?a=Ef(e):r==="max"?a=Cf(e):a=r,a},"getPreference"),kd=v(function(e,r,a){for(var n=[],i=0;i<e;i++)r[i*e+i]+a[i*e+i]>0&&n.push(i);return n},"findExemplars"),jo=v(function(e,r,a){for(var n=[],i=0;i<e;i++){for(var o=-1,s=-1/0,u=0;u<a.length;u++){var l=a[u];r[i*e+l]>s&&(o=l,s=r[i*e+l])}o>0&&n.push(o)}for(var c=0;c<a.length;c++)n[a[c]]=a[c];return n},"assignClusters"),Bd=v(function(e,r,a){for(var n=jo(e,r,a),i=0;i<a.length;i++){for(var o=[],s=0;s<n.length;s++)n[s]===a[i]&&o.push(s);for(var u=-1,l=-1/0,c=0;c<o.length;c++){for(var f=0,d=0;d<o.length;d++)f+=r[o[d]*e+o[c]];f>l&&(u=c,l=f)}a[i]=o[u]}return n=jo(e,r,a),n},"assign"),es=v(function(e){for(var r=this.cy(),a=this.nodes(),n=Sd(e),i={},o=0;o<a.length;o++)i[a[o].id()]=o;var s,u,l,c,f,d;s=a.length,u=s*s,l=new Array(u);for(var g=0;g<u;g++)l[g]=-1/0;for(var h=0;h<s;h++)for(var m=0;m<s;m++)h!==m&&(l[h*s+m]=Dd(n.distance,a[h],a[m],n.attributes));c=Pd(l,n.preference);for(var y=0;y<s;y++)l[y*s+y]=c;f=new Array(u);for(var p=0;p<u;p++)f[p]=0;d=new Array(u);for(var b=0;b<u;b++)d[b]=0;for(var w=new Array(s),x=new Array(s),S=new Array(s),C=0;C<s;C++)w[C]=0,x[C]=0,S[C]=0;for(var E=new Array(s*n.minIterations),D=0;D<E.length;D++)E[D]=0;var T;for(T=0;T<n.maxIterations;T++){for(var A=0;A<s;A++){for(var B=-1/0,k=-1/0,L=-1,R=0,M=0;M<s;M++)w[M]=f[A*s+M],R=d[A*s+M]+l[A*s+M],R>=B?(k=B,B=R,L=M):R>k&&(k=R);for(var I=0;I<s;I++)f[A*s+I]=(1-n.damping)*(l[A*s+I]-B)+n.damping*w[I];f[A*s+L]=(1-n.damping)*(l[A*s+L]-k)+n.damping*w[L]}for(var O=0;O<s;O++){for(var F=0,K=0;K<s;K++)w[K]=d[K*s+O],x[K]=Math.max(0,f[K*s+O]),F+=x[K];F-=x[O],x[O]=f[O*s+O],F+=x[O];for(var $=0;$<s;$++)d[$*s+O]=(1-n.damping)*Math.min(0,F-x[$])+n.damping*w[$];d[O*s+O]=(1-n.damping)*(F-x[O])+n.damping*w[O]}for(var q=0,G=0;G<s;G++){var X=d[G*s+G]+f[G*s+G]>0?1:0;E[T%n.minIterations*s+G]=X,q+=X}if(q>0&&(T>=n.minIterations-1||T==n.maxIterations-1)){for(var Z=0,J=0;J<s;J++){S[J]=0;for(var Q=0;Q<n.minIterations;Q++)S[J]+=E[Q*s+J];(S[J]===0||S[J]===n.minIterations)&&Z++}if(Z===s)break}}for(var ee=kd(s,f,d),re=Bd(s,l,ee),W={},N=0;N<ee.length;N++)W[ee[N]]=[];for(var U=0;U<a.length;U++){var te=i[a[U].id()],oe=re[te];oe!=null&&W[oe].push(a[U])}for(var ue=new Array(ee.length),Se=0;Se<ee.length;Se++)ue[Se]=r.collection(W[ee[Se]]);return ue},"affinityPropagation"),Ad={affinityPropagation:es,ap:es},Rd=et({root:void 0,directed:!1}),Ld={hierholzer:v(function(e){if(!ke(e)){var r=arguments;e={root:r[0],directed:r[1]}}var a=Rd(e),n=a.root,i=a.directed,o=this,s=!1,u,l,c;n&&(c=fe(n)?this.filter(n)[0].id():n[0].id());var f={},d={};i?o.forEach(function(b){var w=b.id();if(b.isNode()){var x=b.indegree(!0),S=b.outdegree(!0),C=x-S,E=S-x;C==1?u?s=!0:u=w:E==1?l?s=!0:l=w:(E>1||C>1)&&(s=!0),f[w]=[],b.outgoers().forEach(function(D){D.isEdge()&&f[w].push(D.id())})}else d[w]=[void 0,b.target().id()]}):o.forEach(function(b){var w=b.id();if(b.isNode()){var x=b.degree(!0);x%2&&(u?l?s=!0:l=w:u=w),f[w]=[],b.connectedEdges().forEach(function(S){return f[w].push(S.id())})}else d[w]=[b.source().id(),b.target().id()]});var g={found:!1,trail:void 0};if(s)return g;if(l&&u)if(i){if(c&&l!=c)return g;c=l}else{if(c&&l!=c&&u!=c)return g;c||(c=l)}else c||(c=o[0].id());var h=v(function(w){for(var x=w,S=[w],C,E,D;f[x].length;)C=f[x].shift(),E=d[C][0],D=d[C][1],x!=D?(f[D]=f[D].filter(function(T){return T!=C}),x=D):!i&&x!=E&&(f[E]=f[E].filter(function(T){return T!=C}),x=E),S.unshift(C),S.unshift(x);return S},"walk"),m=[],y=[];for(y=h(c);y.length!=1;)f[y[0]].length==0?(m.unshift(o.getElementById(y.shift())),m.unshift(o.getElementById(y.shift()))):y=h(y.shift()).concat(y);m.unshift(o.getElementById(y.shift()));for(var p in f)if(f[p].length)return g;return g.found=!0,g.trail=this.spawn(m,!0),g},"hierholzer")},en=v(function(){var e=this,r={},a=0,n=0,i=[],o=[],s={},u=v(function(d,g){for(var h=o.length-1,m=[],y=e.spawn();o[h].x!=d||o[h].y!=g;)m.push(o.pop().edge),h--;m.push(o.pop().edge),m.forEach(function(p){var b=p.connectedNodes().intersection(e);y.merge(p),b.forEach(function(w){var x=w.id(),S=w.connectedEdges().intersection(e);y.merge(w),r[x].cutVertex?y.merge(S.filter(function(C){return C.isLoop()})):y.merge(S)})}),i.push(y)},"buildComponent"),l=v(function f(d,g,h){d===h&&(n+=1),r[g]={id:a,low:a++,cutVertex:!1};var m=e.getElementById(g).connectedEdges().intersection(e);if(m.size()===0)i.push(e.spawn(e.getElementById(g)));else{var y,p,b,w;m.forEach(function(x){y=x.source().id(),p=x.target().id(),b=y===g?p:y,b!==h&&(w=x.id(),s[w]||(s[w]=!0,o.push({x:g,y:b,edge:x})),b in r?r[g].low=Math.min(r[g].low,r[b].id):(f(d,b,g),r[g].low=Math.min(r[g].low,r[b].low),r[g].id<=r[b].low&&(r[g].cutVertex=!0,u(g,b))))})}},"biconnectedSearch");e.forEach(function(f){if(f.isNode()){var d=f.id();d in r||(n=0,l(d,d),r[d].cutVertex=n>1)}});var c=Object.keys(r).filter(function(f){return r[f].cutVertex}).map(function(f){return e.getElementById(f)});return{cut:e.spawn(c),components:i}},"hopcroftTarjanBiconnected"),Id={hopcroftTarjanBiconnected:en,htbc:en,htb:en,hopcroftTarjanBiconnectedComponents:en},tn=v(function(){var e=this,r={},a=0,n=[],i=[],o=e.spawn(e),s=v(function u(l){i.push(l),r[l]={index:a,low:a++,explored:!1};var c=e.getElementById(l).connectedEdges().intersection(e);if(c.forEach(function(m){var y=m.target().id();y!==l&&(y in r||u(y),r[y].explored||(r[l].low=Math.min(r[l].low,r[y].low)))}),r[l].index===r[l].low){for(var f=e.spawn();;){var d=i.pop();if(f.merge(e.getElementById(d)),r[d].low=r[l].index,r[d].explored=!0,d===l)break}var g=f.edgesWith(f),h=f.merge(g);n.push(h),o=o.difference(h)}},"stronglyConnectedSearch");return e.forEach(function(u){if(u.isNode()){var l=u.id();l in r||s(l)}}),{cut:o,components:n}},"tarjanStronglyConnected"),Md={tarjanStronglyConnected:tn,tsc:tn,tscc:tn,tarjanStronglyConnectedComponents:tn},Il={};[Ta,vf,cf,df,gf,yf,wf,$f,Gr,Hr,pi,nd,yd,Cd,Ad,Ld,Id,Md].forEach(function(t){he(Il,t)});var Ml=0,Ol=1,Fl=2,_t=v(function t(e){if(!(this instanceof t))return new t(e);this.id="Thenable/1.0.7",this.state=Ml,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},typeof e=="function"&&e.call(this,this.fulfill.bind(this),this.reject.bind(this))},"api");_t.prototype={fulfill:v(function(e){return ts(this,Ol,"fulfillValue",e)},"fulfill"),reject:v(function(e){return ts(this,Fl,"rejectReason",e)},"reject"),then:v(function(e,r){var a=this,n=new _t;return a.onFulfilled.push(as(e,n,"fulfill")),a.onRejected.push(as(r,n,"reject")),Nl(a),n.proxy},"then")};var ts=v(function(e,r,a,n){return e.state===Ml&&(e.state=r,e[a]=n,Nl(e)),e},"deliver"),Nl=v(function(e){e.state===Ol?rs(e,"onFulfilled",e.fulfillValue):e.state===Fl&&rs(e,"onRejected",e.rejectReason)},"execute"),rs=v(function(e,r,a){if(e[r].length!==0){var n=e[r];e[r]=[];var i=v(function(){for(var s=0;s<n.length;s++)n[s](a)},"func");typeof setImmediate=="function"?setImmediate(i):setTimeout(i,0)}},"execute_handlers"),as=v(function(e,r,a){return function(n){if(typeof e!="function")r[a].call(r,n);else{var i;try{i=e(n)}catch(o){r.reject(o);return}Od(r,i)}}},"resolver"),Od=v(function t(e,r){if(e===r||e.proxy===r){e.reject(new TypeError("cannot resolve promise with itself"));return}var a;if($e(r)==="object"&&r!==null||typeof r=="function")try{a=r.then}catch(i){e.reject(i);return}if(typeof a=="function"){var n=!1;try{a.call(r,function(i){n||(n=!0,i===r?e.reject(new TypeError("circular thenable chain")):t(e,i))},function(i){n||(n=!0,e.reject(i))})}catch(i){n||e.reject(i)}return}e.fulfill(r)},"resolve");_t.all=function(t){return new _t(function(e,r){for(var a=new Array(t.length),n=0,i=v(function(u,l){a[u]=l,n++,n===t.length&&e(a)},"fulfill"),o=0;o<t.length;o++)(function(s){var u=t[s],l=u!=null&&u.then!=null;if(l)u.then(function(f){i(s,f)},function(f){r(f)});else{var c=u;i(s,c)}})(o)})};_t.resolve=function(t){return new _t(function(e,r){e(t)})};_t.reject=function(t){return new _t(function(e,r){r(t)})};var Jr=typeof Promise<"u"?Promise:_t,yi=v(function(e,r,a){var n=Hi(e),i=!n,o=this._private=he({duration:1e3},r,a);if(o.target=e,o.style=o.style||o.css,o.started=!1,o.playing=!1,o.hooked=!1,o.applying=!1,o.progress=0,o.completes=[],o.frames=[],o.complete&&Ge(o.complete)&&o.completes.push(o.complete),i){var s=e.position();o.startPosition=o.startPosition||{x:s.x,y:s.y},o.startStyle=o.startStyle||e.cy().style().getAnimationStartStyle(e,o.style)}if(n){var u=e.pan();o.startPan={x:u.x,y:u.y},o.startZoom=e.zoom()}this.length=1,this[0]=this},"Animation"),Tr=yi.prototype;he(Tr,{instanceString:v(function(){return"animation"},"instanceString"),hook:v(function(){var e=this._private;if(!e.hooked){var r,a=e.target._private.animation;e.queue?r=a.queue:r=a.current,r.push(this),Et(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},"hook"),play:v(function(){var e=this._private;return e.progress===1&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},"play"),playing:v(function(){return this._private.playing},"playing"),apply:v(function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},"apply"),applying:v(function(){return this._private.applying},"applying"),pause:v(function(){var e=this._private;return e.playing=!1,e.started=!1,this},"pause"),stop:v(function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},"stop"),rewind:v(function(){return this.progress(0)},"rewind"),fastforward:v(function(){return this.progress(1)},"fastforward"),time:v(function(e){var r=this._private;return e===void 0?r.progress*r.duration:this.progress(e/r.duration)},"time"),progress:v(function(e){var r=this._private,a=r.playing;return e===void 0?r.progress:(a&&this.pause(),r.progress=e,r.started=!1,a&&this.play(),this)},"progress"),completed:v(function(){return this._private.progress===1},"completed"),reverse:v(function(){var e=this._private,r=e.playing;r&&this.pause(),e.progress=1-e.progress,e.started=!1;var a=v(function(l,c){var f=e[l];f!=null&&(e[l]=e[c],e[c]=f)},"swap");if(a("zoom","startZoom"),a("pan","startPan"),a("position","startPosition"),e.style)for(var n=0;n<e.style.length;n++){var i=e.style[n],o=i.name,s=e.startStyle[o];e.startStyle[o]=i,e.style[n]=s}return r&&this.play(),this},"reverse"),promise:v(function(e){var r=this._private,a;switch(e){case"frame":a=r.frames;break;default:case"complete":case"completed":a=r.completes}return new Jr(function(n,i){a.push(function(){n()})})},"promise")});Tr.complete=Tr.completed;Tr.run=Tr.play;Tr.running=Tr.playing;var Fd={animated:v(function(){return v(function(){var r=this,a=r.length!==void 0,n=a?r:[r],i=this._private.cy||this;if(!i.styleEnabled())return!1;var o=n[0];if(o)return o._private.animation.current.length>0},"animatedImpl")},"animated"),clearQueue:v(function(){return v(function(){var r=this,a=r.length!==void 0,n=a?r:[r],i=this._private.cy||this;if(!i.styleEnabled())return this;for(var o=0;o<n.length;o++){var s=n[o];s._private.animation.queue=[]}return this},"clearQueueImpl")},"clearQueue"),delay:v(function(){return v(function(r,a){var n=this._private.cy||this;return n.styleEnabled()?this.animate({delay:r,duration:r,complete:a}):this},"delayImpl")},"delay"),delayAnimation:v(function(){return v(function(r,a){var n=this._private.cy||this;return n.styleEnabled()?this.animation({delay:r,duration:r,complete:a}):this},"delayAnimationImpl")},"delayAnimation"),animation:v(function(){return v(function(r,a){var n=this,i=n.length!==void 0,o=i?n:[n],s=this._private.cy||this,u=!i,l=!u;if(!s.styleEnabled())return this;var c=s.style();r=he({},r,a);var f=Object.keys(r).length===0;if(f)return new yi(o[0],r);switch(r.duration===void 0&&(r.duration=400),r.duration){case"slow":r.duration=600;break;case"fast":r.duration=200;break}if(l&&(r.style=c.getPropsList(r.style||r.css),r.css=void 0),l&&r.renderedPosition!=null){var d=r.renderedPosition,g=s.pan(),h=s.zoom();r.position=El(d,h,g)}if(u&&r.panBy!=null){var m=r.panBy,y=s.pan();r.pan={x:y.x+m.x,y:y.y+m.y}}var p=r.center||r.centre;if(u&&p!=null){var b=s.getCenterPan(p.eles,r.zoom);b!=null&&(r.pan=b)}if(u&&r.fit!=null){var w=r.fit,x=s.getFitViewport(w.eles||w.boundingBox,w.padding);x!=null&&(r.pan=x.pan,r.zoom=x.zoom)}if(u&&ke(r.zoom)){var S=s.getZoomedViewport(r.zoom);S!=null?(S.zoomed&&(r.zoom=S.zoom),S.panned&&(r.pan=S.pan)):r.zoom=null}return new yi(o[0],r)},"animationImpl")},"animation"),animate:v(function(){return v(function(r,a){var n=this,i=n.length!==void 0,o=i?n:[n],s=this._private.cy||this;if(!s.styleEnabled())return this;a&&(r=he({},r,a));for(var u=0;u<o.length;u++){var l=o[u],c=l.animated()&&(r.queue===void 0||r.queue),f=l.animation(r,c?{queue:!0}:void 0);f.play()}return this},"animateImpl")},"animate"),stop:v(function(){return v(function(r,a){var n=this,i=n.length!==void 0,o=i?n:[n],s=this._private.cy||this;if(!s.styleEnabled())return this;for(var u=0;u<o.length;u++){for(var l=o[u],c=l._private,f=c.animation.current,d=0;d<f.length;d++){var g=f[d],h=g._private;a&&(h.duration=0)}r&&(c.animation.queue=[]),a||(c.animation.current=[])}return s.notify("draw"),this},"stopImpl")},"stop")},Nd=Array.isArray,zn=Nd,zd=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Vd=/^\w*$/;function zl(t,e){if(zn(t))return!1;var r=typeof t;return r=="number"||r=="symbol"||r=="boolean"||t==null||Oa(t)?!0:Vd.test(t)||!zd.test(t)||e!=null&&t in Object(e)}v(zl,"isKey");var qd=zl,Kd="[object AsyncFunction]",Gd="[object Function]",Hd="[object GeneratorFunction]",Wd="[object Proxy]";function Vl(t){if(!xr(t))return!1;var e=ul(t);return e==Gd||e==Hd||e==Kd||e==Wd}v(Vl,"isFunction");var $d=Vl,Ud=On["__core-js_shared__"],oi=Ud,ns=(function(){var t=/[^.]+$/.exec(oi&&oi.keys&&oi.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""})();function ql(t){return!!ns&&ns in t}v(ql,"isMasked");var _d=ql,Xd=Function.prototype,Yd=Xd.toString;function Kl(t){if(t!=null){try{return Yd.call(t)}catch{}try{return t+""}catch{}}return""}v(Kl,"toSource");var Zd=Kl,Qd=/[\\^$.*+?()[\]{}|]/g,Jd=/^\[object .+?Constructor\]$/,jd=Function.prototype,eh=Object.prototype,th=jd.toString,rh=eh.hasOwnProperty,ah=RegExp("^"+th.call(rh).replace(Qd,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Gl(t){if(!xr(t)||_d(t))return!1;var e=$d(t)?ah:Jd;return e.test(Zd(t))}v(Gl,"baseIsNative");var nh=Gl;function Hl(t,e){return t?.[e]}v(Hl,"getValue$1");var ih=Hl;function Wl(t,e){var r=ih(t,e);return nh(r)?r:void 0}v(Wl,"getNative");var Ji=Wl,oh=Ji(Object,"create"),Pa=oh;function $l(){this.__data__=Pa?Pa(null):{},this.size=0}v($l,"hashClear");var sh=$l;function Ul(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}v(Ul,"hashDelete");var lh=Ul,uh="__lodash_hash_undefined__",vh=Object.prototype,ch=vh.hasOwnProperty;function _l(t){var e=this.__data__;if(Pa){var r=e[t];return r===uh?void 0:r}return ch.call(e,t)?e[t]:void 0}v(_l,"hashGet");var fh=_l,dh=Object.prototype,hh=dh.hasOwnProperty;function Xl(t){var e=this.__data__;return Pa?e[t]!==void 0:hh.call(e,t)}v(Xl,"hashHas");var gh=Xl,ph="__lodash_hash_undefined__";function Yl(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=Pa&&e===void 0?ph:e,this}v(Yl,"hashSet");var yh=Yl;function Pr(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}v(Pr,"Hash");Pr.prototype.clear=sh;Pr.prototype.delete=lh;Pr.prototype.get=fh;Pr.prototype.has=gh;Pr.prototype.set=yh;var is=Pr;function Zl(){this.__data__=[],this.size=0}v(Zl,"listCacheClear");var mh=Zl;function Ql(t,e){return t===e||t!==t&&e!==e}v(Ql,"eq");var Jl=Ql;function jl(t,e){for(var r=t.length;r--;)if(Jl(t[r][0],e))return r;return-1}v(jl,"assocIndexOf");var Vn=jl,bh=Array.prototype,wh=bh.splice;function eu(t){var e=this.__data__,r=Vn(e,t);if(r<0)return!1;var a=e.length-1;return r==a?e.pop():wh.call(e,r,1),--this.size,!0}v(eu,"listCacheDelete");var xh=eu;function tu(t){var e=this.__data__,r=Vn(e,t);return r<0?void 0:e[r][1]}v(tu,"listCacheGet");var Eh=tu;function ru(t){return Vn(this.__data__,t)>-1}v(ru,"listCacheHas");var Ch=ru;function au(t,e){var r=this.__data__,a=Vn(r,t);return a<0?(++this.size,r.push([t,e])):r[a][1]=e,this}v(au,"listCacheSet");var Th=au;function kr(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}v(kr,"ListCache");kr.prototype.clear=mh;kr.prototype.delete=xh;kr.prototype.get=Eh;kr.prototype.has=Ch;kr.prototype.set=Th;var Sh=kr,Dh=Ji(On,"Map"),Ph=Dh;function nu(){this.size=0,this.__data__={hash:new is,map:new(Ph||Sh),string:new is}}v(nu,"mapCacheClear");var kh=nu;function iu(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}v(iu,"isKeyable");var Bh=iu;function ou(t,e){var r=t.__data__;return Bh(e)?r[typeof e=="string"?"string":"hash"]:r.map}v(ou,"getMapData");var qn=ou;function su(t){var e=qn(this,t).delete(t);return this.size-=e?1:0,e}v(su,"mapCacheDelete");var Ah=su;function lu(t){return qn(this,t).get(t)}v(lu,"mapCacheGet");var Rh=lu;function uu(t){return qn(this,t).has(t)}v(uu,"mapCacheHas");var Lh=uu;function vu(t,e){var r=qn(this,t),a=r.size;return r.set(t,e),this.size+=r.size==a?0:1,this}v(vu,"mapCacheSet");var Ih=vu;function Br(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}v(Br,"MapCache");Br.prototype.clear=kh;Br.prototype.delete=Ah;Br.prototype.get=Rh;Br.prototype.has=Lh;Br.prototype.set=Ih;var cu=Br,Mh="Expected a function";function Kn(t,e){if(typeof t!="function"||e!=null&&typeof e!="function")throw new TypeError(Mh);var r=v(function(){var a=arguments,n=e?e.apply(this,a):a[0],i=r.cache;if(i.has(n))return i.get(n);var o=t.apply(this,a);return r.cache=i.set(n,o)||i,o},"memoized");return r.cache=new(Kn.Cache||cu),r}v(Kn,"memoize");Kn.Cache=cu;var Oh=Kn,Fh=500;function fu(t){var e=Oh(t,function(a){return r.size===Fh&&r.clear(),a}),r=e.cache;return e}v(fu,"memoizeCapped");var Nh=fu,zh=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Vh=/\\(\\)?/g,qh=Nh(function(t){var e=[];return t.charCodeAt(0)===46&&e.push(""),t.replace(zh,function(r,a,n,i){e.push(n?i.replace(Vh,"$1"):a||r)}),e}),du=qh;function hu(t,e){for(var r=-1,a=t==null?0:t.length,n=Array(a);++r<a;)n[r]=e(t[r],r,t);return n}v(hu,"arrayMap");var gu=hu,os=Ur?Ur.prototype:void 0,ss=os?os.toString:void 0;function ji(t){if(typeof t=="string")return t;if(zn(t))return gu(t,ji)+"";if(Oa(t))return ss?ss.call(t):"";var e=t+"";return e=="0"&&1/t==-1/0?"-0":e}v(ji,"baseToString");var Kh=ji;function pu(t){return t==null?"":Kh(t)}v(pu,"toString$1");var yu=pu;function mu(t,e){return zn(t)?t:qd(t,e)?[t]:du(yu(t))}v(mu,"castPath");var bu=mu;function wu(t){if(typeof t=="string"||Oa(t))return t;var e=t+"";return e=="0"&&1/t==-1/0?"-0":e}v(wu,"toKey");var eo=wu;function xu(t,e){e=bu(e,t);for(var r=0,a=e.length;t!=null&&r<a;)t=t[eo(e[r++])];return r&&r==a?t:void 0}v(xu,"baseGet");var Gh=xu;function Eu(t,e,r){var a=t==null?void 0:Gh(t,e);return a===void 0?r:a}v(Eu,"get");var Hh=Eu,Wh=(function(){try{var t=Ji(Object,"defineProperty");return t({},"",{}),t}catch{}})(),ls=Wh;function Cu(t,e,r){e=="__proto__"&&ls?ls(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}v(Cu,"baseAssignValue");var $h=Cu,Uh=Object.prototype,_h=Uh.hasOwnProperty;function Tu(t,e,r){var a=t[e];(!(_h.call(t,e)&&Jl(a,r))||r===void 0&&!(e in t))&&$h(t,e,r)}v(Tu,"assignValue");var Xh=Tu,Yh=9007199254740991,Zh=/^(?:0|[1-9]\d*)$/;function Su(t,e){var r=typeof t;return e=e??Yh,!!e&&(r=="number"||r!="symbol"&&Zh.test(t))&&t>-1&&t%1==0&&t<e}v(Su,"isIndex");var Qh=Su;function Du(t,e,r,a){if(!xr(t))return t;e=bu(e,t);for(var n=-1,i=e.length,o=i-1,s=t;s!=null&&++n<i;){var u=eo(e[n]),l=r;if(u==="__proto__"||u==="constructor"||u==="prototype")return t;if(n!=o){var c=s[u];l=a?a(c,u,s):void 0,l===void 0&&(l=xr(c)?c:Qh(e[n+1])?[]:{})}Xh(s,u,l),s=s[u]}return t}v(Du,"baseSet");var Jh=Du;function Pu(t,e,r){return t==null?t:Jh(t,e,r)}v(Pu,"set");var jh=Pu;function ku(t,e){var r=-1,a=t.length;for(e||(e=Array(a));++r<a;)e[r]=t[r];return e}v(ku,"copyArray");var eg=ku;function Bu(t){return zn(t)?gu(t,eo):Oa(t)?[t]:eg(du(yu(t)))}v(Bu,"toPath");var tg=Bu,rg={data:v(function(e){var r={field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:v(function(n){},"beforeGet"),beforeSet:v(function(n,i){},"beforeSet"),onSet:v(function(n){},"onSet"),canSet:v(function(n){return!0},"canSet")};return e=he({},r,e),v(function(n,i){var o=e,s=this,u=s.length!==void 0,l=u?s:[s],c=u?s[0]:s;if(fe(n)){var f=n.indexOf(".")!==-1,d=f&&tg(n);if(o.allowGetting&&i===void 0){var g;return c&&(o.beforeGet(c),d&&c._private[o.field][n]===void 0?g=Hh(c._private[o.field],d):g=c._private[o.field][n]),g}else if(o.allowSetting&&i!==void 0){var h=!o.immutableKeys[n];if(h){var m=Ki({},n,i);o.beforeSet(s,m);for(var y=0,p=l.length;y<p;y++){var b=l[y];o.canSet(b)&&(d&&c._private[o.field][n]===void 0?jh(b._private[o.field],d,i):b._private[o.field][n]=i)}o.updateStyle&&s.updateStyle(),o.onSet(s),o.settingTriggersEvent&&s[o.triggerFnName](o.settingEvent)}}}else if(o.allowSetting&&ke(n)){var w=n,x,S,C=Object.keys(w);o.beforeSet(s,w);for(var E=0;E<C.length;E++){x=C[E],S=w[x];var D=!o.immutableKeys[x];if(D)for(var T=0;T<l.length;T++){var A=l[T];o.canSet(A)&&(A._private[o.field][x]=S)}}o.updateStyle&&s.updateStyle(),o.onSet(s),o.settingTriggersEvent&&s[o.triggerFnName](o.settingEvent)}else if(o.allowBinding&&Ge(n)){var B=n;s.on(o.bindingEvent,B)}else if(o.allowGetting&&n===void 0){var k;return c&&(o.beforeGet(c),k=c._private[o.field]),k}return s},"dataImpl")},"data"),removeData:v(function(e){var r={field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}};return e=he({},r,e),v(function(n){var i=e,o=this,s=o.length!==void 0,u=s?o:[o];if(fe(n)){for(var l=n.split(/\s+/),c=l.length,f=0;f<c;f++){var d=l[f];if(!nr(d)){var g=!i.immutableKeys[d];if(g)for(var h=0,m=u.length;h<m;h++)u[h]._private[i.field][d]=void 0}}i.triggerEvent&&o[i.triggerFnName](i.event)}else if(n===void 0){for(var y=0,p=u.length;y<p;y++)for(var b=u[y]._private[i.field],w=Object.keys(b),x=0;x<w.length;x++){var S=w[x],C=!i.immutableKeys[S];C&&(b[S]=void 0)}i.triggerEvent&&o[i.triggerFnName](i.event)}return o},"removeDataImpl")},"removeData")},ag={eventAliasesOn:v(function(e){var r=e;r.addListener=r.listen=r.bind=r.on,r.unlisten=r.unbind=r.off=r.removeListener,r.trigger=r.emit,r.pon=r.promiseOn=function(a,n){var i=this,o=Array.prototype.slice.call(arguments,0);return new Jr(function(s,u){var l=v(function(g){i.off.apply(i,f),s(g)},"callback"),c=o.concat([l]),f=c.concat([]);i.on.apply(i,c)})}},"eventAliasesOn")},Ae={};[Fd,rg,ag].forEach(function(t){he(Ae,t)});var ng={animate:Ae.animate(),animation:Ae.animation(),animated:Ae.animated(),clearQueue:Ae.clearQueue(),delay:Ae.delay(),delayAnimation:Ae.delayAnimation(),stop:Ae.stop()},vn={classes:v(function(e){var r=this;if(e===void 0){var a=[];return r[0]._private.classes.forEach(function(h){return a.push(h)}),a}else Oe(e)||(e=(e||"").match(/\S+/g)||[]);for(var n=[],i=new Qr(e),o=0;o<r.length;o++){for(var s=r[o],u=s._private,l=u.classes,c=!1,f=0;f<e.length;f++){var d=e[f],g=l.has(d);if(!g){c=!0;break}}c||(c=l.size!==e.length),c&&(u.classes=i,n.push(s))}return n.length>0&&this.spawn(n).updateStyle().emit("class"),r},"classes"),addClass:v(function(e){return this.toggleClass(e,!0)},"addClass"),hasClass:v(function(e){var r=this[0];return r!=null&&r._private.classes.has(e)},"hasClass"),toggleClass:v(function(e,r){Oe(e)||(e=e.match(/\S+/g)||[]);for(var a=this,n=r===void 0,i=[],o=0,s=a.length;o<s;o++)for(var u=a[o],l=u._private.classes,c=!1,f=0;f<e.length;f++){var d=e[f],g=l.has(d),h=!1;r||n&&!g?(l.add(d),h=!0):(!r||n&&g)&&(l.delete(d),h=!0),!c&&h&&(i.push(u),c=!0)}return i.length>0&&this.spawn(i).updateStyle().emit("class"),a},"toggleClass"),removeClass:v(function(e){return this.toggleClass(e,!1)},"removeClass"),flashClass:v(function(e,r){var a=this;if(r==null)r=250;else if(r===0)return a;return a.addClass(e),setTimeout(function(){a.removeClass(e)},r),a},"flashClass")};vn.className=vn.classNames=vn.classes;var Pe={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:`"(?:\\\\"|[^"])*"|'(?:\\\\'|[^'])*'`,number:Xe,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};Pe.variable="(?:[\\w-.]|(?:\\\\"+Pe.metaChar+"))+";Pe.className="(?:[\\w-]|(?:\\\\"+Pe.metaChar+"))+";Pe.value=Pe.string+"|"+Pe.number;Pe.id=Pe.variable;(function(){var t,e,r;for(t=Pe.comparatorOp.split("|"),r=0;r<t.length;r++)e=t[r],Pe.comparatorOp+="|@"+e;for(t=Pe.comparatorOp.split("|"),r=0;r<t.length;r++)e=t[r],!(e.indexOf("!")>=0)&&e!=="="&&(Pe.comparatorOp+="|\\!"+e)})();var Me=v(function(){return{checks:[]}},"newQuery"),se={GROUP:0,COLLECTION:1,FILTER:2,DATA_COMPARE:3,DATA_EXIST:4,DATA_BOOL:5,META_COMPARE:6,STATE:7,ID:8,CLASS:9,UNDIRECTED_EDGE:10,DIRECTED_EDGE:11,NODE_SOURCE:12,NODE_TARGET:13,NODE_NEIGHBOR:14,CHILD:15,DESCENDANT:16,PARENT:17,ANCESTOR:18,COMPOUND_SPLIT:19,TRUE:20},mi=[{selector:":selected",matches:v(function(e){return e.selected()},"matches")},{selector:":unselected",matches:v(function(e){return!e.selected()},"matches")},{selector:":selectable",matches:v(function(e){return e.selectable()},"matches")},{selector:":unselectable",matches:v(function(e){return!e.selectable()},"matches")},{selector:":locked",matches:v(function(e){return e.locked()},"matches")},{selector:":unlocked",matches:v(function(e){return!e.locked()},"matches")},{selector:":visible",matches:v(function(e){return e.visible()},"matches")},{selector:":hidden",matches:v(function(e){return!e.visible()},"matches")},{selector:":transparent",matches:v(function(e){return e.transparent()},"matches")},{selector:":grabbed",matches:v(function(e){return e.grabbed()},"matches")},{selector:":free",matches:v(function(e){return!e.grabbed()},"matches")},{selector:":removed",matches:v(function(e){return e.removed()},"matches")},{selector:":inside",matches:v(function(e){return!e.removed()},"matches")},{selector:":grabbable",matches:v(function(e){return e.grabbable()},"matches")},{selector:":ungrabbable",matches:v(function(e){return!e.grabbable()},"matches")},{selector:":animated",matches:v(function(e){return e.animated()},"matches")},{selector:":unanimated",matches:v(function(e){return!e.animated()},"matches")},{selector:":parent",matches:v(function(e){return e.isParent()},"matches")},{selector:":childless",matches:v(function(e){return e.isChildless()},"matches")},{selector:":child",matches:v(function(e){return e.isChild()},"matches")},{selector:":orphan",matches:v(function(e){return e.isOrphan()},"matches")},{selector:":nonorphan",matches:v(function(e){return e.isChild()},"matches")},{selector:":compound",matches:v(function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()},"matches")},{selector:":loop",matches:v(function(e){return e.isLoop()},"matches")},{selector:":simple",matches:v(function(e){return e.isSimple()},"matches")},{selector:":active",matches:v(function(e){return e.active()},"matches")},{selector:":inactive",matches:v(function(e){return!e.active()},"matches")},{selector:":backgrounding",matches:v(function(e){return e.backgrounding()},"matches")},{selector:":nonbackgrounding",matches:v(function(e){return!e.backgrounding()},"matches")}].sort(function(t,e){return gc(t.selector,e.selector)}),ig=(function(){for(var t={},e,r=0;r<mi.length;r++)e=mi[r],t[e.selector]=e.matches;return t})(),og=v(function(e,r){return ig[e](r)},"stateSelectorMatches"),sg="("+mi.map(function(t){return t.selector}).join("|")+")",Ir=v(function(e){return e.replace(new RegExp("\\\\("+Pe.metaChar+")","g"),function(r,a){return a})},"cleanMetaChars"),jt=v(function(e,r,a){e[e.length-1]=a},"replaceLastQuery"),bi=[{name:"group",query:!0,regex:"("+Pe.group+")",populate:v(function(e,r,a){var n=We(a,1),i=n[0];r.checks.push({type:se.GROUP,value:i==="*"?i:i+"s"})},"populate")},{name:"state",query:!0,regex:sg,populate:v(function(e,r,a){var n=We(a,1),i=n[0];r.checks.push({type:se.STATE,value:i})},"populate")},{name:"id",query:!0,regex:"\\#("+Pe.id+")",populate:v(function(e,r,a){var n=We(a,1),i=n[0];r.checks.push({type:se.ID,value:Ir(i)})},"populate")},{name:"className",query:!0,regex:"\\.("+Pe.className+")",populate:v(function(e,r,a){var n=We(a,1),i=n[0];r.checks.push({type:se.CLASS,value:Ir(i)})},"populate")},{name:"dataExists",query:!0,regex:"\\[\\s*("+Pe.variable+")\\s*\\]",populate:v(function(e,r,a){var n=We(a,1),i=n[0];r.checks.push({type:se.DATA_EXIST,field:Ir(i)})},"populate")},{name:"dataCompare",query:!0,regex:"\\[\\s*("+Pe.variable+")\\s*("+Pe.comparatorOp+")\\s*("+Pe.value+")\\s*\\]",populate:v(function(e,r,a){var n=We(a,3),i=n[0],o=n[1],s=n[2],u=new RegExp("^"+Pe.string+"$").exec(s)!=null;u?s=s.substring(1,s.length-1):s=parseFloat(s),r.checks.push({type:se.DATA_COMPARE,field:Ir(i),operator:o,value:s})},"populate")},{name:"dataBool",query:!0,regex:"\\[\\s*("+Pe.boolOp+")\\s*("+Pe.variable+")\\s*\\]",populate:v(function(e,r,a){var n=We(a,2),i=n[0],o=n[1];r.checks.push({type:se.DATA_BOOL,field:Ir(o),operator:i})},"populate")},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+Pe.meta+")\\s*("+Pe.comparatorOp+")\\s*("+Pe.number+")\\s*\\]\\]",populate:v(function(e,r,a){var n=We(a,3),i=n[0],o=n[1],s=n[2];r.checks.push({type:se.META_COMPARE,field:Ir(i),operator:o,value:parseFloat(s)})},"populate")},{name:"nextQuery",separator:!0,regex:Pe.separator,populate:v(function(e,r){var a=e.currentSubject,n=e.edgeCount,i=e.compoundCount,o=e[e.length-1];a!=null&&(o.subject=a,e.currentSubject=null),o.edgeCount=n,o.compoundCount=i,e.edgeCount=0,e.compoundCount=0;var s=e[e.length++]=Me();return s},"populate")},{name:"directedEdge",separator:!0,regex:Pe.directedEdge,populate:v(function(e,r){if(e.currentSubject==null){var a=Me(),n=r,i=Me();return a.checks.push({type:se.DIRECTED_EDGE,source:n,target:i}),jt(e,r,a),e.edgeCount++,i}else{var o=Me(),s=r,u=Me();return o.checks.push({type:se.NODE_SOURCE,source:s,target:u}),jt(e,r,o),e.edgeCount++,u}},"populate")},{name:"undirectedEdge",separator:!0,regex:Pe.undirectedEdge,populate:v(function(e,r){if(e.currentSubject==null){var a=Me(),n=r,i=Me();return a.checks.push({type:se.UNDIRECTED_EDGE,nodes:[n,i]}),jt(e,r,a),e.edgeCount++,i}else{var o=Me(),s=r,u=Me();return o.checks.push({type:se.NODE_NEIGHBOR,node:s,neighbor:u}),jt(e,r,o),u}},"populate")},{name:"child",separator:!0,regex:Pe.child,populate:v(function(e,r){if(e.currentSubject==null){var a=Me(),n=Me(),i=e[e.length-1];return a.checks.push({type:se.CHILD,parent:i,child:n}),jt(e,r,a),e.compoundCount++,n}else if(e.currentSubject===r){var o=Me(),s=e[e.length-1],u=Me(),l=Me(),c=Me(),f=Me();return o.checks.push({type:se.COMPOUND_SPLIT,left:s,right:u,subject:l}),l.checks=r.checks,r.checks=[{type:se.TRUE}],f.checks.push({type:se.TRUE}),u.checks.push({type:se.PARENT,parent:f,child:c}),jt(e,s,o),e.currentSubject=l,e.compoundCount++,c}else{var d=Me(),g=Me(),h=[{type:se.PARENT,parent:d,child:g}];return d.checks=r.checks,r.checks=h,e.compoundCount++,g}},"populate")},{name:"descendant",separator:!0,regex:Pe.descendant,populate:v(function(e,r){if(e.currentSubject==null){var a=Me(),n=Me(),i=e[e.length-1];return a.checks.push({type:se.DESCENDANT,ancestor:i,descendant:n}),jt(e,r,a),e.compoundCount++,n}else if(e.currentSubject===r){var o=Me(),s=e[e.length-1],u=Me(),l=Me(),c=Me(),f=Me();return o.checks.push({type:se.COMPOUND_SPLIT,left:s,right:u,subject:l}),l.checks=r.checks,r.checks=[{type:se.TRUE}],f.checks.push({type:se.TRUE}),u.checks.push({type:se.ANCESTOR,ancestor:f,descendant:c}),jt(e,s,o),e.currentSubject=l,e.compoundCount++,c}else{var d=Me(),g=Me(),h=[{type:se.ANCESTOR,ancestor:d,descendant:g}];return d.checks=r.checks,r.checks=h,e.compoundCount++,g}},"populate")},{name:"subject",modifier:!0,regex:Pe.subject,populate:v(function(e,r){if(e.currentSubject!=null&&e.currentSubject!==r)return Re("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=r;var a=e[e.length-1],n=a.checks[0],i=n==null?null:n.type;i===se.DIRECTED_EDGE?n.type=se.NODE_TARGET:i===se.UNDIRECTED_EDGE&&(n.type=se.NODE_NEIGHBOR,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)},"populate")}];bi.forEach(function(t){return t.regexObj=new RegExp("^"+t.regex)});var lg=v(function(e){for(var r,a,n,i=0;i<bi.length;i++){var o=bi[i],s=o.name,u=e.match(o.regexObj);if(u!=null){a=u,r=o,n=s;var l=u[0];e=e.substring(l.length);break}}return{expr:r,match:a,name:n,remaining:e}},"consumeExpr"),ug=v(function(e){var r=e.match(/^\s+/);if(r){var a=r[0];e=e.substring(a.length)}return e},"consumeWhitespace"),vg=v(function(e){var r=this,a=r.inputText=e,n=r[0]=Me();for(r.length=1,a=ug(a);;){var i=lg(a);if(i.expr==null)return Re("The selector `"+e+"`is invalid"),!1;var o=i.match.slice(1),s=i.expr.populate(r,n,o);if(s===!1)return!1;if(s!=null&&(n=s),a=i.remaining,a.match(/^\s*$/))break}var u=r[r.length-1];r.currentSubject!=null&&(u.subject=r.currentSubject),u.edgeCount=r.edgeCount,u.compoundCount=r.compoundCount;for(var l=0;l<r.length;l++){var c=r[l];if(c.compoundCount>0&&c.edgeCount>0)return Re("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(c.edgeCount>1)return Re("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;c.edgeCount===1&&Re("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},"parse"),cg=v(function(){if(this.toStringCache!=null)return this.toStringCache;for(var e=v(function(c){return c??""},"clean"),r=v(function(c){return fe(c)?'"'+c+'"':e(c)},"cleanVal"),a=v(function(c){return" "+c+" "},"space"),n=v(function(c,f){var d=c.type,g=c.value;switch(d){case se.GROUP:{var h=e(g);return h.substring(0,h.length-1)}case se.DATA_COMPARE:{var m=c.field,y=c.operator;return"["+m+a(e(y))+r(g)+"]"}case se.DATA_BOOL:{var p=c.operator,b=c.field;return"["+e(p)+b+"]"}case se.DATA_EXIST:{var w=c.field;return"["+w+"]"}case se.META_COMPARE:{var x=c.operator,S=c.field;return"[["+S+a(e(x))+r(g)+"]]"}case se.STATE:return g;case se.ID:return"#"+g;case se.CLASS:return"."+g;case se.PARENT:case se.CHILD:return i(c.parent,f)+a(">")+i(c.child,f);case se.ANCESTOR:case se.DESCENDANT:return i(c.ancestor,f)+" "+i(c.descendant,f);case se.COMPOUND_SPLIT:{var C=i(c.left,f),E=i(c.subject,f),D=i(c.right,f);return C+(C.length>0?" ":"")+E+D}case se.TRUE:return""}},"checkToString"),i=v(function(c,f){return c.checks.reduce(function(d,g,h){return d+(f===c&&h===0?"$":"")+n(g,f)},"")},"queryToString"),o="",s=0;s<this.length;s++){var u=this[s];o+=i(u,u.subject),this.length>1&&s<this.length-1&&(o+=", ")}return this.toStringCache=o,o},"toString"),fg={parse:vg,toString:cg},Au=v(function(e,r,a){var n,i=fe(e),o=ae(e),s=fe(a),u,l,c=!1,f=!1,d=!1;switch(r.indexOf("!")>=0&&(r=r.replace("!",""),f=!0),r.indexOf("@")>=0&&(r=r.replace("@",""),c=!0),(i||s||c)&&(u=!i&&!o?"":""+e,l=""+a),c&&(e=u=u.toLowerCase(),a=l=l.toLowerCase()),r){case"*=":n=u.indexOf(l)>=0;break;case"$=":n=u.indexOf(l,u.length-l.length)>=0;break;case"^=":n=u.indexOf(l)===0;break;case"=":n=e===a;break;case">":d=!0,n=e>a;break;case">=":d=!0,n=e>=a;break;case"<":d=!0,n=e<a;break;case"<=":d=!0,n=e<=a;break;default:n=!1;break}return f&&(e!=null||!d)&&(n=!n),n},"valCmp"),dg=v(function(e,r){switch(r){case"?":return!!e;case"!":return!e;case"^":return e===void 0}},"boolCmp"),hg=v(function(e){return e!==void 0},"existCmp"),to=v(function(e,r){return e.data(r)},"data"),gg=v(function(e,r){return e[r]()},"meta"),He=[],Ve=v(function(e,r){return e.checks.every(function(a){return He[a.type](a,r)})},"matches");He[se.GROUP]=function(t,e){var r=t.value;return r==="*"||r===e.group()};He[se.STATE]=function(t,e){var r=t.value;return og(r,e)};He[se.ID]=function(t,e){var r=t.value;return e.id()===r};He[se.CLASS]=function(t,e){var r=t.value;return e.hasClass(r)};He[se.META_COMPARE]=function(t,e){var r=t.field,a=t.operator,n=t.value;return Au(gg(e,r),a,n)};He[se.DATA_COMPARE]=function(t,e){var r=t.field,a=t.operator,n=t.value;return Au(to(e,r),a,n)};He[se.DATA_BOOL]=function(t,e){var r=t.field,a=t.operator;return dg(to(e,r),a)};He[se.DATA_EXIST]=function(t,e){var r=t.field;return t.operator,hg(to(e,r))};He[se.UNDIRECTED_EDGE]=function(t,e){var r=t.nodes[0],a=t.nodes[1],n=e.source(),i=e.target();return Ve(r,n)&&Ve(a,i)||Ve(a,n)&&Ve(r,i)};He[se.NODE_NEIGHBOR]=function(t,e){return Ve(t.node,e)&&e.neighborhood().some(function(r){return r.isNode()&&Ve(t.neighbor,r)})};He[se.DIRECTED_EDGE]=function(t,e){return Ve(t.source,e.source())&&Ve(t.target,e.target())};He[se.NODE_SOURCE]=function(t,e){return Ve(t.source,e)&&e.outgoers().some(function(r){return r.isNode()&&Ve(t.target,r)})};He[se.NODE_TARGET]=function(t,e){return Ve(t.target,e)&&e.incomers().some(function(r){return r.isNode()&&Ve(t.source,r)})};He[se.CHILD]=function(t,e){return Ve(t.child,e)&&Ve(t.parent,e.parent())};He[se.PARENT]=function(t,e){return Ve(t.parent,e)&&e.children().some(function(r){return Ve(t.child,r)})};He[se.DESCENDANT]=function(t,e){return Ve(t.descendant,e)&&e.ancestors().some(function(r){return Ve(t.ancestor,r)})};He[se.ANCESTOR]=function(t,e){return Ve(t.ancestor,e)&&e.descendants().some(function(r){return Ve(t.descendant,r)})};He[se.COMPOUND_SPLIT]=function(t,e){return Ve(t.subject,e)&&Ve(t.left,e)&&Ve(t.right,e)};He[se.TRUE]=function(){return!0};He[se.COLLECTION]=function(t,e){var r=t.value;return r.has(e)};He[se.FILTER]=function(t,e){var r=t.value;return r(e)};var pg=v(function(e){var r=this;if(r.length===1&&r[0].checks.length===1&&r[0].checks[0].type===se.ID)return e.getElementById(r[0].checks[0].value).collection();var a=v(function(i){for(var o=0;o<r.length;o++){var s=r[o];if(Ve(s,i))return!0}return!1},"selectorFunction");return r.text()==null&&(a=v(function(){return!0},"selectorFunction")),e.filter(a)},"filter"),yg=v(function(e){for(var r=this,a=0;a<r.length;a++){var n=r[a];if(Ve(n,e))return!0}return!1},"matches"),mg={matches:yg,filter:pg},sr=v(function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,e==null||fe(e)&&e.match(/^\s*$/)||(Et(e)?this.addQuery({checks:[{type:se.COLLECTION,value:e.collection()}]}):Ge(e)?this.addQuery({checks:[{type:se.FILTER,value:e}]}):fe(e)?this.parse(e)||(this.invalid=!0):Ke("A selector must be created from a string; found "))},"Selector"),lr=sr.prototype;[fg,mg].forEach(function(t){return he(lr,t)});lr.text=function(){return this.inputText};lr.size=function(){return this.length};lr.eq=function(t){return this[t]};lr.sameText=function(t){return!this.invalid&&!t.invalid&&this.text()===t.text()};lr.addQuery=function(t){this[this.length++]=t};lr.selector=lr.toString;var rr={allAre:v(function(e){var r=new sr(e);return this.every(function(a){return r.matches(a)})},"allAre"),is:v(function(e){var r=new sr(e);return this.some(function(a){return r.matches(a)})},"is"),some:v(function(e,r){for(var a=0;a<this.length;a++){var n=r?e.apply(r,[this[a],a,this]):e(this[a],a,this);if(n)return!0}return!1},"some"),every:v(function(e,r){for(var a=0;a<this.length;a++){var n=r?e.apply(r,[this[a],a,this]):e(this[a],a,this);if(!n)return!1}return!0},"every"),same:v(function(e){if(this===e)return!0;e=this.cy().collection(e);var r=this.length,a=e.length;return r!==a?!1:r===1?this[0]===e[0]:this.every(function(n){return e.hasElementWithId(n.id())})},"same"),anySame:v(function(e){return e=this.cy().collection(e),this.some(function(r){return e.hasElementWithId(r.id())})},"anySame"),allAreNeighbors:v(function(e){e=this.cy().collection(e);var r=this.neighborhood();return e.every(function(a){return r.hasElementWithId(a.id())})},"allAreNeighbors"),contains:v(function(e){e=this.cy().collection(e);var r=this;return e.every(function(a){return r.hasElementWithId(a.id())})},"contains")};rr.allAreNeighbours=rr.allAreNeighbors;rr.has=rr.contains;rr.equal=rr.equals=rr.same;var Dt=v(function(e,r){return v(function(n,i,o,s){var u=n,l=this,c;if(u==null?c="":Et(u)&&u.length===1&&(c=u.id()),l.length===1&&c){var f=l[0]._private,d=f.traversalCache=f.traversalCache||{},g=d[r]=d[r]||[],h=ir(c),m=g[h];return m||(g[h]=e.call(l,n,i,o,s))}else return e.call(l,n,i,o,s)},"traversalCache")},"cache"),Yr={parent:v(function(e){var r=[];if(this.length===1){var a=this[0]._private.parent;if(a)return a}for(var n=0;n<this.length;n++){var i=this[n],o=i._private.parent;o&&r.push(o)}return this.spawn(r,!0).filter(e)},"parent"),parents:v(function(e){for(var r=[],a=this.parent();a.nonempty();){for(var n=0;n<a.length;n++){var i=a[n];r.push(i)}a=a.parent()}return this.spawn(r,!0).filter(e)},"parents"),commonAncestors:v(function(e){for(var r,a=0;a<this.length;a++){var n=this[a],i=n.parents();r=r||i,r=r.intersect(i)}return r.filter(e)},"commonAncestors"),orphans:v(function(e){return this.stdFilter(function(r){return r.isOrphan()}).filter(e)},"orphans"),nonorphans:v(function(e){return this.stdFilter(function(r){return r.isChild()}).filter(e)},"nonorphans"),children:Dt(function(t){for(var e=[],r=0;r<this.length;r++)for(var a=this[r],n=a._private.children,i=0;i<n.length;i++)e.push(n[i]);return this.spawn(e,!0).filter(t)},"children"),siblings:v(function(e){return this.parent().children().not(this).filter(e)},"siblings"),isParent:v(function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length!==0},"isParent"),isChildless:v(function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length===0},"isChildless"),isChild:v(function(){var e=this[0];if(e)return e.isNode()&&e._private.parent!=null},"isChild"),isOrphan:v(function(){var e=this[0];if(e)return e.isNode()&&e._private.parent==null},"isOrphan"),descendants:v(function(e){var r=[];function a(n){for(var i=0;i<n.length;i++){var o=n[i];r.push(o),o.children().nonempty()&&a(o.children())}}return v(a,"add"),a(this.children()),this.spawn(r,!0).filter(e)},"descendants")};function Gn(t,e,r,a){for(var n=[],i=new Qr,o=t.cy(),s=o.hasCompoundNodes(),u=0;u<t.length;u++){var l=t[u];r?n.push(l):s&&a(n,i,l)}for(;n.length>0;){var c=n.shift();e(c),i.add(c.id()),s&&a(n,i,c)}return t}v(Gn,"forEachCompound");function ro(t,e,r){if(r.isParent())for(var a=r._private.children,n=0;n<a.length;n++){var i=a[n];e.has(i.id())||t.push(i)}}v(ro,"addChildren");Yr.forEachDown=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Gn(this,t,e,ro)};function ao(t,e,r){if(r.isChild()){var a=r._private.parent;e.has(a.id())||t.push(a)}}v(ao,"addParent");Yr.forEachUp=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Gn(this,t,e,ao)};function Ru(t,e,r){ao(t,e,r),ro(t,e,r)}v(Ru,"addParentAndChildren");Yr.forEachUpAndDown=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Gn(this,t,e,Ru)};Yr.ancestors=Yr.parents;var ka,Lu;ka=Lu={data:Ae.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:Ae.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:Ae.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Ae.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:Ae.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:Ae.removeData({field:"rscratch",triggerEvent:!1}),id:v(function(){var e=this[0];if(e)return e._private.data.id},"id")};ka.attr=ka.data;ka.removeAttr=ka.removeData;var bg=Lu,Hn={};function cn(t){return function(e){var r=this;if(e===void 0&&(e=!0),r.length!==0)if(r.isNode()&&!r.removed()){for(var a=0,n=r[0],i=n._private.edges,o=0;o<i.length;o++){var s=i[o];!e&&s.isLoop()||(a+=t(n,s))}return a}else return}}v(cn,"defineDegreeFunction");he(Hn,{degree:cn(function(t,e){return e.source().same(e.target())?2:1}),indegree:cn(function(t,e){return e.target().same(t)?1:0}),outdegree:cn(function(t,e){return e.source().same(t)?1:0})});function gr(t,e){return function(r){for(var a,n=this.nodes(),i=0;i<n.length;i++){var o=n[i],s=o[t](r);s!==void 0&&(a===void 0||e(s,a))&&(a=s)}return a}}v(gr,"defineDegreeBoundsFunction");he(Hn,{minDegree:gr("degree",function(t,e){return t<e}),maxDegree:gr("degree",function(t,e){return t>e}),minIndegree:gr("indegree",function(t,e){return t<e}),maxIndegree:gr("indegree",function(t,e){return t>e}),minOutdegree:gr("outdegree",function(t,e){return t<e}),maxOutdegree:gr("outdegree",function(t,e){return t>e})});he(Hn,{totalDegree:v(function(e){for(var r=0,a=this.nodes(),n=0;n<a.length;n++)r+=a[n].degree(e);return r},"totalDegree")});var Rt,Iu,Mu=v(function(e,r,a){for(var n=0;n<e.length;n++){var i=e[n];if(!i.locked()){var o=i._private.position,s={x:r.x!=null?r.x-o.x:0,y:r.y!=null?r.y-o.y:0};i.isParent()&&!(s.x===0&&s.y===0)&&i.children().shift(s,a),i.dirtyBoundingBoxCache()}}},"beforePositionSet"),us={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:v(function(e){e.updateCompoundBounds()},"beforeGet"),beforeSet:v(function(e,r){Mu(e,r,!1)},"beforeSet"),onSet:v(function(e){e.dirtyCompoundBoundsCache()},"onSet"),canSet:v(function(e){return!e.locked()},"canSet")};Rt=Iu={position:Ae.data(us),silentPosition:Ae.data(he({},us,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:v(function(e,r){Mu(e,r,!0)},"beforeSet"),onSet:v(function(e){e.dirtyCompoundBoundsCache()},"onSet")})),positions:v(function(e,r){if(ke(e))r?this.silentPosition(e):this.position(e);else if(Ge(e)){var a=e,n=this.cy();n.startBatch();for(var i=0;i<this.length;i++){var o=this[i],s=void 0;(s=a(o,i))&&(r?o.silentPosition(s):o.position(s))}n.endBatch()}return this},"positions"),silentPositions:v(function(e){return this.positions(e,!0)},"silentPositions"),shift:v(function(e,r,a){var n;if(ke(e)?(n={x:ae(e.x)?e.x:0,y:ae(e.y)?e.y:0},a=r):fe(e)&&ae(r)&&(n={x:0,y:0},n[e]=r),n!=null){var i=this.cy();i.startBatch();for(var o=0;o<this.length;o++){var s=this[o];if(!(i.hasCompoundNodes()&&s.isChild()&&s.ancestors().anySame(this))){var u=s.position(),l={x:u.x+n.x,y:u.y+n.y};a?s.silentPosition(l):s.position(l)}}i.endBatch()}return this},"shift"),silentShift:v(function(e,r){return ke(e)?this.shift(e,!0):fe(e)&&ae(r)&&this.shift(e,r,!0),this},"silentShift"),renderedPosition:v(function(e,r){var a=this[0],n=this.cy(),i=n.zoom(),o=n.pan(),s=ke(e)?e:void 0,u=s!==void 0||r!==void 0&&fe(e);if(a&&a.isNode())if(u)for(var l=0;l<this.length;l++){var c=this[l];r!==void 0?c.position(e,(r-o[e])/i):s!==void 0&&c.position(El(s,i,o))}else{var f=a.position();return s=Nn(f,i,o),e===void 0?s:s[e]}else if(!u)return;return this},"renderedPosition"),relativePosition:v(function(e,r){var a=this[0],n=this.cy(),i=ke(e)?e:void 0,o=i!==void 0||r!==void 0&&fe(e),s=n.hasCompoundNodes();if(a&&a.isNode())if(o)for(var u=0;u<this.length;u++){var l=this[u],c=s?l.parent():null,f=c&&c.length>0,d=f;f&&(c=c[0]);var g=d?c.position():{x:0,y:0};r!==void 0?l.position(e,r+g[e]):i!==void 0&&l.position({x:i.x+g.x,y:i.y+g.y})}else{var h=a.position(),m=s?a.parent():null,y=m&&m.length>0,p=y;y&&(m=m[0]);var b=p?m.position():{x:0,y:0};return i={x:h.x-b.x,y:h.y-b.y},e===void 0?i:i[e]}else if(!o)return;return this},"relativePosition")};Rt.modelPosition=Rt.point=Rt.position;Rt.modelPositions=Rt.points=Rt.positions;Rt.renderedPoint=Rt.renderedPosition;Rt.relativePoint=Rt.relativePosition;var wg=Iu,Wr,vr;Wr=vr={};vr.renderedBoundingBox=function(t){var e=this.boundingBox(t),r=this.cy(),a=r.zoom(),n=r.pan(),i=e.x1*a+n.x,o=e.x2*a+n.x,s=e.y1*a+n.y,u=e.y2*a+n.y;return{x1:i,x2:o,y1:s,y2:u,w:o-i,h:u-s}};vr.dirtyCompoundBoundsCache=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();return!e.styleEnabled()||!e.hasCompoundNodes()?this:(this.forEachUp(function(r){if(r.isParent()){var a=r._private;a.compoundBoundsClean=!1,a.bbCache=null,t||r.emitAndNotify("bounds")}}),this)};vr.updateCompoundBounds=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();if(!e.styleEnabled()||!e.hasCompoundNodes())return this;if(!t&&e.batching())return this;function r(o){if(!o.isParent())return;var s=o._private,u=o.children(),l=o.pstyle("compound-sizing-wrt-labels").value==="include",c={width:{val:o.pstyle("min-width").pfValue,left:o.pstyle("min-width-bias-left"),right:o.pstyle("min-width-bias-right")},height:{val:o.pstyle("min-height").pfValue,top:o.pstyle("min-height-bias-top"),bottom:o.pstyle("min-height-bias-bottom")}},f=u.boundingBox({includeLabels:l,includeOverlays:!1,useCache:!1}),d=s.position;(f.w===0||f.h===0)&&(f={w:o.pstyle("width").pfValue,h:o.pstyle("height").pfValue},f.x1=d.x-f.w/2,f.x2=d.x+f.w/2,f.y1=d.y-f.h/2,f.y2=d.y+f.h/2);function g(T,A,B){var k=0,L=0,R=A+B;return T>0&&R>0&&(k=A/R*T,L=B/R*T),{biasDiff:k,biasComplementDiff:L}}v(g,"computeBiasValues");function h(T,A,B,k){if(B.units==="%")switch(k){case"width":return T>0?B.pfValue*T:0;case"height":return A>0?B.pfValue*A:0;case"average":return T>0&&A>0?B.pfValue*(T+A)/2:0;case"min":return T>0&&A>0?T>A?B.pfValue*A:B.pfValue*T:0;case"max":return T>0&&A>0?T>A?B.pfValue*T:B.pfValue*A:0;default:return 0}else return B.units==="px"?B.pfValue:0}v(h,"computePaddingValues");var m=c.width.left.value;c.width.left.units==="px"&&c.width.val>0&&(m=m*100/c.width.val);var y=c.width.right.value;c.width.right.units==="px"&&c.width.val>0&&(y=y*100/c.width.val);var p=c.height.top.value;c.height.top.units==="px"&&c.height.val>0&&(p=p*100/c.height.val);var b=c.height.bottom.value;c.height.bottom.units==="px"&&c.height.val>0&&(b=b*100/c.height.val);var w=g(c.width.val-f.w,m,y),x=w.biasDiff,S=w.biasComplementDiff,C=g(c.height.val-f.h,p,b),E=C.biasDiff,D=C.biasComplementDiff;s.autoPadding=h(f.w,f.h,o.pstyle("padding"),o.pstyle("padding-relative-to").value),s.autoWidth=Math.max(f.w,c.width.val),d.x=(-x+f.x1+f.x2+S)/2,s.autoHeight=Math.max(f.h,c.height.val),d.y=(-E+f.y1+f.y2+D)/2}v(r,"update");for(var a=0;a<this.length;a++){var n=this[a],i=n._private;(!i.compoundBoundsClean||t)&&(r(n),e.batching()||(i.compoundBoundsClean=!0))}return this};var St=v(function(e){return e===1/0||e===-1/0?0:e},"noninf"),Bt=v(function(e,r,a,n,i){n-r===0||i-a===0||r==null||a==null||n==null||i==null||(e.x1=r<e.x1?r:e.x1,e.x2=n>e.x2?n:e.x2,e.y1=a<e.y1?a:e.y1,e.y2=i>e.y2?i:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},"updateBounds"),pr=v(function(e,r){return r==null?e:Bt(e,r.x1,r.y1,r.x2,r.y2)},"updateBoundsFromBox"),sa=v(function(e,r,a){return At(e,r,a)},"prefixedProperty"),rn=v(function(e,r,a){if(!r.cy().headless()){var n=r._private,i=n.rstyle,o=i.arrowWidth/2,s=r.pstyle(a+"-arrow-shape").value,u,l;if(s!=="none"){a==="source"?(u=i.srcX,l=i.srcY):a==="target"?(u=i.tgtX,l=i.tgtY):(u=i.midX,l=i.midY);var c=n.arrowBounds=n.arrowBounds||{},f=c[a]=c[a]||{};f.x1=u-o,f.y1=l-o,f.x2=u+o,f.y2=l+o,f.w=f.x2-f.x1,f.h=f.y2-f.y1,ln(f,1),Bt(e,f.x1,f.y1,f.x2,f.y2)}}},"updateBoundsFromArrow"),si=v(function(e,r,a){if(!r.cy().headless()){var n;a?n=a+"-":n="";var i=r._private,o=i.rstyle,s=r.pstyle(n+"label").strValue;if(s){var u=r.pstyle("text-halign"),l=r.pstyle("text-valign"),c=sa(o,"labelWidth",a),f=sa(o,"labelHeight",a),d=sa(o,"labelX",a),g=sa(o,"labelY",a),h=r.pstyle(n+"text-margin-x").pfValue,m=r.pstyle(n+"text-margin-y").pfValue,y=r.isEdge(),p=r.pstyle(n+"text-rotation"),b=r.pstyle("text-outline-width").pfValue,w=r.pstyle("text-border-width").pfValue,x=w/2,S=r.pstyle("text-background-padding").pfValue,C=2,E=f,D=c,T=D/2,A=E/2,B,k,L,R;if(y)B=d-T,k=d+T,L=g-A,R=g+A;else{switch(u.value){case"left":B=d-D,k=d;break;case"center":B=d-T,k=d+T;break;case"right":B=d,k=d+D;break}switch(l.value){case"top":L=g-E,R=g;break;case"center":L=g-A,R=g+A;break;case"bottom":L=g,R=g+E;break}}var M=h-Math.max(b,x)-S-C,I=h+Math.max(b,x)+S+C,O=m-Math.max(b,x)-S-C,F=m+Math.max(b,x)+S+C;B+=M,k+=I,L+=O,R+=F;var K=a||"main",$=i.labelBounds,q=$[K]=$[K]||{};q.x1=B,q.y1=L,q.x2=k,q.y2=R,q.w=k-B,q.h=R-L,q.leftPad=M,q.rightPad=I,q.topPad=O,q.botPad=F;var G=y&&p.strValue==="autorotate",X=p.pfValue!=null&&p.pfValue!==0;if(G||X){var Z=G?sa(i.rstyle,"labelAngle",a):p.pfValue,J=Math.cos(Z),Q=Math.sin(Z),ee=(B+k)/2,re=(L+R)/2;if(!y){switch(u.value){case"left":ee=k;break;case"right":ee=B;break}switch(l.value){case"top":re=R;break;case"bottom":re=L;break}}var W=v(function(Ie,ve){return Ie=Ie-ee,ve=ve-re,{x:Ie*J-ve*Q+ee,y:Ie*Q+ve*J+re}},"rotate"),N=W(B,L),U=W(B,R),te=W(k,L),oe=W(k,R);B=Math.min(N.x,U.x,te.x,oe.x),k=Math.max(N.x,U.x,te.x,oe.x),L=Math.min(N.y,U.y,te.y,oe.y),R=Math.max(N.y,U.y,te.y,oe.y)}var ue=K+"Rot",Se=$[ue]=$[ue]||{};Se.x1=B,Se.y1=L,Se.x2=k,Se.y2=R,Se.w=k-B,Se.h=R-L,Bt(e,B,L,k,R),Bt(i.labelBounds.all,B,L,k,R)}return e}},"updateBoundsFromLabel"),xg=v(function(e,r){if(!r.cy().headless()){var a=r.pstyle("outline-opacity").value,n=r.pstyle("outline-width").value;if(a>0&&n>0){var i=r.pstyle("outline-offset").value,o=r.pstyle("shape").value,s=n+i,u=(e.w+s*2)/e.w,l=(e.h+s*2)/e.h,c=0,f=0;["diamond","pentagon","round-triangle"].includes(o)?(u=(e.w+s*2.4)/e.w,f=-s/3.6):["concave-hexagon","rhomboid","right-rhomboid"].includes(o)?u=(e.w+s*2.4)/e.w:o==="star"?(u=(e.w+s*2.8)/e.w,l=(e.h+s*2.6)/e.h,f=-s/3.8):o==="triangle"?(u=(e.w+s*2.8)/e.w,l=(e.h+s*2.4)/e.h,f=-s/1.4):o==="vee"&&(u=(e.w+s*4.4)/e.w,l=(e.h+s*3.8)/e.h,f=-s*.5);var d=e.h*l-e.h,g=e.w*u-e.w;if(un(e,[Math.ceil(d/2),Math.ceil(g/2)]),c!=0||f!==0){var h=Rf(e,c,f);Tl(e,h)}}}},"updateBoundsFromOutline"),Eg=v(function(e,r){var a=e._private.cy,n=a.styleEnabled(),i=a.headless(),o=bt(),s=e._private,u=e.isNode(),l=e.isEdge(),c,f,d,g,h,m,y=s.rstyle,p=u&&n?e.pstyle("bounds-expansion").pfValue:[0],b=v(function(Le){return Le.pstyle("display").value!=="none"},"isDisplayed"),w=!n||b(e)&&(!l||b(e.source())&&b(e.target()));if(w){var x=0,S=0;n&&r.includeOverlays&&(x=e.pstyle("overlay-opacity").value,x!==0&&(S=e.pstyle("overlay-padding").value));var C=0,E=0;n&&r.includeUnderlays&&(C=e.pstyle("underlay-opacity").value,C!==0&&(E=e.pstyle("underlay-padding").value));var D=Math.max(S,E),T=0,A=0;if(n&&(T=e.pstyle("width").pfValue,A=T/2),u&&r.includeNodes){var B=e.position();h=B.x,m=B.y;var k=e.outerWidth(),L=k/2,R=e.outerHeight(),M=R/2;c=h-L,f=h+L,d=m-M,g=m+M,Bt(o,c,d,f,g),n&&r.includeOutlines&&xg(o,e)}else if(l&&r.includeEdges)if(n&&!i){var I=e.pstyle("curve-style").strValue;if(c=Math.min(y.srcX,y.midX,y.tgtX),f=Math.max(y.srcX,y.midX,y.tgtX),d=Math.min(y.srcY,y.midY,y.tgtY),g=Math.max(y.srcY,y.midY,y.tgtY),c-=A,f+=A,d-=A,g+=A,Bt(o,c,d,f,g),I==="haystack"){var O=y.haystackPts;if(O&&O.length===2){if(c=O[0].x,d=O[0].y,f=O[1].x,g=O[1].y,c>f){var F=c;c=f,f=F}if(d>g){var K=d;d=g,g=K}Bt(o,c-A,d-A,f+A,g+A)}}else if(I==="bezier"||I==="unbundled-bezier"||I.endsWith("segments")||I.endsWith("taxi")){var $;switch(I){case"bezier":case"unbundled-bezier":$=y.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":$=y.linePts;break}if($!=null)for(var q=0;q<$.length;q++){var G=$[q];c=G.x-A,f=G.x+A,d=G.y-A,g=G.y+A,Bt(o,c,d,f,g)}}}else{var X=e.source(),Z=X.position(),J=e.target(),Q=J.position();if(c=Z.x,f=Q.x,d=Z.y,g=Q.y,c>f){var ee=c;c=f,f=ee}if(d>g){var re=d;d=g,g=re}c-=A,f+=A,d-=A,g+=A,Bt(o,c,d,f,g)}if(n&&r.includeEdges&&l&&(rn(o,e,"mid-source"),rn(o,e,"mid-target"),rn(o,e,"source"),rn(o,e,"target")),n){var W=e.pstyle("ghost").value==="yes";if(W){var N=e.pstyle("ghost-offset-x").pfValue,U=e.pstyle("ghost-offset-y").pfValue;Bt(o,o.x1+N,o.y1+U,o.x2+N,o.y2+U)}}var te=s.bodyBounds=s.bodyBounds||{};Go(te,o),un(te,p),ln(te,1),n&&(c=o.x1,f=o.x2,d=o.y1,g=o.y2,Bt(o,c-D,d-D,f+D,g+D));var oe=s.overlayBounds=s.overlayBounds||{};Go(oe,o),un(oe,p),ln(oe,1);var ue=s.labelBounds=s.labelBounds||{};ue.all!=null?Af(ue.all):ue.all=bt(),n&&r.includeLabels&&(r.includeMainLabels&&si(o,e,null),l&&(r.includeSourceLabels&&si(o,e,"source"),r.includeTargetLabels&&si(o,e,"target")))}return o.x1=St(o.x1),o.y1=St(o.y1),o.x2=St(o.x2),o.y2=St(o.y2),o.w=St(o.x2-o.x1),o.h=St(o.y2-o.y1),o.w>0&&o.h>0&&w&&(un(o,p),ln(o,1)),o},"boundingBoxImpl"),Ou=v(function(e){var r=0,a=v(function(o){return(o?1:0)<<r++},"tf"),n=0;return n+=a(e.incudeNodes),n+=a(e.includeEdges),n+=a(e.includeLabels),n+=a(e.includeMainLabels),n+=a(e.includeSourceLabels),n+=a(e.includeTargetLabels),n+=a(e.includeOverlays),n+=a(e.includeOutlines),n},"getKey"),Fu=v(function(e){if(e.isEdge()){var r=e.source().position(),a=e.target().position(),n=v(function(o){return Math.round(o)},"r");return Zc([n(r.x),n(r.y),n(a.x),n(a.y)])}else return 0},"getBoundingBoxPosKey"),vs=v(function(e,r){var a=e._private,n,i=e.isEdge(),o=r==null?cs:Ou(r),s=o===cs,u=Fu(e),l=a.bbCachePosKey===u,c=r.useCache&&l,f=v(function(m){return m._private.bbCache==null||m._private.styleDirty},"isDirty"),d=!c||f(e)||i&&(f(e.source())||f(e.target()));if(d?(l||e.recalculateRenderedStyle(c),n=Eg(e,Ba),a.bbCache=n,a.bbCachePosKey=u):n=a.bbCache,!s){var g=e.isNode();n=bt(),(r.includeNodes&&g||r.includeEdges&&!g)&&(r.includeOverlays?pr(n,a.overlayBounds):pr(n,a.bodyBounds)),r.includeLabels&&(r.includeMainLabels&&(!i||r.includeSourceLabels&&r.includeTargetLabels)?pr(n,a.labelBounds.all):(r.includeMainLabels&&pr(n,a.labelBounds.mainRot),r.includeSourceLabels&&pr(n,a.labelBounds.sourceRot),r.includeTargetLabels&&pr(n,a.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},"cachedBoundingBoxImpl"),Ba={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,includeOutlines:!0,useCache:!0},cs=Ou(Ba),fs=et(Ba);vr.boundingBox=function(t){var e;if(this.length===1&&this[0]._private.bbCache!=null&&!this[0]._private.styleDirty&&(t===void 0||t.useCache===void 0||t.useCache===!0))t===void 0?t=Ba:t=fs(t),e=vs(this[0],t);else{e=bt(),t=t||Ba;var r=fs(t),a=this,n=a.cy(),i=n.styleEnabled();if(i)for(var o=0;o<a.length;o++){var s=a[o],u=s._private,l=Fu(s),c=u.bbCachePosKey===l,f=r.useCache&&c&&!u.styleDirty;s.recalculateRenderedStyle(f)}this.updateCompoundBounds(!t.useCache);for(var d=0;d<a.length;d++){var g=a[d];pr(e,vs(g,r))}}return e.x1=St(e.x1),e.y1=St(e.y1),e.x2=St(e.x2),e.y2=St(e.y2),e.w=St(e.x2-e.x1),e.h=St(e.y2-e.y1),e};vr.dirtyBoundingBoxCache=function(){for(var t=0;t<this.length;t++){var e=this[t]._private;e.bbCache=null,e.bbCachePosKey=null,e.bodyBounds=null,e.overlayBounds=null,e.labelBounds.all=null,e.labelBounds.source=null,e.labelBounds.target=null,e.labelBounds.main=null,e.labelBounds.sourceRot=null,e.labelBounds.targetRot=null,e.labelBounds.mainRot=null,e.arrowBounds.source=null,e.arrowBounds.target=null,e.arrowBounds["mid-source"]=null,e.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this};vr.boundingBoxAt=function(t){var e=this.nodes(),r=this.cy(),a=r.hasCompoundNodes(),n=r.collection();if(a&&(n=e.filter(function(l){return l.isParent()}),e=e.not(n)),ke(t)){var i=t;t=v(function(){return i},"fn")}var o=v(function(c,f){return c._private.bbAtOldPos=t(c,f)},"storeOldPos"),s=v(function(c){return c._private.bbAtOldPos},"getOldPos");r.startBatch(),e.forEach(o).silentPositions(t),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0));var u=Bf(this.boundingBox({useCache:!1}));return e.silentPositions(s),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0)),r.endBatch(),u};Wr.boundingbox=Wr.bb=Wr.boundingBox;Wr.renderedBoundingbox=Wr.renderedBoundingBox;var Cg=vr,da,Va;da=Va={};var Nu=v(function(e){e.uppercaseName=Oo(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=Oo(e.outerName),da[e.name]=v(function(){var a=this[0],n=a._private,i=n.cy,o=i._private.styleEnabled;if(a)if(o){if(a.isParent())return a.updateCompoundBounds(),n[e.autoName]||0;var s=a.pstyle(e.name);switch(s.strValue){case"label":return a.recalculateRenderedStyle(),n.rstyle[e.labelName]||0;default:return s.pfValue}}else return 1},"dimImpl"),da["outer"+e.uppercaseName]=v(function(){var a=this[0],n=a._private,i=n.cy,o=i._private.styleEnabled;if(a)if(o){var s=a[e.name](),u=a.pstyle("border-width").pfValue,l=2*a.padding();return s+u+l}else return 1},"outerDimImpl"),da["rendered"+e.uppercaseName]=v(function(){var a=this[0];if(a){var n=a[e.name]();return n*this.cy().zoom()}},"renderedDimImpl"),da["rendered"+e.uppercaseOuterName]=v(function(){var a=this[0];if(a){var n=a[e.outerName]();return n*this.cy().zoom()}},"renderedOuterDimImpl")},"defineDimFns");Nu({name:"width"});Nu({name:"height"});Va.padding=function(){var t=this[0],e=t._private;return t.isParent()?(t.updateCompoundBounds(),e.autoPadding!==void 0?e.autoPadding:t.pstyle("padding").pfValue):t.pstyle("padding").pfValue};Va.paddedHeight=function(){var t=this[0];return t.height()+2*t.padding()};Va.paddedWidth=function(){var t=this[0];return t.width()+2*t.padding()};var Tg=Va,Sg=v(function(e,r){if(e.isEdge())return r(e)},"ifEdge"),Dg=v(function(e,r){if(e.isEdge()){var a=e.cy();return Nn(r(e),a.zoom(),a.pan())}},"ifEdgeRenderedPosition"),Pg=v(function(e,r){if(e.isEdge()){var a=e.cy(),n=a.pan(),i=a.zoom();return r(e).map(function(o){return Nn(o,i,n)})}},"ifEdgeRenderedPositions"),kg=v(function(e){return e.renderer().getControlPoints(e)},"controlPoints"),Bg=v(function(e){return e.renderer().getSegmentPoints(e)},"segmentPoints"),Ag=v(function(e){return e.renderer().getSourceEndpoint(e)},"sourceEndpoint"),Rg=v(function(e){return e.renderer().getTargetEndpoint(e)},"targetEndpoint"),Lg=v(function(e){return e.renderer().getEdgeMidpoint(e)},"midpoint"),ds={controlPoints:{get:kg,mult:!0},segmentPoints:{get:Bg,mult:!0},sourceEndpoint:{get:Ag},targetEndpoint:{get:Rg},midpoint:{get:Lg}},Ig=v(function(e){return"rendered"+e[0].toUpperCase()+e.substr(1)},"renderedName"),Mg=Object.keys(ds).reduce(function(t,e){var r=ds[e],a=Ig(e);return t[e]=function(){return Sg(this,r.get)},r.mult?t[a]=function(){return Pg(this,r.get)}:t[a]=function(){return Dg(this,r.get)},t},{}),Og=he({},wg,Cg,Tg,Mg),zu=v(function(e,r){this.recycle(e,r)},"Event");function Fr(){return!1}v(Fr,"returnFalse");function ha(){return!0}v(ha,"returnTrue");zu.prototype={instanceString:v(function(){return"event"},"instanceString"),recycle:v(function(e,r){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=Fr,e!=null&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?ha:Fr):e!=null&&e.type?r=e:this.type=e,r!=null&&(this.originalEvent=r.originalEvent,this.type=r.type!=null?r.type:this.type,this.cy=r.cy,this.target=r.target,this.position=r.position,this.renderedPosition=r.renderedPosition,this.namespace=r.namespace,this.layout=r.layout),this.cy!=null&&this.position!=null&&this.renderedPosition==null){var a=this.position,n=this.cy.zoom(),i=this.cy.pan();this.renderedPosition={x:a.x*n+i.x,y:a.y*n+i.y}}this.timeStamp=e&&e.timeStamp||Date.now()},"recycle"),preventDefault:v(function(){this.isDefaultPrevented=ha;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},"preventDefault"),stopPropagation:v(function(){this.isPropagationStopped=ha;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},"stopPropagation"),stopImmediatePropagation:v(function(){this.isImmediatePropagationStopped=ha,this.stopPropagation()},"stopImmediatePropagation"),isDefaultPrevented:Fr,isPropagationStopped:Fr,isImmediatePropagationStopped:Fr};var Vu=/^([^.]+)(\.(?:[^.]+))?$/,Fg=".*",qu={qualifierCompare:v(function(e,r){return e===r},"qualifierCompare"),eventMatches:v(function(){return!0},"eventMatches"),addEventFields:v(function(){},"addEventFields"),callbackContext:v(function(e){return e},"callbackContext"),beforeEmit:v(function(){},"beforeEmit"),afterEmit:v(function(){},"afterEmit"),bubble:v(function(){return!1},"bubble"),parent:v(function(){return null},"parent"),context:null},hs=Object.keys(qu),Ng={};function qa(){for(var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Ng,e=arguments.length>1?arguments[1]:void 0,r=0;r<hs.length;r++){var a=hs[r];this[a]=t[a]||qu[a]}this.context=e||this.context,this.listeners=[],this.emitting=0}v(qa,"Emitter");var ur=qa.prototype,Ku=v(function(e,r,a,n,i,o,s){Ge(n)&&(i=n,n=null),s&&(o==null?o=s:o=he({},o,s));for(var u=Oe(a)?a:a.split(/\s+/),l=0;l<u.length;l++){var c=u[l];if(!nr(c)){var f=c.match(Vu);if(f){var d=f[1],g=f[2]?f[2]:null,h=r(e,c,d,g,n,i,o);if(h===!1)break}}}},"forEachEvent"),gs=v(function(e,r){return e.addEventFields(e.context,r),new zu(r.type,r)},"makeEventObj"),zg=v(function(e,r,a){if(nc(a)){r(e,a);return}else if(ke(a)){r(e,gs(e,a));return}for(var n=Oe(a)?a:a.split(/\s+/),i=0;i<n.length;i++){var o=n[i];if(!nr(o)){var s=o.match(Vu);if(s){var u=s[1],l=s[2]?s[2]:null,c=gs(e,{type:u,namespace:l,target:e.context});r(e,c)}}}},"forEachEventObj");ur.on=ur.addListener=function(t,e,r,a,n){return Ku(this,function(i,o,s,u,l,c,f){Ge(c)&&i.listeners.push({event:o,callback:c,type:s,namespace:u,qualifier:l,conf:f})},t,e,r,a,n),this};ur.one=function(t,e,r,a){return this.on(t,e,r,a,{one:!0})};ur.removeListener=ur.off=function(t,e,r,a){var n=this;this.emitting!==0&&(this.listeners=tf(this.listeners));for(var i=this.listeners,o=v(function(l){var c=i[l];Ku(n,function(f,d,g,h,m,y){if((c.type===g||t==="*")&&(!h&&c.namespace!==".*"||c.namespace===h)&&(!m||f.qualifierCompare(c.qualifier,m))&&(!y||c.callback===y))return i.splice(l,1),!1},t,e,r,a)},"_loop"),s=i.length-1;s>=0;s--)o(s);return this};ur.removeAllListeners=function(){return this.removeListener("*")};ur.emit=ur.trigger=function(t,e,r){var a=this.listeners,n=a.length;return this.emitting++,Oe(e)||(e=[e]),zg(this,function(i,o){r!=null&&(a=[{event:o.event,type:o.type,namespace:o.namespace,callback:r}],n=a.length);for(var s=v(function(c){var f=a[c];if(f.type===o.type&&(!f.namespace||f.namespace===o.namespace||f.namespace===Fg)&&i.eventMatches(i.context,f,o)){var d=[o];e!=null&&af(d,e),i.beforeEmit(i.context,f,o),f.conf&&f.conf.one&&(i.listeners=i.listeners.filter(function(m){return m!==f}));var g=i.callbackContext(i.context,f,o),h=f.callback.apply(g,d);i.afterEmit(i.context,f,o),h===!1&&(o.stopPropagation(),o.preventDefault())}},"_loop2"),u=0;u<n;u++)s(u);i.bubble(i.context)&&!o.isPropagationStopped()&&i.parent(i.context).emit(o,e)},t),this.emitting--,this};var Vg={qualifierCompare:v(function(e,r){return e==null||r==null?e==null&&r==null:e.sameText(r)},"qualifierCompare"),eventMatches:v(function(e,r,a){var n=r.qualifier;return n!=null?e!==a.target&&Ma(a.target)&&n.matches(a.target):!0},"eventMatches"),addEventFields:v(function(e,r){r.cy=e.cy(),r.target=e},"addEventFields"),callbackContext:v(function(e,r,a){return r.qualifier!=null?a.target:e},"callbackContext"),beforeEmit:v(function(e,r){r.conf&&r.conf.once&&r.conf.onceCollection.removeListener(r.event,r.qualifier,r.callback)},"beforeEmit"),bubble:v(function(){return!0},"bubble"),parent:v(function(e){return e.isChild()?e.parent():e.cy()},"parent")},an=v(function(e){return fe(e)?new sr(e):e},"argSelector"),Gu={createEmitter:v(function(){for(var e=0;e<this.length;e++){var r=this[e],a=r._private;a.emitter||(a.emitter=new qa(Vg,r))}return this},"createEmitter"),emitter:v(function(){return this._private.emitter},"emitter"),on:v(function(e,r,a){for(var n=an(r),i=0;i<this.length;i++){var o=this[i];o.emitter().on(e,n,a)}return this},"on"),removeListener:v(function(e,r,a){for(var n=an(r),i=0;i<this.length;i++){var o=this[i];o.emitter().removeListener(e,n,a)}return this},"removeListener"),removeAllListeners:v(function(){for(var e=0;e<this.length;e++){var r=this[e];r.emitter().removeAllListeners()}return this},"removeAllListeners"),one:v(function(e,r,a){for(var n=an(r),i=0;i<this.length;i++){var o=this[i];o.emitter().one(e,n,a)}return this},"one"),once:v(function(e,r,a){for(var n=an(r),i=0;i<this.length;i++){var o=this[i];o.emitter().on(e,n,a,{once:!0,onceCollection:this})}},"once"),emit:v(function(e,r){for(var a=0;a<this.length;a++){var n=this[a];n.emitter().emit(e,r)}return this},"emit"),emitAndNotify:v(function(e,r){if(this.length!==0)return this.cy().notify(e,this),this.emit(e,r),this},"emitAndNotify")};Ae.eventAliasesOn(Gu);var Hu={nodes:v(function(e){return this.filter(function(r){return r.isNode()}).filter(e)},"nodes"),edges:v(function(e){return this.filter(function(r){return r.isEdge()}).filter(e)},"edges"),byGroup:v(function(){for(var e=this.spawn(),r=this.spawn(),a=0;a<this.length;a++){var n=this[a];n.isNode()?e.push(n):r.push(n)}return{nodes:e,edges:r}},"byGroup"),filter:v(function(e,r){if(e===void 0)return this;if(fe(e)||Et(e))return new sr(e).filter(this);if(Ge(e)){for(var a=this.spawn(),n=this,i=0;i<n.length;i++){var o=n[i],s=r?e.apply(r,[o,i,n]):e(o,i,n);s&&a.push(o)}return a}return this.spawn()},"filter"),not:v(function(e){if(e){fe(e)&&(e=this.filter(e));for(var r=this.spawn(),a=0;a<this.length;a++){var n=this[a],i=e.has(n);i||r.push(n)}return r}else return this},"not"),absoluteComplement:v(function(){var e=this.cy();return e.mutableElements().not(this)},"absoluteComplement"),intersect:v(function(e){if(fe(e)){var r=e;return this.filter(r)}for(var a=this.spawn(),n=this,i=e,o=this.length<e.length,s=o?n:i,u=o?i:n,l=0;l<s.length;l++){var c=s[l];u.has(c)&&a.push(c)}return a},"intersect"),xor:v(function(e){var r=this._private.cy;fe(e)&&(e=r.$(e));var a=this.spawn(),n=this,i=e,o=v(function(u,l){for(var c=0;c<u.length;c++){var f=u[c],d=f._private.data.id,g=l.hasElementWithId(d);g||a.push(f)}},"add");return o(n,i),o(i,n),a},"xor"),diff:v(function(e){var r=this._private.cy;fe(e)&&(e=r.$(e));var a=this.spawn(),n=this.spawn(),i=this.spawn(),o=this,s=e,u=v(function(c,f,d){for(var g=0;g<c.length;g++){var h=c[g],m=h._private.data.id,y=f.hasElementWithId(m);y?i.merge(h):d.push(h)}},"add");return u(o,s,a),u(s,o,n),{left:a,right:n,both:i}},"diff"),add:v(function(e){var r=this._private.cy;if(!e)return this;if(fe(e)){var a=e;e=r.mutableElements().filter(a)}for(var n=this.spawnSelf(),i=0;i<e.length;i++){var o=e[i],s=!this.has(o);s&&n.push(o)}return n},"add"),merge:v(function(e){var r=this._private,a=r.cy;if(!e)return this;if(e&&fe(e)){var n=e;e=a.mutableElements().filter(n)}for(var i=r.map,o=0;o<e.length;o++){var s=e[o],u=s._private.data.id,l=!i.has(u);if(l){var c=this.length++;this[c]=s,i.set(u,{ele:s,index:c})}}return this},"merge"),unmergeAt:v(function(e){var r=this[e],a=r.id(),n=this._private,i=n.map;this[e]=void 0,i.delete(a);var o=e===this.length-1;if(this.length>1&&!o){var s=this.length-1,u=this[s],l=u._private.data.id;this[s]=void 0,this[e]=u,i.set(l,{ele:u,index:e})}return this.length--,this},"unmergeAt"),unmergeOne:v(function(e){e=e[0];var r=this._private,a=e._private.data.id,n=r.map,i=n.get(a);if(!i)return this;var o=i.index;return this.unmergeAt(o),this},"unmergeOne"),unmerge:v(function(e){var r=this._private.cy;if(!e)return this;if(e&&fe(e)){var a=e;e=r.mutableElements().filter(a)}for(var n=0;n<e.length;n++)this.unmergeOne(e[n]);return this},"unmerge"),unmergeBy:v(function(e){for(var r=this.length-1;r>=0;r--){var a=this[r];e(a)&&this.unmergeAt(r)}return this},"unmergeBy"),map:v(function(e,r){for(var a=[],n=this,i=0;i<n.length;i++){var o=n[i],s=r?e.apply(r,[o,i,n]):e(o,i,n);a.push(s)}return a},"map"),reduce:v(function(e,r){for(var a=r,n=this,i=0;i<n.length;i++)a=e(a,n[i],i,n);return a},"reduce"),max:v(function(e,r){for(var a=-1/0,n,i=this,o=0;o<i.length;o++){var s=i[o],u=r?e.apply(r,[s,o,i]):e(s,o,i);u>a&&(a=u,n=s)}return{value:a,ele:n}},"max"),min:v(function(e,r){for(var a=1/0,n,i=this,o=0;o<i.length;o++){var s=i[o],u=r?e.apply(r,[s,o,i]):e(s,o,i);u<a&&(a=u,n=s)}return{value:a,ele:n}},"min")},Be=Hu;Be.u=Be["|"]=Be["+"]=Be.union=Be.or=Be.add;Be["\\"]=Be["!"]=Be["-"]=Be.difference=Be.relativeComplement=Be.subtract=Be.not;Be.n=Be["&"]=Be["."]=Be.and=Be.intersection=Be.intersect;Be["^"]=Be["(+)"]=Be["(-)"]=Be.symmetricDifference=Be.symdiff=Be.xor;Be.fnFilter=Be.filterFn=Be.stdFilter=Be.filter;Be.complement=Be.abscomp=Be.absoluteComplement;var qg={isNode:v(function(){return this.group()==="nodes"},"isNode"),isEdge:v(function(){return this.group()==="edges"},"isEdge"),isLoop:v(function(){return this.isEdge()&&this.source()[0]===this.target()[0]},"isLoop"),isSimple:v(function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},"isSimple"),group:v(function(){var e=this[0];if(e)return e._private.group},"group")},Wu=v(function(e,r){var a=e.cy(),n=a.hasCompoundNodes();function i(c){var f=c.pstyle("z-compound-depth");return f.value==="auto"?n?c.zDepth():0:f.value==="bottom"?-1:f.value==="top"?$i:0}v(i,"getDepth");var o=i(e)-i(r);if(o!==0)return o;function s(c){var f=c.pstyle("z-index-compare");return f.value==="auto"&&c.isNode()?1:0}v(s,"getEleDepth");var u=s(e)-s(r);if(u!==0)return u;var l=e.pstyle("z-index").value-r.pstyle("z-index").value;return l!==0?l:e.poolIndex()-r.poolIndex()},"zIndexSort"),kn={forEach:v(function(e,r){if(Ge(e))for(var a=this.length,n=0;n<a;n++){var i=this[n],o=r?e.apply(r,[i,n,this]):e(i,n,this);if(o===!1)break}return this},"forEach"),toArray:v(function(){for(var e=[],r=0;r<this.length;r++)e.push(this[r]);return e},"toArray"),slice:v(function(e,r){var a=[],n=this.length;r==null&&(r=n),e==null&&(e=0),e<0&&(e=n+e),r<0&&(r=n+r);for(var i=e;i>=0&&i<r&&i<n;i++)a.push(this[i]);return this.spawn(a)},"slice"),size:v(function(){return this.length},"size"),eq:v(function(e){return this[e]||this.spawn()},"eq"),first:v(function(){return this[0]||this.spawn()},"first"),last:v(function(){return this[this.length-1]||this.spawn()},"last"),empty:v(function(){return this.length===0},"empty"),nonempty:v(function(){return!this.empty()},"nonempty"),sort:v(function(e){if(!Ge(e))return this;var r=this.toArray().sort(e);return this.spawn(r)},"sort"),sortByZIndex:v(function(){return this.sort(Wu)},"sortByZIndex"),zDepth:v(function(){var e=this[0];if(e){var r=e._private,a=r.group;if(a==="nodes"){var n=r.data.parent?e.parents().size():0;return e.isParent()?n:$i-1}else{var i=r.source,o=r.target,s=i.zDepth(),u=o.zDepth();return Math.max(s,u,0)}}},"zDepth")};kn.each=kn.forEach;var Kg=v(function(){var e="undefined",r=(typeof Symbol>"u"?"undefined":$e(Symbol))!=e&&$e(Symbol.iterator)!=e;r&&(kn[Symbol.iterator]=function(){var a=this,n={value:void 0,done:!1},i=0,o=this.length;return Ki({next:v(function(){return i<o?n.value=a[i++]:(n.value=void 0,n.done=!0),n},"next")},Symbol.iterator,function(){return this})})},"defineSymbolIterator");Kg();var Gg=et({nodeDimensionsIncludeLabels:!1}),fn={layoutDimensions:v(function(e){e=Gg(e);var r;if(!this.takesUpSpace())r={w:0,h:0};else if(e.nodeDimensionsIncludeLabels){var a=this.boundingBox();r={w:a.w,h:a.h}}else r={w:this.outerWidth(),h:this.outerHeight()};return(r.w===0||r.h===0)&&(r.w=r.h=1),r},"layoutDimensions"),layoutPositions:v(function(e,r,a){var n=this.nodes().filter(function(S){return!S.isParent()}),i=this.cy(),o=r.eles,s=v(function(C){return C.id()},"getMemoizeKey"),u=xa(a,s);e.emit({type:"layoutstart",layout:e}),e.animations=[];var l=v(function(C,E,D){var T={x:E.x1+E.w/2,y:E.y1+E.h/2},A={x:(D.x-T.x)*C,y:(D.y-T.y)*C};return{x:T.x+A.x,y:T.y+A.y}},"calculateSpacing"),c=r.spacingFactor&&r.spacingFactor!==1,f=v(function(){if(!c)return null;for(var C=bt(),E=0;E<n.length;E++){var D=n[E],T=u(D,E);Lf(C,T.x,T.y)}return C},"spacingBb"),d=f(),g=xa(function(S,C){var E=u(S,C);if(c){var D=Math.abs(r.spacingFactor);E=l(D,d,E)}return r.transform!=null&&(E=r.transform(S,E)),E},s);if(r.animate){for(var h=0;h<n.length;h++){var m=n[h],y=g(m,h),p=r.animateFilter==null||r.animateFilter(m,h);if(p){var b=m.animation({position:y,duration:r.animationDuration,easing:r.animationEasing});e.animations.push(b)}else m.position(y)}if(r.fit){var w=i.animation({fit:{boundingBox:o.boundingBoxAt(g),padding:r.padding},duration:r.animationDuration,easing:r.animationEasing});e.animations.push(w)}else if(r.zoom!==void 0&&r.pan!==void 0){var x=i.animation({zoom:r.zoom,pan:r.pan,duration:r.animationDuration,easing:r.animationEasing});e.animations.push(x)}e.animations.forEach(function(S){return S.play()}),e.one("layoutready",r.ready),e.emit({type:"layoutready",layout:e}),Jr.all(e.animations.map(function(S){return S.promise()})).then(function(){e.one("layoutstop",r.stop),e.emit({type:"layoutstop",layout:e})})}else n.positions(g),r.fit&&i.fit(r.eles,r.padding),r.zoom!=null&&i.zoom(r.zoom),r.pan&&i.pan(r.pan),e.one("layoutready",r.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",r.stop),e.emit({type:"layoutstop",layout:e});return this},"layoutPositions"),layout:v(function(e){var r=this.cy();return r.makeLayout(he({},e,{eles:this}))},"layout")};fn.createLayout=fn.makeLayout=fn.layout;function no(t,e,r){var a=r._private,n=a.styleCache=a.styleCache||[],i;return(i=n[t])!=null||(i=n[t]=e(r)),i}v(no,"styleCache");function Ka(t,e){return t=ir(t),v(function(a){return no(t,e,a)},"cachedStyleFunction")}v(Ka,"cacheStyleFunction");function Ga(t,e){t=ir(t);var r=v(function(n){return e.call(n)},"selfFn");return v(function(){var n=this[0];if(n)return no(t,r,n)},"cachedPrototypeStyleFunction")}v(Ga,"cachePrototypeStyleFunction");var rt={recalculateRenderedStyle:v(function(e){var r=this.cy(),a=r.renderer(),n=r.styleEnabled();return a&&n&&a.recalculateRenderedStyle(this,e),this},"recalculateRenderedStyle"),dirtyStyleCache:v(function(){var e=this.cy(),r=v(function(i){return i._private.styleCache=null},"dirty");if(e.hasCompoundNodes()){var a;a=this.spawnSelf().merge(this.descendants()).merge(this.parents()),a.merge(a.connectedEdges()),a.forEach(r)}else this.forEach(function(n){r(n),n.connectedEdges().forEach(r)});return this},"dirtyStyleCache"),updateStyle:v(function(e){var r=this._private.cy;if(!r.styleEnabled())return this;if(r.batching()){var a=r._private.batchStyleEles;return a.merge(this),this}var n=r.hasCompoundNodes(),i=this;e=!!(e||e===void 0),n&&(i=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var o=i;return e?o.emitAndNotify("style"):o.emit("style"),i.forEach(function(s){return s._private.styleDirty=!0}),this},"updateStyle"),cleanStyle:v(function(){var e=this.cy();if(e.styleEnabled())for(var r=0;r<this.length;r++){var a=this[r];a._private.styleDirty&&(a._private.styleDirty=!1,e.style().apply(a))}},"cleanStyle"),parsedStyle:v(function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,a=this[0],n=a.cy();if(n.styleEnabled()&&a){a._private.styleDirty&&(a._private.styleDirty=!1,n.style().apply(a));var i=a._private.style[e];return i??(r?n.style().getDefaultProperty(e):null)}},"parsedStyle"),numericStyle:v(function(e){var r=this[0];if(r.cy().styleEnabled()&&r){var a=r.pstyle(e);return a.pfValue!==void 0?a.pfValue:a.value}},"numericStyle"),numericStyleUnits:v(function(e){var r=this[0];if(r.cy().styleEnabled()&&r)return r.pstyle(e).units},"numericStyleUnits"),renderedStyle:v(function(e){var r=this.cy();if(!r.styleEnabled())return this;var a=this[0];if(a)return r.style().getRenderedStyle(a,e)},"renderedStyle"),style:v(function(e,r){var a=this.cy();if(!a.styleEnabled())return this;var n=!1,i=a.style();if(ke(e)){var o=e;i.applyBypass(this,o,n),this.emitAndNotify("style")}else if(fe(e))if(r===void 0){var s=this[0];return s?i.getStylePropertyValue(s,e):void 0}else i.applyBypass(this,e,r,n),this.emitAndNotify("style");else if(e===void 0){var u=this[0];return u?i.getRawStyle(u):void 0}return this},"style"),removeStyle:v(function(e){var r=this.cy();if(!r.styleEnabled())return this;var a=!1,n=r.style(),i=this;if(e===void 0)for(var o=0;o<i.length;o++){var s=i[o];n.removeAllBypasses(s,a)}else{e=e.split(/\s+/);for(var u=0;u<i.length;u++){var l=i[u];n.removeBypasses(l,e,a)}}return this.emitAndNotify("style"),this},"removeStyle"),show:v(function(){return this.css("display","element"),this},"show"),hide:v(function(){return this.css("display","none"),this},"hide"),effectiveOpacity:v(function(){var e=this.cy();if(!e.styleEnabled())return 1;var r=e.hasCompoundNodes(),a=this[0];if(a){var n=a._private,i=a.pstyle("opacity").value;if(!r)return i;var o=n.data.parent?a.parents():null;if(o)for(var s=0;s<o.length;s++){var u=o[s],l=u.pstyle("opacity").value;i=l*i}return i}},"effectiveOpacity"),transparent:v(function(){var e=this.cy();if(!e.styleEnabled())return!1;var r=this[0],a=r.cy().hasCompoundNodes();if(r)return a?r.effectiveOpacity()===0:r.pstyle("opacity").value===0},"transparent"),backgrounding:v(function(){var e=this.cy();if(!e.styleEnabled())return!1;var r=this[0];return!!r._private.backgrounding},"backgrounding")};function dn(t,e){var r=t._private,a=r.data.parent?t.parents():null;if(a)for(var n=0;n<a.length;n++){var i=a[n];if(!e(i))return!1}return!0}v(dn,"checkCompound");function Wn(t){var e=t.ok,r=t.edgeOkViaNode||t.ok,a=t.parentOk||t.ok;return function(){var n=this.cy();if(!n.styleEnabled())return!0;var i=this[0],o=n.hasCompoundNodes();if(i){var s=i._private;if(!e(i))return!1;if(i.isNode())return!o||dn(i,a);var u=s.source,l=s.target;return r(u)&&(!o||dn(u,r))&&(u===l||r(l)&&(!o||dn(l,r)))}}}v(Wn,"defineDerivedStateFunction");var jr=Ka("eleTakesUpSpace",function(t){return t.pstyle("display").value==="element"&&t.width()!==0&&(t.isNode()?t.height()!==0:!0)});rt.takesUpSpace=Ga("takesUpSpace",Wn({ok:jr}));var Hg=Ka("eleInteractive",function(t){return t.pstyle("events").value==="yes"&&t.pstyle("visibility").value==="visible"&&jr(t)}),Wg=Ka("parentInteractive",function(t){return t.pstyle("visibility").value==="visible"&&jr(t)});rt.interactive=Ga("interactive",Wn({ok:Hg,parentOk:Wg,edgeOkViaNode:jr}));rt.noninteractive=function(){var t=this[0];if(t)return!t.interactive()};var $g=Ka("eleVisible",function(t){return t.pstyle("visibility").value==="visible"&&t.pstyle("opacity").pfValue!==0&&jr(t)}),Ug=jr;rt.visible=Ga("visible",Wn({ok:$g,edgeOkViaNode:Ug}));rt.hidden=function(){var t=this[0];if(t)return!t.visible()};rt.isBundledBezier=Ga("isBundledBezier",function(){return this.cy().styleEnabled()?!this.removed()&&this.pstyle("curve-style").value==="bezier"&&this.takesUpSpace():!1});rt.bypass=rt.css=rt.style;rt.renderedCss=rt.renderedStyle;rt.removeBypass=rt.removeCss=rt.removeStyle;rt.pstyle=rt.parsedStyle;var ar={};function wi(t){return function(){var e=arguments,r=[];if(e.length===2){var a=e[0],n=e[1];this.on(t.event,a,n)}else if(e.length===1&&Ge(e[0])){var i=e[0];this.on(t.event,i)}else if(e.length===0||e.length===1&&Oe(e[0])){for(var o=e.length===1?e[0]:null,s=0;s<this.length;s++){var u=this[s],l=!t.ableField||u._private[t.ableField],c=u._private[t.field]!=t.value;if(t.overrideAble){var f=t.overrideAble(u);if(f!==void 0&&(l=f,!f))return this}l&&(u._private[t.field]=t.value,c&&r.push(u))}var d=this.spawn(r);d.updateStyle(),d.emit(t.event),o&&d.emit(o)}return this}}v(wi,"defineSwitchFunction");function Ar(t){ar[t.field]=function(){var e=this[0];if(e){if(t.overrideField){var r=t.overrideField(e);if(r!==void 0)return r}return e._private[t.field]}},ar[t.on]=wi({event:t.on,field:t.field,ableField:t.ableField,overrideAble:t.overrideAble,value:!0}),ar[t.off]=wi({event:t.off,field:t.field,ableField:t.ableField,overrideAble:t.overrideAble,value:!1})}v(Ar,"defineSwitchSet");Ar({field:"locked",overrideField:v(function(e){return e.cy().autolock()?!0:void 0},"overrideField"),on:"lock",off:"unlock"});Ar({field:"grabbable",overrideField:v(function(e){return e.cy().autoungrabify()||e.pannable()?!1:void 0},"overrideField"),on:"grabify",off:"ungrabify"});Ar({field:"selected",ableField:"selectable",overrideAble:v(function(e){return e.cy().autounselectify()?!1:void 0},"overrideAble"),on:"select",off:"unselect"});Ar({field:"selectable",overrideField:v(function(e){return e.cy().autounselectify()?!1:void 0},"overrideField"),on:"selectify",off:"unselectify"});ar.deselect=ar.unselect;ar.grabbed=function(){var t=this[0];if(t)return t._private.grabbed};Ar({field:"active",on:"activate",off:"unactivate"});Ar({field:"pannable",on:"panify",off:"unpanify"});ar.inactive=function(){var t=this[0];if(t)return!t._private.active};var st={},ps=v(function(e){return v(function(a){for(var n=this,i=[],o=0;o<n.length;o++){var s=n[o];if(s.isNode()){for(var u=!1,l=s.connectedEdges(),c=0;c<l.length;c++){var f=l[c],d=f.source(),g=f.target();if(e.noIncomingEdges&&g===s&&d!==s||e.noOutgoingEdges&&d===s&&g!==s){u=!0;break}}u||i.push(s)}}return this.spawn(i,!0).filter(a)},"dagExtremityImpl")},"defineDagExtremity"),ys=v(function(e){return function(r){for(var a=this,n=[],i=0;i<a.length;i++){var o=a[i];if(o.isNode())for(var s=o.connectedEdges(),u=0;u<s.length;u++){var l=s[u],c=l.source(),f=l.target();e.outgoing&&c===o?(n.push(l),n.push(f)):e.incoming&&f===o&&(n.push(l),n.push(c))}}return this.spawn(n,!0).filter(r)}},"defineDagOneHop"),ms=v(function(e){return function(r){for(var a=this,n=[],i={};;){var o=e.outgoing?a.outgoers():a.incomers();if(o.length===0)break;for(var s=!1,u=0;u<o.length;u++){var l=o[u],c=l.id();i[c]||(i[c]=!0,n.push(l),s=!0)}if(!s)break;a=o}return this.spawn(n,!0).filter(r)}},"defineDagAllHops");st.clearTraversalCache=function(){for(var t=0;t<this.length;t++)this[t]._private.traversalCache=null};he(st,{roots:ps({noIncomingEdges:!0}),leaves:ps({noOutgoingEdges:!0}),outgoers:Dt(ys({outgoing:!0}),"outgoers"),successors:ms({outgoing:!0}),incomers:Dt(ys({incoming:!0}),"incomers"),predecessors:ms({incoming:!0})});he(st,{neighborhood:Dt(function(t){for(var e=[],r=this.nodes(),a=0;a<r.length;a++)for(var n=r[a],i=n.connectedEdges(),o=0;o<i.length;o++){var s=i[o],u=s.source(),l=s.target(),c=n===u?l:u;c.length>0&&e.push(c[0]),e.push(s[0])}return this.spawn(e,!0).filter(t)},"neighborhood"),closedNeighborhood:v(function(e){return this.neighborhood().add(this).filter(e)},"closedNeighborhood"),openNeighborhood:v(function(e){return this.neighborhood(e)},"openNeighborhood")});st.neighbourhood=st.neighborhood;st.closedNeighbourhood=st.closedNeighborhood;st.openNeighbourhood=st.openNeighborhood;he(st,{source:Dt(v(function(e){var r=this[0],a;return r&&(a=r._private.source||r.cy().collection()),a&&e?a.filter(e):a},"sourceImpl"),"source"),target:Dt(v(function(e){var r=this[0],a;return r&&(a=r._private.target||r.cy().collection()),a&&e?a.filter(e):a},"targetImpl"),"target"),sources:xi({attr:"source"}),targets:xi({attr:"target"})});function xi(t){return v(function(r){for(var a=[],n=0;n<this.length;n++){var i=this[n],o=i._private[t.attr];o&&a.push(o)}return this.spawn(a,!0).filter(r)},"sourceImpl")}v(xi,"defineSourceFunction");he(st,{edgesWith:Dt(Ei(),"edgesWith"),edgesTo:Dt(Ei({thisIsSrc:!0}),"edgesTo")});function Ei(t){return v(function(r){var a=[],n=this._private.cy,i=t||{};fe(r)&&(r=n.$(r));for(var o=0;o<r.length;o++)for(var s=r[o]._private.edges,u=0;u<s.length;u++){var l=s[u],c=l._private.data,f=this.hasElementWithId(c.source)&&r.hasElementWithId(c.target),d=r.hasElementWithId(c.source)&&this.hasElementWithId(c.target),g=f||d;g&&((i.thisIsSrc||i.thisIsTgt)&&(i.thisIsSrc&&!f||i.thisIsTgt&&!d)||a.push(l))}return this.spawn(a,!0)},"edgesWithImpl")}v(Ei,"defineEdgesWithFunction");he(st,{connectedEdges:Dt(function(t){for(var e=[],r=this,a=0;a<r.length;a++){var n=r[a];if(n.isNode())for(var i=n._private.edges,o=0;o<i.length;o++){var s=i[o];e.push(s)}}return this.spawn(e,!0).filter(t)},"connectedEdges"),connectedNodes:Dt(function(t){for(var e=[],r=this,a=0;a<r.length;a++){var n=r[a];n.isEdge()&&(e.push(n.source()[0]),e.push(n.target()[0]))}return this.spawn(e,!0).filter(t)},"connectedNodes"),parallelEdges:Dt(Ci(),"parallelEdges"),codirectedEdges:Dt(Ci({codirected:!0}),"codirectedEdges")});function Ci(t){var e={codirected:!1};return t=he({},e,t),v(function(a){for(var n=[],i=this.edges(),o=t,s=0;s<i.length;s++)for(var u=i[s],l=u._private,c=l.source,f=c._private.data.id,d=l.data.target,g=c._private.edges,h=0;h<g.length;h++){var m=g[h],y=m._private.data,p=y.target,b=y.source,w=p===d&&b===f,x=f===p&&d===b;(o.codirected&&w||!o.codirected&&(w||x))&&n.push(m)}return this.spawn(n,!0).filter(a)},"parallelEdgesImpl")}v(Ci,"defineParallelEdgesFunction");he(st,{components:v(function(e){var r=this,a=r.cy(),n=a.collection(),i=e==null?r.nodes():e.nodes(),o=[];e!=null&&i.empty()&&(i=e.sources());var s=v(function(c,f){n.merge(c),i.unmerge(c),f.merge(c)},"visitInComponent");if(i.empty())return r.spawn();var u=v(function(){var c=a.collection();o.push(c);var f=i[0];s(f,c),r.bfs({directed:!1,roots:f,visit:v(function(g){return s(g,c)},"visit")}),c.forEach(function(d){d.connectedEdges().forEach(function(g){r.has(g)&&c.has(g.source())&&c.has(g.target())&&c.merge(g)})})},"_loop");do u();while(i.length>0);return o},"components"),component:v(function(){var e=this[0];return e.cy().mutableElements().components(e)[0]},"component")});st.componentsOf=st.components;var at=v(function(e,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e===void 0){Ke("A collection must have a reference to the core");return}var i=new zt,o=!1;if(!r)r=[];else if(r.length>0&&ke(r[0])&&!Ma(r[0])){o=!0;for(var s=[],u=new Qr,l=0,c=r.length;l<c;l++){var f=r[l];f.data==null&&(f.data={});var d=f.data;if(d.id==null)d.id=wl();else if(e.hasElementWithId(d.id)||u.has(d.id))continue;var g=new Fn(e,f,!1);s.push(g),u.add(d.id)}r=s}this.length=0;for(var h=0,m=r.length;h<m;h++){var y=r[h][0];if(y!=null){var p=y._private.data.id;(!a||!i.has(p))&&(a&&i.set(p,{index:this.length,ele:y}),this[this.length]=y,this.length++)}}this._private={eles:this,cy:e,get map(){return this.lazyMap==null&&this.rebuildMap(),this.lazyMap},set map(b){this.lazyMap=b},rebuildMap:v(function(){for(var w=this.lazyMap=new zt,x=this.eles,S=0;S<x.length;S++){var C=x[S];w.set(C.id(),{index:S,ele:C})}},"rebuildMap")},a&&(this._private.map=i),o&&!n&&this.restore()},"Collection"),ze=Fn.prototype=at.prototype=Object.create(Array.prototype);ze.instanceString=function(){return"collection"};ze.spawn=function(t,e){return new at(this.cy(),t,e)};ze.spawnSelf=function(){return this.spawn(this)};ze.cy=function(){return this._private.cy};ze.renderer=function(){return this._private.cy.renderer()};ze.element=function(){return this[0]};ze.collection=function(){return Xs(this)?this:new at(this._private.cy,[this])};ze.unique=function(){return new at(this._private.cy,this,!0)};ze.hasElementWithId=function(t){return t=""+t,this._private.map.has(t)};ze.getElementById=function(t){t=""+t;var e=this._private.cy,r=this._private.map.get(t);return r?r.ele:new at(e)};ze.$id=ze.getElementById;ze.poolIndex=function(){var t=this._private.cy,e=t._private.elements,r=this[0]._private.data.id;return e._private.map.get(r).index};ze.indexOf=function(t){var e=t[0]._private.data.id;return this._private.map.get(e).index};ze.indexOfId=function(t){return t=""+t,this._private.map.get(t).index};ze.json=function(t){var e=this.element(),r=this.cy();if(e==null&&t)return this;if(e!=null){var a=e._private;if(ke(t)){if(r.startBatch(),t.data){e.data(t.data);var n=a.data;if(e.isEdge()){var i=!1,o={},s=t.data.source,u=t.data.target;s!=null&&s!=n.source&&(o.source=""+s,i=!0),u!=null&&u!=n.target&&(o.target=""+u,i=!0),i&&(e=e.move(o))}else{var l="parent"in t.data,c=t.data.parent;l&&(c!=null||n.parent!=null)&&c!=n.parent&&(c===void 0&&(c=null),c!=null&&(c=""+c),e=e.move({parent:c}))}}t.position&&e.position(t.position);var f=v(function(m,y,p){var b=t[m];b!=null&&b!==a[m]&&(b?e[y]():e[p]())},"checkSwitch");return f("removed","remove","restore"),f("selected","select","unselect"),f("selectable","selectify","unselectify"),f("locked","lock","unlock"),f("grabbable","grabify","ungrabify"),f("pannable","panify","unpanify"),t.classes!=null&&e.classes(t.classes),r.endBatch(),this}else if(t===void 0){var d={data:Nt(a.data),position:Nt(a.position),group:a.group,removed:a.removed,selected:a.selected,selectable:a.selectable,locked:a.locked,grabbable:a.grabbable,pannable:a.pannable,classes:null};d.classes="";var g=0;return a.classes.forEach(function(h){return d.classes+=g++===0?h:" "+h}),d}}};ze.jsons=function(){for(var t=[],e=0;e<this.length;e++){var r=this[e],a=r.json();t.push(a)}return t};ze.clone=function(){for(var t=this.cy(),e=[],r=0;r<this.length;r++){var a=this[r],n=a.json(),i=new Fn(t,n,!1);e.push(i)}return new at(t,e)};ze.copy=ze.clone;ze.restore=function(){for(var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,r=this,a=r.cy(),n=a._private,i=[],o=[],s,u=0,l=r.length;u<l;u++){var c=r[u];e&&!c.removed()||(c.isNode()?i.push(c):o.push(c))}s=i.concat(o);var f,d=v(function(){s.splice(f,1),f--},"removeFromElements");for(f=0;f<s.length;f++){var g=s[f],h=g._private,m=h.data;if(g.clearTraversalCache(),!(!e&&!h.removed)){if(m.id===void 0)m.id=wl();else if(ae(m.id))m.id=""+m.id;else if(nr(m.id)||!fe(m.id)){Ke("Can not create element with invalid string ID `"+m.id+"`"),d();continue}else if(a.hasElementWithId(m.id)){Ke("Can not create second element with ID `"+m.id+"`"),d();continue}}var y=m.id;if(g.isNode()){var p=h.position;p.x==null&&(p.x=0),p.y==null&&(p.y=0)}if(g.isEdge()){for(var b=g,w=["source","target"],x=w.length,S=!1,C=0;C<x;C++){var E=w[C],D=m[E];ae(D)&&(D=m[E]=""+m[E]),D==null||D===""?(Ke("Can not create edge `"+y+"` with unspecified "+E),S=!0):a.hasElementWithId(D)||(Ke("Can not create edge `"+y+"` with nonexistant "+E+" `"+D+"`"),S=!0)}if(S){d();continue}var T=a.getElementById(m.source),A=a.getElementById(m.target);T.same(A)?T._private.edges.push(b):(T._private.edges.push(b),A._private.edges.push(b)),b._private.source=T,b._private.target=A}h.map=new zt,h.map.set(y,{ele:g,index:0}),h.removed=!1,e&&a.addToPool(g)}for(var B=0;B<i.length;B++){var k=i[B],L=k._private.data;ae(L.parent)&&(L.parent=""+L.parent);var R=L.parent,M=R!=null;if(M||k._private.parent){var I=k._private.parent?a.collection().merge(k._private.parent):a.getElementById(R);if(I.empty())L.parent=void 0;else if(I[0].removed())Re("Node added with missing parent, reference to parent removed"),L.parent=void 0,k._private.parent=null;else{for(var O=!1,F=I;!F.empty();){if(k.same(F)){O=!0,L.parent=void 0;break}F=F.parent()}O||(I[0]._private.children.push(k),k._private.parent=I[0],n.hasCompoundNodes=!0)}}}if(s.length>0){for(var K=s.length===r.length?r:new at(a,s),$=0;$<K.length;$++){var q=K[$];q.isNode()||(q.parallelEdges().clearTraversalCache(),q.source().clearTraversalCache(),q.target().clearTraversalCache())}var G;n.hasCompoundNodes?G=a.collection().merge(K).merge(K.connectedNodes()).merge(K.parent()):G=K,G.dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(t),t?K.emitAndNotify("add"):e&&K.emit("add")}return r};ze.removed=function(){var t=this[0];return t&&t._private.removed};ze.inside=function(){var t=this[0];return t&&!t._private.removed};ze.remove=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,r=this,a=[],n={},i=r._private.cy;function o(R){for(var M=R._private.edges,I=0;I<M.length;I++)u(M[I])}v(o,"addConnectedEdges");function s(R){for(var M=R._private.children,I=0;I<M.length;I++)u(M[I])}v(s,"addChildren");function u(R){var M=n[R.id()];e&&R.removed()||M||(n[R.id()]=!0,R.isNode()?(a.push(R),o(R),s(R)):a.unshift(R))}v(u,"add");for(var l=0,c=r.length;l<c;l++){var f=r[l];u(f)}function d(R,M){var I=R._private.edges;or(I,M),R.clearTraversalCache()}v(d,"removeEdgeRef");function g(R){R.clearTraversalCache()}v(g,"removeParallelRef");var h=[];h.ids={};function m(R,M){M=M[0],R=R[0];var I=R._private.children,O=R.id();or(I,M),M._private.parent=null,h.ids[O]||(h.ids[O]=!0,h.push(R))}v(m,"removeChildRef"),r.dirtyCompoundBoundsCache(),e&&i.removeFromPool(a);for(var y=0;y<a.length;y++){var p=a[y];if(p.isEdge()){var b=p.source()[0],w=p.target()[0];d(b,p),d(w,p);for(var x=p.parallelEdges(),S=0;S<x.length;S++){var C=x[S];g(C),C.isBundledBezier()&&C.dirtyBoundingBoxCache()}}else{var E=p.parent();E.length!==0&&m(E,p)}e&&(p._private.removed=!0)}var D=i._private.elements;i._private.hasCompoundNodes=!1;for(var T=0;T<D.length;T++){var A=D[T];if(A.isParent()){i._private.hasCompoundNodes=!0;break}}var B=new at(this.cy(),a);B.size()>0&&(t?B.emitAndNotify("remove"):e&&B.emit("remove"));for(var k=0;k<h.length;k++){var L=h[k];(!e||!L.removed())&&L.updateStyle()}return B};ze.move=function(t){var e=this._private.cy,r=this,a=!1,n=!1,i=v(function(h){return h==null?h:""+h},"toString");if(t.source!==void 0||t.target!==void 0){var o=i(t.source),s=i(t.target),u=o!=null&&e.hasElementWithId(o),l=s!=null&&e.hasElementWithId(s);(u||l)&&(e.batch(function(){r.remove(a,n),r.emitAndNotify("moveout");for(var g=0;g<r.length;g++){var h=r[g],m=h._private.data;h.isEdge()&&(u&&(m.source=o),l&&(m.target=s))}r.restore(a,n)}),r.emitAndNotify("move"))}else if(t.parent!==void 0){var c=i(t.parent),f=c===null||e.hasElementWithId(c);if(f){var d=c===null?void 0:c;e.batch(function(){var g=r.remove(a,n);g.emitAndNotify("moveout");for(var h=0;h<r.length;h++){var m=r[h],y=m._private.data;m.isNode()&&(y.parent=d)}g.restore(a,n)}),r.emitAndNotify("move")}}return this};[Il,ng,vn,rr,Yr,bg,Hn,Og,Gu,Hu,qg,kn,fn,rt,ar,st].forEach(function(t){he(ze,t)});var _g={add:v(function(e){var r,a=this;if(Et(e)){var n=e;if(n._private.cy===a)r=n.restore();else{for(var i=[],o=0;o<n.length;o++){var s=n[o];i.push(s.json())}r=new at(a,i)}}else if(Oe(e)){var u=e;r=new at(a,u)}else if(ke(e)&&(Oe(e.nodes)||Oe(e.edges))){for(var l=e,c=[],f=["nodes","edges"],d=0,g=f.length;d<g;d++){var h=f[d],m=l[h];if(Oe(m))for(var y=0,p=m.length;y<p;y++){var b=he({group:h},m[y]);c.push(b)}}r=new at(a,c)}else{var w=e;r=new Fn(a,w).collection()}return r},"add"),remove:v(function(e){if(!Et(e)){if(fe(e)){var r=e;e=this.$(r)}}return e.remove()},"remove")};function $u(t,e,r,a){var n=4,i=.001,o=1e-7,s=10,u=11,l=1/(u-1),c=typeof Float32Array<"u";if(arguments.length!==4)return!1;for(var f=0;f<4;++f)if(typeof arguments[f]!="number"||isNaN(arguments[f])||!isFinite(arguments[f]))return!1;t=Math.min(t,1),r=Math.min(r,1),t=Math.max(t,0),r=Math.max(r,0);var d=c?new Float32Array(u):new Array(u);function g(A,B){return 1-3*B+3*A}v(g,"A");function h(A,B){return 3*B-6*A}v(h,"B");function m(A){return 3*A}v(m,"C");function y(A,B,k){return((g(B,k)*A+h(B,k))*A+m(B))*A}v(y,"calcBezier");function p(A,B,k){return 3*g(B,k)*A*A+2*h(B,k)*A+m(B)}v(p,"getSlope");function b(A,B){for(var k=0;k<n;++k){var L=p(B,t,r);if(L===0)return B;var R=y(B,t,r)-A;B-=R/L}return B}v(b,"newtonRaphsonIterate");function w(){for(var A=0;A<u;++A)d[A]=y(A*l,t,r)}v(w,"calcSampleValues");function x(A,B,k){var L,R,M=0;do R=B+(k-B)/2,L=y(R,t,r)-A,L>0?k=R:B=R;while(Math.abs(L)>o&&++M<s);return R}v(x,"binarySubdivide");function S(A){for(var B=0,k=1,L=u-1;k!==L&&d[k]<=A;++k)B+=l;--k;var R=(A-d[k])/(d[k+1]-d[k]),M=B+R*l,I=p(M,t,r);return I>=i?b(A,M):I===0?M:x(A,B,B+l)}v(S,"getTForX");var C=!1;function E(){C=!0,(t!==e||r!==a)&&w()}v(E,"precompute");var D=v(function(B){return C||E(),t===e&&r===a?B:B===0?0:B===1?1:y(S(B),e,a)},"f");D.getControlPoints=function(){return[{x:t,y:e},{x:r,y:a}]};var T="generateBezier("+[t,e,r,a]+")";return D.toString=function(){return T},D}v($u,"generateCubicBezier");var Xg=(function(){function t(a){return-a.tension*a.x-a.friction*a.v}v(t,"springAccelerationForState");function e(a,n,i){var o={x:a.x+i.dx*n,v:a.v+i.dv*n,tension:a.tension,friction:a.friction};return{dx:o.v,dv:t(o)}}v(e,"springEvaluateStateWithDerivative");function r(a,n){var i={dx:a.v,dv:t(a)},o=e(a,n*.5,i),s=e(a,n*.5,o),u=e(a,n,s),l=1/6*(i.dx+2*(o.dx+s.dx)+u.dx),c=1/6*(i.dv+2*(o.dv+s.dv)+u.dv);return a.x=a.x+l*n,a.v=a.v+c*n,a}return v(r,"springIntegrateState"),v(function a(n,i,o){var s={x:-1,v:0,tension:null,friction:null},u=[0],l=0,c=1/1e4,f=16/1e3,d,g,h;for(n=parseFloat(n)||500,i=parseFloat(i)||20,o=o||null,s.tension=n,s.friction=i,d=o!==null,d?(l=a(n,i),g=l/o*f):g=f;h=r(h||s,g),u.push(1+h.x),l+=16,Math.abs(h.x)>c&&Math.abs(h.v)>c;);return d?function(m){return u[m*(u.length-1)|0]}:l},"springRK4Factory")})(),Ne=v(function(e,r,a,n){var i=$u(e,r,a,n);return function(o,s,u){return o+(s-o)*i(u)}},"cubicBezier"),hn={linear:v(function(e,r,a){return e+(r-e)*a},"linear"),ease:Ne(.25,.1,.25,1),"ease-in":Ne(.42,0,1,1),"ease-out":Ne(0,0,.58,1),"ease-in-out":Ne(.42,0,.58,1),"ease-in-sine":Ne(.47,0,.745,.715),"ease-out-sine":Ne(.39,.575,.565,1),"ease-in-out-sine":Ne(.445,.05,.55,.95),"ease-in-quad":Ne(.55,.085,.68,.53),"ease-out-quad":Ne(.25,.46,.45,.94),"ease-in-out-quad":Ne(.455,.03,.515,.955),"ease-in-cubic":Ne(.55,.055,.675,.19),"ease-out-cubic":Ne(.215,.61,.355,1),"ease-in-out-cubic":Ne(.645,.045,.355,1),"ease-in-quart":Ne(.895,.03,.685,.22),"ease-out-quart":Ne(.165,.84,.44,1),"ease-in-out-quart":Ne(.77,0,.175,1),"ease-in-quint":Ne(.755,.05,.855,.06),"ease-out-quint":Ne(.23,1,.32,1),"ease-in-out-quint":Ne(.86,0,.07,1),"ease-in-expo":Ne(.95,.05,.795,.035),"ease-out-expo":Ne(.19,1,.22,1),"ease-in-out-expo":Ne(1,0,0,1),"ease-in-circ":Ne(.6,.04,.98,.335),"ease-out-circ":Ne(.075,.82,.165,1),"ease-in-out-circ":Ne(.785,.135,.15,.86),spring:v(function(e,r,a){if(a===0)return hn.linear;var n=Xg(e,r,a);return function(i,o,s){return i+(o-i)*n(s)}},"spring"),"cubic-bezier":Ne};function Ti(t,e,r,a,n){if(a===1||e===r)return r;var i=n(e,r,a);return t==null||((t.roundValue||t.color)&&(i=Math.round(i)),t.min!==void 0&&(i=Math.max(i,t.min)),t.max!==void 0&&(i=Math.min(i,t.max))),i}v(Ti,"getEasedValue");function Si(t,e){return t.pfValue!=null||t.value!=null?t.pfValue!=null&&(e==null||e.type.units!=="%")?t.pfValue:t.value:t}v(Si,"getValue");function yr(t,e,r,a,n){var i=n!=null?n.type:null;r<0?r=0:r>1&&(r=1);var o=Si(t,n),s=Si(e,n);if(ae(o)&&ae(s))return Ti(i,o,s,r,a);if(Oe(o)&&Oe(s)){for(var u=[],l=0;l<s.length;l++){var c=o[l],f=s[l];if(c!=null&&f!=null){var d=Ti(i,c,f,r,a);u.push(d)}else u.push(f)}return u}}v(yr,"ease");function Uu(t,e,r,a){var n=!a,i=t._private,o=e._private,s=o.easing,u=o.startTime,l=a?t:t.cy(),c=l.style();if(!o.easingImpl)if(s==null)o.easingImpl=hn.linear;else{var f;if(fe(s)){var d=c.parse("transition-timing-function",s);f=d.value}else f=s;var g,h;fe(f)?(g=f,h=[]):(g=f[1],h=f.slice(2).map(function(K){return+K})),h.length>0?(g==="spring"&&h.push(o.duration),o.easingImpl=hn[g].apply(null,h)):o.easingImpl=hn[g]}var m=o.easingImpl,y;if(o.duration===0?y=1:y=(r-u)/o.duration,o.applying&&(y=o.progress),y<0?y=0:y>1&&(y=1),o.delay==null){var p=o.startPosition,b=o.position;if(b&&n&&!t.locked()){var w={};Nr(p.x,b.x)&&(w.x=yr(p.x,b.x,y,m)),Nr(p.y,b.y)&&(w.y=yr(p.y,b.y,y,m)),t.position(w)}var x=o.startPan,S=o.pan,C=i.pan,E=S!=null&&a;E&&(Nr(x.x,S.x)&&(C.x=yr(x.x,S.x,y,m)),Nr(x.y,S.y)&&(C.y=yr(x.y,S.y,y,m)),t.emit("pan"));var D=o.startZoom,T=o.zoom,A=T!=null&&a;A&&(Nr(D,T)&&(i.zoom=Sa(i.minZoom,yr(D,T,y,m),i.maxZoom)),t.emit("zoom")),(E||A)&&t.emit("viewport");var B=o.style;if(B&&B.length>0&&n){for(var k=0;k<B.length;k++){var L=B[k],R=L.name,M=L,I=o.startStyle[R],O=c.properties[I.name],F=yr(I,M,y,m,O);c.overrideBypass(t,R,F)}t.emit("style")}}return o.progress=y,y}v(Uu,"step$1");function Nr(t,e){return t==null||e==null?!1:ae(t)&&ae(e)?!0:!!(t&&e)}v(Nr,"valid");function _u(t,e,r,a){var n=e._private;n.started=!0,n.startTime=r-n.progress*n.duration}v(_u,"startAnimation");function Di(t,e){var r=e._private.aniEles,a=[];function n(c,f){var d=c._private,g=d.animation.current,h=d.animation.queue,m=!1;if(g.length===0){var y=h.shift();y&&g.push(y)}for(var p=v(function(C){for(var E=C.length-1;E>=0;E--){var D=C[E];D()}C.splice(0,C.length)},"callbacks"),b=g.length-1;b>=0;b--){var w=g[b],x=w._private;if(x.stopped){g.splice(b,1),x.hooked=!1,x.playing=!1,x.started=!1,p(x.frames);continue}!x.playing&&!x.applying||(x.playing&&x.applying&&(x.applying=!1),x.started||_u(c,w,t),Uu(c,w,t,f),x.applying&&(x.applying=!1),p(x.frames),x.step!=null&&x.step(t),w.completed()&&(g.splice(b,1),x.hooked=!1,x.playing=!1,x.started=!1,p(x.completes)),m=!0)}return!f&&g.length===0&&h.length===0&&a.push(c),m}v(n,"stepOne");for(var i=!1,o=0;o<r.length;o++){var s=r[o],u=n(s);i=i||u}var l=n(e,!0);(i||l)&&(r.length>0?e.notify("draw",r):e.notify("draw")),r.unmerge(a),e.emit("step")}v(Di,"stepAll");var Yg={animate:Ae.animate(),animation:Ae.animation(),animated:Ae.animated(),clearQueue:Ae.clearQueue(),delay:Ae.delay(),delayAnimation:Ae.delayAnimation(),stop:Ae.stop(),addToAnimationPool:v(function(e){var r=this;r.styleEnabled()&&r._private.aniEles.merge(e)},"addToAnimationPool"),stopAnimationLoop:v(function(){this._private.animationsRunning=!1},"stopAnimationLoop"),startAnimationLoop:v(function(){var e=this;if(e._private.animationsRunning=!0,!e.styleEnabled())return;function r(){e._private.animationsRunning&&Cn(v(function(i){Di(i,e),r()},"animationStep"))}v(r,"headlessStep");var a=e.renderer();a&&a.beforeRender?a.beforeRender(v(function(i,o){Di(o,e)},"rendererAnimationStep"),a.beforeRenderPriorities.animations):r()},"startAnimationLoop")},Zg={qualifierCompare:v(function(e,r){return e==null||r==null?e==null&&r==null:e.sameText(r)},"qualifierCompare"),eventMatches:v(function(e,r,a){var n=r.qualifier;return n!=null?e!==a.target&&Ma(a.target)&&n.matches(a.target):!0},"eventMatches"),addEventFields:v(function(e,r){r.cy=e,r.target=e},"addEventFields"),callbackContext:v(function(e,r,a){return r.qualifier!=null?a.target:e},"callbackContext")},nn=v(function(e){return fe(e)?new sr(e):e},"argSelector"),Xu={createEmitter:v(function(){var e=this._private;return e.emitter||(e.emitter=new qa(Zg,this)),this},"createEmitter"),emitter:v(function(){return this._private.emitter},"emitter"),on:v(function(e,r,a){return this.emitter().on(e,nn(r),a),this},"on"),removeListener:v(function(e,r,a){return this.emitter().removeListener(e,nn(r),a),this},"removeListener"),removeAllListeners:v(function(){return this.emitter().removeAllListeners(),this},"removeAllListeners"),one:v(function(e,r,a){return this.emitter().one(e,nn(r),a),this},"one"),once:v(function(e,r,a){return this.emitter().one(e,nn(r),a),this},"once"),emit:v(function(e,r){return this.emitter().emit(e,r),this},"emit"),emitAndNotify:v(function(e,r){return this.emit(e),this.notify(e,r),this},"emitAndNotify")};Ae.eventAliasesOn(Xu);var Pi={png:v(function(e){var r=this._private.renderer;return e=e||{},r.png(e)},"png"),jpg:v(function(e){var r=this._private.renderer;return e=e||{},e.bg=e.bg||"#fff",r.jpg(e)},"jpg")};Pi.jpeg=Pi.jpg;var gn={layout:v(function(e){var r=this;if(e==null){Ke("Layout options must be specified to make a layout");return}if(e.name==null){Ke("A `name` must be specified to make a layout");return}var a=e.name,n=r.extension("layout",a);if(n==null){Ke("No such layout `"+a+"` found.  Did you forget to import it and `cytoscape.use()` it?");return}var i;fe(e.eles)?i=r.$(e.eles):i=e.eles!=null?e.eles:r.$();var o=new n(he({},e,{cy:r,eles:i}));return o},"layout")};gn.createLayout=gn.makeLayout=gn.layout;var Qg={notify:v(function(e,r){var a=this._private;if(this.batching()){a.batchNotifications=a.batchNotifications||{};var n=a.batchNotifications[e]=a.batchNotifications[e]||this.collection();r!=null&&n.merge(r);return}if(a.notificationsEnabled){var i=this.renderer();this.destroyed()||!i||i.notify(e,r)}},"notify"),notifications:v(function(e){var r=this._private;return e===void 0?r.notificationsEnabled:(r.notificationsEnabled=!!e,this)},"notifications"),noNotifications:v(function(e){this.notifications(!1),e(),this.notifications(!0)},"noNotifications"),batching:v(function(){return this._private.batchCount>0},"batching"),startBatch:v(function(){var e=this._private;return e.batchCount==null&&(e.batchCount=0),e.batchCount===0&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},"startBatch"),endBatch:v(function(){var e=this._private;if(e.batchCount===0)return this;if(e.batchCount--,e.batchCount===0){e.batchStyleEles.updateStyle();var r=this.renderer();Object.keys(e.batchNotifications).forEach(function(a){var n=e.batchNotifications[a];n.empty()?r.notify(a):r.notify(a,n)})}return this},"endBatch"),batch:v(function(e){return this.startBatch(),e(),this.endBatch(),this},"batch"),batchData:v(function(e){var r=this;return this.batch(function(){for(var a=Object.keys(e),n=0;n<a.length;n++){var i=a[n],o=e[i],s=r.getElementById(i);s.data(o)}})},"batchData")},Jg=et({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1,webgl:!1,webglDebug:!1,webglDebugShowAtlases:!1,webglTexSize:2048,webglTexRows:12,webglBatchSize:2048,webglTexPerBatch:14,webglBgColor:[255,255,255]}),ki={renderTo:v(function(e,r,a,n){var i=this._private.renderer;return i.renderTo(e,r,a,n),this},"renderTo"),renderer:v(function(){return this._private.renderer},"renderer"),forceRender:v(function(){return this.notify("draw"),this},"forceRender"),resize:v(function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},"resize"),initRenderer:v(function(e){var r=this,a=r.extension("renderer",e.name);if(a==null){Ke("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"));return}e.wheelSensitivity!==void 0&&Re("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var n=Jg(e);n.cy=r,r._private.renderer=new a(n),this.notify("init")},"initRenderer"),destroyRenderer:v(function(){var e=this;e.notify("destroy");var r=e.container();if(r)for(r._cyreg=null;r.childNodes.length>0;)r.removeChild(r.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach(function(a){var n=a._private;n.rscratch={},n.rstyle={},n.animation.current=[],n.animation.queue=[]})},"destroyRenderer"),onRender:v(function(e){return this.on("render",e)},"onRender"),offRender:v(function(e){return this.off("render",e)},"offRender")};ki.invalidateDimensions=ki.resize;var pn={collection:v(function(e,r){return fe(e)?this.$(e):Et(e)?e.collection():Oe(e)?(r||(r={}),new at(this,e,r.unique,r.removed)):new at(this)},"collection"),nodes:v(function(e){var r=this.$(function(a){return a.isNode()});return e?r.filter(e):r},"nodes"),edges:v(function(e){var r=this.$(function(a){return a.isEdge()});return e?r.filter(e):r},"edges"),$:v(function(e){var r=this._private.elements;return e?r.filter(e):r.spawnSelf()},"$"),mutableElements:v(function(){return this._private.elements},"mutableElements")};pn.elements=pn.filter=pn.$;var ut={},ma="t",jg="f";ut.apply=function(t){for(var e=this,r=e._private,a=r.cy,n=a.collection(),i=0;i<t.length;i++){var o=t[i],s=e.getContextMeta(o);if(!s.empty){var u=e.getContextStyle(s),l=e.applyContextStyle(s,u,o);o._private.appliedInitStyle?e.updateTransitions(o,l.diffProps):o._private.appliedInitStyle=!0;var c=e.updateStyleHints(o);c&&n.push(o)}}return n};ut.getPropertiesDiff=function(t,e){var r=this,a=r._private.propDiffs=r._private.propDiffs||{},n=t+"-"+e,i=a[n];if(i)return i;for(var o=[],s={},u=0;u<r.length;u++){var l=r[u],c=t[u]===ma,f=e[u]===ma,d=c!==f,g=l.mappedProperties.length>0;if(d||f&&g){var h=void 0;d&&g||d?h=l.properties:g&&(h=l.mappedProperties);for(var m=0;m<h.length;m++){for(var y=h[m],p=y.name,b=!1,w=u+1;w<r.length;w++){var x=r[w],S=e[w]===ma;if(S&&(b=x.properties[y.name]!=null,b))break}!s[p]&&!b&&(s[p]=!0,o.push(p))}}}return a[n]=o,o};ut.getContextMeta=function(t){for(var e=this,r="",a,n=t._private.styleCxtKey||"",i=0;i<e.length;i++){var o=e[i],s=o.selector&&o.selector.matches(t);s?r+=ma:r+=jg}return a=e.getPropertiesDiff(n,r),t._private.styleCxtKey=r,{key:r,diffPropNames:a,empty:a.length===0}};ut.getContextStyle=function(t){var e=t.key,r=this,a=this._private.contextStyles=this._private.contextStyles||{};if(a[e])return a[e];for(var n={_private:{key:e}},i=0;i<r.length;i++){var o=r[i],s=e[i]===ma;if(s)for(var u=0;u<o.properties.length;u++){var l=o.properties[u];n[l.name]=l}}return a[e]=n,n};ut.applyContextStyle=function(t,e,r){for(var a=this,n=t.diffPropNames,i={},o=a.types,s=0;s<n.length;s++){var u=n[s],l=e[u],c=r.pstyle(u);if(!l)if(c)c.bypass?l={name:u,deleteBypassed:!0}:l={name:u,delete:!0};else continue;if(c!==l){if(l.mapped===o.fn&&c!=null&&c.mapping!=null&&c.mapping.value===l.value){var f=c.mapping,d=f.fnValue=l.value(r);if(d===f.prevFnValue)continue}var g=i[u]={prev:c};a.applyParsedProperty(r,l),g.next=r.pstyle(u),g.next&&g.next.bypass&&(g.next=g.next.bypassed)}}return{diffProps:i}};ut.updateStyleHints=function(t){var e=t._private,r=this,a=r.propertyGroupNames,n=r.propertyGroupKeys,i=v(function(U,te,oe){return r.getPropertiesHash(U,te,oe)},"propHash"),o=e.styleKey;if(t.removed())return!1;var s=e.group==="nodes",u=t._private.style;a=Object.keys(u);for(var l=0;l<n.length;l++){var c=n[l];e.styleKeys[c]=[zr,ca]}for(var f=v(function(U,te){return e.styleKeys[te][0]=Ea(U,e.styleKeys[te][0])},"updateGrKey1"),d=v(function(U,te){return e.styleKeys[te][1]=Ca(U,e.styleKeys[te][1])},"updateGrKey2"),g=v(function(U,te){f(U,te),d(U,te)},"updateGrKey"),h=v(function(U,te){for(var oe=0;oe<U.length;oe++){var ue=U.charCodeAt(oe);f(ue,te),d(ue,te)}},"updateGrKeyWStr"),m=2e9,y=v(function(U){return-128<U&&U<128&&Math.floor(U)!==U?m-(U*1024|0):U},"cleanNum"),p=0;p<a.length;p++){var b=a[p],w=u[b];if(w!=null){var x=this.properties[b],S=x.type,C=x.groupKey,E=void 0;x.hashOverride!=null?E=x.hashOverride(t,w):w.pfValue!=null&&(E=w.pfValue);var D=x.enums==null?w.value:null,T=E!=null,A=D!=null,B=T||A,k=w.units;if(S.number&&B&&!S.multiple){var L=T?E:D;g(y(L),C),!T&&k!=null&&h(k,C)}else h(w.strValue,C)}}for(var R=[zr,ca],M=0;M<n.length;M++){var I=n[M],O=e.styleKeys[I];R[0]=Ea(O[0],R[0]),R[1]=Ca(O[1],R[1])}e.styleKey=Yc(R[0],R[1]);var F=e.styleKeys;e.labelDimsKey=Jt(F.labelDimensions);var K=i(t,["label"],F.labelDimensions);if(e.labelKey=Jt(K),e.labelStyleKey=Jt(Za(F.commonLabel,K)),!s){var $=i(t,["source-label"],F.labelDimensions);e.sourceLabelKey=Jt($),e.sourceLabelStyleKey=Jt(Za(F.commonLabel,$));var q=i(t,["target-label"],F.labelDimensions);e.targetLabelKey=Jt(q),e.targetLabelStyleKey=Jt(Za(F.commonLabel,q))}if(s){var G=e.styleKeys,X=G.nodeBody,Z=G.nodeBorder,J=G.nodeOutline,Q=G.backgroundImage,ee=G.compound,re=G.pie,W=[X,Z,J,Q,ee,re].filter(function(N){return N!=null}).reduce(Za,[zr,ca]);e.nodeKey=Jt(W),e.hasPie=re!=null&&re[0]!==zr&&re[1]!==ca}return o!==e.styleKey};ut.clearStyleHints=function(t){var e=t._private;e.styleCxtKey="",e.styleKeys={},e.styleKey=null,e.labelKey=null,e.labelStyleKey=null,e.sourceLabelKey=null,e.sourceLabelStyleKey=null,e.targetLabelKey=null,e.targetLabelStyleKey=null,e.nodeKey=null,e.hasPie=null};ut.applyParsedProperty=function(t,e){var r=this,a=e,n=t._private.style,i,o=r.types,s=r.properties[a.name].type,u=a.bypass,l=n[a.name],c=l&&l.bypass,f=t._private,d="mapping",g=v(function(X){return X==null?null:X.pfValue!=null?X.pfValue:X.value},"getVal"),h=v(function(){var X=g(l),Z=g(a);r.checkTriggers(t,a.name,X,Z)},"checkTriggers");if(e.name==="curve-style"&&t.isEdge()&&(e.value!=="bezier"&&t.isLoop()||e.value==="haystack"&&(t.source().isParent()||t.target().isParent()))&&(a=e=this.parse(e.name,"bezier",u)),a.delete)return n[a.name]=void 0,h(),!0;if(a.deleteBypassed)return l?l.bypass?(l.bypassed=void 0,h(),!0):!1:(h(),!0);if(a.deleteBypass)return l?l.bypass?(n[a.name]=l.bypassed,h(),!0):!1:(h(),!0);var m=v(function(){Re("Do not assign mappings to elements without corresponding data (i.e. ele `"+t.id()+"` has no mapping for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")},"printMappingErr");switch(a.mapped){case o.mapData:{for(var y=a.field.split("."),p=f.data,b=0;b<y.length&&p;b++){var w=y[b];p=p[w]}if(p==null)return m(),!1;var x;if(ae(p)){var S=a.fieldMax-a.fieldMin;S===0?x=0:x=(p-a.fieldMin)/S}else return Re("Do not use continuous mappers without specifying numeric data (i.e. `"+a.field+": "+p+"` for `"+t.id()+"` is non-numeric)"),!1;if(x<0?x=0:x>1&&(x=1),s.color){var C=a.valueMin[0],E=a.valueMax[0],D=a.valueMin[1],T=a.valueMax[1],A=a.valueMin[2],B=a.valueMax[2],k=a.valueMin[3]==null?1:a.valueMin[3],L=a.valueMax[3]==null?1:a.valueMax[3],R=[Math.round(C+(E-C)*x),Math.round(D+(T-D)*x),Math.round(A+(B-A)*x),Math.round(k+(L-k)*x)];i={bypass:a.bypass,name:a.name,value:R,strValue:"rgb("+R[0]+", "+R[1]+", "+R[2]+")"}}else if(s.number){var M=a.valueMin+(a.valueMax-a.valueMin)*x;i=this.parse(a.name,M,a.bypass,d)}else return!1;if(!i)return m(),!1;i.mapping=a,a=i;break}case o.data:{for(var I=a.field.split("."),O=f.data,F=0;F<I.length&&O;F++){var K=I[F];O=O[K]}if(O!=null&&(i=this.parse(a.name,O,a.bypass,d)),!i)return m(),!1;i.mapping=a,a=i;break}case o.fn:{var $=a.value,q=a.fnValue!=null?a.fnValue:$(t);if(a.prevFnValue=q,q==null)return Re("Custom function mappers may not return null (i.e. `"+a.name+"` for ele `"+t.id()+"` is null)"),!1;if(i=this.parse(a.name,q,a.bypass,d),!i)return Re("Custom function mappers may not return invalid values for the property type (i.e. `"+a.name+"` for ele `"+t.id()+"` is invalid)"),!1;i.mapping=Nt(a),a=i;break}case void 0:break;default:return!1}return u?(c?a.bypassed=l.bypassed:a.bypassed=l,n[a.name]=a):c?l.bypassed=a:n[a.name]=a,h(),!0};ut.cleanElements=function(t,e){for(var r=0;r<t.length;r++){var a=t[r];if(this.clearStyleHints(a),a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),!e)a._private.style={};else for(var n=a._private.style,i=Object.keys(n),o=0;o<i.length;o++){var s=i[o],u=n[s];u!=null&&(u.bypass?u.bypassed=null:n[s]=null)}}};ut.update=function(){var t=this._private.cy,e=t.mutableElements();e.updateStyle()};ut.updateTransitions=function(t,e){var r=this,a=t._private,n=t.pstyle("transition-property").value,i=t.pstyle("transition-duration").pfValue,o=t.pstyle("transition-delay").pfValue;if(n.length>0&&i>0){for(var s={},u=!1,l=0;l<n.length;l++){var c=n[l],f=t.pstyle(c),d=e[c];if(d){var g=d.prev,h=g,m=d.next!=null?d.next:f,y=!1,p=void 0,b=1e-6;h&&(ae(h.pfValue)&&ae(m.pfValue)?(y=m.pfValue-h.pfValue,p=h.pfValue+b*y):ae(h.value)&&ae(m.value)?(y=m.value-h.value,p=h.value+b*y):Oe(h.value)&&Oe(m.value)&&(y=h.value[0]!==m.value[0]||h.value[1]!==m.value[1]||h.value[2]!==m.value[2],p=h.strValue),y&&(s[c]=m.strValue,this.applyBypass(t,c,p),u=!0))}}if(!u)return;a.transitioning=!0,new Jr(function(w){o>0?t.delayAnimation(o).play().promise().then(w):w()}).then(function(){return t.animation({style:s,duration:i,easing:t.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){r.removeBypasses(t,n),t.emitAndNotify("style"),a.transitioning=!1})}else a.transitioning&&(this.removeBypasses(t,n),t.emitAndNotify("style"),a.transitioning=!1)};ut.checkTrigger=function(t,e,r,a,n,i){var o=this.properties[e],s=n(o);s!=null&&s(r,a)&&i(o)};ut.checkZOrderTrigger=function(t,e,r,a){var n=this;this.checkTrigger(t,e,r,a,function(i){return i.triggersZOrder},function(){n._private.cy.notify("zorder",t)})};ut.checkBoundsTrigger=function(t,e,r,a){this.checkTrigger(t,e,r,a,function(n){return n.triggersBounds},function(n){t.dirtyCompoundBoundsCache(),t.dirtyBoundingBoxCache(),n.triggersBoundsOfParallelBeziers&&e==="curve-style"&&(r==="bezier"||a==="bezier")&&t.parallelEdges().forEach(function(i){i.dirtyBoundingBoxCache()}),n.triggersBoundsOfConnectedEdges&&e==="display"&&(r==="none"||a==="none")&&t.connectedEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})};ut.checkTriggers=function(t,e,r,a){t.dirtyStyleCache(),this.checkZOrderTrigger(t,e,r,a),this.checkBoundsTrigger(t,e,r,a)};var Ha={};Ha.applyBypass=function(t,e,r,a){var n=this,i=[],o=!0;if(e==="*"||e==="**"){if(r!==void 0)for(var s=0;s<n.properties.length;s++){var u=n.properties[s],l=u.name,c=this.parse(l,r,!0);c&&i.push(c)}}else if(fe(e)){var f=this.parse(e,r,!0);f&&i.push(f)}else if(ke(e)){var d=e;a=r;for(var g=Object.keys(d),h=0;h<g.length;h++){var m=g[h],y=d[m];if(y===void 0&&(y=d[Mn(m)]),y!==void 0){var p=this.parse(m,y,!0);p&&i.push(p)}}}else return!1;if(i.length===0)return!1;for(var b=!1,w=0;w<t.length;w++){for(var x=t[w],S={},C=void 0,E=0;E<i.length;E++){var D=i[E];if(a){var T=x.pstyle(D.name);C=S[D.name]={prev:T}}b=this.applyParsedProperty(x,Nt(D))||b,a&&(C.next=x.pstyle(D.name))}b&&this.updateStyleHints(x),a&&this.updateTransitions(x,S,o)}return b};Ha.overrideBypass=function(t,e,r){e=Wi(e);for(var a=0;a<t.length;a++){var n=t[a],i=n._private.style[e],o=this.properties[e].type,s=o.color,u=o.mutiple,l=i?i.pfValue!=null?i.pfValue:i.value:null;!i||!i.bypass?this.applyBypass(n,e,r):(i.value=r,i.pfValue!=null&&(i.pfValue=r),s?i.strValue="rgb("+r.join(",")+")":u?i.strValue=r.join(" "):i.strValue=""+r,this.updateStyleHints(n)),this.checkTriggers(n,e,l,r)}};Ha.removeAllBypasses=function(t,e){return this.removeBypasses(t,this.propertyNames,e)};Ha.removeBypasses=function(t,e,r){for(var a=!0,n=0;n<t.length;n++){for(var i=t[n],o={},s=0;s<e.length;s++){var u=e[s],l=this.properties[u],c=i.pstyle(l.name);if(!(!c||!c.bypass)){var f="",d=this.parse(u,f,!0),g=o[l.name]={prev:c};this.applyParsedProperty(i,d),g.next=i.pstyle(l.name)}}this.updateStyleHints(i),r&&this.updateTransitions(i,o,a)}};var io={};io.getEmSizeInPixels=function(){var t=this.containerCss("font-size");return t!=null?parseFloat(t):1};io.containerCss=function(t){var e=this._private.cy,r=e.container(),a=e.window();if(a&&r&&a.getComputedStyle)return a.getComputedStyle(r).getPropertyValue(t)};var Vt={};Vt.getRenderedStyle=function(t,e){return e?this.getStylePropertyValue(t,e,!0):this.getRawStyle(t,!0)};Vt.getRawStyle=function(t,e){var r=this;if(t=t[0],t){for(var a={},n=0;n<r.properties.length;n++){var i=r.properties[n],o=r.getStylePropertyValue(t,i.name,e);o!=null&&(a[i.name]=o,a[Mn(i.name)]=o)}return a}};Vt.getIndexedStyle=function(t,e,r,a){var n=t.pstyle(e)[r][a];return n??t.cy().style().getDefaultProperty(e)[r][0]};Vt.getStylePropertyValue=function(t,e,r){var a=this;if(t=t[0],t){var n=a.properties[e];n.alias&&(n=n.pointsTo);var i=n.type,o=t.pstyle(n.name);if(o){var s=o.value,u=o.units,l=o.strValue;if(r&&i.number&&s!=null&&ae(s)){var c=t.cy().zoom(),f=v(function(y){return y*c},"getRenderedValue"),d=v(function(y,p){return f(y)+p},"getValueStringWithUnits"),g=Oe(s),h=g?u.every(function(m){return m!=null}):u!=null;return h?g?s.map(function(m,y){return d(m,u[y])}).join(" "):d(s,u):g?s.map(function(m){return fe(m)?m:""+f(m)}).join(" "):""+f(s)}else if(l!=null)return l}return null}};Vt.getAnimationStartStyle=function(t,e){for(var r={},a=0;a<e.length;a++){var n=e[a],i=n.name,o=t.pstyle(i);o!==void 0&&(ke(o)?o=this.parse(i,o.strValue):o=this.parse(i,o)),o&&(r[i]=o)}return r};Vt.getPropsList=function(t){var e=this,r=[],a=t,n=e.properties;if(a)for(var i=Object.keys(a),o=0;o<i.length;o++){var s=i[o],u=a[s],l=n[s]||n[Wi(s)],c=this.parse(l.name,u);c&&r.push(c)}return r};Vt.getNonDefaultPropertiesHash=function(t,e,r){var a=r.slice(),n,i,o,s,u,l;for(u=0;u<e.length;u++)if(n=e[u],i=t.pstyle(n,!1),i!=null)if(i.pfValue!=null)a[0]=Ea(s,a[0]),a[1]=Ca(s,a[1]);else for(o=i.strValue,l=0;l<o.length;l++)s=o.charCodeAt(l),a[0]=Ea(s,a[0]),a[1]=Ca(s,a[1]);return a};Vt.getPropertiesHash=Vt.getNonDefaultPropertiesHash;var $n={};$n.appendFromJson=function(t){for(var e=this,r=0;r<t.length;r++){var a=t[r],n=a.selector,i=a.style||a.css,o=Object.keys(i);e.selector(n);for(var s=0;s<o.length;s++){var u=o[s],l=i[u];e.css(u,l)}}return e};$n.fromJson=function(t){var e=this;return e.resetToDefault(),e.appendFromJson(t),e};$n.json=function(){for(var t=[],e=this.defaultLength;e<this.length;e++){for(var r=this[e],a=r.selector,n=r.properties,i={},o=0;o<n.length;o++){var s=n[o];i[s.name]=s.strValue}t.push({selector:a?a.toString():"core",style:i})}return t};var oo={};oo.appendFromString=function(t){var e=this,r=this,a=""+t,n,i,o;a=a.replace(/[/][*](\s|.)+?[*][/]/g,"");function s(){a.length>n.length?a=a.substr(n.length):a=""}v(s,"removeSelAndBlockFromRemaining");function u(){i.length>o.length?i=i.substr(o.length):i=""}for(v(u,"removePropAndValFromRem");;){var l=a.match(/^\s*$/);if(l)break;var c=a.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!c){Re("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+a);break}n=c[0];var f=c[1];if(f!=="core"){var d=new sr(f);if(d.invalid){Re("Skipping parsing of block: Invalid selector found in string stylesheet: "+f),s();continue}}var g=c[2],h=!1;i=g;for(var m=[];;){var y=i.match(/^\s*$/);if(y)break;var p=i.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!p){Re("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+g),h=!0;break}o=p[0];var b=p[1],w=p[2],x=e.properties[b];if(!x){Re("Skipping property: Invalid property name in: "+o),u();continue}var S=r.parse(b,w);if(!S){Re("Skipping property: Invalid property definition in: "+o),u();continue}m.push({name:b,val:w}),u()}if(h){s();break}r.selector(f);for(var C=0;C<m.length;C++){var E=m[C];r.css(E.name,E.val)}s()}return r};oo.fromString=function(t){var e=this;return e.resetToDefault(),e.appendFromString(t),e};var tt={};(function(){var t=Xe,e=vc,r=fc,a=dc,n=hc,i=v(function(W){return"^"+W+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},"data"),o=v(function(W){var N=t+"|\\w+|"+e+"|"+r+"|"+a+"|"+n;return"^"+W+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+t+")\\s*\\,\\s*("+t+")\\s*,\\s*("+N+")\\s*\\,\\s*("+N+")\\)$"},"mapData"),s=[`^url\\s*\\(\\s*['"]?(.+?)['"]?\\s*\\)$`,"^(none)$","^(.+)$"];tt.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},nonNegativeNumber:{number:!0,min:0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:!0},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:!0,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:!0,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:i("data")},layoutData:{mapping:!0,regex:i("layoutData")},scratch:{mapping:!0,regex:i("scratch")},mapData:{mapping:!0,regex:o("mapData")},mapLayoutData:{mapping:!0,regex:o("mapLayoutData")},mapScratch:{mapping:!0,regex:o("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:s,singleRegexMatchValue:!0},urls:{regexes:s,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:v(function(W,N){switch(W.length){case 2:return N[0]!=="deg"&&N[0]!=="rad"&&N[1]!=="deg"&&N[1]!=="rad";case 1:return fe(W[0])||N[0]==="deg"||N[0]==="rad";default:return!1}},"validate")},easing:{regexes:["^(spring)\\s*\\(\\s*("+t+")\\s*,\\s*("+t+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+t+")\\s*,\\s*("+t+")\\s*,\\s*("+t+")\\s*,\\s*("+t+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:v(function(W){var N=W.length;return N===1||N===2||N===4},"validate")}};var u={zeroNonZero:v(function(W,N){return(W==null||N==null)&&W!==N||W==0&&N!=0?!0:W!=0&&N==0},"zeroNonZero"),any:v(function(W,N){return W!=N},"any"),emptyNonEmpty:v(function(W,N){var U=nr(W),te=nr(N);return U&&!te||!U&&te},"emptyNonEmpty")},l=tt.types,c=[{name:"label",type:l.text,triggersBounds:u.any,triggersZOrder:u.emptyNonEmpty},{name:"text-rotation",type:l.textRotation,triggersBounds:u.any},{name:"text-margin-x",type:l.bidirectionalSize,triggersBounds:u.any},{name:"text-margin-y",type:l.bidirectionalSize,triggersBounds:u.any}],f=[{name:"source-label",type:l.text,triggersBounds:u.any},{name:"source-text-rotation",type:l.textRotation,triggersBounds:u.any},{name:"source-text-margin-x",type:l.bidirectionalSize,triggersBounds:u.any},{name:"source-text-margin-y",type:l.bidirectionalSize,triggersBounds:u.any},{name:"source-text-offset",type:l.size,triggersBounds:u.any}],d=[{name:"target-label",type:l.text,triggersBounds:u.any},{name:"target-text-rotation",type:l.textRotation,triggersBounds:u.any},{name:"target-text-margin-x",type:l.bidirectionalSize,triggersBounds:u.any},{name:"target-text-margin-y",type:l.bidirectionalSize,triggersBounds:u.any},{name:"target-text-offset",type:l.size,triggersBounds:u.any}],g=[{name:"font-family",type:l.fontFamily,triggersBounds:u.any},{name:"font-style",type:l.fontStyle,triggersBounds:u.any},{name:"font-weight",type:l.fontWeight,triggersBounds:u.any},{name:"font-size",type:l.size,triggersBounds:u.any},{name:"text-transform",type:l.textTransform,triggersBounds:u.any},{name:"text-wrap",type:l.textWrap,triggersBounds:u.any},{name:"text-overflow-wrap",type:l.textOverflowWrap,triggersBounds:u.any},{name:"text-max-width",type:l.size,triggersBounds:u.any},{name:"text-outline-width",type:l.size,triggersBounds:u.any},{name:"line-height",type:l.positiveNumber,triggersBounds:u.any}],h=[{name:"text-valign",type:l.valign,triggersBounds:u.any},{name:"text-halign",type:l.halign,triggersBounds:u.any},{name:"color",type:l.color},{name:"text-outline-color",type:l.color},{name:"text-outline-opacity",type:l.zeroOneNumber},{name:"text-background-color",type:l.color},{name:"text-background-opacity",type:l.zeroOneNumber},{name:"text-background-padding",type:l.size,triggersBounds:u.any},{name:"text-border-opacity",type:l.zeroOneNumber},{name:"text-border-color",type:l.color},{name:"text-border-width",type:l.size,triggersBounds:u.any},{name:"text-border-style",type:l.borderStyle,triggersBounds:u.any},{name:"text-background-shape",type:l.textBackgroundShape,triggersBounds:u.any},{name:"text-justification",type:l.justification}],m=[{name:"events",type:l.bool,triggersZOrder:u.any},{name:"text-events",type:l.bool,triggersZOrder:u.any}],y=[{name:"display",type:l.display,triggersZOrder:u.any,triggersBounds:u.any,triggersBoundsOfConnectedEdges:!0},{name:"visibility",type:l.visibility,triggersZOrder:u.any},{name:"opacity",type:l.zeroOneNumber,triggersZOrder:u.zeroNonZero},{name:"text-opacity",type:l.zeroOneNumber},{name:"min-zoomed-font-size",type:l.size},{name:"z-compound-depth",type:l.zCompoundDepth,triggersZOrder:u.any},{name:"z-index-compare",type:l.zIndexCompare,triggersZOrder:u.any},{name:"z-index",type:l.number,triggersZOrder:u.any}],p=[{name:"overlay-padding",type:l.size,triggersBounds:u.any},{name:"overlay-color",type:l.color},{name:"overlay-opacity",type:l.zeroOneNumber,triggersBounds:u.zeroNonZero},{name:"overlay-shape",type:l.overlayShape,triggersBounds:u.any},{name:"overlay-corner-radius",type:l.cornerRadius}],b=[{name:"underlay-padding",type:l.size,triggersBounds:u.any},{name:"underlay-color",type:l.color},{name:"underlay-opacity",type:l.zeroOneNumber,triggersBounds:u.zeroNonZero},{name:"underlay-shape",type:l.overlayShape,triggersBounds:u.any},{name:"underlay-corner-radius",type:l.cornerRadius}],w=[{name:"transition-property",type:l.propList},{name:"transition-duration",type:l.time},{name:"transition-delay",type:l.time},{name:"transition-timing-function",type:l.easing}],x=v(function(W,N){return N.value==="label"?-W.poolIndex():N.pfValue},"nodeSizeHashOverride"),S=[{name:"height",type:l.nodeSize,triggersBounds:u.any,hashOverride:x},{name:"width",type:l.nodeSize,triggersBounds:u.any,hashOverride:x},{name:"shape",type:l.nodeShape,triggersBounds:u.any},{name:"shape-polygon-points",type:l.polygonPointList,triggersBounds:u.any},{name:"corner-radius",type:l.cornerRadius},{name:"background-color",type:l.color},{name:"background-fill",type:l.fill},{name:"background-opacity",type:l.zeroOneNumber},{name:"background-blacken",type:l.nOneOneNumber},{name:"background-gradient-stop-colors",type:l.colors},{name:"background-gradient-stop-positions",type:l.percentages},{name:"background-gradient-direction",type:l.gradientDirection},{name:"padding",type:l.sizeMaybePercent,triggersBounds:u.any},{name:"padding-relative-to",type:l.paddingRelativeTo,triggersBounds:u.any},{name:"bounds-expansion",type:l.boundsExpansion,triggersBounds:u.any}],C=[{name:"border-color",type:l.color},{name:"border-opacity",type:l.zeroOneNumber},{name:"border-width",type:l.size,triggersBounds:u.any},{name:"border-style",type:l.borderStyle},{name:"border-cap",type:l.lineCap},{name:"border-join",type:l.lineJoin},{name:"border-dash-pattern",type:l.numbers},{name:"border-dash-offset",type:l.number},{name:"border-position",type:l.linePosition}],E=[{name:"outline-color",type:l.color},{name:"outline-opacity",type:l.zeroOneNumber},{name:"outline-width",type:l.size,triggersBounds:u.any},{name:"outline-style",type:l.borderStyle},{name:"outline-offset",type:l.size,triggersBounds:u.any}],D=[{name:"background-image",type:l.urls},{name:"background-image-crossorigin",type:l.bgCrossOrigin},{name:"background-image-opacity",type:l.zeroOneNumbers},{name:"background-image-containment",type:l.bgContainment},{name:"background-image-smoothing",type:l.bools},{name:"background-position-x",type:l.bgPos},{name:"background-position-y",type:l.bgPos},{name:"background-width-relative-to",type:l.bgRelativeTo},{name:"background-height-relative-to",type:l.bgRelativeTo},{name:"background-repeat",type:l.bgRepeat},{name:"background-fit",type:l.bgFit},{name:"background-clip",type:l.bgClip},{name:"background-width",type:l.bgWH},{name:"background-height",type:l.bgWH},{name:"background-offset-x",type:l.bgPos},{name:"background-offset-y",type:l.bgPos}],T=[{name:"position",type:l.position,triggersBounds:u.any},{name:"compound-sizing-wrt-labels",type:l.compoundIncludeLabels,triggersBounds:u.any},{name:"min-width",type:l.size,triggersBounds:u.any},{name:"min-width-bias-left",type:l.sizeMaybePercent,triggersBounds:u.any},{name:"min-width-bias-right",type:l.sizeMaybePercent,triggersBounds:u.any},{name:"min-height",type:l.size,triggersBounds:u.any},{name:"min-height-bias-top",type:l.sizeMaybePercent,triggersBounds:u.any},{name:"min-height-bias-bottom",type:l.sizeMaybePercent,triggersBounds:u.any}],A=[{name:"line-style",type:l.lineStyle},{name:"line-color",type:l.color},{name:"line-fill",type:l.fill},{name:"line-cap",type:l.lineCap},{name:"line-opacity",type:l.zeroOneNumber},{name:"line-dash-pattern",type:l.numbers},{name:"line-dash-offset",type:l.number},{name:"line-outline-width",type:l.size},{name:"line-outline-color",type:l.color},{name:"line-gradient-stop-colors",type:l.colors},{name:"line-gradient-stop-positions",type:l.percentages},{name:"curve-style",type:l.curveStyle,triggersBounds:u.any,triggersBoundsOfParallelBeziers:!0},{name:"haystack-radius",type:l.zeroOneNumber,triggersBounds:u.any},{name:"source-endpoint",type:l.edgeEndpoint,triggersBounds:u.any},{name:"target-endpoint",type:l.edgeEndpoint,triggersBounds:u.any},{name:"control-point-step-size",type:l.size,triggersBounds:u.any},{name:"control-point-distances",type:l.bidirectionalSizes,triggersBounds:u.any},{name:"control-point-weights",type:l.numbers,triggersBounds:u.any},{name:"segment-distances",type:l.bidirectionalSizes,triggersBounds:u.any},{name:"segment-weights",type:l.numbers,triggersBounds:u.any},{name:"segment-radii",type:l.numbers,triggersBounds:u.any},{name:"radius-type",type:l.radiusType,triggersBounds:u.any},{name:"taxi-turn",type:l.bidirectionalSizeMaybePercent,triggersBounds:u.any},{name:"taxi-turn-min-distance",type:l.size,triggersBounds:u.any},{name:"taxi-direction",type:l.axisDirection,triggersBounds:u.any},{name:"taxi-radius",type:l.number,triggersBounds:u.any},{name:"edge-distances",type:l.edgeDistances,triggersBounds:u.any},{name:"arrow-scale",type:l.positiveNumber,triggersBounds:u.any},{name:"loop-direction",type:l.angle,triggersBounds:u.any},{name:"loop-sweep",type:l.angle,triggersBounds:u.any},{name:"source-distance-from-node",type:l.size,triggersBounds:u.any},{name:"target-distance-from-node",type:l.size,triggersBounds:u.any}],B=[{name:"ghost",type:l.bool,triggersBounds:u.any},{name:"ghost-offset-x",type:l.bidirectionalSize,triggersBounds:u.any},{name:"ghost-offset-y",type:l.bidirectionalSize,triggersBounds:u.any},{name:"ghost-opacity",type:l.zeroOneNumber}],k=[{name:"selection-box-color",type:l.color},{name:"selection-box-opacity",type:l.zeroOneNumber},{name:"selection-box-border-color",type:l.color},{name:"selection-box-border-width",type:l.size},{name:"active-bg-color",type:l.color},{name:"active-bg-opacity",type:l.zeroOneNumber},{name:"active-bg-size",type:l.size},{name:"outside-texture-bg-color",type:l.color},{name:"outside-texture-bg-opacity",type:l.zeroOneNumber}],L=[];tt.pieBackgroundN=16,L.push({name:"pie-size",type:l.sizeMaybePercent});for(var R=1;R<=tt.pieBackgroundN;R++)L.push({name:"pie-"+R+"-background-color",type:l.color}),L.push({name:"pie-"+R+"-background-size",type:l.percent}),L.push({name:"pie-"+R+"-background-opacity",type:l.zeroOneNumber});var M=[],I=tt.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:l.arrowShape,triggersBounds:u.any},{name:"arrow-color",type:l.color},{name:"arrow-fill",type:l.arrowFill},{name:"arrow-width",type:l.arrowWidth}].forEach(function(re){I.forEach(function(W){var N=W+"-"+re.name,U=re.type,te=re.triggersBounds;M.push({name:N,type:U,triggersBounds:te})})},{});var O=tt.properties=[].concat(m,w,y,p,b,B,h,g,c,f,d,S,C,E,D,L,T,A,M,k),F=tt.propertyGroups={behavior:m,transition:w,visibility:y,overlay:p,underlay:b,ghost:B,commonLabel:h,labelDimensions:g,mainLabel:c,sourceLabel:f,targetLabel:d,nodeBody:S,nodeBorder:C,nodeOutline:E,backgroundImage:D,pie:L,compound:T,edgeLine:A,edgeArrow:M,core:k},K=tt.propertyGroupNames={},$=tt.propertyGroupKeys=Object.keys(F);$.forEach(function(re){K[re]=F[re].map(function(W){return W.name}),F[re].forEach(function(W){return W.groupKey=re})});var q=tt.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];tt.propertyNames=O.map(function(re){return re.name});for(var G=0;G<O.length;G++){var X=O[G];O[X.name]=X}for(var Z=0;Z<q.length;Z++){var J=q[Z],Q=O[J.pointsTo],ee={name:J.name,alias:!0,pointsTo:Q};O.push(ee),O[J.name]=ee}})();tt.getDefaultProperty=function(t){return this.getDefaultProperties()[t]};tt.getDefaultProperties=function(){var t=this._private;if(t.defaultProperties!=null)return t.defaultProperties;for(var e=he({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(u,l){for(var c=1;c<=tt.pieBackgroundN;c++){var f=l.name.replace("{{i}}",c),d=l.value;u[f]=d}return u},{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce(function(u,l){return tt.arrowPrefixes.forEach(function(c){var f=c+"-"+l.name,d=l.value;u[f]=d}),u},{})),r={},a=0;a<this.properties.length;a++){var n=this.properties[a];if(!n.pointsTo){var i=n.name,o=e[i],s=this.parse(i,o);r[i]=s}}return t.defaultProperties=r,t.defaultProperties};tt.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var Un={};Un.parse=function(t,e,r,a){var n=this;if(Ge(e))return n.parseImplWarn(t,e,r,a);var i=a==="mapping"||a===!0||a===!1||a==null?"dontcare":a,o=r?"t":"f",s=""+e,u=yl(t,s,o,i),l=n.propCache=n.propCache||[],c;return(c=l[u])||(c=l[u]=n.parseImplWarn(t,e,r,a)),(r||a==="mapping")&&(c=Nt(c),c&&(c.value=Nt(c.value))),c};Un.parseImplWarn=function(t,e,r,a){var n=this.parseImpl(t,e,r,a);return!n&&e!=null&&Re("The style property `".concat(t,": ").concat(e,"` is invalid")),n&&(n.name==="width"||n.name==="height")&&e==="label"&&Re("The style value of `label` is deprecated for `"+n.name+"`"),n};Un.parseImpl=function(t,e,r,a){var n=this;t=Wi(t);var i=n.properties[t],o=e,s=n.types;if(!i||e===void 0)return null;i.alias&&(i=i.pointsTo,t=i.name);var u=fe(e);u&&(e=e.trim());var l=i.type;if(!l)return null;if(r&&(e===""||e===null))return{name:t,value:e,bypass:!0,deleteBypass:!0};if(Ge(e))return{name:t,value:e,strValue:"fn",mapped:s.fn,bypass:r};var c,f;if(!(!u||a||e.length<7||e[1]!=="a")){if(e.length>=7&&e[0]==="d"&&(c=new RegExp(s.data.regex).exec(e))){if(r)return!1;var d=s.data;return{name:t,value:c,strValue:""+e,mapped:d,field:c[1],bypass:r}}else if(e.length>=10&&e[0]==="m"&&(f=new RegExp(s.mapData.regex).exec(e))){if(r||l.multiple)return!1;var g=s.mapData;if(!(l.color||l.number))return!1;var h=this.parse(t,f[4]);if(!h||h.mapped)return!1;var m=this.parse(t,f[5]);if(!m||m.mapped)return!1;if(h.pfValue===m.pfValue||h.strValue===m.strValue)return Re("`"+t+": "+e+"` is not a valid mapper because the output range is zero; converting to `"+t+": "+h.strValue+"`"),this.parse(t,h.strValue);if(l.color){var y=h.value,p=m.value,b=y[0]===p[0]&&y[1]===p[1]&&y[2]===p[2]&&(y[3]===p[3]||(y[3]==null||y[3]===1)&&(p[3]==null||p[3]===1));if(b)return!1}return{name:t,value:f,strValue:""+e,mapped:g,field:f[1],fieldMin:parseFloat(f[2]),fieldMax:parseFloat(f[3]),valueMin:h.value,valueMax:m.value,bypass:r}}}if(l.multiple&&a!=="multiple"){var w;if(u?w=e.split(/\s+/):Oe(e)?w=e:w=[e],l.evenMultiple&&w.length%2!==0)return null;for(var x=[],S=[],C=[],E="",D=!1,T=0;T<w.length;T++){var A=n.parse(t,w[T],r,"multiple");D=D||fe(A.value),x.push(A.value),C.push(A.pfValue!=null?A.pfValue:A.value),S.push(A.units),E+=(T>0?" ":"")+A.strValue}return l.validate&&!l.validate(x,S)?null:l.singleEnum&&D?x.length===1&&fe(x[0])?{name:t,value:x[0],strValue:x[0],bypass:r}:null:{name:t,value:x,pfValue:C,strValue:E,bypass:r,units:S}}var B=v(function(){for(var W=0;W<l.enums.length;W++){var N=l.enums[W];if(N===e)return{name:t,value:e,strValue:""+e,bypass:r}}return null},"checkEnums");if(l.number){var k,L="px";if(l.units&&(k=l.units),l.implicitUnits&&(L=l.implicitUnits),!l.unitless)if(u){var R="px|em"+(l.allowPercent?"|\\%":"");k&&(R=k);var M=e.match("^("+Xe+")("+R+")?$");M&&(e=M[1],k=M[2]||L)}else(!k||l.implicitUnits)&&(k=L);if(e=parseFloat(e),isNaN(e)&&l.enums===void 0)return null;if(isNaN(e)&&l.enums!==void 0)return e=o,B();if(l.integer&&!ac(e)||l.min!==void 0&&(e<l.min||l.strictMin&&e===l.min)||l.max!==void 0&&(e>l.max||l.strictMax&&e===l.max))return null;var I={name:t,value:e,strValue:""+e+(k||""),units:k,bypass:r};return l.unitless||k!=="px"&&k!=="em"?I.pfValue=e:I.pfValue=k==="px"||!k?e:this.getEmSizeInPixels()*e,(k==="ms"||k==="s")&&(I.pfValue=k==="ms"?e:1e3*e),(k==="deg"||k==="rad")&&(I.pfValue=k==="rad"?e:Df(e)),k==="%"&&(I.pfValue=e/100),I}else if(l.propList){var O=[],F=""+e;if(F!=="none"){for(var K=F.split(/\s*,\s*|\s+/),$=0;$<K.length;$++){var q=K[$].trim();n.properties[q]?O.push(q):Re("`"+q+"` is not a valid property name")}if(O.length===0)return null}return{name:t,value:O,strValue:O.length===0?"none":O.join(" "),bypass:r}}else if(l.color){var G=Js(e);return G?{name:t,value:G,pfValue:G,strValue:"rgb("+G[0]+","+G[1]+","+G[2]+")",bypass:r}:null}else if(l.regex||l.regexes){if(l.enums){var X=B();if(X)return X}for(var Z=l.regexes?l.regexes:[l.regex],J=0;J<Z.length;J++){var Q=new RegExp(Z[J]),ee=Q.exec(e);if(ee)return{name:t,value:l.singleRegexMatchValue?ee[1]:ee,strValue:""+e,bypass:r}}return null}else return l.string?{name:t,value:""+e,strValue:""+e,bypass:r}:l.enums?B():null};var ot=v(function t(e){if(!(this instanceof t))return new t(e);if(!Hi(e)){Ke("A style must have a core reference");return}this._private={cy:e,coreStyle:{}},this.length=0,this.resetToDefault()},"Style"),lt=ot.prototype;lt.instanceString=function(){return"style"};lt.clear=function(){for(var t=this._private,e=t.cy,r=e.elements(),a=0;a<this.length;a++)this[a]=void 0;return this.length=0,t.contextStyles={},t.propDiffs={},this.cleanElements(r,!0),r.forEach(function(n){var i=n[0]._private;i.styleDirty=!0,i.appliedInitStyle=!1}),this};lt.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this};lt.core=function(t){return this._private.coreStyle[t]||this.getDefaultProperty(t)};lt.selector=function(t){var e=t==="core"?null:new sr(t),r=this.length++;return this[r]={selector:e,properties:[],mappedProperties:[],index:r},this};lt.css=function(){var t=this,e=arguments;if(e.length===1)for(var r=e[0],a=0;a<t.properties.length;a++){var n=t.properties[a],i=r[n.name];i===void 0&&(i=r[Mn(n.name)]),i!==void 0&&this.cssRule(n.name,i)}else e.length===2&&this.cssRule(e[0],e[1]);return this};lt.style=lt.css;lt.cssRule=function(t,e){var r=this.parse(t,e);if(r){var a=this.length-1;this[a].properties.push(r),this[a].properties[r.name]=r,r.name.match(/pie-(\d+)-background-size/)&&r.value&&(this._private.hasPie=!0),r.mapped&&this[a].mappedProperties.push(r);var n=!this[a].selector;n&&(this._private.coreStyle[r.name]=r)}return this};lt.append=function(t){return Ys(t)?t.appendToStyle(this):Oe(t)?this.appendFromJson(t):fe(t)&&this.appendFromString(t),this};ot.fromJson=function(t,e){var r=new ot(t);return r.fromJson(e),r};ot.fromString=function(t,e){return new ot(t).fromString(e)};[ut,Ha,io,Vt,$n,oo,tt,Un].forEach(function(t){he(lt,t)});ot.types=lt.types;ot.properties=lt.properties;ot.propertyGroups=lt.propertyGroups;ot.propertyGroupNames=lt.propertyGroupNames;ot.propertyGroupKeys=lt.propertyGroupKeys;var ep={style:v(function(e){if(e){var r=this.setStyle(e);r.update()}return this._private.style},"style"),setStyle:v(function(e){var r=this._private;return Ys(e)?r.style=e.generateStyle(this):Oe(e)?r.style=ot.fromJson(this,e):fe(e)?r.style=ot.fromString(this,e):r.style=ot(this),r.style},"setStyle"),updateStyle:v(function(){this.mutableElements().updateStyle()},"updateStyle")},tp="single",Sr={autolock:v(function(e){if(e!==void 0)this._private.autolock=!!e;else return this._private.autolock;return this},"autolock"),autoungrabify:v(function(e){if(e!==void 0)this._private.autoungrabify=!!e;else return this._private.autoungrabify;return this},"autoungrabify"),autounselectify:v(function(e){if(e!==void 0)this._private.autounselectify=!!e;else return this._private.autounselectify;return this},"autounselectify"),selectionType:v(function(e){var r=this._private;if(r.selectionType==null&&(r.selectionType=tp),e!==void 0)(e==="additive"||e==="single")&&(r.selectionType=e);else return r.selectionType;return this},"selectionType"),panningEnabled:v(function(e){if(e!==void 0)this._private.panningEnabled=!!e;else return this._private.panningEnabled;return this},"panningEnabled"),userPanningEnabled:v(function(e){if(e!==void 0)this._private.userPanningEnabled=!!e;else return this._private.userPanningEnabled;return this},"userPanningEnabled"),zoomingEnabled:v(function(e){if(e!==void 0)this._private.zoomingEnabled=!!e;else return this._private.zoomingEnabled;return this},"zoomingEnabled"),userZoomingEnabled:v(function(e){if(e!==void 0)this._private.userZoomingEnabled=!!e;else return this._private.userZoomingEnabled;return this},"userZoomingEnabled"),boxSelectionEnabled:v(function(e){if(e!==void 0)this._private.boxSelectionEnabled=!!e;else return this._private.boxSelectionEnabled;return this},"boxSelectionEnabled"),pan:v(function(){var e=arguments,r=this._private.pan,a,n,i,o,s;switch(e.length){case 0:return r;case 1:if(fe(e[0]))return a=e[0],r[a];if(ke(e[0])){if(!this._private.panningEnabled)return this;i=e[0],o=i.x,s=i.y,ae(o)&&(r.x=o),ae(s)&&(r.y=s),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;a=e[0],n=e[1],(a==="x"||a==="y")&&ae(n)&&(r[a]=n),this.emit("pan viewport");break}return this.notify("viewport"),this},"pan"),panBy:v(function(e,r){var a=arguments,n=this._private.pan,i,o,s,u,l;if(!this._private.panningEnabled)return this;switch(a.length){case 1:ke(e)&&(s=a[0],u=s.x,l=s.y,ae(u)&&(n.x+=u),ae(l)&&(n.y+=l),this.emit("pan viewport"));break;case 2:i=e,o=r,(i==="x"||i==="y")&&ae(o)&&(n[i]+=o),this.emit("pan viewport");break}return this.notify("viewport"),this},"panBy"),gc:v(function(){this.notify("gc")},"gc"),fit:v(function(e,r){var a=this.getFitViewport(e,r);if(a){var n=this._private;n.zoom=a.zoom,n.pan=a.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},"fit"),getFitViewport:v(function(e,r){if(ae(e)&&r===void 0&&(r=e,e=void 0),!(!this._private.panningEnabled||!this._private.zoomingEnabled)){var a;if(fe(e)){var n=e;e=this.$(n)}else if(oc(e)){var i=e;a={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2},a.w=a.x2-a.x1,a.h=a.y2-a.y1}else Et(e)||(e=this.mutableElements());if(!(Et(e)&&e.empty())){a=a||e.boundingBox();var o=this.width(),s=this.height(),u;if(r=ae(r)?r:0,!isNaN(o)&&!isNaN(s)&&o>0&&s>0&&!isNaN(a.w)&&!isNaN(a.h)&&a.w>0&&a.h>0){u=Math.min((o-2*r)/a.w,(s-2*r)/a.h),u=u>this._private.maxZoom?this._private.maxZoom:u,u=u<this._private.minZoom?this._private.minZoom:u;var l={x:(o-u*(a.x1+a.x2))/2,y:(s-u*(a.y1+a.y2))/2};return{zoom:u,pan:l}}}}},"getFitViewport"),zoomRange:v(function(e,r){var a=this._private;if(r==null){var n=e;e=n.min,r=n.max}return ae(e)&&ae(r)&&e<=r?(a.minZoom=e,a.maxZoom=r):ae(e)&&r===void 0&&e<=a.maxZoom?a.minZoom=e:ae(r)&&e===void 0&&r>=a.minZoom&&(a.maxZoom=r),this},"zoomRange"),minZoom:v(function(e){return e===void 0?this._private.minZoom:this.zoomRange({min:e})},"minZoom"),maxZoom:v(function(e){return e===void 0?this._private.maxZoom:this.zoomRange({max:e})},"maxZoom"),getZoomedViewport:v(function(e){var r=this._private,a=r.pan,n=r.zoom,i,o,s=!1;if(r.zoomingEnabled||(s=!0),ae(e)?o=e:ke(e)&&(o=e.level,e.position!=null?i=Nn(e.position,n,a):e.renderedPosition!=null&&(i=e.renderedPosition),i!=null&&!r.panningEnabled&&(s=!0)),o=o>r.maxZoom?r.maxZoom:o,o=o<r.minZoom?r.minZoom:o,s||!ae(o)||o===n||i!=null&&(!ae(i.x)||!ae(i.y)))return null;if(i!=null){var u=a,l=n,c=o,f={x:-c/l*(i.x-u.x)+i.x,y:-c/l*(i.y-u.y)+i.y};return{zoomed:!0,panned:!0,zoom:c,pan:f}}else return{zoomed:!0,panned:!1,zoom:o,pan:a}},"getZoomedViewport"),zoom:v(function(e){if(e===void 0)return this._private.zoom;var r=this.getZoomedViewport(e),a=this._private;return r==null||!r.zoomed?this:(a.zoom=r.zoom,r.panned&&(a.pan.x=r.pan.x,a.pan.y=r.pan.y),this.emit("zoom"+(r.panned?" pan":"")+" viewport"),this.notify("viewport"),this)},"zoom"),viewport:v(function(e){var r=this._private,a=!0,n=!0,i=[],o=!1,s=!1;if(!e)return this;if(ae(e.zoom)||(a=!1),ke(e.pan)||(n=!1),!a&&!n)return this;if(a){var u=e.zoom;u<r.minZoom||u>r.maxZoom||!r.zoomingEnabled?o=!0:(r.zoom=u,i.push("zoom"))}if(n&&(!o||!e.cancelOnFailedZoom)&&r.panningEnabled){var l=e.pan;ae(l.x)&&(r.pan.x=l.x,s=!1),ae(l.y)&&(r.pan.y=l.y,s=!1),s||i.push("pan")}return i.length>0&&(i.push("viewport"),this.emit(i.join(" ")),this.notify("viewport")),this},"viewport"),center:v(function(e){var r=this.getCenterPan(e);return r&&(this._private.pan=r,this.emit("pan viewport"),this.notify("viewport")),this},"center"),getCenterPan:v(function(e,r){if(this._private.panningEnabled){if(fe(e)){var a=e;e=this.mutableElements().filter(a)}else Et(e)||(e=this.mutableElements());if(e.length!==0){var n=e.boundingBox(),i=this.width(),o=this.height();r=r===void 0?this._private.zoom:r;var s={x:(i-r*(n.x1+n.x2))/2,y:(o-r*(n.y1+n.y2))/2};return s}}},"getCenterPan"),reset:v(function(){return!this._private.panningEnabled||!this._private.zoomingEnabled?this:(this.viewport({pan:{x:0,y:0},zoom:1}),this)},"reset"),invalidateSize:v(function(){this._private.sizeCache=null},"invalidateSize"),size:v(function(){var e=this._private,r=e.container,a=this;return e.sizeCache=e.sizeCache||(r?(function(){var n=a.window().getComputedStyle(r),i=v(function(s){return parseFloat(n.getPropertyValue(s))},"val");return{width:r.clientWidth-i("padding-left")-i("padding-right"),height:r.clientHeight-i("padding-top")-i("padding-bottom")}})():{width:1,height:1})},"size"),width:v(function(){return this.size().width},"width"),height:v(function(){return this.size().height},"height"),extent:v(function(){var e=this._private.pan,r=this._private.zoom,a=this.renderedExtent(),n={x1:(a.x1-e.x)/r,x2:(a.x2-e.x)/r,y1:(a.y1-e.y)/r,y2:(a.y2-e.y)/r};return n.w=n.x2-n.x1,n.h=n.y2-n.y1,n},"extent"),renderedExtent:v(function(){var e=this.width(),r=this.height();return{x1:0,y1:0,x2:e,y2:r,w:e,h:r}},"renderedExtent"),multiClickDebounceTime:v(function(e){if(e)this._private.multiClickDebounceTime=e;else return this._private.multiClickDebounceTime;return this},"multiClickDebounceTime")};Sr.centre=Sr.center;Sr.autolockNodes=Sr.autolock;Sr.autoungrabifyNodes=Sr.autoungrabify;var Aa={data:Ae.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:Ae.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:Ae.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Ae.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};Aa.attr=Aa.data;Aa.removeAttr=Aa.removeData;var Ra=v(function(e){var r=this;e=he({},e);var a=e.container;a&&!En(a)&&En(a[0])&&(a=a[0]);var n=a?a._cyreg:null;n=n||{},n&&n.cy&&(n.cy.destroy(),n={});var i=n.readies=n.readies||[];a&&(a._cyreg=n),n.cy=r;var o=_e!==void 0&&a!==void 0&&!e.headless,s=e;s.layout=he({name:o?"grid":"null"},s.layout),s.renderer=he({name:o?"canvas":"null"},s.renderer);var u=v(function(h,m,y){return m!==void 0?m:y!==void 0?y:h},"defVal"),l=this._private={container:a,ready:!1,options:s,elements:new at(this),listeners:[],aniEles:new at(this),data:s.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:u(!0,s.zoomingEnabled),userZoomingEnabled:u(!0,s.userZoomingEnabled),panningEnabled:u(!0,s.panningEnabled),userPanningEnabled:u(!0,s.userPanningEnabled),boxSelectionEnabled:u(!0,s.boxSelectionEnabled),autolock:u(!1,s.autolock,s.autolockNodes),autoungrabify:u(!1,s.autoungrabify,s.autoungrabifyNodes),autounselectify:u(!1,s.autounselectify),styleEnabled:s.styleEnabled===void 0?o:s.styleEnabled,zoom:ae(s.zoom)?s.zoom:1,pan:{x:ke(s.pan)&&ae(s.pan.x)?s.pan.x:0,y:ke(s.pan)&&ae(s.pan.y)?s.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:u(250,s.multiClickDebounceTime)};this.createEmitter(),this.selectionType(s.selectionType),this.zoomRange({min:s.minZoom,max:s.maxZoom});var c=v(function(h,m){var y=h.some(sc);if(y)return Jr.all(h).then(m);m(h)},"loadExtData");l.styleEnabled&&r.setStyle([]);var f=he({},s,s.renderer);r.initRenderer(f);var d=v(function(h,m,y){r.notifications(!1);var p=r.mutableElements();p.length>0&&p.remove(),h!=null&&(ke(h)||Oe(h))&&r.add(h),r.one("layoutready",function(w){r.notifications(!0),r.emit(w),r.one("load",m),r.emitAndNotify("load")}).one("layoutstop",function(){r.one("done",y),r.emit("done")});var b=he({},r._private.options.layout);b.eles=r.elements(),r.layout(b).run()},"setElesAndLayout");c([s.style,s.elements],function(g){var h=g[0],m=g[1];l.styleEnabled&&r.style().append(h),d(m,function(){r.startAnimationLoop(),l.ready=!0,Ge(s.ready)&&r.on("ready",s.ready);for(var y=0;y<i.length;y++){var p=i[y];r.on("ready",p)}n&&(n.readies=[]),r.emit("ready")},s.done)})},"Core"),Bn=Ra.prototype;he(Bn,{instanceString:v(function(){return"core"},"instanceString"),isReady:v(function(){return this._private.ready},"isReady"),destroyed:v(function(){return this._private.destroyed},"destroyed"),ready:v(function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},"ready"),destroy:v(function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},"destroy"),hasElementWithId:v(function(e){return this._private.elements.hasElementWithId(e)},"hasElementWithId"),getElementById:v(function(e){return this._private.elements.getElementById(e)},"getElementById"),hasCompoundNodes:v(function(){return this._private.hasCompoundNodes},"hasCompoundNodes"),headless:v(function(){return this._private.renderer.isHeadless()},"headless"),styleEnabled:v(function(){return this._private.styleEnabled},"styleEnabled"),addToPool:v(function(e){return this._private.elements.merge(e),this},"addToPool"),removeFromPool:v(function(e){return this._private.elements.unmerge(e),this},"removeFromPool"),container:v(function(){return this._private.container||null},"container"),window:v(function(){var e=this._private.container;if(e==null)return _e;var r=this._private.container.ownerDocument;return r===void 0||r==null?_e:r.defaultView||_e},"window"),mount:v(function(e){if(e!=null){var r=this,a=r._private,n=a.options;return!En(e)&&En(e[0])&&(e=e[0]),r.stopAnimationLoop(),r.destroyRenderer(),a.container=e,a.styleEnabled=!0,r.invalidateSize(),r.initRenderer(he({},n,n.renderer,{name:n.renderer.name==="null"?"canvas":n.renderer.name})),r.startAnimationLoop(),r.style(n.style),r.emit("mount"),r}},"mount"),unmount:v(function(){var e=this;return e.stopAnimationLoop(),e.destroyRenderer(),e.initRenderer({name:"null"}),e.emit("unmount"),e},"unmount"),options:v(function(){return Nt(this._private.options)},"options"),json:v(function(e){var r=this,a=r._private,n=r.mutableElements(),i=v(function(x){return r.getElementById(x.id())},"getFreshRef");if(ke(e)){if(r.startBatch(),e.elements){var o={},s=v(function(x,S){for(var C=[],E=[],D=0;D<x.length;D++){var T=x[D];if(!T.data.id){Re("cy.json() cannot handle elements without an ID attribute");continue}var A=""+T.data.id,B=r.getElementById(A);o[A]=!0,B.length!==0?E.push({ele:B,json:T}):(S&&(T.group=S),C.push(T))}r.add(C);for(var k=0;k<E.length;k++){var L=E[k],R=L.ele,M=L.json;R.json(M)}},"updateEles");if(Oe(e.elements))s(e.elements);else for(var u=["nodes","edges"],l=0;l<u.length;l++){var c=u[l],f=e.elements[c];Oe(f)&&s(f,c)}var d=r.collection();n.filter(function(w){return!o[w.id()]}).forEach(function(w){w.isParent()?d.merge(w):w.remove()}),d.forEach(function(w){return w.children().move({parent:null})}),d.forEach(function(w){return i(w).remove()})}e.style&&r.style(e.style),e.zoom!=null&&e.zoom!==a.zoom&&r.zoom(e.zoom),e.pan&&(e.pan.x!==a.pan.x||e.pan.y!==a.pan.y)&&r.pan(e.pan),e.data&&r.data(e.data);for(var g=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],h=0;h<g.length;h++){var m=g[h];e[m]!=null&&r[m](e[m])}return r.endBatch(),this}else{var y=!!e,p={};y?p.elements=this.elements().map(function(w){return w.json()}):(p.elements={},n.forEach(function(w){var x=w.group();p.elements[x]||(p.elements[x]=[]),p.elements[x].push(w.json())})),this._private.styleEnabled&&(p.style=r.style().json()),p.data=Nt(r.data());var b=a.options;return p.zoomingEnabled=a.zoomingEnabled,p.userZoomingEnabled=a.userZoomingEnabled,p.zoom=a.zoom,p.minZoom=a.minZoom,p.maxZoom=a.maxZoom,p.panningEnabled=a.panningEnabled,p.userPanningEnabled=a.userPanningEnabled,p.pan=Nt(a.pan),p.boxSelectionEnabled=a.boxSelectionEnabled,p.renderer=Nt(b.renderer),p.hideEdgesOnViewport=b.hideEdgesOnViewport,p.textureOnViewport=b.textureOnViewport,p.wheelSensitivity=b.wheelSensitivity,p.motionBlur=b.motionBlur,p.multiClickDebounceTime=b.multiClickDebounceTime,p}},"json")});Bn.$id=Bn.getElementById;[_g,Yg,Xu,Pi,gn,Qg,ki,pn,ep,Sr,Aa].forEach(function(t){he(Bn,t)});var rp={fit:!0,directed:!1,padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:v(function(e,r){return!0},"animateFilter"),ready:void 0,stop:void 0,transform:v(function(e,r){return r},"transform")},ap={maximal:!1,acyclic:!1},Mr=v(function(e){return e.scratch("breadthfirst")},"getInfo"),bs=v(function(e,r){return e.scratch("breadthfirst",r)},"setInfo");function so(t){this.options=he({},rp,ap,t)}v(so,"BreadthFirstLayout");so.prototype.run=function(){var t=this.options,e=t.cy,r=t.eles,a=r.nodes().filter(function(ve){return ve.isChildless()}),n=r,i=t.directed,o=t.acyclic||t.maximal||t.maximalAdjustments>0,s=!!t.boundingBox,u=e.extent(),l=bt(s?t.boundingBox:{x1:u.x1,y1:u.y1,w:u.w,h:u.h}),c;if(Et(t.roots))c=t.roots;else if(Oe(t.roots)){for(var f=[],d=0;d<t.roots.length;d++){var g=t.roots[d],h=e.getElementById(g);f.push(h)}c=e.collection(f)}else if(fe(t.roots))c=e.$(t.roots);else if(i)c=a.roots();else{var m=r.components();c=e.collection();for(var y=v(function(le){var ye=m[le],me=ye.maxDegree(!1),ge=ye.filter(function(be){return be.degree(!1)===me});c=c.add(ge)},"_loop"),p=0;p<m.length;p++)y(p)}var b=[],w={},x=v(function(le,ye){b[ye]==null&&(b[ye]=[]);var me=b[ye].length;b[ye].push(le),bs(le,{index:me,depth:ye})},"addToDepth"),S=v(function(le,ye){var me=Mr(le),ge=me.depth,be=me.index;b[ge][be]=null,le.isChildless()&&x(le,ye)},"changeDepth");n.bfs({roots:c,directed:t.directed,visit:v(function(le,ye,me,ge,be){var Ce=le[0],De=Ce.id();Ce.isChildless()&&x(Ce,be),w[De]=!0},"visit")});for(var C=[],E=0;E<a.length;E++){var D=a[E];w[D.id()]||C.push(D)}var T=v(function(le){for(var ye=b[le],me=0;me<ye.length;me++){var ge=ye[me];if(ge==null){ye.splice(me,1),me--;continue}bs(ge,{depth:le,index:me})}},"assignDepthsAt"),A=v(function(le,ye){for(var me=Mr(le),ge=le.incomers().filter(function(_){return _.isNode()&&r.has(_)}),be=-1,Ce=le.id(),De=0;De<ge.length;De++){var j=ge[De],P=Mr(j);be=Math.max(be,P.depth)}if(me.depth<=be){if(!t.acyclic&&ye[Ce])return null;var z=be+1;return S(le,z),ye[Ce]=z,!0}return!1},"adjustMaximally");if(i&&o){var B=[],k={},L=v(function(le){return B.push(le)},"enqueue"),R=v(function(){return B.shift()},"dequeue");for(a.forEach(function(ve){return B.push(ve)});B.length>0;){var M=R(),I=A(M,k);if(I)M.outgoers().filter(function(ve){return ve.isNode()&&r.has(ve)}).forEach(L);else if(I===null){Re("Detected double maximal shift for node `"+M.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}var O=0;if(t.avoidOverlap)for(var F=0;F<a.length;F++){var K=a[F],$=K.layoutDimensions(t),q=$.w,G=$.h;O=Math.max(O,q,G)}var X={},Z=v(function(le){if(X[le.id()])return X[le.id()];for(var ye=Mr(le).depth,me=le.neighborhood(),ge=0,be=0,Ce=0;Ce<me.length;Ce++){var De=me[Ce];if(!(De.isEdge()||De.isParent()||!a.has(De))){var j=Mr(De);if(j!=null){var P=j.index,z=j.depth;if(!(P==null||z==null)){var _=b[z].length;z<ye&&(ge+=P/_,be++)}}}}return be=Math.max(1,be),ge=ge/be,be===0&&(ge=0),X[le.id()]=ge,ge},"getWeightedPercent"),J=v(function(le,ye){var me=Z(le),ge=Z(ye),be=me-ge;return be===0?Qs(le.id(),ye.id()):be},"sortFn");t.depthSort!==void 0&&(J=t.depthSort);for(var Q=b.length,ee=0;ee<Q;ee++)b[ee].sort(J),T(ee);for(var re=[],W=0;W<C.length;W++)re.push(C[W]);var N=v(function(){for(var le=0;le<Q;le++)T(le)},"assignDepths");re.length&&(b.unshift(re),Q=b.length,N());for(var U=0,te=0;te<Q;te++)U=Math.max(b[te].length,U);var oe={x:l.x1+l.w/2,y:l.y1+l.h/2},ue=a.reduce(function(ve,le){return(function(ye){return{w:ve.w===-1?ye.w:(ve.w+ye.w)/2,h:ve.h===-1?ye.h:(ve.h+ye.h)/2}})(le.boundingBox({includeLabels:t.nodeDimensionsIncludeLabels}))},{w:-1,h:-1}),Se=Math.max(Q===1?0:s?(l.h-t.padding*2-ue.h)/(Q-1):(l.h-t.padding*2-ue.h)/(Q+1),O),Le=b.reduce(function(ve,le){return Math.max(ve,le.length)},0),Ie=v(function(le){var ye=Mr(le),me=ye.depth,ge=ye.index;if(t.circle){var be=Math.min(l.w/2/Q,l.h/2/Q);be=Math.max(be,O);var Ce=be*me+be-(Q>0&&b[0].length<=3?be/2:0),De=2*Math.PI/b[me].length*ge;return me===0&&b[0].length===1&&(Ce=1),{x:oe.x+Ce*Math.cos(De),y:oe.y+Ce*Math.sin(De)}}else{var j=b[me].length,P=Math.max(j===1?0:s?(l.w-t.padding*2-ue.w)/((t.grid?Le:j)-1):(l.w-t.padding*2-ue.w)/((t.grid?Le:j)+1),O),z={x:oe.x+(ge+1-(j+1)/2)*P,y:oe.y+(me+1-(Q+1)/2)*Se};return z}},"getPosition");return r.nodes().layoutPositions(this,t,Ie),this};var np={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:v(function(e,r){return!0},"animateFilter"),ready:void 0,stop:void 0,transform:v(function(e,r){return r},"transform")};function lo(t){this.options=he({},np,t)}v(lo,"CircleLayout");lo.prototype.run=function(){var t=this.options,e=t,r=t.cy,a=e.eles,n=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,i=a.nodes().not(":parent");e.sort&&(i=i.sort(e.sort));for(var o=bt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()}),s={x:o.x1+o.w/2,y:o.y1+o.h/2},u=e.sweep===void 0?2*Math.PI-2*Math.PI/i.length:e.sweep,l=u/Math.max(1,i.length-1),c,f=0,d=0;d<i.length;d++){var g=i[d],h=g.layoutDimensions(e),m=h.w,y=h.h;f=Math.max(f,m,y)}if(ae(e.radius)?c=e.radius:i.length<=1?c=0:c=Math.min(o.h,o.w)/2-f,i.length>1&&e.avoidOverlap){f*=1.75;var p=Math.cos(l)-Math.cos(0),b=Math.sin(l)-Math.sin(0),w=Math.sqrt(f*f/(p*p+b*b));c=Math.max(w,c)}var x=v(function(C,E){var D=e.startAngle+E*l*(n?1:-1),T=c*Math.cos(D),A=c*Math.sin(D),B={x:s.x+T,y:s.y+A};return B},"getPos");return a.nodes().layoutPositions(this,e,x),this};var ip={fit:!0,padding:30,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:v(function(e){return e.degree()},"concentric"),levelWidth:v(function(e){return e.maxDegree()/4},"levelWidth"),animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:v(function(e,r){return!0},"animateFilter"),ready:void 0,stop:void 0,transform:v(function(e,r){return r},"transform")};function uo(t){this.options=he({},ip,t)}v(uo,"ConcentricLayout");uo.prototype.run=function(){for(var t=this.options,e=t,r=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,a=t.cy,n=e.eles,i=n.nodes().not(":parent"),o=bt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:a.width(),h:a.height()}),s={x:o.x1+o.w/2,y:o.y1+o.h/2},u=[],l=0,c=0;c<i.length;c++){var f=i[c],d=void 0;d=e.concentric(f),u.push({value:d,node:f}),f._private.scratch.concentric=d}i.updateStyle();for(var g=0;g<i.length;g++){var h=i[g],m=h.layoutDimensions(e);l=Math.max(l,m.w,m.h)}u.sort(function(ue,Se){return Se.value-ue.value});for(var y=e.levelWidth(i),p=[[]],b=p[0],w=0;w<u.length;w++){var x=u[w];if(b.length>0){var S=Math.abs(b[0].value-x.value);S>=y&&(b=[],p.push(b))}b.push(x)}var C=l+e.minNodeSpacing;if(!e.avoidOverlap){var E=p.length>0&&p[0].length>1,D=Math.min(o.w,o.h)/2-C,T=D/(p.length+E?1:0);C=Math.min(C,T)}for(var A=0,B=0;B<p.length;B++){var k=p[B],L=e.sweep===void 0?2*Math.PI-2*Math.PI/k.length:e.sweep,R=k.dTheta=L/Math.max(1,k.length-1);if(k.length>1&&e.avoidOverlap){var M=Math.cos(R)-Math.cos(0),I=Math.sin(R)-Math.sin(0),O=Math.sqrt(C*C/(M*M+I*I));A=Math.max(O,A)}k.r=A,A+=C}if(e.equidistant){for(var F=0,K=0,$=0;$<p.length;$++){var q=p[$],G=q.r-K;F=Math.max(F,G)}K=0;for(var X=0;X<p.length;X++){var Z=p[X];X===0&&(K=Z.r),Z.r=K,K+=F}}for(var J={},Q=0;Q<p.length;Q++)for(var ee=p[Q],re=ee.dTheta,W=ee.r,N=0;N<ee.length;N++){var U=ee[N],te=e.startAngle+(r?1:-1)*re*N,oe={x:s.x+W*Math.cos(te),y:s.y+W*Math.sin(te)};J[U.node.id()]=oe}return n.nodes().layoutPositions(this,e,function(ue){var Se=ue.id();return J[Se]}),this};var li,op={ready:v(function(){},"ready"),stop:v(function(){},"stop"),animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:v(function(e,r){return!0},"animateFilter"),animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:v(function(e){return 2048},"nodeRepulsion"),nodeOverlap:4,idealEdgeLength:v(function(e){return 32},"idealEdgeLength"),edgeElasticity:v(function(e){return 32},"edgeElasticity"),nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function Wa(t){this.options=he({},op,t),this.options.layout=this;var e=this.options.eles.nodes(),r=this.options.eles.edges(),a=r.filter(function(n){var i=n.source().data("id"),o=n.target().data("id"),s=e.some(function(l){return l.data("id")===i}),u=e.some(function(l){return l.data("id")===o});return!s||!u});this.options.eles=this.options.eles.not(a)}v(Wa,"CoseLayout");Wa.prototype.run=function(){var t=this.options,e=t.cy,r=this;r.stopped=!1,(t.animate===!0||t.animate===!1)&&r.emit({type:"layoutstart",layout:r}),t.debug===!0?li=!0:li=!1;var a=sp(e,r,t);li&&vp(a),t.randomize&&cp(a);var n=$t(),i=v(function(){fp(a,e,t),t.fit===!0&&e.fit(t.padding)},"refresh"),o=v(function(d){return!(r.stopped||d>=t.numIter||(dp(a,t),a.temperature=a.temperature*t.coolingFactor,a.temperature<t.minTemp))},"mainLoop"),s=v(function(){if(t.animate===!0||t.animate===!1)i(),r.one("layoutstop",t.stop),r.emit({type:"layoutstop",layout:r});else{var d=t.eles.nodes(),g=Yu(a,t,d);d.layoutPositions(r,t,g)}},"done"),u=0,l=!0;if(t.animate===!0){var c=v(function f(){for(var d=0;l&&d<t.refresh;)l=o(u),u++,d++;if(!l)xs(a,t),s();else{var g=$t();g-n>=t.animationThreshold&&i(),Cn(f)}},"frame");c()}else{for(;l;)l=o(u),u++;xs(a,t),s()}return this};Wa.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this};Wa.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var sp=v(function(e,r,a){for(var n=a.eles.edges(),i=a.eles.nodes(),o=bt(a.boundingBox?a.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),s={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:n.size(),temperature:a.initialTemp,clientWidth:o.w,clientHeight:o.h,boundingBox:o},u=a.eles.components(),l={},c=0;c<u.length;c++)for(var f=u[c],d=0;d<f.length;d++){var g=f[d];l[g.id()]=c}for(var c=0;c<s.nodeSize;c++){var h=i[c],m=h.layoutDimensions(a),y={};y.isLocked=h.locked(),y.id=h.data("id"),y.parentId=h.data("parent"),y.cmptId=l[h.id()],y.children=[],y.positionX=h.position("x"),y.positionY=h.position("y"),y.offsetX=0,y.offsetY=0,y.height=m.w,y.width=m.h,y.maxX=y.positionX+y.width/2,y.minX=y.positionX-y.width/2,y.maxY=y.positionY+y.height/2,y.minY=y.positionY-y.height/2,y.padLeft=parseFloat(h.style("padding")),y.padRight=parseFloat(h.style("padding")),y.padTop=parseFloat(h.style("padding")),y.padBottom=parseFloat(h.style("padding")),y.nodeRepulsion=Ge(a.nodeRepulsion)?a.nodeRepulsion(h):a.nodeRepulsion,s.layoutNodes.push(y),s.idToIndex[y.id]=c}for(var p=[],b=0,w=-1,x=[],c=0;c<s.nodeSize;c++){var h=s.layoutNodes[c],S=h.parentId;S!=null?s.layoutNodes[s.idToIndex[S]].children.push(h.id):(p[++w]=h.id,x.push(h.id))}for(s.graphSet.push(x);b<=w;){var C=p[b++],E=s.idToIndex[C],g=s.layoutNodes[E],D=g.children;if(D.length>0){s.graphSet.push(D);for(var c=0;c<D.length;c++)p[++w]=D[c]}}for(var c=0;c<s.graphSet.length;c++)for(var T=s.graphSet[c],d=0;d<T.length;d++){var A=s.idToIndex[T[d]];s.indexToGraph[A]=c}for(var c=0;c<s.edgeSize;c++){var B=n[c],k={};k.id=B.data("id"),k.sourceId=B.data("source"),k.targetId=B.data("target");var L=Ge(a.idealEdgeLength)?a.idealEdgeLength(B):a.idealEdgeLength,R=Ge(a.edgeElasticity)?a.edgeElasticity(B):a.edgeElasticity,M=s.idToIndex[k.sourceId],I=s.idToIndex[k.targetId],O=s.indexToGraph[M],F=s.indexToGraph[I];if(O!=F){for(var K=lp(k.sourceId,k.targetId,s),$=s.graphSet[K],q=0,y=s.layoutNodes[M];$.indexOf(y.id)===-1;)y=s.layoutNodes[s.idToIndex[y.parentId]],q++;for(y=s.layoutNodes[I];$.indexOf(y.id)===-1;)y=s.layoutNodes[s.idToIndex[y.parentId]],q++;L*=q*a.nestingFactor}k.idealLength=L,k.elasticity=R,s.layoutEdges.push(k)}return s},"createLayoutInfo"),lp=v(function(e,r,a){var n=up(e,r,0,a);return 2>n.count?0:n.graph},"findLCA"),up=v(function t(e,r,a,n){var i=n.graphSet[a];if(-1<i.indexOf(e)&&-1<i.indexOf(r))return{count:2,graph:a};for(var o=0,s=0;s<i.length;s++){var u=i[s],l=n.idToIndex[u],c=n.layoutNodes[l].children;if(c.length!==0){var f=n.indexToGraph[n.idToIndex[c[0]]],d=t(e,r,f,n);if(d.count!==0)if(d.count===1){if(o++,o===2)break}else return d}}return{count:o,graph:a}},"findLCA_aux"),vp,cp=v(function(e,r){for(var a=e.clientWidth,n=e.clientHeight,i=0;i<e.nodeSize;i++){var o=e.layoutNodes[i];o.children.length===0&&!o.isLocked&&(o.positionX=Math.random()*a,o.positionY=Math.random()*n)}},"randomizePositions"),Yu=v(function(e,r,a){var n=e.boundingBox,i={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return r.boundingBox&&(a.forEach(function(o){var s=e.layoutNodes[e.idToIndex[o.data("id")]];i.x1=Math.min(i.x1,s.positionX),i.x2=Math.max(i.x2,s.positionX),i.y1=Math.min(i.y1,s.positionY),i.y2=Math.max(i.y2,s.positionY)}),i.w=i.x2-i.x1,i.h=i.y2-i.y1),function(o,s){var u=e.layoutNodes[e.idToIndex[o.data("id")]];if(r.boundingBox){var l=(u.positionX-i.x1)/i.w,c=(u.positionY-i.y1)/i.h;return{x:n.x1+l*n.w,y:n.y1+c*n.h}}else return{x:u.positionX,y:u.positionY}}},"getScaleInBoundsFn"),fp=v(function(e,r,a){var n=a.layout,i=a.eles.nodes(),o=Yu(e,a,i);i.positions(o),e.ready!==!0&&(e.ready=!0,n.one("layoutready",a.ready),n.emit({type:"layoutready",layout:this}))},"refreshPositions"),dp=v(function(e,r,a){hp(e,r),yp(e),mp(e,r),bp(e),wp(e)},"step"),hp=v(function(e,r){for(var a=0;a<e.graphSet.length;a++)for(var n=e.graphSet[a],i=n.length,o=0;o<i;o++)for(var s=e.layoutNodes[e.idToIndex[n[o]]],u=o+1;u<i;u++){var l=e.layoutNodes[e.idToIndex[n[u]]];gp(s,l,e,r)}},"calculateNodeForces"),ws=v(function(e){return-e+2*e*Math.random()},"randomDistance"),gp=v(function(e,r,a,n){var i=e.cmptId,o=r.cmptId;if(!(i!==o&&!a.isCompound)){var s=r.positionX-e.positionX,u=r.positionY-e.positionY,l=1;s===0&&u===0&&(s=ws(l),u=ws(l));var c=pp(e,r,s,u);if(c>0)var f=n.nodeOverlap*c,d=Math.sqrt(s*s+u*u),g=f*s/d,h=f*u/d;else var m=An(e,s,u),y=An(r,-1*s,-1*u),p=y.x-m.x,b=y.y-m.y,w=p*p+b*b,d=Math.sqrt(w),f=(e.nodeRepulsion+r.nodeRepulsion)/w,g=f*p/d,h=f*b/d;e.isLocked||(e.offsetX-=g,e.offsetY-=h),r.isLocked||(r.offsetX+=g,r.offsetY+=h)}},"nodeRepulsion"),pp=v(function(e,r,a,n){if(a>0)var i=e.maxX-r.minX;else var i=r.maxX-e.minX;if(n>0)var o=e.maxY-r.minY;else var o=r.maxY-e.minY;return i>=0&&o>=0?Math.sqrt(i*i+o*o):0},"nodesOverlap"),An=v(function(e,r,a){var n=e.positionX,i=e.positionY,o=e.height||1,s=e.width||1,u=a/r,l=o/s,c={};return r===0&&0<a||r===0&&0>a?(c.x=n,c.y=i+o/2,c):0<r&&-1*l<=u&&u<=l?(c.x=n+s/2,c.y=i+s*a/2/r,c):0>r&&-1*l<=u&&u<=l?(c.x=n-s/2,c.y=i-s*a/2/r,c):0<a&&(u<=-1*l||u>=l)?(c.x=n+o*r/2/a,c.y=i+o/2,c):(0>a&&(u<=-1*l||u>=l)&&(c.x=n-o*r/2/a,c.y=i-o/2),c)},"findClippingPoint"),yp=v(function(e,r){for(var a=0;a<e.edgeSize;a++){var n=e.layoutEdges[a],i=e.idToIndex[n.sourceId],o=e.layoutNodes[i],s=e.idToIndex[n.targetId],u=e.layoutNodes[s],l=u.positionX-o.positionX,c=u.positionY-o.positionY;if(!(l===0&&c===0)){var f=An(o,l,c),d=An(u,-1*l,-1*c),g=d.x-f.x,h=d.y-f.y,m=Math.sqrt(g*g+h*h),y=Math.pow(n.idealLength-m,2)/n.elasticity;if(m!==0)var p=y*g/m,b=y*h/m;else var p=0,b=0;o.isLocked||(o.offsetX+=p,o.offsetY+=b),u.isLocked||(u.offsetX-=p,u.offsetY-=b)}}},"calculateEdgeForces"),mp=v(function(e,r){if(r.gravity!==0)for(var a=1,n=0;n<e.graphSet.length;n++){var i=e.graphSet[n],o=i.length;if(n===0)var s=e.clientHeight/2,u=e.clientWidth/2;else var l=e.layoutNodes[e.idToIndex[i[0]]],c=e.layoutNodes[e.idToIndex[l.parentId]],s=c.positionX,u=c.positionY;for(var f=0;f<o;f++){var d=e.layoutNodes[e.idToIndex[i[f]]];if(!d.isLocked){var g=s-d.positionX,h=u-d.positionY,m=Math.sqrt(g*g+h*h);if(m>a){var y=r.gravity*g/m,p=r.gravity*h/m;d.offsetX+=y,d.offsetY+=p}}}}},"calculateGravityForces"),bp=v(function(e,r){var a=[],n=0,i=-1;for(a.push.apply(a,e.graphSet[0]),i+=e.graphSet[0].length;n<=i;){var o=a[n++],s=e.idToIndex[o],u=e.layoutNodes[s],l=u.children;if(0<l.length&&!u.isLocked){for(var c=u.offsetX,f=u.offsetY,d=0;d<l.length;d++){var g=e.layoutNodes[e.idToIndex[l[d]]];g.offsetX+=c,g.offsetY+=f,a[++i]=l[d]}u.offsetX=0,u.offsetY=0}}},"propagateForces"),wp=v(function(e,r){for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&(n.maxX=void 0,n.minX=void 0,n.maxY=void 0,n.minY=void 0)}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];if(!(0<n.children.length||n.isLocked)){var i=xp(n.offsetX,n.offsetY,e.temperature);n.positionX+=i.x,n.positionY+=i.y,n.offsetX=0,n.offsetY=0,n.minX=n.positionX-n.width,n.maxX=n.positionX+n.width,n.minY=n.positionY-n.height,n.maxY=n.positionY+n.height,Ep(n,e)}}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&!n.isLocked&&(n.positionX=(n.maxX+n.minX)/2,n.positionY=(n.maxY+n.minY)/2,n.width=n.maxX-n.minX,n.height=n.maxY-n.minY)}},"updatePositions"),xp=v(function(e,r,a){var n=Math.sqrt(e*e+r*r);if(n>a)var i={x:a*e/n,y:a*r/n};else var i={x:e,y:r};return i},"limitForce"),Ep=v(function t(e,r){var a=e.parentId;if(a!=null){var n=r.layoutNodes[r.idToIndex[a]],i=!1;if((n.maxX==null||e.maxX+n.padRight>n.maxX)&&(n.maxX=e.maxX+n.padRight,i=!0),(n.minX==null||e.minX-n.padLeft<n.minX)&&(n.minX=e.minX-n.padLeft,i=!0),(n.maxY==null||e.maxY+n.padBottom>n.maxY)&&(n.maxY=e.maxY+n.padBottom,i=!0),(n.minY==null||e.minY-n.padTop<n.minY)&&(n.minY=e.minY-n.padTop,i=!0),i)return t(n,r)}},"updateAncestryBoundaries"),xs=v(function(e,r){for(var a=e.layoutNodes,n=[],i=0;i<a.length;i++){var o=a[i],s=o.cmptId,u=n[s]=n[s]||[];u.push(o)}for(var l=0,i=0;i<n.length;i++){var c=n[i];if(c){c.x1=1/0,c.x2=-1/0,c.y1=1/0,c.y2=-1/0;for(var f=0;f<c.length;f++){var d=c[f];c.x1=Math.min(c.x1,d.positionX-d.width/2),c.x2=Math.max(c.x2,d.positionX+d.width/2),c.y1=Math.min(c.y1,d.positionY-d.height/2),c.y2=Math.max(c.y2,d.positionY+d.height/2)}c.w=c.x2-c.x1,c.h=c.y2-c.y1,l+=c.w*c.h}}n.sort(function(b,w){return w.w*w.h-b.w*b.h});for(var g=0,h=0,m=0,y=0,p=Math.sqrt(l)*e.clientWidth/e.clientHeight,i=0;i<n.length;i++){var c=n[i];if(c){for(var f=0;f<c.length;f++){var d=c[f];d.isLocked||(d.positionX+=g-c.x1,d.positionY+=h-c.y1)}g+=c.w+r.componentSpacing,m+=c.w+r.componentSpacing,y=Math.max(y,c.h),m>p&&(h+=y+r.componentSpacing,g=0,m=0,y=0)}}},"separateComponents"),Cp={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:v(function(e){},"position"),sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:v(function(e,r){return!0},"animateFilter"),ready:void 0,stop:void 0,transform:v(function(e,r){return r},"transform")};function vo(t){this.options=he({},Cp,t)}v(vo,"GridLayout");vo.prototype.run=function(){var t=this.options,e=t,r=t.cy,a=e.eles,n=a.nodes().not(":parent");e.sort&&(n=n.sort(e.sort));var i=bt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()});if(i.h===0||i.w===0)a.nodes().layoutPositions(this,e,function(X){return{x:i.x1,y:i.y1}});else{var o=n.size(),s=Math.sqrt(o*i.h/i.w),u=Math.round(s),l=Math.round(i.w/i.h*s),c=v(function(Z){if(Z==null)return Math.min(u,l);var J=Math.min(u,l);J==u?u=Z:l=Z},"small"),f=v(function(Z){if(Z==null)return Math.max(u,l);var J=Math.max(u,l);J==u?u=Z:l=Z},"large"),d=e.rows,g=e.cols!=null?e.cols:e.columns;if(d!=null&&g!=null)u=d,l=g;else if(d!=null&&g==null)u=d,l=Math.ceil(o/u);else if(d==null&&g!=null)l=g,u=Math.ceil(o/l);else if(l*u>o){var h=c(),m=f();(h-1)*m>=o?c(h-1):(m-1)*h>=o&&f(m-1)}else for(;l*u<o;){var y=c(),p=f();(p+1)*y>=o?f(p+1):c(y+1)}var b=i.w/l,w=i.h/u;if(e.condense&&(b=0,w=0),e.avoidOverlap)for(var x=0;x<n.length;x++){var S=n[x],C=S._private.position;(C.x==null||C.y==null)&&(C.x=0,C.y=0);var E=S.layoutDimensions(e),D=e.avoidOverlapPadding,T=E.w+D,A=E.h+D;b=Math.max(b,T),w=Math.max(w,A)}for(var B={},k=v(function(Z,J){return!!B["c-"+Z+"-"+J]},"used"),L=v(function(Z,J){B["c-"+Z+"-"+J]=!0},"use"),R=0,M=0,I=v(function(){M++,M>=l&&(M=0,R++)},"moveToNextCell"),O={},F=0;F<n.length;F++){var K=n[F],$=e.position(K);if($&&($.row!==void 0||$.col!==void 0)){var q={row:$.row,col:$.col};if(q.col===void 0)for(q.col=0;k(q.row,q.col);)q.col++;else if(q.row===void 0)for(q.row=0;k(q.row,q.col);)q.row++;O[K.id()]=q,L(q.row,q.col)}}var G=v(function(Z,J){var Q,ee;if(Z.locked()||Z.isParent())return!1;var re=O[Z.id()];if(re)Q=re.col*b+b/2+i.x1,ee=re.row*w+w/2+i.y1;else{for(;k(R,M);)I();Q=M*b+b/2+i.x1,ee=R*w+w/2+i.y1,L(R,M),I()}return{x:Q,y:ee}},"getPos");n.layoutPositions(this,e,G)}return this};var Tp={ready:v(function(){},"ready"),stop:v(function(){},"stop")};function _n(t){this.options=he({},Tp,t)}v(_n,"NullLayout");_n.prototype.run=function(){var t=this.options,e=t.eles,r=this;return t.cy,r.emit("layoutstart"),e.nodes().positions(function(){return{x:0,y:0}}),r.one("layoutready",t.ready),r.emit("layoutready"),r.one("layoutstop",t.stop),r.emit("layoutstop"),this};_n.prototype.stop=function(){return this};var Sp={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,spacingFactor:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:v(function(e,r){return!0},"animateFilter"),ready:void 0,stop:void 0,transform:v(function(e,r){return r},"transform")};function co(t){this.options=he({},Sp,t)}v(co,"PresetLayout");co.prototype.run=function(){var t=this.options,e=t.eles,r=e.nodes(),a=Ge(t.positions);function n(i){if(t.positions==null)return xf(i.position());if(a)return t.positions(i);var o=t.positions[i._private.data.id];return o??null}return v(n,"getPosition"),r.layoutPositions(this,t,function(i,o){var s=n(i);return i.locked()||s==null?!1:s}),this};var Dp={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:v(function(e,r){return!0},"animateFilter"),ready:void 0,stop:void 0,transform:v(function(e,r){return r},"transform")};function fo(t){this.options=he({},Dp,t)}v(fo,"RandomLayout");fo.prototype.run=function(){var t=this.options,e=t.cy,r=t.eles,a=bt(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),n=v(function(o,s){return{x:a.x1+Math.round(Math.random()*a.w),y:a.y1+Math.round(Math.random()*a.h)}},"getPos");return r.nodes().layoutPositions(this,t,n),this};var Pp=[{name:"breadthfirst",impl:so},{name:"circle",impl:lo},{name:"concentric",impl:uo},{name:"cose",impl:Wa},{name:"grid",impl:vo},{name:"null",impl:_n},{name:"preset",impl:co},{name:"random",impl:fo}];function ho(t){this.options=t,this.notifications=0}v(ho,"NullRenderer");var Es=v(function(){},"noop"),Cs=v(function(){throw new Error("A headless instance can not render images")},"throwImgErr");ho.prototype={recalculateRenderedStyle:Es,notify:v(function(){this.notifications++},"notify"),init:Es,isHeadless:v(function(){return!0},"isHeadless"),png:Cs,jpg:Cs};var go={};go.arrowShapeWidth=.3;go.registerArrowShapes=function(){var t=this.arrowShapes={},e=this,r=v(function(l,c,f,d,g,h,m){var y=g.x-f/2-m,p=g.x+f/2+m,b=g.y-f/2-m,w=g.y+f/2+m,x=y<=l&&l<=p&&b<=c&&c<=w;return x},"bbCollide"),a=v(function(l,c,f,d,g){var h=l*Math.cos(d)-c*Math.sin(d),m=l*Math.sin(d)+c*Math.cos(d),y=h*f,p=m*f,b=y+g.x,w=p+g.y;return{x:b,y:w}},"transform"),n=v(function(l,c,f,d){for(var g=[],h=0;h<l.length;h+=2){var m=l[h],y=l[h+1];g.push(a(m,y,c,f,d))}return g},"transformPoints"),i=v(function(l){for(var c=[],f=0;f<l.length;f++){var d=l[f];c.push(d.x,d.y)}return c},"pointsToArr"),o=v(function(l){return l.pstyle("width").pfValue*l.pstyle("arrow-scale").pfValue*2},"standardGap"),s=v(function(l,c){fe(c)&&(c=t[c]),t[l]=he({name:l,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:v(function(d,g,h,m,y,p){var b=i(n(this.points,h+2*p,m,y)),w=yt(d,g,b);return w},"collide"),roughCollide:r,draw:v(function(d,g,h,m){var y=n(this.points,g,h,m);e.arrowShapeImpl("polygon")(d,y)},"draw"),spacing:v(function(d){return 0},"spacing"),gap:o},c)},"defineArrowShape");s("none",{collide:Tn,roughCollide:Tn,draw:Ui,spacing:qo,gap:qo}),s("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),s("arrow","triangle"),s("triangle-backcurve",{points:t.triangle.points,controlPoint:[0,-.15],roughCollide:r,draw:v(function(l,c,f,d,g){var h=n(this.points,c,f,d),m=this.controlPoint,y=a(m[0],m[1],c,f,d);e.arrowShapeImpl(this.name)(l,h,y)},"draw"),gap:v(function(l){return o(l)*.8},"gap")}),s("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:v(function(l,c,f,d,g,h,m){var y=i(n(this.points,f+2*m,d,g)),p=i(n(this.pointsTee,f+2*m,d,g)),b=yt(l,c,y)||yt(l,c,p);return b},"collide"),draw:v(function(l,c,f,d,g){var h=n(this.points,c,f,d),m=n(this.pointsTee,c,f,d);e.arrowShapeImpl(this.name)(l,h,m)},"draw")}),s("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:v(function(l,c,f,d,g,h,m){var y=g,p=Math.pow(y.x-l,2)+Math.pow(y.y-c,2)<=Math.pow((f+2*m)*this.radius,2),b=i(n(this.points,f+2*m,d,g));return yt(l,c,b)||p},"collide"),draw:v(function(l,c,f,d,g){var h=n(this.pointsTr,c,f,d);e.arrowShapeImpl(this.name)(l,h,d.x,d.y,this.radius*c)},"draw"),spacing:v(function(l){return e.getArrowWidth(l.pstyle("width").pfValue,l.pstyle("arrow-scale").value)*this.radius},"spacing")}),s("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:v(function(l,c){var f=this.baseCrossLinePts.slice(),d=c/l,g=3,h=5;return f[g]=f[g]-d,f[h]=f[h]-d,f},"crossLinePts"),collide:v(function(l,c,f,d,g,h,m){var y=i(n(this.points,f+2*m,d,g)),p=i(n(this.crossLinePts(f,h),f+2*m,d,g)),b=yt(l,c,y)||yt(l,c,p);return b},"collide"),draw:v(function(l,c,f,d,g){var h=n(this.points,c,f,d),m=n(this.crossLinePts(c,g),c,f,d);e.arrowShapeImpl(this.name)(l,h,m)},"draw")}),s("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:v(function(l){return o(l)*.525},"gap")}),s("circle",{radius:.15,collide:v(function(l,c,f,d,g,h,m){var y=g,p=Math.pow(y.x-l,2)+Math.pow(y.y-c,2)<=Math.pow((f+2*m)*this.radius,2);return p},"collide"),draw:v(function(l,c,f,d,g){e.arrowShapeImpl(this.name)(l,d.x,d.y,this.radius*c)},"draw"),spacing:v(function(l){return e.getArrowWidth(l.pstyle("width").pfValue,l.pstyle("arrow-scale").value)*this.radius},"spacing")}),s("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:v(function(l){return 1},"spacing"),gap:v(function(l){return 1},"gap")}),s("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),s("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:v(function(l){return l.pstyle("width").pfValue*l.pstyle("arrow-scale").value},"gap")}),s("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:v(function(l){return .95*l.pstyle("width").pfValue*l.pstyle("arrow-scale").value},"gap")})};var Rr={};Rr.projectIntoViewport=function(t,e){var r=this.cy,a=this.findContainerClientCoords(),n=a[0],i=a[1],o=a[4],s=r.pan(),u=r.zoom(),l=((t-n)/o-s.x)/u,c=((e-i)/o-s.y)/u;return[l,c]};Rr.findContainerClientCoords=function(){if(this.containerBB)return this.containerBB;var t=this.container,e=t.getBoundingClientRect(),r=this.cy.window().getComputedStyle(t),a=v(function(p){return parseFloat(r.getPropertyValue(p))},"styleValue"),n={left:a("padding-left"),right:a("padding-right"),top:a("padding-top"),bottom:a("padding-bottom")},i={left:a("border-left-width"),right:a("border-right-width"),top:a("border-top-width"),bottom:a("border-bottom-width")},o=t.clientWidth,s=t.clientHeight,u=n.left+n.right,l=n.top+n.bottom,c=i.left+i.right,f=e.width/(o+c),d=o-u,g=s-l,h=e.left+n.left+i.left,m=e.top+n.top+i.top;return this.containerBB=[h,m,d,g,f]};Rr.invalidateContainerClientCoordsCache=function(){this.containerBB=null};Rr.findNearestElement=function(t,e,r,a){return this.findNearestElements(t,e,r,a)[0]};Rr.findNearestElements=function(t,e,r,a){var n=this,i=this,o=i.getCachedZSortedEles(),s=[],u=i.cy.zoom(),l=i.cy.hasCompoundNodes(),c=(a?24:8)/u,f=(a?8:2)/u,d=(a?8:2)/u,g=1/0,h,m;r&&(o=o.interactive);function y(E,D){if(E.isNode()){if(m)return;m=E,s.push(E)}if(E.isEdge()&&(D==null||D<g))if(h){if(h.pstyle("z-compound-depth").value===E.pstyle("z-compound-depth").value&&h.pstyle("z-compound-depth").value===E.pstyle("z-compound-depth").value){for(var T=0;T<s.length;T++)if(s[T].isEdge()){s[T]=E,h=E,g=D??g;break}}}else s.push(E),h=E,g=D??g}v(y,"addEle");function p(E){var D=E.outerWidth()+2*f,T=E.outerHeight()+2*f,A=D/2,B=T/2,k=E.position(),L=E.pstyle("corner-radius").value==="auto"?"auto":E.pstyle("corner-radius").pfValue,R=E._private.rscratch;if(k.x-A<=t&&t<=k.x+A&&k.y-B<=e&&e<=k.y+B){var M=i.nodeShapes[n.getNodeShape(E)];if(M.checkPoint(t,e,0,D,T,k.x,k.y,L,R))return y(E,0),!0}}v(p,"checkNode");function b(E){var D=E._private,T=D.rscratch,A=E.pstyle("width").pfValue,B=E.pstyle("arrow-scale").value,k=A/2+c,L=k*k,R=k*2,F=D.source,K=D.target,M;if(T.edgeType==="segments"||T.edgeType==="straight"||T.edgeType==="haystack"){for(var I=T.allpts,O=0;O+3<I.length;O+=2)if(Mf(t,e,I[O],I[O+1],I[O+2],I[O+3],R)&&L>(M=Vf(t,e,I[O],I[O+1],I[O+2],I[O+3])))return y(E,M),!0}else if(T.edgeType==="bezier"||T.edgeType==="multibezier"||T.edgeType==="self"||T.edgeType==="compound"){for(var I=T.allpts,O=0;O+5<T.allpts.length;O+=4)if(Of(t,e,I[O],I[O+1],I[O+2],I[O+3],I[O+4],I[O+5],R)&&L>(M=zf(t,e,I[O],I[O+1],I[O+2],I[O+3],I[O+4],I[O+5])))return y(E,M),!0}for(var F=F||D.source,K=K||D.target,$=n.getArrowWidth(A,B),q=[{name:"source",x:T.arrowStartX,y:T.arrowStartY,angle:T.srcArrowAngle},{name:"target",x:T.arrowEndX,y:T.arrowEndY,angle:T.tgtArrowAngle},{name:"mid-source",x:T.midX,y:T.midY,angle:T.midsrcArrowAngle},{name:"mid-target",x:T.midX,y:T.midY,angle:T.midtgtArrowAngle}],O=0;O<q.length;O++){var G=q[O],X=i.arrowShapes[E.pstyle(G.name+"-arrow-shape").value],Z=E.pstyle("width").pfValue;if(X.roughCollide(t,e,$,G.angle,{x:G.x,y:G.y},Z,c)&&X.collide(t,e,$,G.angle,{x:G.x,y:G.y},Z,c))return y(E),!0}l&&s.length>0&&(p(F),p(K))}v(b,"checkEdge");function w(E,D,T){return At(E,D,T)}v(w,"preprop");function x(E,D){var T=E._private,A=d,B;D?B=D+"-":B="",E.boundingBox();var k=T.labelBounds[D||"main"],L=E.pstyle(B+"label").value,R=E.pstyle("text-events").strValue==="yes";if(!(!R||!L)){var M=w(T.rscratch,"labelX",D),I=w(T.rscratch,"labelY",D),O=w(T.rscratch,"labelAngle",D),F=E.pstyle(B+"text-margin-x").pfValue,K=E.pstyle(B+"text-margin-y").pfValue,$=k.x1-A-F,q=k.x2+A-F,G=k.y1-A-K,X=k.y2+A-K;if(O){var Z=Math.cos(O),J=Math.sin(O),Q=v(function(oe,ue){return oe=oe-M,ue=ue-I,{x:oe*Z-ue*J+M,y:oe*J+ue*Z+I}},"rotate"),ee=Q($,G),re=Q($,X),W=Q(q,G),N=Q(q,X),U=[ee.x+F,ee.y+K,W.x+F,W.y+K,N.x+F,N.y+K,re.x+F,re.y+K];if(yt(t,e,U))return y(E),!0}else if(_r(k,t,e))return y(E),!0}}v(x,"checkLabel");for(var S=o.length-1;S>=0;S--){var C=o[S];C.isNode()?p(C)||x(C):b(C)||x(C)||x(C,"source")||x(C,"target")}return s};Rr.getAllInBox=function(t,e,r,a){var n=this.getCachedZSortedEles().interactive,i=[],o=Math.min(t,r),s=Math.max(t,r),u=Math.min(e,a),l=Math.max(e,a);t=o,r=s,e=u,a=l;for(var c=bt({x1:t,y1:e,x2:r,y2:a}),f=0;f<n.length;f++){var d=n[f];if(d.isNode()){var g=d,h=g.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:!1});Yi(c,h)&&!Sl(h,c)&&i.push(g)}else{var m=d,y=m._private,p=y.rscratch;if(p.startX!=null&&p.startY!=null&&!_r(c,p.startX,p.startY)||p.endX!=null&&p.endY!=null&&!_r(c,p.endX,p.endY))continue;if(p.edgeType==="bezier"||p.edgeType==="multibezier"||p.edgeType==="self"||p.edgeType==="compound"||p.edgeType==="segments"||p.edgeType==="haystack"){for(var b=y.rstyle.bezierPts||y.rstyle.linePts||y.rstyle.haystackPts,w=!0,x=0;x<b.length;x++)if(!If(c,b[x])){w=!1;break}w&&i.push(m)}else(p.edgeType==="haystack"||p.edgeType==="straight")&&i.push(m)}}return i};var Rn={};Rn.calculateArrowAngles=function(t){var e=t._private.rscratch,r=e.edgeType==="haystack",a=e.edgeType==="bezier",n=e.edgeType==="multibezier",i=e.edgeType==="segments",o=e.edgeType==="compound",s=e.edgeType==="self",u,l,c,f,d,g,p,b;if(r?(c=e.haystackPts[0],f=e.haystackPts[1],d=e.haystackPts[2],g=e.haystackPts[3]):(c=e.arrowStartX,f=e.arrowStartY,d=e.arrowEndX,g=e.arrowEndY),p=e.midX,b=e.midY,i)u=c-e.segpts[0],l=f-e.segpts[1];else if(n||o||s||a){var h=e.allpts,m=je(h[0],h[2],h[4],.1),y=je(h[1],h[3],h[5],.1);u=c-m,l=f-y}else u=c-p,l=f-b;e.srcArrowAngle=Qa(u,l);var p=e.midX,b=e.midY;if(r&&(p=(c+d)/2,b=(f+g)/2),u=d-c,l=g-f,i){var h=e.allpts;if(h.length/2%2===0){var w=h.length/2,x=w-2;u=h[w]-h[x],l=h[w+1]-h[x+1]}else if(e.isRound)u=e.midVector[1],l=-e.midVector[0];else{var w=h.length/2-1,x=w-2;u=h[w]-h[x],l=h[w+1]-h[x+1]}}else if(n||o||s){var h=e.allpts,S=e.ctrlpts,C,E,D,T;if(S.length/2%2===0){var A=h.length/2-1,B=A+2,k=B+2;C=je(h[A],h[B],h[k],0),E=je(h[A+1],h[B+1],h[k+1],0),D=je(h[A],h[B],h[k],1e-4),T=je(h[A+1],h[B+1],h[k+1],1e-4)}else{var B=h.length/2-1,A=B-2,k=B+2;C=je(h[A],h[B],h[k],.4999),E=je(h[A+1],h[B+1],h[k+1],.4999),D=je(h[A],h[B],h[k],.5),T=je(h[A+1],h[B+1],h[k+1],.5)}u=D-C,l=T-E}if(e.midtgtArrowAngle=Qa(u,l),e.midDispX=u,e.midDispY=l,u*=-1,l*=-1,i){var h=e.allpts;if(h.length/2%2!==0){if(!e.isRound){var w=h.length/2-1,L=w+2;u=-(h[L]-h[w]),l=-(h[L+1]-h[w+1])}}}if(e.midsrcArrowAngle=Qa(u,l),i)u=d-e.segpts[e.segpts.length-2],l=g-e.segpts[e.segpts.length-1];else if(n||o||s||a){var h=e.allpts,R=h.length,m=je(h[R-6],h[R-4],h[R-2],.9),y=je(h[R-5],h[R-3],h[R-1],.9);u=d-m,l=g-y}else u=d-p,l=g-b;e.tgtArrowAngle=Qa(u,l)};Rn.getArrowWidth=Rn.getArrowHeight=function(t,e){var r=this.arrowWidthCache=this.arrowWidthCache||{},a=r[t+", "+e];return a||(a=Math.max(Math.pow(t*13.37,.9),29)*e,r[t+", "+e]=a,a)};var Bi,Ai,Ft={},Tt={},Ts,Ss,br,yn,Wt,fr,mr,Ot,Or,on,Zu,Qu,Ri,Li,Ds,Ps=v(function(e,r,a){a.x=r.x-e.x,a.y=r.y-e.y,a.len=Math.sqrt(a.x*a.x+a.y*a.y),a.nx=a.x/a.len,a.ny=a.y/a.len,a.ang=Math.atan2(a.ny,a.nx)},"asVec"),kp=v(function(e,r){r.x=e.x*-1,r.y=e.y*-1,r.nx=e.nx*-1,r.ny=e.ny*-1,r.ang=e.ang>0?-(Math.PI-e.ang):Math.PI+e.ang},"invertVec"),Bp=v(function(e,r,a,n,i){if(e!==Ds?Ps(r,e,Ft):kp(Tt,Ft),Ps(r,a,Tt),Ts=Ft.nx*Tt.ny-Ft.ny*Tt.nx,Ss=Ft.nx*Tt.nx-Ft.ny*-Tt.ny,Wt=Math.asin(Math.max(-1,Math.min(1,Ts))),Math.abs(Wt)<1e-6){Bi=r.x,Ai=r.y,mr=Or=0;return}br=1,yn=!1,Ss<0?Wt<0?Wt=Math.PI+Wt:(Wt=Math.PI-Wt,br=-1,yn=!0):Wt>0&&(br=-1,yn=!0),r.radius!==void 0?Or=r.radius:Or=n,fr=Wt/2,on=Math.min(Ft.len/2,Tt.len/2),i?(Ot=Math.abs(Math.cos(fr)*Or/Math.sin(fr)),Ot>on?(Ot=on,mr=Math.abs(Ot*Math.sin(fr)/Math.cos(fr))):mr=Or):(Ot=Math.min(on,Or),mr=Math.abs(Ot*Math.sin(fr)/Math.cos(fr))),Ri=r.x+Tt.nx*Ot,Li=r.y+Tt.ny*Ot,Bi=Ri-Tt.ny*mr*br,Ai=Li+Tt.nx*mr*br,Zu=r.x+Ft.nx*Ot,Qu=r.y+Ft.ny*Ot,Ds=r},"calcCornerArc");function po(t,e){e.radius===0?t.lineTo(e.cx,e.cy):t.arc(e.cx,e.cy,e.radius,e.startAngle,e.endAngle,e.counterClockwise)}v(po,"drawPreparedRoundCorner");function Xn(t,e,r,a){var n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0;return a===0||e.radius===0?{cx:e.x,cy:e.y,radius:0,startX:e.x,startY:e.y,stopX:e.x,stopY:e.y,startAngle:void 0,endAngle:void 0,counterClockwise:void 0}:(Bp(t,e,r,a,n),{cx:Bi,cy:Ai,radius:mr,startX:Zu,startY:Qu,stopX:Ri,stopY:Li,startAngle:Ft.ang+Math.PI/2*br,endAngle:Tt.ang-Math.PI/2*br,counterClockwise:yn})}v(Xn,"getRoundCorner");var vt={};vt.findMidptPtsEtc=function(t,e){var r=e.posPts,a=e.intersectionPts,n=e.vectorNormInverse,i,o=t.pstyle("source-endpoint"),s=t.pstyle("target-endpoint"),u=o.units!=null&&s.units!=null,l=v(function(S,C,E,D){var T=D-C,A=E-S,B=Math.sqrt(A*A+T*T);return{x:-T/B,y:A/B}},"recalcVectorNormInverse"),c=t.pstyle("edge-distances").value;switch(c){case"node-position":i=r;break;case"intersection":i=a;break;case"endpoints":{if(u){var f=this.manualEndptToPx(t.source()[0],o),d=We(f,2),g=d[0],h=d[1],m=this.manualEndptToPx(t.target()[0],s),y=We(m,2),p=y[0],b=y[1],w={x1:g,y1:h,x2:p,y2:b};n=l(g,h,p,b),i=w}else Re("Edge ".concat(t.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default).")),i=a;break}}return{midptPts:i,vectorNormInverse:n}};vt.findHaystackPoints=function(t){for(var e=0;e<t.length;e++){var r=t[e],a=r._private,n=a.rscratch;if(!n.haystack){var i=Math.random()*2*Math.PI;n.source={x:Math.cos(i),y:Math.sin(i)},i=Math.random()*2*Math.PI,n.target={x:Math.cos(i),y:Math.sin(i)}}var o=a.source,s=a.target,u=o.position(),l=s.position(),c=o.width(),f=s.width(),d=o.height(),g=s.height(),h=r.pstyle("haystack-radius").value,m=h/2;n.haystackPts=n.allpts=[n.source.x*c*m+u.x,n.source.y*d*m+u.y,n.target.x*f*m+l.x,n.target.y*g*m+l.y],n.midX=(n.allpts[0]+n.allpts[2])/2,n.midY=(n.allpts[1]+n.allpts[3])/2,n.edgeType="haystack",n.haystack=!0,this.storeEdgeProjections(r),this.calculateArrowAngles(r),this.recalculateEdgeLabelProjections(r),this.calculateLabelAngles(r)}};vt.findSegmentsPoints=function(t,e){var r=t._private.rscratch,a=t.pstyle("segment-weights"),n=t.pstyle("segment-distances"),i=t.pstyle("segment-radii"),o=t.pstyle("radius-type"),s=Math.min(a.pfValue.length,n.pfValue.length),u=i.pfValue[i.pfValue.length-1],l=o.pfValue[o.pfValue.length-1];r.edgeType="segments",r.segpts=[],r.radii=[],r.isArcRadius=[];for(var c=0;c<s;c++){var f=a.pfValue[c],d=n.pfValue[c],g=1-f,h=f,m=this.findMidptPtsEtc(t,e),y=m.midptPts,p=m.vectorNormInverse,b={x:y.x1*g+y.x2*h,y:y.y1*g+y.y2*h};r.segpts.push(b.x+p.x*d,b.y+p.y*d),r.radii.push(i.pfValue[c]!==void 0?i.pfValue[c]:u),r.isArcRadius.push((o.pfValue[c]!==void 0?o.pfValue[c]:l)==="arc-radius")}};vt.findLoopPoints=function(t,e,r,a){var n=t._private.rscratch,i=e.dirCounts,o=e.srcPos,s=t.pstyle("control-point-distances"),u=s?s.pfValue[0]:void 0,l=t.pstyle("loop-direction").pfValue,c=t.pstyle("loop-sweep").pfValue,f=t.pstyle("control-point-step-size").pfValue;n.edgeType="self";var d=r,g=f;a&&(d=0,g=u);var h=l-Math.PI/2,m=h-c/2,y=h+c/2,p=l+"_"+c;d=i[p]===void 0?i[p]=0:++i[p],n.ctrlpts=[o.x+Math.cos(m)*1.4*g*(d/3+1),o.y+Math.sin(m)*1.4*g*(d/3+1),o.x+Math.cos(y)*1.4*g*(d/3+1),o.y+Math.sin(y)*1.4*g*(d/3+1)]};vt.findCompoundLoopPoints=function(t,e,r,a){var n=t._private.rscratch;n.edgeType="compound";var i=e.srcPos,o=e.tgtPos,s=e.srcW,u=e.srcH,l=e.tgtW,c=e.tgtH,f=t.pstyle("control-point-step-size").pfValue,d=t.pstyle("control-point-distances"),g=d?d.pfValue[0]:void 0,h=r,m=f;a&&(h=0,m=g);var y=50,p={x:i.x-s/2,y:i.y-u/2},b={x:o.x-l/2,y:o.y-c/2},w={x:Math.min(p.x,b.x),y:Math.min(p.y,b.y)},x=.5,S=Math.max(x,Math.log(s*.01)),C=Math.max(x,Math.log(l*.01));n.ctrlpts=[w.x,w.y-(1+Math.pow(y,1.12)/100)*m*(h/3+1)*S,w.x-(1+Math.pow(y,1.12)/100)*m*(h/3+1)*C,w.y]};vt.findStraightEdgePoints=function(t){t._private.rscratch.edgeType="straight"};vt.findBezierPoints=function(t,e,r,a,n){var i=t._private.rscratch,o=t.pstyle("control-point-step-size").pfValue,s=t.pstyle("control-point-distances"),u=t.pstyle("control-point-weights"),l=s&&u?Math.min(s.value.length,u.value.length):1,c=s?s.pfValue[0]:void 0,f=u.value[0],d=a;i.edgeType=d?"multibezier":"bezier",i.ctrlpts=[];for(var g=0;g<l;g++){var h=(.5-e.eles.length/2+r)*o*(n?-1:1),m=void 0,y=Cl(h);d&&(c=s?s.pfValue[g]:o,f=u.value[g]),a?m=c:m=c!==void 0?y*c:void 0;var p=m!==void 0?m:h,b=1-f,w=f,x=this.findMidptPtsEtc(t,e),S=x.midptPts,C=x.vectorNormInverse,E={x:S.x1*b+S.x2*w,y:S.y1*b+S.y2*w};i.ctrlpts.push(E.x+C.x*p,E.y+C.y*p)}};vt.findTaxiPoints=function(t,e){var r=t._private.rscratch;r.edgeType="segments";var a="vertical",n="horizontal",i="leftward",o="rightward",s="downward",u="upward",l="auto",c=e.posPts,f=e.srcW,d=e.srcH,g=e.tgtW,h=e.tgtH,m=t.pstyle("edge-distances").value,y=m!=="node-position",p=t.pstyle("taxi-direction").value,b=p,w=t.pstyle("taxi-turn"),x=w.units==="%",S=w.pfValue,C=S<0,E=t.pstyle("taxi-turn-min-distance").pfValue,D=y?(f+g)/2:0,T=y?(d+h)/2:0,A=c.x2-c.x1,B=c.y2-c.y1,k=v(function(Y,ie){return Y>0?Math.max(Y-ie,0):Math.min(Y+ie,0)},"subDWH"),L=k(A,D),R=k(B,T),M=!1;b===l?p=Math.abs(L)>Math.abs(R)?n:a:b===u||b===s?(p=a,M=!0):(b===i||b===o)&&(p=n,M=!0);var I=p===a,O=I?R:L,F=I?B:A,K=Cl(F),$=!1;!(M&&(x||C))&&(b===s&&F<0||b===u&&F>0||b===i&&F>0||b===o&&F<0)&&(K*=-1,O=K*Math.abs(O),$=!0);var q;if(x){var G=S<0?1+S:S;q=G*O}else{var X=S<0?O:0;q=X+S*K}var Z=v(function(Y){return Math.abs(Y)<E||Math.abs(Y)>=Math.abs(O)},"getIsTooClose"),J=Z(q),Q=Z(Math.abs(O)-Math.abs(q)),ee=J||Q;if(ee&&!$)if(I){var re=Math.abs(F)<=d/2,W=Math.abs(A)<=g/2;if(re){var N=(c.x1+c.x2)/2,U=c.y1,te=c.y2;r.segpts=[N,U,N,te]}else if(W){var oe=(c.y1+c.y2)/2,ue=c.x1,Se=c.x2;r.segpts=[ue,oe,Se,oe]}else r.segpts=[c.x1,c.y2]}else{var Le=Math.abs(F)<=f/2,Ie=Math.abs(B)<=h/2;if(Le){var ve=(c.y1+c.y2)/2,le=c.x1,ye=c.x2;r.segpts=[le,ve,ye,ve]}else if(Ie){var me=(c.x1+c.x2)/2,ge=c.y1,be=c.y2;r.segpts=[me,ge,me,be]}else r.segpts=[c.x2,c.y1]}else if(I){var Ce=c.y1+q+(y?d/2*K:0),De=c.x1,j=c.x2;r.segpts=[De,Ce,j,Ce]}else{var P=c.x1+q+(y?f/2*K:0),z=c.y1,_=c.y2;r.segpts=[P,z,P,_]}if(r.isRound){var V=t.pstyle("taxi-radius").value,H=t.pstyle("radius-type").value[0]==="arc-radius";r.radii=new Array(r.segpts.length/2).fill(V),r.isArcRadius=new Array(r.segpts.length/2).fill(H)}};vt.tryToCorrectInvalidPoints=function(t,e){var r=t._private.rscratch;if(r.edgeType==="bezier"){var a=e.srcPos,n=e.tgtPos,i=e.srcW,o=e.srcH,s=e.tgtW,u=e.tgtH,l=e.srcShape,c=e.tgtShape,f=e.srcCornerRadius,d=e.tgtCornerRadius,g=e.srcRs,h=e.tgtRs,m=!ae(r.startX)||!ae(r.startY),y=!ae(r.arrowStartX)||!ae(r.arrowStartY),p=!ae(r.endX)||!ae(r.endY),b=!ae(r.arrowEndX)||!ae(r.arrowEndY),w=3,x=this.getArrowWidth(t.pstyle("width").pfValue,t.pstyle("arrow-scale").value)*this.arrowShapeWidth,S=w*x,C=Er({x:r.ctrlpts[0],y:r.ctrlpts[1]},{x:r.startX,y:r.startY}),E=C<S,D=Er({x:r.ctrlpts[0],y:r.ctrlpts[1]},{x:r.endX,y:r.endY}),T=D<S,A=!1;if(m||y||E){A=!0;var B={x:r.ctrlpts[0]-a.x,y:r.ctrlpts[1]-a.y},k=Math.sqrt(B.x*B.x+B.y*B.y),L={x:B.x/k,y:B.y/k},R=Math.max(i,o),M={x:r.ctrlpts[0]+L.x*2*R,y:r.ctrlpts[1]+L.y*2*R},I=l.intersectLine(a.x,a.y,i,o,M.x,M.y,0,f,g);E?(r.ctrlpts[0]=r.ctrlpts[0]+L.x*(S-C),r.ctrlpts[1]=r.ctrlpts[1]+L.y*(S-C)):(r.ctrlpts[0]=I[0]+L.x*S,r.ctrlpts[1]=I[1]+L.y*S)}if(p||b||T){A=!0;var O={x:r.ctrlpts[0]-n.x,y:r.ctrlpts[1]-n.y},F=Math.sqrt(O.x*O.x+O.y*O.y),K={x:O.x/F,y:O.y/F},$=Math.max(i,o),q={x:r.ctrlpts[0]+K.x*2*$,y:r.ctrlpts[1]+K.y*2*$},G=c.intersectLine(n.x,n.y,s,u,q.x,q.y,0,d,h);T?(r.ctrlpts[0]=r.ctrlpts[0]+K.x*(S-D),r.ctrlpts[1]=r.ctrlpts[1]+K.y*(S-D)):(r.ctrlpts[0]=G[0]+K.x*S,r.ctrlpts[1]=G[1]+K.y*S)}A&&this.findEndpoints(t)}};vt.storeAllpts=function(t){var e=t._private.rscratch;if(e.edgeType==="multibezier"||e.edgeType==="bezier"||e.edgeType==="self"||e.edgeType==="compound"){e.allpts=[],e.allpts.push(e.startX,e.startY);for(var r=0;r+1<e.ctrlpts.length;r+=2)e.allpts.push(e.ctrlpts[r],e.ctrlpts[r+1]),r+3<e.ctrlpts.length&&e.allpts.push((e.ctrlpts[r]+e.ctrlpts[r+2])/2,(e.ctrlpts[r+1]+e.ctrlpts[r+3])/2);e.allpts.push(e.endX,e.endY);var a,n;e.ctrlpts.length/2%2===0?(a=e.allpts.length/2-1,e.midX=e.allpts[a],e.midY=e.allpts[a+1]):(a=e.allpts.length/2-3,n=.5,e.midX=je(e.allpts[a],e.allpts[a+2],e.allpts[a+4],n),e.midY=je(e.allpts[a+1],e.allpts[a+3],e.allpts[a+5],n))}else if(e.edgeType==="straight")e.allpts=[e.startX,e.startY,e.endX,e.endY],e.midX=(e.startX+e.endX+e.arrowStartX+e.arrowEndX)/4,e.midY=(e.startY+e.endY+e.arrowStartY+e.arrowEndY)/4;else if(e.edgeType==="segments"){if(e.allpts=[],e.allpts.push(e.startX,e.startY),e.allpts.push.apply(e.allpts,e.segpts),e.allpts.push(e.endX,e.endY),e.isRound){e.roundCorners=[];for(var i=2;i+3<e.allpts.length;i+=2){var o=e.radii[i/2-1],s=e.isArcRadius[i/2-1];e.roundCorners.push(Xn({x:e.allpts[i-2],y:e.allpts[i-1]},{x:e.allpts[i],y:e.allpts[i+1],radius:o},{x:e.allpts[i+2],y:e.allpts[i+3]},o,s))}}if(e.segpts.length%4===0){var u=e.segpts.length/2,l=u-2;e.midX=(e.segpts[l]+e.segpts[u])/2,e.midY=(e.segpts[l+1]+e.segpts[u+1])/2}else{var c=e.segpts.length/2-1;if(!e.isRound)e.midX=e.segpts[c],e.midY=e.segpts[c+1];else{var f={x:e.segpts[c],y:e.segpts[c+1]},d=e.roundCorners[c/2],g=[f.x-d.cx,f.y-d.cy],h=d.radius/Math.sqrt(Math.pow(g[0],2)+Math.pow(g[1],2));g=g.map(function(m){return m*h}),e.midX=d.cx+g[0],e.midY=d.cy+g[1],e.midVector=g}}}};vt.checkForInvalidEdgeWarning=function(t){var e=t[0]._private.rscratch;e.nodesOverlap||ae(e.startX)&&ae(e.startY)&&ae(e.endX)&&ae(e.endY)?e.loggedErr=!1:e.loggedErr||(e.loggedErr=!0,Re("Edge `"+t.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))};vt.findEdgeControlPoints=function(t){var e=this;if(!(!t||t.length===0)){for(var r=this,a=r.cy,n=a.hasCompoundNodes(),i={map:new zt,get:v(function(E){var D=this.map.get(E[0]);return D!=null?D.get(E[1]):null},"get"),set:v(function(E,D){var T=this.map.get(E[0]);T==null&&(T=new zt,this.map.set(E[0],T)),T.set(E[1],D)},"set")},o=[],s=[],u=0;u<t.length;u++){var l=t[u],c=l._private,f=l.pstyle("curve-style").value;if(!(l.removed()||!l.takesUpSpace())){if(f==="haystack"){s.push(l);continue}var d=f==="unbundled-bezier"||f.endsWith("segments")||f==="straight"||f==="straight-triangle"||f.endsWith("taxi"),g=f==="unbundled-bezier"||f==="bezier",h=c.source,m=c.target,y=h.poolIndex(),p=m.poolIndex(),b=[y,p].sort(),w=i.get(b);w==null&&(w={eles:[]},i.set(b,w),o.push(b)),w.eles.push(l),d&&(w.hasUnbundled=!0),g&&(w.hasBezier=!0)}}for(var x=v(function(E){var D=o[E],T=i.get(D),A=void 0;if(!T.hasUnbundled){var B=T.eles[0].parallelEdges().filter(function(P){return P.isBundledBezier()});_i(T.eles),B.forEach(function(P){return T.eles.push(P)}),T.eles.sort(function(P,z){return P.poolIndex()-z.poolIndex()})}var k=T.eles[0],L=k.source(),R=k.target();if(L.poolIndex()>R.poolIndex()){var M=L;L=R,R=M}var I=T.srcPos=L.position(),O=T.tgtPos=R.position(),F=T.srcW=L.outerWidth(),K=T.srcH=L.outerHeight(),$=T.tgtW=R.outerWidth(),q=T.tgtH=R.outerHeight(),G=T.srcShape=r.nodeShapes[e.getNodeShape(L)],X=T.tgtShape=r.nodeShapes[e.getNodeShape(R)],Z=T.srcCornerRadius=L.pstyle("corner-radius").value==="auto"?"auto":L.pstyle("corner-radius").pfValue,J=T.tgtCornerRadius=R.pstyle("corner-radius").value==="auto"?"auto":R.pstyle("corner-radius").pfValue,Q=T.tgtRs=R._private.rscratch,ee=T.srcRs=L._private.rscratch;T.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var re=0;re<T.eles.length;re++){var W=T.eles[re],N=W[0]._private.rscratch,U=W.pstyle("curve-style").value,te=U==="unbundled-bezier"||U.endsWith("segments")||U.endsWith("taxi"),oe=!L.same(W.source());if(!T.calculatedIntersection&&L!==R&&(T.hasBezier||T.hasUnbundled)){T.calculatedIntersection=!0;var ue=G.intersectLine(I.x,I.y,F,K,O.x,O.y,0,Z,ee),Se=T.srcIntn=ue,Le=X.intersectLine(O.x,O.y,$,q,I.x,I.y,0,J,Q),Ie=T.tgtIntn=Le,ve=T.intersectionPts={x1:ue[0],x2:Le[0],y1:ue[1],y2:Le[1]},le=T.posPts={x1:I.x,x2:O.x,y1:I.y,y2:O.y},ye=Le[1]-ue[1],me=Le[0]-ue[0],ge=Math.sqrt(me*me+ye*ye),be=T.vector={x:me,y:ye},Ce=T.vectorNorm={x:be.x/ge,y:be.y/ge},De={x:-Ce.y,y:Ce.x};T.nodesOverlap=!ae(ge)||X.checkPoint(ue[0],ue[1],0,$,q,O.x,O.y,J,Q)||G.checkPoint(Le[0],Le[1],0,F,K,I.x,I.y,Z,ee),T.vectorNormInverse=De,A={nodesOverlap:T.nodesOverlap,dirCounts:T.dirCounts,calculatedIntersection:!0,hasBezier:T.hasBezier,hasUnbundled:T.hasUnbundled,eles:T.eles,srcPos:O,srcRs:Q,tgtPos:I,tgtRs:ee,srcW:$,srcH:q,tgtW:F,tgtH:K,srcIntn:Ie,tgtIntn:Se,srcShape:X,tgtShape:G,posPts:{x1:le.x2,y1:le.y2,x2:le.x1,y2:le.y1},intersectionPts:{x1:ve.x2,y1:ve.y2,x2:ve.x1,y2:ve.y1},vector:{x:-be.x,y:-be.y},vectorNorm:{x:-Ce.x,y:-Ce.y},vectorNormInverse:{x:-De.x,y:-De.y}}}var j=oe?A:T;N.nodesOverlap=j.nodesOverlap,N.srcIntn=j.srcIntn,N.tgtIntn=j.tgtIntn,N.isRound=U.startsWith("round"),n&&(L.isParent()||L.isChild()||R.isParent()||R.isChild())&&(L.parents().anySame(R)||R.parents().anySame(L)||L.same(R)&&L.isParent())?e.findCompoundLoopPoints(W,j,re,te):L===R?e.findLoopPoints(W,j,re,te):U.endsWith("segments")?e.findSegmentsPoints(W,j):U.endsWith("taxi")?e.findTaxiPoints(W,j):U==="straight"||!te&&T.eles.length%2===1&&re===Math.floor(T.eles.length/2)?e.findStraightEdgePoints(W):e.findBezierPoints(W,j,re,te,oe),e.findEndpoints(W),e.tryToCorrectInvalidPoints(W,j),e.checkForInvalidEdgeWarning(W),e.storeAllpts(W),e.storeEdgeProjections(W),e.calculateArrowAngles(W),e.recalculateEdgeLabelProjections(W),e.calculateLabelAngles(W)}},"_loop"),S=0;S<o.length;S++)x(S);this.findHaystackPoints(s)}};function yo(t){var e=[];if(t!=null){for(var r=0;r<t.length;r+=2){var a=t[r],n=t[r+1];e.push({x:a,y:n})}return e}}v(yo,"getPts");vt.getSegmentPoints=function(t){var e=t[0]._private.rscratch;this.recalculateRenderedStyle(t);var r=e.edgeType;if(r==="segments")return yo(e.segpts)};vt.getControlPoints=function(t){var e=t[0]._private.rscratch;this.recalculateRenderedStyle(t);var r=e.edgeType;if(r==="bezier"||r==="multibezier"||r==="self"||r==="compound")return yo(e.ctrlpts)};vt.getEdgeMidpoint=function(t){var e=t[0]._private.rscratch;return this.recalculateRenderedStyle(t),{x:e.midX,y:e.midY}};var $a={};$a.manualEndptToPx=function(t,e){var r=this,a=t.position(),n=t.outerWidth(),i=t.outerHeight(),o=t._private.rscratch;if(e.value.length===2){var s=[e.pfValue[0],e.pfValue[1]];return e.units[0]==="%"&&(s[0]=s[0]*n),e.units[1]==="%"&&(s[1]=s[1]*i),s[0]+=a.x,s[1]+=a.y,s}else{var u=e.pfValue[0];u=-Math.PI/2+u;var l=2*Math.max(n,i),c=[a.x+Math.cos(u)*l,a.y+Math.sin(u)*l];return r.nodeShapes[this.getNodeShape(t)].intersectLine(a.x,a.y,n,i,c[0],c[1],0,t.pstyle("corner-radius").value==="auto"?"auto":t.pstyle("corner-radius").pfValue,o)}};$a.findEndpoints=function(t){var e=this,r,a=t.source()[0],n=t.target()[0],i=a.position(),o=n.position(),s=t.pstyle("target-arrow-shape").value,u=t.pstyle("source-arrow-shape").value,l=t.pstyle("target-distance-from-node").pfValue,c=t.pstyle("source-distance-from-node").pfValue,f=a._private.rscratch,d=n._private.rscratch,g=t.pstyle("curve-style").value,h=t._private.rscratch,m=h.edgeType,y=g==="taxi",p=m==="self"||m==="compound",b=m==="bezier"||m==="multibezier"||p,w=m!=="bezier",x=m==="straight"||m==="segments",S=m==="segments",C=b||w||x,E=p||y,D=t.pstyle("source-endpoint"),T=E?"outside-to-node":D.value,A=a.pstyle("corner-radius").value==="auto"?"auto":a.pstyle("corner-radius").pfValue,B=t.pstyle("target-endpoint"),k=E?"outside-to-node":B.value,L=n.pstyle("corner-radius").value==="auto"?"auto":n.pstyle("corner-radius").pfValue;h.srcManEndpt=D,h.tgtManEndpt=B;var R,M,I,O;if(b){var F=[h.ctrlpts[0],h.ctrlpts[1]],K=w?[h.ctrlpts[h.ctrlpts.length-2],h.ctrlpts[h.ctrlpts.length-1]]:F;R=K,M=F}else if(x){var $=S?h.segpts.slice(0,2):[o.x,o.y],q=S?h.segpts.slice(h.segpts.length-2):[i.x,i.y];R=q,M=$}if(k==="inside-to-node")r=[o.x,o.y];else if(B.units)r=this.manualEndptToPx(n,B);else if(k==="outside-to-line")r=h.tgtIntn;else if(k==="outside-to-node"||k==="outside-to-node-or-label"?I=R:(k==="outside-to-line"||k==="outside-to-line-or-label")&&(I=[i.x,i.y]),r=e.nodeShapes[this.getNodeShape(n)].intersectLine(o.x,o.y,n.outerWidth(),n.outerHeight(),I[0],I[1],0,L,d),k==="outside-to-node-or-label"||k==="outside-to-line-or-label"){var G=n._private.rscratch,X=G.labelWidth,Z=G.labelHeight,J=G.labelX,Q=G.labelY,ee=X/2,re=Z/2,W=n.pstyle("text-valign").value;W==="top"?Q-=re:W==="bottom"&&(Q+=re);var N=n.pstyle("text-halign").value;N==="left"?J-=ee:N==="right"&&(J+=ee);var U=Da(I[0],I[1],[J-ee,Q-re,J+ee,Q-re,J+ee,Q+re,J-ee,Q+re],o.x,o.y);if(U.length>0){var te=i,oe=hr(te,Vr(r)),ue=hr(te,Vr(U)),Se=oe;if(ue<oe&&(r=U,Se=ue),U.length>2){var Le=hr(te,{x:U[2],y:U[3]});Le<Se&&(r=[U[2],U[3]])}}}var Ie=Ja(r,R,e.arrowShapes[s].spacing(t)+l),ve=Ja(r,R,e.arrowShapes[s].gap(t)+l);if(h.endX=ve[0],h.endY=ve[1],h.arrowEndX=Ie[0],h.arrowEndY=Ie[1],T==="inside-to-node")r=[i.x,i.y];else if(D.units)r=this.manualEndptToPx(a,D);else if(T==="outside-to-line")r=h.srcIntn;else if(T==="outside-to-node"||T==="outside-to-node-or-label"?O=M:(T==="outside-to-line"||T==="outside-to-line-or-label")&&(O=[o.x,o.y]),r=e.nodeShapes[this.getNodeShape(a)].intersectLine(i.x,i.y,a.outerWidth(),a.outerHeight(),O[0],O[1],0,A,f),T==="outside-to-node-or-label"||T==="outside-to-line-or-label"){var le=a._private.rscratch,ye=le.labelWidth,me=le.labelHeight,ge=le.labelX,be=le.labelY,Ce=ye/2,De=me/2,j=a.pstyle("text-valign").value;j==="top"?be-=De:j==="bottom"&&(be+=De);var P=a.pstyle("text-halign").value;P==="left"?ge-=Ce:P==="right"&&(ge+=Ce);var z=Da(O[0],O[1],[ge-Ce,be-De,ge+Ce,be-De,ge+Ce,be+De,ge-Ce,be+De],i.x,i.y);if(z.length>0){var _=o,V=hr(_,Vr(r)),H=hr(_,Vr(z)),ne=V;if(H<V&&(r=[z[0],z[1]],ne=H),z.length>2){var Y=hr(_,{x:z[2],y:z[3]});Y<ne&&(r=[z[2],z[3]])}}}var ie=Ja(r,M,e.arrowShapes[u].spacing(t)+c),de=Ja(r,M,e.arrowShapes[u].gap(t)+c);h.startX=de[0],h.startY=de[1],h.arrowStartX=ie[0],h.arrowStartY=ie[1],C&&(!ae(h.startX)||!ae(h.startY)||!ae(h.endX)||!ae(h.endY)?h.badLine=!0:h.badLine=!1)};$a.getSourceEndpoint=function(t){var e=t[0]._private.rscratch;switch(this.recalculateRenderedStyle(t),e.edgeType){case"haystack":return{x:e.haystackPts[0],y:e.haystackPts[1]};default:return{x:e.arrowStartX,y:e.arrowStartY}}};$a.getTargetEndpoint=function(t){var e=t[0]._private.rscratch;switch(this.recalculateRenderedStyle(t),e.edgeType){case"haystack":return{x:e.haystackPts[2],y:e.haystackPts[3]};default:return{x:e.arrowEndX,y:e.arrowEndY}}};var mo={};function Ju(t,e,r){for(var a=v(function(l,c,f,d){return je(l,c,f,d)},"qbezierAt$1"),n=e._private,i=n.rstyle.bezierPts,o=0;o<t.bezierProjPcts.length;o++){var s=t.bezierProjPcts[o];i.push({x:a(r[0],r[2],r[4],s),y:a(r[1],r[3],r[5],s)})}}v(Ju,"pushBezierPts");mo.storeEdgeProjections=function(t){var e=t._private,r=e.rscratch,a=r.edgeType;if(e.rstyle.bezierPts=null,e.rstyle.linePts=null,e.rstyle.haystackPts=null,a==="multibezier"||a==="bezier"||a==="self"||a==="compound"){e.rstyle.bezierPts=[];for(var n=0;n+5<r.allpts.length;n+=4)Ju(this,t,r.allpts.slice(n,n+6))}else if(a==="segments")for(var i=e.rstyle.linePts=[],n=0;n+1<r.allpts.length;n+=2)i.push({x:r.allpts[n],y:r.allpts[n+1]});else if(a==="haystack"){var o=r.haystackPts;e.rstyle.haystackPts=[{x:o[0],y:o[1]},{x:o[2],y:o[3]}]}e.rstyle.arrowWidth=this.getArrowWidth(t.pstyle("width").pfValue,t.pstyle("arrow-scale").value)*this.arrowShapeWidth};mo.recalculateEdgeProjections=function(t){this.findEdgeControlPoints(t)};var qt={};qt.recalculateNodeLabelProjection=function(t){var e=t.pstyle("label").strValue;if(!nr(e)){var r,a,n=t._private,i=t.width(),o=t.height(),s=t.padding(),u=t.position(),l=t.pstyle("text-halign").strValue,c=t.pstyle("text-valign").strValue,f=n.rscratch,d=n.rstyle;switch(l){case"left":r=u.x-i/2-s;break;case"right":r=u.x+i/2+s;break;default:r=u.x}switch(c){case"top":a=u.y-o/2-s;break;case"bottom":a=u.y+o/2+s;break;default:a=u.y}f.labelX=r,f.labelY=a,d.labelX=r,d.labelY=a,this.calculateLabelAngles(t),this.applyLabelDimensions(t)}};var ju=v(function(e,r){var a=Math.atan(r/e);return e===0&&a<0&&(a=a*-1),a},"lineAngleFromDelta"),ev=v(function(e,r){var a=r.x-e.x,n=r.y-e.y;return ju(a,n)},"lineAngle"),Ap=v(function(e,r,a,n){var i=Sa(0,n-.001,1),o=Sa(0,n+.001,1),s=Kr(e,r,a,i),u=Kr(e,r,a,o);return ev(s,u)},"bezierAngle");qt.recalculateEdgeLabelProjections=function(t){var e,r=t._private,a=r.rscratch,n=this,i={mid:t.pstyle("label").strValue,source:t.pstyle("source-label").strValue,target:t.pstyle("target-label").strValue};if(i.mid||i.source||i.target){e={x:a.midX,y:a.midY};var o=v(function(f,d,g){er(r.rscratch,f,d,g),er(r.rstyle,f,d,g)},"setRs");o("labelX",null,e.x),o("labelY",null,e.y);var s=ju(a.midDispX,a.midDispY);o("labelAutoAngle",null,s);var u=v(function c(){if(c.cache)return c.cache;for(var f=[],d=0;d+5<a.allpts.length;d+=4){var g={x:a.allpts[d],y:a.allpts[d+1]},h={x:a.allpts[d+2],y:a.allpts[d+3]},m={x:a.allpts[d+4],y:a.allpts[d+5]};f.push({p0:g,p1:h,p2:m,startDist:0,length:0,segments:[]})}var y=r.rstyle.bezierPts,p=n.bezierProjPcts.length;function b(E,D,T,A,B){var k=Er(D,T),L=E.segments[E.segments.length-1],R={p0:D,p1:T,t0:A,t1:B,startDist:L?L.startDist+L.length:0,length:k};E.segments.push(R),E.length+=k}v(b,"addSegment");for(var w=0;w<f.length;w++){var x=f[w],S=f[w-1];S&&(x.startDist=S.startDist+S.length),b(x,x.p0,y[w*p],0,n.bezierProjPcts[0]);for(var C=0;C<p-1;C++)b(x,y[w*p+C],y[w*p+C+1],n.bezierProjPcts[C],n.bezierProjPcts[C+1]);b(x,y[w*p+p-1],x.p2,n.bezierProjPcts[p-1],1)}return c.cache=f},"createControlPointInfo"),l=v(function(f){var d,g=f==="source";if(i[f]){var h=t.pstyle(f+"-text-offset").pfValue;switch(a.edgeType){case"self":case"compound":case"bezier":case"multibezier":{for(var m=u(),y,p=0,b=0,w=0;w<m.length;w++){for(var x=m[g?w:m.length-1-w],S=0;S<x.segments.length;S++){var C=x.segments[g?S:x.segments.length-1-S],E=w===m.length-1&&S===x.segments.length-1;if(p=b,b+=C.length,b>=h||E){y={cp:x,segment:C};break}}if(y)break}var D=y.cp,T=y.segment,A=(h-p)/T.length,B=T.t1-T.t0,k=g?T.t0+B*A:T.t1-B*A;k=Sa(0,k,1),e=Kr(D.p0,D.p1,D.p2,k),d=Ap(D.p0,D.p1,D.p2,k);break}case"straight":case"segments":case"haystack":{for(var L=0,R,M,I,O,F=a.allpts.length,K=0;K+3<F&&(g?(I={x:a.allpts[K],y:a.allpts[K+1]},O={x:a.allpts[K+2],y:a.allpts[K+3]}):(I={x:a.allpts[F-2-K],y:a.allpts[F-1-K]},O={x:a.allpts[F-4-K],y:a.allpts[F-3-K]}),R=Er(I,O),M=L,L+=R,!(L>=h));K+=2);var $=h-M,q=$/R;q=Sa(0,q,1),e=kf(I,O,q),d=ev(I,O);break}}o("labelX",f,e.x),o("labelY",f,e.y),o("labelAutoAngle",f,d)}},"calculateEndProjection");l("source"),l("target"),this.applyLabelDimensions(t)}};qt.applyLabelDimensions=function(t){this.applyPrefixedLabelDimensions(t),t.isEdge()&&(this.applyPrefixedLabelDimensions(t,"source"),this.applyPrefixedLabelDimensions(t,"target"))};qt.applyPrefixedLabelDimensions=function(t,e){var r=t._private,a=this.getLabelText(t,e),n=this.calculateLabelDimensions(t,a),i=t.pstyle("line-height").pfValue,o=t.pstyle("text-wrap").strValue,s=At(r.rscratch,"labelWrapCachedLines",e)||[],u=o!=="wrap"?1:Math.max(s.length,1),l=n.height/u,c=l*i,f=n.width,d=n.height+(u-1)*(i-1)*l;er(r.rstyle,"labelWidth",e,f),er(r.rscratch,"labelWidth",e,f),er(r.rstyle,"labelHeight",e,d),er(r.rscratch,"labelHeight",e,d),er(r.rscratch,"labelLineHeight",e,c)};qt.getLabelText=function(t,e){var r=t._private,a=e?e+"-":"",n=t.pstyle(a+"label").strValue,i=t.pstyle("text-transform").value,o=v(function(X,Z){return Z?(er(r.rscratch,X,e,Z),Z):At(r.rscratch,X,e)},"rscratch");if(!n)return"";i=="none"||(i=="uppercase"?n=n.toUpperCase():i=="lowercase"&&(n=n.toLowerCase()));var s=t.pstyle("text-wrap").value;if(s==="wrap"){var u=o("labelKey");if(u!=null&&o("labelWrapKey")===u)return o("labelWrapCachedText");for(var l="​",c=n.split(`
`),f=t.pstyle("text-max-width").pfValue,d=t.pstyle("text-overflow-wrap").value,g=d==="anywhere",h=[],m=/[\s\u200b]+|$/g,y=0;y<c.length;y++){var p=c[y],b=this.calculateLabelDimensions(t,p),w=b.width;if(g){var x=p.split("").join(l);p=x}if(w>f){var S=p.matchAll(m),C="",E=0,D=mt(S),T;try{for(D.s();!(T=D.n()).done;){var A=T.value,B=A[0],k=p.substring(E,A.index);E=A.index+B.length;var L=C.length===0?k:C+k+B,R=this.calculateLabelDimensions(t,L),M=R.width;M<=f?C+=k+B:(C&&h.push(C),C=k+B)}}catch(G){D.e(G)}finally{D.f()}C.match(/^[\s\u200b]+$/)||h.push(C)}else h.push(p)}o("labelWrapCachedLines",h),n=o("labelWrapCachedText",h.join(`
`)),o("labelWrapKey",u)}else if(s==="ellipsis"){var I=t.pstyle("text-max-width").pfValue,O="",F="…",K=!1;if(this.calculateLabelDimensions(t,n).width<I)return n;for(var $=0;$<n.length;$++){var q=this.calculateLabelDimensions(t,O+n[$]+F).width;if(q>I)break;O+=n[$],$===n.length-1&&(K=!0)}return K||(O+=F),O}return n};qt.getLabelJustification=function(t){var e=t.pstyle("text-justification").strValue,r=t.pstyle("text-halign").strValue;if(e==="auto")if(t.isNode())switch(r){case"left":return"right";case"right":return"left";default:return"center"}else return"center";else return e};qt.calculateLabelDimensions=function(t,e){var r=this,a=r.cy.window(),n=a.document,i=ir(e,t._private.labelDimsKey),o=r.labelDimCache||(r.labelDimCache=[]),s=o[i];if(s!=null)return s;var u=0,l=t.pstyle("font-style").strValue,c=t.pstyle("font-size").pfValue,f=t.pstyle("font-family").strValue,d=t.pstyle("font-weight").strValue,g=this.labelCalcCanvas,h=this.labelCalcCanvasContext;if(!g){g=this.labelCalcCanvas=n.createElement("canvas"),h=this.labelCalcCanvasContext=g.getContext("2d");var m=g.style;m.position="absolute",m.left="-9999px",m.top="-9999px",m.zIndex="-1",m.visibility="hidden",m.pointerEvents="none"}h.font="".concat(l," ").concat(d," ").concat(c,"px ").concat(f);for(var y=0,p=0,b=e.split(`
`),w=0;w<b.length;w++){var x=b[w],S=h.measureText(x),C=Math.ceil(S.width),E=c;y=Math.max(C,y),p+=E}return y+=u,p+=u,o[i]={width:y,height:p}};qt.calculateLabelAngle=function(t,e){var r=t._private,a=r.rscratch,n=t.isEdge(),i=e?e+"-":"",o=t.pstyle(i+"text-rotation"),s=o.strValue;return s==="none"?0:n&&s==="autorotate"?a.labelAutoAngle:s==="autorotate"?0:o.pfValue};qt.calculateLabelAngles=function(t){var e=this,r=t.isEdge(),a=t._private,n=a.rscratch;n.labelAngle=e.calculateLabelAngle(t),r&&(n.sourceLabelAngle=e.calculateLabelAngle(t,"source"),n.targetLabelAngle=e.calculateLabelAngle(t,"target"))};var tv={},ks=28,Bs=!1;tv.getNodeShape=function(t){var e=this,r=t.pstyle("shape").value;if(r==="cutrectangle"&&(t.width()<ks||t.height()<ks))return Bs||(Re("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),Bs=!0),"rectangle";if(t.isParent())return r==="rectangle"||r==="roundrectangle"||r==="round-rectangle"||r==="cutrectangle"||r==="cut-rectangle"||r==="barrel"?r:"rectangle";if(r==="polygon"){var a=t.pstyle("shape-polygon-points").value;return e.nodeShapes.makePolygon(a).name}return r};var Yn={};Yn.registerCalculationListeners=function(){var t=this.cy,e=t.collection(),r=this,a=v(function(o){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(e.merge(o),s)for(var u=0;u<o.length;u++){var l=o[u],c=l._private,f=c.rstyle;f.clean=!1,f.cleanConnected=!1}},"enqueue");r.binder(t).on("bounds.* dirty.*",v(function(o){var s=o.target;a(s)},"onDirtyBounds")).on("style.* background.*",v(function(o){var s=o.target;a(s,!1)},"onDirtyStyle"));var n=v(function(o){if(o){var s=r.onUpdateEleCalcsFns;e.cleanStyle();for(var u=0;u<e.length;u++){var l=e[u],c=l._private.rstyle;l.isNode()&&!c.cleanConnected&&(a(l.connectedEdges()),c.cleanConnected=!0)}if(s)for(var f=0;f<s.length;f++){var d=s[f];d(o,e)}r.recalculateRenderedStyle(e),e=t.collection()}},"updateEleCalcs");r.flushRenderedStyleQueue=function(){n(!0)},r.beforeRender(n,r.beforeRenderPriorities.eleCalcs)};Yn.onUpdateEleCalcs=function(t){var e=this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[];e.push(t)};Yn.recalculateRenderedStyle=function(t,e){var r=v(function(x){return x._private.rstyle.cleanConnected},"isCleanConnected"),a=[],n=[];if(!this.destroyed){e===void 0&&(e=!0);for(var i=0;i<t.length;i++){var o=t[i],s=o._private,u=s.rstyle;o.isEdge()&&(!r(o.source())||!r(o.target()))&&(u.clean=!1),!(e&&u.clean||o.removed())&&o.pstyle("display").value!=="none"&&(s.group==="nodes"?n.push(o):a.push(o),u.clean=!0)}for(var l=0;l<n.length;l++){var c=n[l],f=c._private,d=f.rstyle,g=c.position();this.recalculateNodeLabelProjection(c),d.nodeX=g.x,d.nodeY=g.y,d.nodeW=c.pstyle("width").pfValue,d.nodeH=c.pstyle("height").pfValue}this.recalculateEdgeProjections(a);for(var h=0;h<a.length;h++){var m=a[h],y=m._private,p=y.rstyle,b=y.rscratch;p.srcX=b.arrowStartX,p.srcY=b.arrowStartY,p.tgtX=b.arrowEndX,p.tgtY=b.arrowEndY,p.midX=b.midX,p.midY=b.midY,p.labelAngle=b.labelAngle,p.sourceLabelAngle=b.sourceLabelAngle,p.targetLabelAngle=b.targetLabelAngle}}};var Zn={};Zn.updateCachedGrabbedEles=function(){var t=this.cachedZSortedEles;if(t){t.drag=[],t.nondrag=[];for(var e=[],r=0;r<t.length;r++){var a=t[r],n=a._private.rscratch;a.grabbed()&&!a.isParent()?e.push(a):n.inDragLayer?t.drag.push(a):t.nondrag.push(a)}for(var r=0;r<e.length;r++){var a=e[r];t.drag.push(a)}}};Zn.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null};Zn.getCachedZSortedEles=function(t){if(t||!this.cachedZSortedEles){var e=this.cy.mutableElements().toArray();e.sort(Wu),e.interactive=e.filter(function(r){return r.interactive()}),this.cachedZSortedEles=e,this.updateCachedGrabbedEles()}else e=this.cachedZSortedEles;return e};var rv={};[Rr,Rn,vt,$a,mo,qt,tv,Yn,Zn].forEach(function(t){he(rv,t)});var av={};av.getCachedImage=function(t,e,r){var a=this,n=a.imageCache=a.imageCache||{},i=n[t];if(i)return i.image.complete||i.image.addEventListener("load",r),i.image;i=n[t]=n[t]||{};var o=i.image=new Image;o.addEventListener("load",r),o.addEventListener("error",function(){o.error=!0});var s="data:",u=t.substring(0,s.length).toLowerCase()===s;return u||(e=e==="null"?null:e,o.crossOrigin=e),o.src=t,o};var ea={};ea.registerBinding=function(t,e,r,a){var n=Array.prototype.slice.apply(arguments,[1]);if(Array.isArray(t)){for(var i=[],o=0;o<t.length;o++){var s=t[o];if(s!==void 0){var u=this.binder(s);i.push(u.on.apply(u,n))}}return i}var u=this.binder(t);return u.on.apply(u,n)};ea.binder=function(t){var e=this,r=e.cy.window(),a=t===r||t===r.document||t===r.document.body||ic(t);if(e.supportsPassiveEvents==null){var n=!1;try{var i=Object.defineProperty({},"passive",{get:v(function(){return n=!0,!0},"get")});r.addEventListener("test",null,i)}catch{}e.supportsPassiveEvents=n}var o=v(function(u,l,c){var f=Array.prototype.slice.call(arguments);return a&&e.supportsPassiveEvents&&(f[2]={capture:c??!1,passive:!1,once:!1}),e.bindings.push({target:t,args:f}),(t.addEventListener||t.on).apply(t,f),this},"on");return{on:o,addEventListener:o,addListener:o,bind:o}};ea.nodeIsDraggable=function(t){return t&&t.isNode()&&!t.locked()&&t.grabbable()};ea.nodeIsGrabbable=function(t){return this.nodeIsDraggable(t)&&t.interactive()};ea.load=function(){var t=this,e=t.cy.window(),r=v(function(P){return P.selected()},"isSelected"),a=v(function(P){var z=P.getRootNode();if(z&&z.nodeType===11&&z.host!==void 0)return z},"getShadowRoot"),n=v(function(P,z,_,V){P==null&&(P=t.cy);for(var H=0;H<z.length;H++){var ne=z[H];P.emit({originalEvent:_,type:ne,position:V})}},"triggerEvents"),i=v(function(P){return P.shiftKey||P.metaKey||P.ctrlKey},"isMultSelKeyDown"),o=v(function(P,z){var _=!0;if(t.cy.hasCompoundNodes()&&P&&P.pannable())for(var V=0;z&&V<z.length;V++){var P=z[V];if(P.isNode()&&P.isParent()&&!P.pannable()){_=!1;break}}else _=!0;return _},"allowPanningPassthrough"),s=v(function(P){P[0]._private.grabbed=!0},"setGrabbed"),u=v(function(P){P[0]._private.grabbed=!1},"setFreed"),l=v(function(P){P[0]._private.rscratch.inDragLayer=!0},"setInDragLayer"),c=v(function(P){P[0]._private.rscratch.inDragLayer=!1},"setOutDragLayer"),f=v(function(P){P[0]._private.rscratch.isGrabTarget=!0},"setGrabTarget"),d=v(function(P){P[0]._private.rscratch.isGrabTarget=!1},"removeGrabTarget"),g=v(function(P,z){var _=z.addToList,V=_.has(P);!V&&P.grabbable()&&!P.locked()&&(_.merge(P),s(P))},"addToDragList"),h=v(function(P,z){if(P.cy().hasCompoundNodes()&&!(z.inDragLayer==null&&z.addToList==null)){var _=P.descendants();z.inDragLayer&&(_.forEach(l),_.connectedEdges().forEach(l)),z.addToList&&g(_,z)}},"addDescendantsToDrag"),m=v(function(P,z){z=z||{};var _=P.cy().hasCompoundNodes();z.inDragLayer&&(P.forEach(l),P.neighborhood().stdFilter(function(V){return!_||V.isEdge()}).forEach(l)),z.addToList&&P.forEach(function(V){g(V,z)}),h(P,z),b(P,{inDragLayer:z.inDragLayer}),t.updateCachedGrabbedEles()},"addNodesToDrag"),y=m,p=v(function(P){P&&(t.getCachedZSortedEles().forEach(function(z){u(z),c(z),d(z)}),t.updateCachedGrabbedEles())},"freeDraggedElements"),b=v(function(P,z){if(!(z.inDragLayer==null&&z.addToList==null)&&P.cy().hasCompoundNodes()){var _=P.ancestors().orphans();if(!_.same(P)){var V=_.descendants().spawnSelf().merge(_).unmerge(P).unmerge(P.descendants()),H=V.connectedEdges();z.inDragLayer&&(H.forEach(l),V.forEach(l)),z.addToList&&V.forEach(function(ne){g(ne,z)})}}},"updateAncestorsInDragLayer"),w=v(function(){document.activeElement!=null&&document.activeElement.blur!=null&&document.activeElement.blur()},"blurActiveDomElement"),x=typeof MutationObserver<"u",S=typeof ResizeObserver<"u";x?(t.removeObserver=new MutationObserver(function(j){for(var P=0;P<j.length;P++){var z=j[P],_=z.removedNodes;if(_)for(var V=0;V<_.length;V++){var H=_[V];if(H===t.container){t.destroy();break}}}}),t.container.parentNode&&t.removeObserver.observe(t.container.parentNode,{childList:!0})):t.registerBinding(t.container,"DOMNodeRemoved",function(j){t.destroy()});var C=Fa(function(){t.cy.resize()},100);x&&(t.styleObserver=new MutationObserver(C),t.styleObserver.observe(t.container,{attributes:!0})),t.registerBinding(e,"resize",C),S&&(t.resizeObserver=new ResizeObserver(C),t.resizeObserver.observe(t.container));var E=v(function(P,z){for(;P!=null;)z(P),P=P.parentNode},"forEachUp"),D=v(function(){t.invalidateContainerClientCoordsCache()},"invalidateCoords");E(t.container,function(j){t.registerBinding(j,"transitionend",D),t.registerBinding(j,"animationend",D),t.registerBinding(j,"scroll",D)}),t.registerBinding(t.container,"contextmenu",function(j){j.preventDefault()});var T=v(function(){return t.selection[4]!==0},"inBoxSelection"),A=v(function(P){for(var z=t.findContainerClientCoords(),_=z[0],V=z[1],H=z[2],ne=z[3],Y=P.touches?P.touches:[P],ie=!1,de=0;de<Y.length;de++){var Ee=Y[de];if(_<=Ee.clientX&&Ee.clientX<=_+H&&V<=Ee.clientY&&Ee.clientY<=V+ne){ie=!0;break}}if(!ie)return!1;for(var ce=t.container,we=P.target,xe=we.parentNode,pe=!1;xe;){if(xe===ce){pe=!0;break}xe=xe.parentNode}return!!pe},"eventInContainer");t.registerBinding(t.container,"mousedown",v(function(P){if(A(P)&&!(t.hoverData.which===1&&P.which!==1)){P.preventDefault(),w(),t.hoverData.capture=!0,t.hoverData.which=P.which;var z=t.cy,_=[P.clientX,P.clientY],V=t.projectIntoViewport(_[0],_[1]),H=t.selection,ne=t.findNearestElements(V[0],V[1],!0,!1),Y=ne[0],ie=t.dragData.possibleDragElements;t.hoverData.mdownPos=V,t.hoverData.mdownGPos=_;var de=v(function(){t.hoverData.tapholdCancelled=!1,clearTimeout(t.hoverData.tapholdTimeout),t.hoverData.tapholdTimeout=setTimeout(function(){if(!t.hoverData.tapholdCancelled){var Fe=t.hoverData.down;Fe?Fe.emit({originalEvent:P,type:"taphold",position:{x:V[0],y:V[1]}}):z.emit({originalEvent:P,type:"taphold",position:{x:V[0],y:V[1]}})}},t.tapholdDuration)},"checkForTaphold");if(P.which==3){t.hoverData.cxtStarted=!0;var Ee={originalEvent:P,type:"cxttapstart",position:{x:V[0],y:V[1]}};Y?(Y.activate(),Y.emit(Ee),t.hoverData.down=Y):z.emit(Ee),t.hoverData.downTime=new Date().getTime(),t.hoverData.cxtDragged=!1}else if(P.which==1){Y&&Y.activate();{if(Y!=null&&t.nodeIsGrabbable(Y)){var ce=v(function(Fe){return{originalEvent:P,type:Fe,position:{x:V[0],y:V[1]}}},"makeEvent"),we=v(function(Fe){Fe.emit(ce("grab"))},"triggerGrab");if(f(Y),!Y.selected())ie=t.dragData.possibleDragElements=z.collection(),y(Y,{addToList:ie}),Y.emit(ce("grabon")).emit(ce("grab"));else{ie=t.dragData.possibleDragElements=z.collection();var xe=z.$(function(pe){return pe.isNode()&&pe.selected()&&t.nodeIsGrabbable(pe)});m(xe,{addToList:ie}),Y.emit(ce("grabon")),xe.forEach(we)}t.redrawHint("eles",!0),t.redrawHint("drag",!0)}t.hoverData.down=Y,t.hoverData.downs=ne,t.hoverData.downTime=new Date().getTime()}n(Y,["mousedown","tapstart","vmousedown"],P,{x:V[0],y:V[1]}),Y==null?(H[4]=1,t.data.bgActivePosistion={x:V[0],y:V[1]},t.redrawHint("select",!0),t.redraw()):Y.pannable()&&(H[4]=1),de()}H[0]=H[2]=V[0],H[1]=H[3]=V[1]}},"mousedownHandler"),!1);var B=a(t.container);t.registerBinding([e,B],"mousemove",v(function(P){var z=t.hoverData.capture;if(!(!z&&!A(P))){var _=!1,V=t.cy,H=V.zoom(),ne=[P.clientX,P.clientY],Y=t.projectIntoViewport(ne[0],ne[1]),ie=t.hoverData.mdownPos,de=t.hoverData.mdownGPos,Ee=t.selection,ce=null;!t.hoverData.draggingEles&&!t.hoverData.dragging&&!t.hoverData.selecting&&(ce=t.findNearestElement(Y[0],Y[1],!0,!1));var we=t.hoverData.last,xe=t.hoverData.down,pe=[Y[0]-Ee[2],Y[1]-Ee[3]],Fe=t.dragData.possibleDragElements,qe;if(de){var wt=ne[0]-de[0],xt=wt*wt,Ue=ne[1]-de[1],Ze=Ue*Ue,Qe=xt+Ze;t.hoverData.isOverThresholdDrag=qe=Qe>=t.desktopTapThreshold2}var ct=i(P);qe&&(t.hoverData.tapholdCancelled=!0);var ft=v(function(){var Mt=t.hoverData.dragDelta=t.hoverData.dragDelta||[];Mt.length===0?(Mt.push(pe[0]),Mt.push(pe[1])):(Mt[0]+=pe[0],Mt[1]+=pe[1])},"updateDragDelta");_=!0,n(ce,["mousemove","vmousemove","tapdrag"],P,{x:Y[0],y:Y[1]});var Lt=v(function(){t.data.bgActivePosistion=void 0,t.hoverData.selecting||V.emit({originalEvent:P,type:"boxstart",position:{x:Y[0],y:Y[1]}}),Ee[4]=1,t.hoverData.selecting=!0,t.redrawHint("select",!0),t.redraw()},"goIntoBoxMode");if(t.hoverData.which===3){if(qe){var Ct={originalEvent:P,type:"cxtdrag",position:{x:Y[0],y:Y[1]}};xe?xe.emit(Ct):V.emit(Ct),t.hoverData.cxtDragged=!0,(!t.hoverData.cxtOver||ce!==t.hoverData.cxtOver)&&(t.hoverData.cxtOver&&t.hoverData.cxtOver.emit({originalEvent:P,type:"cxtdragout",position:{x:Y[0],y:Y[1]}}),t.hoverData.cxtOver=ce,ce&&ce.emit({originalEvent:P,type:"cxtdragover",position:{x:Y[0],y:Y[1]}}))}}else if(t.hoverData.dragging){if(_=!0,V.panningEnabled()&&V.userPanningEnabled()){var It;if(t.hoverData.justStartedPan){var Gt=t.hoverData.mdownPos;It={x:(Y[0]-Gt[0])*H,y:(Y[1]-Gt[1])*H},t.hoverData.justStartedPan=!1}else It={x:pe[0]*H,y:pe[1]*H};V.panBy(It),V.emit("dragpan"),t.hoverData.dragged=!0}Y=t.projectIntoViewport(P.clientX,P.clientY)}else if(Ee[4]==1&&(xe==null||xe.pannable())){if(qe){if(!t.hoverData.dragging&&V.boxSelectionEnabled()&&(ct||!V.panningEnabled()||!V.userPanningEnabled()))Lt();else if(!t.hoverData.selecting&&V.panningEnabled()&&V.userPanningEnabled()){var Ht=o(xe,t.hoverData.downs);Ht&&(t.hoverData.dragging=!0,t.hoverData.justStartedPan=!0,Ee[4]=0,t.data.bgActivePosistion=Vr(ie),t.redrawHint("select",!0),t.redraw())}xe&&xe.pannable()&&xe.active()&&xe.unactivate()}}else{if(xe&&xe.pannable()&&xe.active()&&xe.unactivate(),(!xe||!xe.grabbed())&&ce!=we&&(we&&n(we,["mouseout","tapdragout"],P,{x:Y[0],y:Y[1]}),ce&&n(ce,["mouseover","tapdragover"],P,{x:Y[0],y:Y[1]}),t.hoverData.last=ce),xe)if(qe){if(V.boxSelectionEnabled()&&ct)xe&&xe.grabbed()&&(p(Fe),xe.emit("freeon"),Fe.emit("free"),t.dragData.didDrag&&(xe.emit("dragfreeon"),Fe.emit("dragfree"))),Lt();else if(xe&&xe.grabbed()&&t.nodeIsDraggable(xe)){var it=!t.dragData.didDrag;it&&t.redrawHint("eles",!0),t.dragData.didDrag=!0,t.hoverData.draggingEles||m(Fe,{inDragLayer:!0});var Je={x:0,y:0};if(ae(pe[0])&&ae(pe[1])&&(Je.x+=pe[0],Je.y+=pe[1],it)){var gt=t.hoverData.dragDelta;gt&&ae(gt[0])&&ae(gt[1])&&(Je.x+=gt[0],Je.y+=gt[1])}t.hoverData.draggingEles=!0,Fe.silentShift(Je).emit("position drag"),t.redrawHint("drag",!0),t.redraw()}}else ft();_=!0}if(Ee[2]=Y[0],Ee[3]=Y[1],_)return P.stopPropagation&&P.stopPropagation(),P.preventDefault&&P.preventDefault(),!1}},"mousemoveHandler"),!1);var k,L,R;t.registerBinding(e,"mouseup",v(function(P){if(!(t.hoverData.which===1&&P.which!==1&&t.hoverData.capture)){var z=t.hoverData.capture;if(z){t.hoverData.capture=!1;var _=t.cy,V=t.projectIntoViewport(P.clientX,P.clientY),H=t.selection,ne=t.findNearestElement(V[0],V[1],!0,!1),Y=t.dragData.possibleDragElements,ie=t.hoverData.down,de=i(P);if(t.data.bgActivePosistion&&(t.redrawHint("select",!0),t.redraw()),t.hoverData.tapholdCancelled=!0,t.data.bgActivePosistion=void 0,ie&&ie.unactivate(),t.hoverData.which===3){var Ee={originalEvent:P,type:"cxttapend",position:{x:V[0],y:V[1]}};if(ie?ie.emit(Ee):_.emit(Ee),!t.hoverData.cxtDragged){var ce={originalEvent:P,type:"cxttap",position:{x:V[0],y:V[1]}};ie?ie.emit(ce):_.emit(ce)}t.hoverData.cxtDragged=!1,t.hoverData.which=null}else if(t.hoverData.which===1){if(n(ne,["mouseup","tapend","vmouseup"],P,{x:V[0],y:V[1]}),!t.dragData.didDrag&&!t.hoverData.dragged&&!t.hoverData.selecting&&!t.hoverData.isOverThresholdDrag&&(n(ie,["click","tap","vclick"],P,{x:V[0],y:V[1]}),L=!1,P.timeStamp-R<=_.multiClickDebounceTime()?(k&&clearTimeout(k),L=!0,R=null,n(ie,["dblclick","dbltap","vdblclick"],P,{x:V[0],y:V[1]})):(k=setTimeout(function(){L||n(ie,["oneclick","onetap","voneclick"],P,{x:V[0],y:V[1]})},_.multiClickDebounceTime()),R=P.timeStamp)),ie==null&&!t.dragData.didDrag&&!t.hoverData.selecting&&!t.hoverData.dragged&&!i(P)&&(_.$(r).unselect(["tapunselect"]),Y.length>0&&t.redrawHint("eles",!0),t.dragData.possibleDragElements=Y=_.collection()),ne==ie&&!t.dragData.didDrag&&!t.hoverData.selecting&&ne!=null&&ne._private.selectable&&(t.hoverData.dragging||(_.selectionType()==="additive"||de?ne.selected()?ne.unselect(["tapunselect"]):ne.select(["tapselect"]):de||(_.$(r).unmerge(ne).unselect(["tapunselect"]),ne.select(["tapselect"]))),t.redrawHint("eles",!0)),t.hoverData.selecting){var we=_.collection(t.getAllInBox(H[0],H[1],H[2],H[3]));t.redrawHint("select",!0),we.length>0&&t.redrawHint("eles",!0),_.emit({type:"boxend",originalEvent:P,position:{x:V[0],y:V[1]}});var xe=v(function(qe){return qe.selectable()&&!qe.selected()},"eleWouldBeSelected");_.selectionType()==="additive"||de||_.$(r).unmerge(we).unselect(),we.emit("box").stdFilter(xe).select().emit("boxselect"),t.redraw()}if(t.hoverData.dragging&&(t.hoverData.dragging=!1,t.redrawHint("select",!0),t.redrawHint("eles",!0),t.redraw()),!H[4]){t.redrawHint("drag",!0),t.redrawHint("eles",!0);var pe=ie&&ie.grabbed();p(Y),pe&&(ie.emit("freeon"),Y.emit("free"),t.dragData.didDrag&&(ie.emit("dragfreeon"),Y.emit("dragfree")))}}H[4]=0,t.hoverData.down=null,t.hoverData.cxtStarted=!1,t.hoverData.draggingEles=!1,t.hoverData.selecting=!1,t.hoverData.isOverThresholdDrag=!1,t.dragData.didDrag=!1,t.hoverData.dragged=!1,t.hoverData.dragDelta=[],t.hoverData.mdownPos=null,t.hoverData.mdownGPos=null,t.hoverData.which=null}}},"mouseupHandler"),!1);var M=v(function(P){if(!t.scrollingPage){var z=t.cy,_=z.zoom(),V=z.pan(),H=t.projectIntoViewport(P.clientX,P.clientY),ne=[H[0]*_+V.x,H[1]*_+V.y];if(t.hoverData.draggingEles||t.hoverData.dragging||t.hoverData.cxtStarted||T()){P.preventDefault();return}if(z.panningEnabled()&&z.userPanningEnabled()&&z.zoomingEnabled()&&z.userZoomingEnabled()){P.preventDefault(),t.data.wheelZooming=!0,clearTimeout(t.data.wheelTimeout),t.data.wheelTimeout=setTimeout(function(){t.data.wheelZooming=!1,t.redrawHint("eles",!0),t.redraw()},150);var Y;P.deltaY!=null?Y=P.deltaY/-250:P.wheelDeltaY!=null?Y=P.wheelDeltaY/1e3:Y=P.wheelDelta/1e3,Y=Y*t.wheelSensitivity;var ie=P.deltaMode===1;ie&&(Y*=33);var de=z.zoom()*Math.pow(10,Y);P.type==="gesturechange"&&(de=t.gestureStartZoom*P.scale),z.zoom({level:de,renderedPosition:{x:ne[0],y:ne[1]}}),z.emit(P.type==="gesturechange"?"pinchzoom":"scrollzoom")}}},"wheelHandler");t.registerBinding(t.container,"wheel",M,!0),t.registerBinding(e,"scroll",v(function(P){t.scrollingPage=!0,clearTimeout(t.scrollingPageTimeout),t.scrollingPageTimeout=setTimeout(function(){t.scrollingPage=!1},250)},"scrollHandler"),!0),t.registerBinding(t.container,"gesturestart",v(function(P){t.gestureStartZoom=t.cy.zoom(),t.hasTouchStarted||P.preventDefault()},"gestureStartHandler"),!0),t.registerBinding(t.container,"gesturechange",function(j){t.hasTouchStarted||M(j)},!0),t.registerBinding(t.container,"mouseout",v(function(P){var z=t.projectIntoViewport(P.clientX,P.clientY);t.cy.emit({originalEvent:P,type:"mouseout",position:{x:z[0],y:z[1]}})},"mouseOutHandler"),!1),t.registerBinding(t.container,"mouseover",v(function(P){var z=t.projectIntoViewport(P.clientX,P.clientY);t.cy.emit({originalEvent:P,type:"mouseover",position:{x:z[0],y:z[1]}})},"mouseOverHandler"),!1);var I,O,F,K,$,q,G,X,Z,J,Q,ee,re,W=v(function(P,z,_,V){return Math.sqrt((_-P)*(_-P)+(V-z)*(V-z))},"distance"),N=v(function(P,z,_,V){return(_-P)*(_-P)+(V-z)*(V-z)},"distanceSq"),U;t.registerBinding(t.container,"touchstart",U=v(function(P){if(t.hasTouchStarted=!0,!!A(P)){w(),t.touchData.capture=!0,t.data.bgActivePosistion=void 0;var z=t.cy,_=t.touchData.now,V=t.touchData.earlier;if(P.touches[0]){var H=t.projectIntoViewport(P.touches[0].clientX,P.touches[0].clientY);_[0]=H[0],_[1]=H[1]}if(P.touches[1]){var H=t.projectIntoViewport(P.touches[1].clientX,P.touches[1].clientY);_[2]=H[0],_[3]=H[1]}if(P.touches[2]){var H=t.projectIntoViewport(P.touches[2].clientX,P.touches[2].clientY);_[4]=H[0],_[5]=H[1]}if(P.touches[1]){t.touchData.singleTouchMoved=!0,p(t.dragData.touchDragEles);var ne=t.findContainerClientCoords();Z=ne[0],J=ne[1],Q=ne[2],ee=ne[3],I=P.touches[0].clientX-Z,O=P.touches[0].clientY-J,F=P.touches[1].clientX-Z,K=P.touches[1].clientY-J,re=0<=I&&I<=Q&&0<=F&&F<=Q&&0<=O&&O<=ee&&0<=K&&K<=ee;var Y=z.pan(),ie=z.zoom();$=W(I,O,F,K),q=N(I,O,F,K),G=[(I+F)/2,(O+K)/2],X=[(G[0]-Y.x)/ie,(G[1]-Y.y)/ie];var de=200,Ee=de*de;if(q<Ee&&!P.touches[2]){var ce=t.findNearestElement(_[0],_[1],!0,!0),we=t.findNearestElement(_[2],_[3],!0,!0);ce&&ce.isNode()?(ce.activate().emit({originalEvent:P,type:"cxttapstart",position:{x:_[0],y:_[1]}}),t.touchData.start=ce):we&&we.isNode()?(we.activate().emit({originalEvent:P,type:"cxttapstart",position:{x:_[0],y:_[1]}}),t.touchData.start=we):z.emit({originalEvent:P,type:"cxttapstart",position:{x:_[0],y:_[1]}}),t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxt=!0,t.touchData.cxtDragged=!1,t.data.bgActivePosistion=void 0,t.redraw();return}}if(P.touches[2])z.boxSelectionEnabled()&&P.preventDefault();else if(!P.touches[1]){if(P.touches[0]){var xe=t.findNearestElements(_[0],_[1],!0,!0),pe=xe[0];if(pe!=null&&(pe.activate(),t.touchData.start=pe,t.touchData.starts=xe,t.nodeIsGrabbable(pe))){var Fe=t.dragData.touchDragEles=z.collection(),qe=null;t.redrawHint("eles",!0),t.redrawHint("drag",!0),pe.selected()?(qe=z.$(function(Qe){return Qe.selected()&&t.nodeIsGrabbable(Qe)}),m(qe,{addToList:Fe})):y(pe,{addToList:Fe}),f(pe);var wt=v(function(ct){return{originalEvent:P,type:ct,position:{x:_[0],y:_[1]}}},"makeEvent");pe.emit(wt("grabon")),qe?qe.forEach(function(Qe){Qe.emit(wt("grab"))}):pe.emit(wt("grab"))}n(pe,["touchstart","tapstart","vmousedown"],P,{x:_[0],y:_[1]}),pe==null&&(t.data.bgActivePosistion={x:H[0],y:H[1]},t.redrawHint("select",!0),t.redraw()),t.touchData.singleTouchMoved=!1,t.touchData.singleTouchStartTime=+new Date,clearTimeout(t.touchData.tapholdTimeout),t.touchData.tapholdTimeout=setTimeout(function(){t.touchData.singleTouchMoved===!1&&!t.pinching&&!t.touchData.selecting&&n(t.touchData.start,["taphold"],P,{x:_[0],y:_[1]})},t.tapholdDuration)}}if(P.touches.length>=1){for(var xt=t.touchData.startPosition=[null,null,null,null,null,null],Ue=0;Ue<_.length;Ue++)xt[Ue]=V[Ue]=_[Ue];var Ze=P.touches[0];t.touchData.startGPosition=[Ze.clientX,Ze.clientY]}}},"touchstartHandler"),!1);var te;t.registerBinding(e,"touchmove",te=v(function(P){var z=t.touchData.capture;if(!(!z&&!A(P))){var _=t.selection,V=t.cy,H=t.touchData.now,ne=t.touchData.earlier,Y=V.zoom();if(P.touches[0]){var ie=t.projectIntoViewport(P.touches[0].clientX,P.touches[0].clientY);H[0]=ie[0],H[1]=ie[1]}if(P.touches[1]){var ie=t.projectIntoViewport(P.touches[1].clientX,P.touches[1].clientY);H[2]=ie[0],H[3]=ie[1]}if(P.touches[2]){var ie=t.projectIntoViewport(P.touches[2].clientX,P.touches[2].clientY);H[4]=ie[0],H[5]=ie[1]}var de=t.touchData.startGPosition,Ee;if(z&&P.touches[0]&&de){for(var ce=[],we=0;we<H.length;we++)ce[we]=H[we]-ne[we];var xe=P.touches[0].clientX-de[0],pe=xe*xe,Fe=P.touches[0].clientY-de[1],qe=Fe*Fe,wt=pe+qe;Ee=wt>=t.touchTapThreshold2}if(z&&t.touchData.cxt){P.preventDefault();var xt=P.touches[0].clientX-Z,Ue=P.touches[0].clientY-J,Ze=P.touches[1].clientX-Z,Qe=P.touches[1].clientY-J,ct=N(xt,Ue,Ze,Qe),ft=ct/q,Lt=150,Ct=Lt*Lt,It=1.5,Gt=It*It;if(ft>=Gt||ct>=Ct){t.touchData.cxt=!1,t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var Ht={originalEvent:P,type:"cxttapend",position:{x:H[0],y:H[1]}};t.touchData.start?(t.touchData.start.unactivate().emit(Ht),t.touchData.start=null):V.emit(Ht)}}if(z&&t.touchData.cxt){var Ht={originalEvent:P,type:"cxtdrag",position:{x:H[0],y:H[1]}};t.data.bgActivePosistion=void 0,t.redrawHint("select",!0),t.touchData.start?t.touchData.start.emit(Ht):V.emit(Ht),t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxtDragged=!0;var it=t.findNearestElement(H[0],H[1],!0,!0);(!t.touchData.cxtOver||it!==t.touchData.cxtOver)&&(t.touchData.cxtOver&&t.touchData.cxtOver.emit({originalEvent:P,type:"cxtdragout",position:{x:H[0],y:H[1]}}),t.touchData.cxtOver=it,it&&it.emit({originalEvent:P,type:"cxtdragover",position:{x:H[0],y:H[1]}}))}else if(z&&P.touches[2]&&V.boxSelectionEnabled())P.preventDefault(),t.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,t.touchData.selecting||V.emit({originalEvent:P,type:"boxstart",position:{x:H[0],y:H[1]}}),t.touchData.selecting=!0,t.touchData.didSelect=!0,_[4]=1,!_||_.length===0||_[0]===void 0?(_[0]=(H[0]+H[2]+H[4])/3,_[1]=(H[1]+H[3]+H[5])/3,_[2]=(H[0]+H[2]+H[4])/3+1,_[3]=(H[1]+H[3]+H[5])/3+1):(_[2]=(H[0]+H[2]+H[4])/3,_[3]=(H[1]+H[3]+H[5])/3),t.redrawHint("select",!0),t.redraw();else if(z&&P.touches[1]&&!t.touchData.didSelect&&V.zoomingEnabled()&&V.panningEnabled()&&V.userZoomingEnabled()&&V.userPanningEnabled()){P.preventDefault(),t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var Je=t.dragData.touchDragEles;if(Je){t.redrawHint("drag",!0);for(var gt=0;gt<Je.length;gt++){var Xa=Je[gt]._private;Xa.grabbed=!1,Xa.rscratch.inDragLayer=!1}}var Mt=t.touchData.start,xt=P.touches[0].clientX-Z,Ue=P.touches[0].clientY-J,Ze=P.touches[1].clientX-Z,Qe=P.touches[1].clientY-J,Ao=W(xt,Ue,Ze,Qe),Wv=Ao/$;if(re){var $v=xt-I,Uv=Ue-O,_v=Ze-F,Xv=Qe-K,Yv=($v+_v)/2,Zv=(Uv+Xv)/2,aa=V.zoom(),jn=aa*Wv,Ya=V.pan(),Ro=X[0]*aa+Ya.x,Lo=X[1]*aa+Ya.y,Qv={x:-jn/aa*(Ro-Ya.x-Yv)+Ro,y:-jn/aa*(Lo-Ya.y-Zv)+Lo};if(Mt&&Mt.active()){var Je=t.dragData.touchDragEles;p(Je),t.redrawHint("drag",!0),t.redrawHint("eles",!0),Mt.unactivate().emit("freeon"),Je.emit("free"),t.dragData.didDrag&&(Mt.emit("dragfreeon"),Je.emit("dragfree"))}V.viewport({zoom:jn,pan:Qv,cancelOnFailedZoom:!0}),V.emit("pinchzoom"),$=Ao,I=xt,O=Ue,F=Ze,K=Qe,t.pinching=!0}if(P.touches[0]){var ie=t.projectIntoViewport(P.touches[0].clientX,P.touches[0].clientY);H[0]=ie[0],H[1]=ie[1]}if(P.touches[1]){var ie=t.projectIntoViewport(P.touches[1].clientX,P.touches[1].clientY);H[2]=ie[0],H[3]=ie[1]}if(P.touches[2]){var ie=t.projectIntoViewport(P.touches[2].clientX,P.touches[2].clientY);H[4]=ie[0],H[5]=ie[1]}}else if(P.touches[0]&&!t.touchData.didSelect){var Pt=t.touchData.start,ei=t.touchData.last,it;if(!t.hoverData.draggingEles&&!t.swipePanning&&(it=t.findNearestElement(H[0],H[1],!0,!0)),z&&Pt!=null&&P.preventDefault(),z&&Pt!=null&&t.nodeIsDraggable(Pt))if(Ee){var Je=t.dragData.touchDragEles,Io=!t.dragData.didDrag;Io&&m(Je,{inDragLayer:!0}),t.dragData.didDrag=!0;var na={x:0,y:0};if(ae(ce[0])&&ae(ce[1])&&(na.x+=ce[0],na.y+=ce[1],Io)){t.redrawHint("eles",!0);var kt=t.touchData.dragDelta;kt&&ae(kt[0])&&ae(kt[1])&&(na.x+=kt[0],na.y+=kt[1])}t.hoverData.draggingEles=!0,Je.silentShift(na).emit("position drag"),t.redrawHint("drag",!0),t.touchData.startPosition[0]==ne[0]&&t.touchData.startPosition[1]==ne[1]&&t.redrawHint("eles",!0),t.redraw()}else{var kt=t.touchData.dragDelta=t.touchData.dragDelta||[];kt.length===0?(kt.push(ce[0]),kt.push(ce[1])):(kt[0]+=ce[0],kt[1]+=ce[1])}if(n(Pt||it,["touchmove","tapdrag","vmousemove"],P,{x:H[0],y:H[1]}),(!Pt||!Pt.grabbed())&&it!=ei&&(ei&&ei.emit({originalEvent:P,type:"tapdragout",position:{x:H[0],y:H[1]}}),it&&it.emit({originalEvent:P,type:"tapdragover",position:{x:H[0],y:H[1]}})),t.touchData.last=it,z)for(var gt=0;gt<H.length;gt++)H[gt]&&t.touchData.startPosition[gt]&&Ee&&(t.touchData.singleTouchMoved=!0);if(z&&(Pt==null||Pt.pannable())&&V.panningEnabled()&&V.userPanningEnabled()){var Jv=o(Pt,t.touchData.starts);Jv&&(P.preventDefault(),t.data.bgActivePosistion||(t.data.bgActivePosistion=Vr(t.touchData.startPosition)),t.swipePanning?(V.panBy({x:ce[0]*Y,y:ce[1]*Y}),V.emit("dragpan")):Ee&&(t.swipePanning=!0,V.panBy({x:xe*Y,y:Fe*Y}),V.emit("dragpan"),Pt&&(Pt.unactivate(),t.redrawHint("select",!0),t.touchData.start=null)));var ie=t.projectIntoViewport(P.touches[0].clientX,P.touches[0].clientY);H[0]=ie[0],H[1]=ie[1]}}for(var we=0;we<H.length;we++)ne[we]=H[we];z&&P.touches.length>0&&!t.hoverData.draggingEles&&!t.swipePanning&&t.data.bgActivePosistion!=null&&(t.data.bgActivePosistion=void 0,t.redrawHint("select",!0),t.redraw())}},"touchmoveHandler"),!1);var oe;t.registerBinding(e,"touchcancel",oe=v(function(P){var z=t.touchData.start;t.touchData.capture=!1,z&&z.unactivate()},"touchcancelHandler"));var ue,Se,Le,Ie;if(t.registerBinding(e,"touchend",ue=v(function(P){var z=t.touchData.start,_=t.touchData.capture;if(_)P.touches.length===0&&(t.touchData.capture=!1),P.preventDefault();else return;var V=t.selection;t.swipePanning=!1,t.hoverData.draggingEles=!1;var H=t.cy,ne=H.zoom(),Y=t.touchData.now,ie=t.touchData.earlier;if(P.touches[0]){var de=t.projectIntoViewport(P.touches[0].clientX,P.touches[0].clientY);Y[0]=de[0],Y[1]=de[1]}if(P.touches[1]){var de=t.projectIntoViewport(P.touches[1].clientX,P.touches[1].clientY);Y[2]=de[0],Y[3]=de[1]}if(P.touches[2]){var de=t.projectIntoViewport(P.touches[2].clientX,P.touches[2].clientY);Y[4]=de[0],Y[5]=de[1]}z&&z.unactivate();var Ee;if(t.touchData.cxt){if(Ee={originalEvent:P,type:"cxttapend",position:{x:Y[0],y:Y[1]}},z?z.emit(Ee):H.emit(Ee),!t.touchData.cxtDragged){var ce={originalEvent:P,type:"cxttap",position:{x:Y[0],y:Y[1]}};z?z.emit(ce):H.emit(ce)}t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxt=!1,t.touchData.start=null,t.redraw();return}if(!P.touches[2]&&H.boxSelectionEnabled()&&t.touchData.selecting){t.touchData.selecting=!1;var we=H.collection(t.getAllInBox(V[0],V[1],V[2],V[3]));V[0]=void 0,V[1]=void 0,V[2]=void 0,V[3]=void 0,V[4]=0,t.redrawHint("select",!0),H.emit({type:"boxend",originalEvent:P,position:{x:Y[0],y:Y[1]}});var xe=v(function(Ct){return Ct.selectable()&&!Ct.selected()},"eleWouldBeSelected");we.emit("box").stdFilter(xe).select().emit("boxselect"),we.nonempty()&&t.redrawHint("eles",!0),t.redraw()}if(z?.unactivate(),P.touches[2])t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);else if(!P.touches[1]){if(!P.touches[0]){if(!P.touches[0]){t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var pe=t.dragData.touchDragEles;if(z!=null){var Fe=z._private.grabbed;p(pe),t.redrawHint("drag",!0),t.redrawHint("eles",!0),Fe&&(z.emit("freeon"),pe.emit("free"),t.dragData.didDrag&&(z.emit("dragfreeon"),pe.emit("dragfree"))),n(z,["touchend","tapend","vmouseup","tapdragout"],P,{x:Y[0],y:Y[1]}),z.unactivate(),t.touchData.start=null}else{var qe=t.findNearestElement(Y[0],Y[1],!0,!0);n(qe,["touchend","tapend","vmouseup","tapdragout"],P,{x:Y[0],y:Y[1]})}var wt=t.touchData.startPosition[0]-Y[0],xt=wt*wt,Ue=t.touchData.startPosition[1]-Y[1],Ze=Ue*Ue,Qe=xt+Ze,ct=Qe*ne*ne;t.touchData.singleTouchMoved||(z||H.$(":selected").unselect(["tapunselect"]),n(z,["tap","vclick"],P,{x:Y[0],y:Y[1]}),Se=!1,P.timeStamp-Ie<=H.multiClickDebounceTime()?(Le&&clearTimeout(Le),Se=!0,Ie=null,n(z,["dbltap","vdblclick"],P,{x:Y[0],y:Y[1]})):(Le=setTimeout(function(){Se||n(z,["onetap","voneclick"],P,{x:Y[0],y:Y[1]})},H.multiClickDebounceTime()),Ie=P.timeStamp)),z!=null&&!t.dragData.didDrag&&z._private.selectable&&ct<t.touchTapThreshold2&&!t.pinching&&(H.selectionType()==="single"?(H.$(r).unmerge(z).unselect(["tapunselect"]),z.select(["tapselect"])):z.selected()?z.unselect(["tapunselect"]):z.select(["tapselect"]),t.redrawHint("eles",!0)),t.touchData.singleTouchMoved=!0}}}for(var ft=0;ft<Y.length;ft++)ie[ft]=Y[ft];t.dragData.didDrag=!1,P.touches.length===0&&(t.touchData.dragDelta=[],t.touchData.startPosition=[null,null,null,null,null,null],t.touchData.startGPosition=null,t.touchData.didSelect=!1),P.touches.length<2&&(P.touches.length===1&&(t.touchData.startGPosition=[P.touches[0].clientX,P.touches[0].clientY]),t.pinching=!1,t.redrawHint("eles",!0),t.redraw())},"touchendHandler"),!1),typeof TouchEvent>"u"){var ve=[],le=v(function(P){return{clientX:P.clientX,clientY:P.clientY,force:1,identifier:P.pointerId,pageX:P.pageX,pageY:P.pageY,radiusX:P.width/2,radiusY:P.height/2,screenX:P.screenX,screenY:P.screenY,target:P.target}},"makeTouch"),ye=v(function(P){return{event:P,touch:le(P)}},"makePointer"),me=v(function(P){ve.push(ye(P))},"addPointer"),ge=v(function(P){for(var z=0;z<ve.length;z++){var _=ve[z];if(_.event.pointerId===P.pointerId){ve.splice(z,1);return}}},"removePointer"),be=v(function(P){var z=ve.filter(function(_){return _.event.pointerId===P.pointerId})[0];z.event=P,z.touch=le(P)},"updatePointer"),Ce=v(function(P){P.touches=ve.map(function(z){return z.touch})},"addTouchesToEvent"),De=v(function(P){return P.pointerType==="mouse"||P.pointerType===4},"pointerIsMouse");t.registerBinding(t.container,"pointerdown",function(j){De(j)||(j.preventDefault(),me(j),Ce(j),U(j))}),t.registerBinding(t.container,"pointerup",function(j){De(j)||(ge(j),Ce(j),ue(j))}),t.registerBinding(t.container,"pointercancel",function(j){De(j)||(ge(j),Ce(j),oe(j))}),t.registerBinding(t.container,"pointermove",function(j){De(j)||(j.preventDefault(),be(j),Ce(j),te(j))})}};var Zt={};Zt.generatePolygon=function(t,e){return this.nodeShapes[t]={renderer:this,name:t,points:e,draw:v(function(a,n,i,o,s,u){this.renderer.nodeShapeImpl("polygon",a,n,i,o,s,this.points)},"draw"),intersectLine:v(function(a,n,i,o,s,u,l,c){return Da(s,u,this.points,a,n,i/2,o/2,l)},"intersectLine"),checkPoint:v(function(a,n,i,o,s,u,l,c){return Ut(a,n,this.points,u,l,o,s,[0,-1],i)},"checkPoint")}};Zt.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:v(function(e,r,a,n,i,o){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i)},"draw"),intersectLine:v(function(e,r,a,n,i,o,s,u){return Kf(i,o,e,r,a/2+s,n/2+s)},"intersectLine"),checkPoint:v(function(e,r,a,n,i,o,s,u){return wr(e,r,n,i,o,s,a)},"checkPoint")}};Zt.generateRoundPolygon=function(t,e){return this.nodeShapes[t]={renderer:this,name:t,points:e,getOrCreateCorners:v(function(a,n,i,o,s,u,l){if(u[l]!==void 0&&u[l+"-cx"]===a&&u[l+"-cy"]===n)return u[l];u[l]=new Array(e.length/2),u[l+"-cx"]=a,u[l+"-cy"]=n;var c=i/2,f=o/2;s=s==="auto"?kl(i,o):s;for(var d=new Array(e.length/2),g=0;g<e.length/2;g++)d[g]={x:a+c*e[g*2],y:n+f*e[g*2+1]};var h,m,y,p,b=d.length;for(m=d[b-1],h=0;h<b;h++)y=d[h%b],p=d[(h+1)%b],u[l][h]=Xn(m,y,p,s),m=y,y=p;return u[l]},"getOrCreateCorners"),draw:v(function(a,n,i,o,s,u,l){this.renderer.nodeShapeImpl("round-polygon",a,n,i,o,s,this.points,this.getOrCreateCorners(n,i,o,s,u,l,"drawCorners"))},"draw"),intersectLine:v(function(a,n,i,o,s,u,l,c,f){return Gf(s,u,this.points,a,n,i,o,l,this.getOrCreateCorners(a,n,i,o,c,f,"corners"))},"intersectLine"),checkPoint:v(function(a,n,i,o,s,u,l,c,f){return qf(a,n,this.points,u,l,o,s,this.getOrCreateCorners(u,l,o,s,c,f,"corners"))},"checkPoint")}};Zt.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:dt(4,0),draw:v(function(e,r,a,n,i,o){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i,this.points,o)},"draw"),intersectLine:v(function(e,r,a,n,i,o,s,u){return Dl(i,o,e,r,a,n,s,u)},"intersectLine"),checkPoint:v(function(e,r,a,n,i,o,s,u){var l=n/2,c=i/2;u=u==="auto"?Cr(n,i):u,u=Math.min(l,c,u);var f=u*2;return!!(Ut(e,r,this.points,o,s,n,i-f,[0,-1],a)||Ut(e,r,this.points,o,s,n-f,i,[0,-1],a)||wr(e,r,f,f,o-l+u,s-c+u,a)||wr(e,r,f,f,o+l-u,s-c+u,a)||wr(e,r,f,f,o+l-u,s+c-u,a)||wr(e,r,f,f,o-l+u,s+c-u,a))},"checkPoint")}};Zt.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:Zi(),points:dt(4,0),draw:v(function(e,r,a,n,i,o){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i,null,o)},"draw"),generateCutTrianglePts:v(function(e,r,a,n,i){var o=i==="auto"?this.cornerLength:i,s=r/2,u=e/2,l=a-u,c=a+u,f=n-s,d=n+s;return{topLeft:[l,f+o,l+o,f,l+o,f+o],topRight:[c-o,f,c,f+o,c-o,f+o],bottomRight:[c,d-o,c-o,d,c-o,d-o],bottomLeft:[l+o,d,l,d-o,l+o,d-o]}},"generateCutTrianglePts"),intersectLine:v(function(e,r,a,n,i,o,s,u){var l=this.generateCutTrianglePts(a+2*s,n+2*s,e,r,u),c=[].concat.apply([],[l.topLeft.splice(0,4),l.topRight.splice(0,4),l.bottomRight.splice(0,4),l.bottomLeft.splice(0,4)]);return Da(i,o,c,e,r)},"intersectLine"),checkPoint:v(function(e,r,a,n,i,o,s,u){var l=u==="auto"?this.cornerLength:u;if(Ut(e,r,this.points,o,s,n,i-2*l,[0,-1],a)||Ut(e,r,this.points,o,s,n-2*l,i,[0,-1],a))return!0;var c=this.generateCutTrianglePts(n,i,o,s);return yt(e,r,c.topLeft)||yt(e,r,c.topRight)||yt(e,r,c.bottomRight)||yt(e,r,c.bottomLeft)},"checkPoint")}};Zt.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:dt(4,0),draw:v(function(e,r,a,n,i,o){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i)},"draw"),intersectLine:v(function(e,r,a,n,i,o,s,u){var l=.15,c=.5,f=.85,d=this.generateBarrelBezierPts(a+2*s,n+2*s,e,r),g=v(function(y){var p=Kr({x:y[0],y:y[1]},{x:y[2],y:y[3]},{x:y[4],y:y[5]},l),b=Kr({x:y[0],y:y[1]},{x:y[2],y:y[3]},{x:y[4],y:y[5]},c),w=Kr({x:y[0],y:y[1]},{x:y[2],y:y[3]},{x:y[4],y:y[5]},f);return[y[0],y[1],p.x,p.y,b.x,b.y,w.x,w.y,y[4],y[5]]},"approximateBarrelCurvePts"),h=[].concat(g(d.topLeft),g(d.topRight),g(d.bottomRight),g(d.bottomLeft));return Da(i,o,h,e,r)},"intersectLine"),generateBarrelBezierPts:v(function(e,r,a,n){var i=r/2,o=e/2,s=a-o,u=a+o,l=n-i,c=n+i,f=gi(e,r),d=f.heightOffset,g=f.widthOffset,h=f.ctrlPtOffsetPct*e,m={topLeft:[s,l+d,s+h,l,s+g,l],topRight:[u-g,l,u-h,l,u,l+d],bottomRight:[u,c-d,u-h,c,u-g,c],bottomLeft:[s+g,c,s+h,c,s,c-d]};return m.topLeft.isTop=!0,m.topRight.isTop=!0,m.bottomLeft.isBottom=!0,m.bottomRight.isBottom=!0,m},"generateBarrelBezierPts"),checkPoint:v(function(e,r,a,n,i,o,s,u){var l=gi(n,i),c=l.heightOffset,f=l.widthOffset;if(Ut(e,r,this.points,o,s,n,i-2*c,[0,-1],a)||Ut(e,r,this.points,o,s,n-2*f,i,[0,-1],a))return!0;for(var d=this.generateBarrelBezierPts(n,i,o,s),g=v(function(D,T,A){var B=A[4],k=A[2],L=A[0],R=A[5],M=A[1],I=Math.min(B,L),O=Math.max(B,L),F=Math.min(R,M),K=Math.max(R,M);if(I<=D&&D<=O&&F<=T&&T<=K){var $=Hf(B,k,L),q=Ff($[0],$[1],$[2],D),G=q.filter(function(X){return 0<=X&&X<=1});if(G.length>0)return G[0]}return null},"getCurveT"),h=Object.keys(d),m=0;m<h.length;m++){var y=h[m],p=d[y],b=g(e,r,p);if(b!=null){var w=p[5],x=p[3],S=p[1],C=je(w,x,S,b);if(p.isTop&&C<=r||p.isBottom&&r<=C)return!0}}return!1},"checkPoint")}};Zt.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:dt(4,0),draw:v(function(e,r,a,n,i,o){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i,this.points,o)},"draw"),intersectLine:v(function(e,r,a,n,i,o,s,u){var l=e-(a/2+s),c=r-(n/2+s),f=c,d=e+(a/2+s),g=tr(i,o,e,r,l,c,d,f,!1);return g.length>0?g:Dl(i,o,e,r,a,n,s,u)},"intersectLine"),checkPoint:v(function(e,r,a,n,i,o,s,u){u=u==="auto"?Cr(n,i):u;var l=2*u;if(Ut(e,r,this.points,o,s,n,i-l,[0,-1],a)||Ut(e,r,this.points,o,s,n-l,i,[0,-1],a))return!0;var c=n/2+2*a,f=i/2+2*a,d=[o-c,s-f,o-c,s,o+c,s,o+c,s-f];return!!(yt(e,r,d)||wr(e,r,l,l,o+n/2-u,s+i/2-u,a)||wr(e,r,l,l,o-n/2+u,s+i/2-u,a))},"checkPoint")}};Zt.registerNodeShapes=function(){var t=this.nodeShapes={},e=this;this.generateEllipse(),this.generatePolygon("triangle",dt(3,0)),this.generateRoundPolygon("round-triangle",dt(3,0)),this.generatePolygon("rectangle",dt(4,0)),t.square=t.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();{var r=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",r),this.generateRoundPolygon("round-diamond",r)}this.generatePolygon("pentagon",dt(5,0)),this.generateRoundPolygon("round-pentagon",dt(5,0)),this.generatePolygon("hexagon",dt(6,0)),this.generateRoundPolygon("round-hexagon",dt(6,0)),this.generatePolygon("heptagon",dt(7,0)),this.generateRoundPolygon("round-heptagon",dt(7,0)),this.generatePolygon("octagon",dt(8,0)),this.generateRoundPolygon("round-octagon",dt(8,0));var a=new Array(20);{var n=hi(5,0),i=hi(5,Math.PI/5),o=.5*(3-Math.sqrt(5));o*=1.57;for(var s=0;s<i.length/2;s++)i[s*2]*=o,i[s*2+1]*=o;for(var s=0;s<20/4;s++)a[s*4]=n[s*2],a[s*4+1]=n[s*2+1],a[s*4+2]=i[s*2],a[s*4+3]=i[s*2+1]}a=Pl(a),this.generatePolygon("star",a),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);{var u=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",u),this.generateRoundPolygon("round-tag",u)}t.makePolygon=function(l){var c=l.join("$"),f="polygon-"+c,d;return(d=this[f])?d:e.generatePolygon(f,l)}};var Ua={};Ua.timeToRender=function(){return this.redrawTotalTime/this.redrawCount};Ua.redraw=function(t){t=t||xl();var e=this;e.averageRedrawTime===void 0&&(e.averageRedrawTime=0),e.lastRedrawTime===void 0&&(e.lastRedrawTime=0),e.lastDrawTime===void 0&&(e.lastDrawTime=0),e.requestedFrame=!0,e.renderOptions=t};Ua.beforeRender=function(t,e){if(!this.destroyed){e==null&&Ke("Priority is not optional for beforeRender");var r=this.beforeRenderCallbacks;r.push({fn:t,priority:e}),r.sort(function(a,n){return n.priority-a.priority})}};var As=v(function(e,r,a){for(var n=e.beforeRenderCallbacks,i=0;i<n.length;i++)n[i].fn(r,a)},"beforeRenderCallbacks");Ua.startRenderLoop=function(){var t=this,e=t.cy;if(!t.renderLoopStarted){t.renderLoopStarted=!0;var r=v(function a(n){if(!t.destroyed){if(!e.batching())if(t.requestedFrame&&!t.skipFrame){As(t,!0,n);var i=$t();t.render(t.renderOptions);var o=t.lastDrawTime=$t();t.averageRedrawTime===void 0&&(t.averageRedrawTime=o-i),t.redrawCount===void 0&&(t.redrawCount=0),t.redrawCount++,t.redrawTotalTime===void 0&&(t.redrawTotalTime=0);var s=o-i;t.redrawTotalTime+=s,t.lastRedrawTime=s,t.averageRedrawTime=t.averageRedrawTime/2+s/2,t.requestedFrame=!1}else As(t,!1,n);t.skipFrame=!1,Cn(a)}},"renderFn");Cn(r)}};var Rp=v(function(e){this.init(e)},"BaseRenderer"),nv=Rp,ta=nv.prototype;ta.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"];ta.init=function(t){var e=this;e.options=t,e.cy=t.cy;var r=e.container=t.cy.container(),a=e.cy.window();if(a){var n=a.document,i=n.head,o="__________cytoscape_stylesheet",s="__________cytoscape_container",u=n.getElementById(o)!=null;if(r.className.indexOf(s)<0&&(r.className=(r.className||"")+" "+s),!u){var l=n.createElement("style");l.id=o,l.textContent="."+s+" { position: relative; }",i.insertBefore(l,i.children[0])}var c=a.getComputedStyle(r),f=c.getPropertyValue("position");f==="static"&&Re("A Cytoscape container has style position:static and so can not use UI extensions properly")}e.selection=[void 0,void 0,void 0,void 0,0],e.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],e.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},e.dragData={possibleDragElements:[]},e.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},e.redraws=0,e.showFps=t.showFps,e.debug=t.debug,e.webgl=t.webgl,e.hideEdgesOnViewport=t.hideEdgesOnViewport,e.textureOnViewport=t.textureOnViewport,e.wheelSensitivity=t.wheelSensitivity,e.motionBlurEnabled=t.motionBlur,e.forcedPixelRatio=ae(t.pixelRatio)?t.pixelRatio:null,e.motionBlur=t.motionBlur,e.motionBlurOpacity=t.motionBlurOpacity,e.motionBlurTransparency=1-e.motionBlurOpacity,e.motionBlurPxRatio=1,e.mbPxRBlurry=1,e.minMbLowQualFrames=4,e.fullQualityMb=!1,e.clearedForMotionBlur=[],e.desktopTapThreshold=t.desktopTapThreshold,e.desktopTapThreshold2=t.desktopTapThreshold*t.desktopTapThreshold,e.touchTapThreshold=t.touchTapThreshold,e.touchTapThreshold2=t.touchTapThreshold*t.touchTapThreshold,e.tapholdDuration=500,e.bindings=[],e.beforeRenderCallbacks=[],e.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},e.registerNodeShapes(),e.registerArrowShapes(),e.registerCalculationListeners()};ta.notify=function(t,e){var r=this,a=r.cy;if(!this.destroyed){if(t==="init"){r.load();return}if(t==="destroy"){r.destroy();return}(t==="add"||t==="remove"||t==="move"&&a.hasCompoundNodes()||t==="load"||t==="zorder"||t==="mount")&&r.invalidateCachedZSortedEles(),t==="viewport"&&r.redrawHint("select",!0),t==="gc"&&r.redrawHint("gc",!0),(t==="load"||t==="resize"||t==="mount")&&(r.invalidateContainerClientCoordsCache(),r.matchCanvasSize(r.container)),r.redrawHint("eles",!0),r.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()}};ta.destroy=function(){var t=this;t.destroyed=!0,t.cy.stopAnimationLoop();for(var e=0;e<t.bindings.length;e++){var r=t.bindings[e],a=r,n=a.target;(n.off||n.removeEventListener).apply(n,a.args)}if(t.bindings=[],t.beforeRenderCallbacks=[],t.onUpdateEleCalcsFns=[],t.removeObserver&&t.removeObserver.disconnect(),t.styleObserver&&t.styleObserver.disconnect(),t.resizeObserver&&t.resizeObserver.disconnect(),t.labelCalcDiv)try{document.body.removeChild(t.labelCalcDiv)}catch{}};ta.isHeadless=function(){return!1};[go,rv,av,ea,Zt,Ua].forEach(function(t){he(ta,t)});var ui=1e3/60,iv={setupDequeueing:v(function(e){return v(function(){var a=this,n=this.renderer;if(!a.dequeueingSetup){a.dequeueingSetup=!0;var i=Fa(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),o=v(function(l,c){var f=$t(),d=n.averageRedrawTime,g=n.lastRedrawTime,h=[],m=n.cy.extent(),y=n.getPixelRatio();for(l||n.flushRenderedStyleQueue();;){var p=$t(),b=p-f,w=p-c;if(g<ui){var x=ui-(l?d:0);if(w>=e.deqFastCost*x)break}else if(l){if(b>=e.deqCost*g||b>=e.deqAvgCost*d)break}else if(w>=e.deqNoDrawCost*ui)break;var S=e.deq(a,y,m);if(S.length>0)for(var C=0;C<S.length;C++)h.push(S[C]);else break}h.length>0&&(e.onDeqd(a,h),!l&&e.shouldRedraw(a,h,y,m)&&i())},"dequeue"),s=e.priority||Ui;n.beforeRender(o,s(a))}},"setupDequeueingImpl")},"setupDequeueing")},Lp=(function(){function t(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Tn;Xt(this,t),this.idsByKey=new zt,this.keyForId=new zt,this.cachesByLvl=new zt,this.lvls=[],this.getKey=e,this.doesEleInvalidateKey=r}return v(t,"ElementTextureCacheLookup"),Yt(t,[{key:"getIdsFor",value:v(function(r){r==null&&Ke("Can not get id list for null key");var a=this.idsByKey,n=this.idsByKey.get(r);return n||(n=new Qr,a.set(r,n)),n},"getIdsFor")},{key:"addIdForKey",value:v(function(r,a){r!=null&&this.getIdsFor(r).add(a)},"addIdForKey")},{key:"deleteIdForKey",value:v(function(r,a){r!=null&&this.getIdsFor(r).delete(a)},"deleteIdForKey")},{key:"getNumberOfIdsForKey",value:v(function(r){return r==null?0:this.getIdsFor(r).size},"getNumberOfIdsForKey")},{key:"updateKeyMappingFor",value:v(function(r){var a=r.id(),n=this.keyForId.get(a),i=this.getKey(r);this.deleteIdForKey(n,a),this.addIdForKey(i,a),this.keyForId.set(a,i)},"updateKeyMappingFor")},{key:"deleteKeyMappingFor",value:v(function(r){var a=r.id(),n=this.keyForId.get(a);this.deleteIdForKey(n,a),this.keyForId.delete(a)},"deleteKeyMappingFor")},{key:"keyHasChangedFor",value:v(function(r){var a=r.id(),n=this.keyForId.get(a),i=this.getKey(r);return n!==i},"keyHasChangedFor")},{key:"isInvalid",value:v(function(r){return this.keyHasChangedFor(r)||this.doesEleInvalidateKey(r)},"isInvalid")},{key:"getCachesAt",value:v(function(r){var a=this.cachesByLvl,n=this.lvls,i=a.get(r);return i||(i=new zt,a.set(r,i),n.push(r)),i},"getCachesAt")},{key:"getCache",value:v(function(r,a){return this.getCachesAt(a).get(r)},"getCache")},{key:"get",value:v(function(r,a){var n=this.getKey(r),i=this.getCache(n,a);return i!=null&&this.updateKeyMappingFor(r),i},"get")},{key:"getForCachedKey",value:v(function(r,a){var n=this.keyForId.get(r.id()),i=this.getCache(n,a);return i},"getForCachedKey")},{key:"hasCache",value:v(function(r,a){return this.getCachesAt(a).has(r)},"hasCache")},{key:"has",value:v(function(r,a){var n=this.getKey(r);return this.hasCache(n,a)},"has")},{key:"setCache",value:v(function(r,a,n){n.key=r,this.getCachesAt(a).set(r,n)},"setCache")},{key:"set",value:v(function(r,a,n){var i=this.getKey(r);this.setCache(i,a,n),this.updateKeyMappingFor(r)},"set")},{key:"deleteCache",value:v(function(r,a){this.getCachesAt(a).delete(r)},"deleteCache")},{key:"delete",value:v(function(r,a){var n=this.getKey(r);this.deleteCache(n,a)},"_delete")},{key:"invalidateKey",value:v(function(r){var a=this;this.lvls.forEach(function(n){return a.deleteCache(r,n)})},"invalidateKey")},{key:"invalidate",value:v(function(r){var a=r.id(),n=this.keyForId.get(a);this.deleteKeyMappingFor(r);var i=this.doesEleInvalidateKey(r);return i&&this.invalidateKey(n),i||this.getNumberOfIdsForKey(n)===0},"invalidate")}]),t})(),Rs=25,sn=50,mn=-4,Ii=3,ov=7.99,Ip=8,Mp=1024,Op=1024,Fp=1024,Np=.2,zp=.8,Vp=10,qp=.15,Kp=.1,Gp=.9,Hp=.9,Wp=100,$p=1,qr={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},Up=et({getKey:null,doesEleInvalidateKey:Tn,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:ml,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),ga=v(function(e,r){var a=this;a.renderer=e,a.onDequeues=[];var n=Up(r);he(a,n),a.lookup=new Lp(n.getKey,n.doesEleInvalidateKey),a.setupDequeueing()},"ElementTextureCache"),Ye=ga.prototype;Ye.reasons=qr;Ye.getTextureQueue=function(t){var e=this;return e.eleImgCaches=e.eleImgCaches||{},e.eleImgCaches[t]=e.eleImgCaches[t]||[]};Ye.getRetiredTextureQueue=function(t){var e=this,r=e.eleImgCaches.retired=e.eleImgCaches.retired||{},a=r[t]=r[t]||[];return a};Ye.getElementQueue=function(){var t=this,e=t.eleCacheQueue=t.eleCacheQueue||new Na(function(r,a){return a.reqs-r.reqs});return e};Ye.getElementKeyToQueue=function(){var t=this,e=t.eleKeyToCacheQueue=t.eleKeyToCacheQueue||{};return e};Ye.getElement=function(t,e,r,a,n){var i=this,o=this.renderer,s=o.cy.zoom(),u=this.lookup;if(!e||e.w===0||e.h===0||isNaN(e.w)||isNaN(e.h)||!t.visible()||t.removed()||!i.allowEdgeTxrCaching&&t.isEdge()||!i.allowParentTxrCaching&&t.isParent())return null;if(a==null&&(a=Math.ceil(Xi(s*r))),a<mn)a=mn;else if(s>=ov||a>Ii)return null;var l=Math.pow(2,a),c=e.h*l,f=e.w*l,d=o.eleTextBiggerThanMin(t,l);if(!this.isVisible(t,d))return null;var g=u.get(t,a);if(g&&g.invalidated&&(g.invalidated=!1,g.texture.invalidatedWidth-=g.width),g)return g;var h;if(c<=Rs?h=Rs:c<=sn?h=sn:h=Math.ceil(c/sn)*sn,c>Fp||f>Op)return null;var m=i.getTextureQueue(h),y=m[m.length-2],p=v(function(){return i.recycleTexture(h,f)||i.addTexture(h,f)},"addNewTxr");y||(y=m[m.length-1]),y||(y=p()),y.width-y.usedWidth<f&&(y=p());for(var b=v(function(I){return I&&I.scaledLabelShown===d},"scalableFrom"),w=n&&n===qr.dequeue,x=n&&n===qr.highQuality,S=n&&n===qr.downscale,C,E=a+1;E<=Ii;E++){var D=u.get(t,E);if(D){C=D;break}}var T=C&&C.level===a+1?C:null,A=v(function(){y.context.drawImage(T.texture.canvas,T.x,0,T.width,T.height,y.usedWidth,0,f,c)},"downscale");if(y.context.setTransform(1,0,0,1,0,0),y.context.clearRect(y.usedWidth,0,f,h),b(T))A();else if(b(C))if(x){for(var B=C.level;B>a;B--)T=i.getElement(t,e,r,B,qr.downscale);A()}else return i.queueElement(t,C.level-1),C;else{var k;if(!w&&!x&&!S)for(var L=a-1;L>=mn;L--){var R=u.get(t,L);if(R){k=R;break}}if(b(k))return i.queueElement(t,a),k;y.context.translate(y.usedWidth,0),y.context.scale(l,l),this.drawElement(y.context,t,e,d,!1),y.context.scale(1/l,1/l),y.context.translate(-y.usedWidth,0)}return g={x:y.usedWidth,texture:y,level:a,scale:l,width:f,height:c,scaledLabelShown:d},y.usedWidth+=Math.ceil(f+Ip),y.eleCaches.push(g),u.set(t,a,g),i.checkTextureFullness(y),g};Ye.invalidateElements=function(t){for(var e=0;e<t.length;e++)this.invalidateElement(t[e])};Ye.invalidateElement=function(t){var e=this,r=e.lookup,a=[],n=r.isInvalid(t);if(n){for(var i=mn;i<=Ii;i++){var o=r.getForCachedKey(t,i);o&&a.push(o)}var s=r.invalidate(t);if(s)for(var u=0;u<a.length;u++){var l=a[u],c=l.texture;c.invalidatedWidth+=l.width,l.invalidated=!0,e.checkTextureUtility(c)}e.removeFromQueue(t)}};Ye.checkTextureUtility=function(t){t.invalidatedWidth>=Np*t.width&&this.retireTexture(t)};Ye.checkTextureFullness=function(t){var e=this,r=e.getTextureQueue(t.height);t.usedWidth/t.width>zp&&t.fullnessChecks>=Vp?or(r,t):t.fullnessChecks++};Ye.retireTexture=function(t){var e=this,r=t.height,a=e.getTextureQueue(r),n=this.lookup;or(a,t),t.retired=!0;for(var i=t.eleCaches,o=0;o<i.length;o++){var s=i[o];n.deleteCache(s.key,s.level)}_i(i);var u=e.getRetiredTextureQueue(r);u.push(t)};Ye.addTexture=function(t,e){var r=this,a=r.getTextureQueue(t),n={};return a.push(n),n.eleCaches=[],n.height=t,n.width=Math.max(Mp,e),n.usedWidth=0,n.invalidatedWidth=0,n.fullnessChecks=0,n.canvas=r.renderer.makeOffscreenCanvas(n.width,n.height),n.context=n.canvas.getContext("2d"),n};Ye.recycleTexture=function(t,e){for(var r=this,a=r.getTextureQueue(t),n=r.getRetiredTextureQueue(t),i=0;i<n.length;i++){var o=n[i];if(o.width>=e)return o.retired=!1,o.usedWidth=0,o.invalidatedWidth=0,o.fullnessChecks=0,_i(o.eleCaches),o.context.setTransform(1,0,0,1,0,0),o.context.clearRect(0,0,o.width,o.height),or(n,o),a.push(o),o}};Ye.queueElement=function(t,e){var r=this,a=r.getElementQueue(),n=r.getElementKeyToQueue(),i=this.getKey(t),o=n[i];if(o)o.level=Math.max(o.level,e),o.eles.merge(t),o.reqs++,a.updateItem(o);else{var s={eles:t.spawn().merge(t),level:e,reqs:1,key:i};a.push(s),n[i]=s}};Ye.dequeue=function(t){for(var e=this,r=e.getElementQueue(),a=e.getElementKeyToQueue(),n=[],i=e.lookup,o=0;o<$p&&r.size()>0;o++){var s=r.pop(),u=s.key,l=s.eles[0],c=i.hasCache(l,s.level);if(a[u]=null,c)continue;n.push(s);var f=e.getBoundingBox(l);e.getElement(l,f,t,s.level,qr.dequeue)}return n};Ye.removeFromQueue=function(t){var e=this,r=e.getElementQueue(),a=e.getElementKeyToQueue(),n=this.getKey(t),i=a[n];i!=null&&(i.eles.length===1?(i.reqs=$i,r.updateItem(i),r.pop(),a[n]=null):i.eles.unmerge(t))};Ye.onDequeue=function(t){this.onDequeues.push(t)};Ye.offDequeue=function(t){or(this.onDequeues,t)};Ye.setupDequeueing=iv.setupDequeueing({deqRedrawThreshold:Wp,deqCost:qp,deqAvgCost:Kp,deqNoDrawCost:Gp,deqFastCost:Hp,deq:v(function(e,r,a){return e.dequeue(r,a)},"deq"),onDeqd:v(function(e,r){for(var a=0;a<e.onDequeues.length;a++){var n=e.onDequeues[a];n(r)}},"onDeqd"),shouldRedraw:v(function(e,r,a,n){for(var i=0;i<r.length;i++)for(var o=r[i].eles,s=0;s<o.length;s++){var u=o[s].boundingBox();if(Yi(u,n))return!0}return!1},"shouldRedraw"),priority:v(function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq},"priority")});var _p=1,ba=-4,Ln=2,Xp=3.99,Yp=50,Zp=50,Qp=.15,Jp=.1,jp=.9,ey=.9,ty=1,Ls=250,ry=4e3*4e3,Is=32767,ay=!0,sv=v(function(e){var r=this,a=r.renderer=e,n=a.cy;r.layersByLevel={},r.firstGet=!0,r.lastInvalidationTime=$t()-2*Ls,r.skipping=!1,r.eleTxrDeqs=n.collection(),r.scheduleElementRefinement=Fa(function(){r.refineElementTextures(r.eleTxrDeqs),r.eleTxrDeqs.unmerge(r.eleTxrDeqs)},Zp),a.beforeRender(function(o,s){s-r.lastInvalidationTime<=Ls?r.skipping=!0:r.skipping=!1},a.beforeRenderPriorities.lyrTxrSkip);var i=v(function(s,u){return u.reqs-s.reqs},"qSort");r.layersQueue=new Na(i),r.setupDequeueing()},"LayeredTextureCache"),nt=sv.prototype,Ms=0,ny=Math.pow(2,53)-1;nt.makeLayer=function(t,e){var r=Math.pow(2,e),a=Math.ceil(t.w*r),n=Math.ceil(t.h*r),i=this.renderer.makeOffscreenCanvas(a,n),o={id:Ms=++Ms%ny,bb:t,level:e,width:a,height:n,canvas:i,context:i.getContext("2d"),eles:[],elesQueue:[],reqs:0},s=o.context,u=-o.bb.x1,l=-o.bb.y1;return s.scale(r,r),s.translate(u,l),o};nt.getLayers=function(t,e,r){var a=this,n=a.renderer,i=n.cy,o=i.zoom(),s=a.firstGet;if(a.firstGet=!1,r==null){if(r=Math.ceil(Xi(o*e)),r<ba)r=ba;else if(o>=Xp||r>Ln)return null}a.validateLayersElesOrdering(r,t);var u=a.layersByLevel,l=Math.pow(2,r),c=u[r]=u[r]||[],f,d=a.levelIsComplete(r,t),g,h=v(function(){var A=v(function(M){if(a.validateLayersElesOrdering(M,t),a.levelIsComplete(M,t))return g=u[M],!0},"canUseAsTmpLvl"),B=v(function(M){if(!g)for(var I=r+M;ba<=I&&I<=Ln&&!A(I);I+=M);},"checkLvls");B(1),B(-1);for(var k=c.length-1;k>=0;k--){var L=c[k];L.invalid&&or(c,L)}},"checkTempLevels");if(!d)h();else return c;var m=v(function(){if(!f){f=bt();for(var A=0;A<t.length;A++)Tl(f,t[A].boundingBox())}return f},"getBb"),y=v(function(A){A=A||{};var B=A.after;m();var k=Math.ceil(f.w*l),L=Math.ceil(f.h*l);if(k>Is||L>Is)return null;var R=k*L;if(R>ry)return null;var M=a.makeLayer(f,r);if(B!=null){var I=c.indexOf(B)+1;c.splice(I,0,M)}else(A.insert===void 0||A.insert)&&c.unshift(M);return M},"makeLayer");if(a.skipping&&!s)return null;for(var p=null,b=t.length/_p,w=!s,x=0;x<t.length;x++){var S=t[x],C=S._private.rscratch,E=C.imgLayerCaches=C.imgLayerCaches||{},D=E[r];if(D){p=D;continue}if((!p||p.eles.length>=b||!Sl(p.bb,S.boundingBox()))&&(p=y({insert:!0,after:p}),!p))return null;g||w?a.queueLayer(p,S):a.drawEleInLayer(p,S,r,e),p.eles.push(S),E[r]=p}return g||(w?null:c)};nt.getEleLevelForLayerLevel=function(t,e){return t};nt.drawEleInLayer=function(t,e,r,a){var n=this,i=this.renderer,o=t.context,s=e.boundingBox();s.w===0||s.h===0||!e.visible()||(r=n.getEleLevelForLayerLevel(r,a),i.setImgSmoothing(o,!1),i.drawCachedElement(o,e,null,null,r,ay),i.setImgSmoothing(o,!0))};nt.levelIsComplete=function(t,e){var r=this,a=r.layersByLevel[t];if(!a||a.length===0)return!1;for(var n=0,i=0;i<a.length;i++){var o=a[i];if(o.reqs>0||o.invalid)return!1;n+=o.eles.length}return n===e.length};nt.validateLayersElesOrdering=function(t,e){var r=this.layersByLevel[t];if(r)for(var a=0;a<r.length;a++){for(var n=r[a],i=-1,o=0;o<e.length;o++)if(n.eles[0]===e[o]){i=o;break}if(i<0){this.invalidateLayer(n);continue}for(var s=i,o=0;o<n.eles.length;o++)if(n.eles[o]!==e[s+o]){this.invalidateLayer(n);break}}};nt.updateElementsInLayers=function(t,e){for(var r=this,a=Ma(t[0]),n=0;n<t.length;n++)for(var i=a?null:t[n],o=a?t[n]:t[n].ele,s=o._private.rscratch,u=s.imgLayerCaches=s.imgLayerCaches||{},l=ba;l<=Ln;l++){var c=u[l];c&&(i&&r.getEleLevelForLayerLevel(c.level)!==i.level||e(c,o,i))}};nt.haveLayers=function(){for(var t=this,e=!1,r=ba;r<=Ln;r++){var a=t.layersByLevel[r];if(a&&a.length>0){e=!0;break}}return e};nt.invalidateElements=function(t){var e=this;t.length!==0&&(e.lastInvalidationTime=$t(),!(t.length===0||!e.haveLayers())&&e.updateElementsInLayers(t,v(function(a,n,i){e.invalidateLayer(a)},"invalAssocLayers")))};nt.invalidateLayer=function(t){if(this.lastInvalidationTime=$t(),!t.invalid){var e=t.level,r=t.eles,a=this.layersByLevel[e];or(a,t),t.elesQueue=[],t.invalid=!0,t.replacement&&(t.replacement.invalid=!0);for(var n=0;n<r.length;n++){var i=r[n]._private.rscratch.imgLayerCaches;i&&(i[e]=null)}}};nt.refineElementTextures=function(t){var e=this;e.updateElementsInLayers(t,v(function(a,n,i){var o=a.replacement;if(o||(o=a.replacement=e.makeLayer(a.bb,a.level),o.replaces=a,o.eles=a.eles),!o.reqs)for(var s=0;s<o.eles.length;s++)e.queueLayer(o,o.eles[s])},"refineEachEle"))};nt.enqueueElementRefinement=function(t){this.eleTxrDeqs.merge(t),this.scheduleElementRefinement()};nt.queueLayer=function(t,e){var r=this,a=r.layersQueue,n=t.elesQueue,i=n.hasId=n.hasId||{};if(!t.replacement){if(e){if(i[e.id()])return;n.push(e),i[e.id()]=!0}t.reqs?(t.reqs++,a.updateItem(t)):(t.reqs=1,a.push(t))}};nt.dequeue=function(t){for(var e=this,r=e.layersQueue,a=[],n=0;n<ty&&r.size()!==0;){var i=r.peek();if(i.replacement){r.pop();continue}if(i.replaces&&i!==i.replaces.replacement){r.pop();continue}if(i.invalid){r.pop();continue}var o=i.elesQueue.shift();o&&(e.drawEleInLayer(i,o,i.level,t),n++),a.length===0&&a.push(!0),i.elesQueue.length===0&&(r.pop(),i.reqs=0,i.replaces&&e.applyLayerReplacement(i),e.requestRedraw())}return a};nt.applyLayerReplacement=function(t){var e=this,r=e.layersByLevel[t.level],a=t.replaces,n=r.indexOf(a);if(!(n<0||a.invalid)){r[n]=t;for(var i=0;i<t.eles.length;i++){var o=t.eles[i]._private,s=o.imgLayerCaches=o.imgLayerCaches||{};s&&(s[t.level]=t)}e.requestRedraw()}};nt.requestRedraw=Fa(function(){var t=this.renderer;t.redrawHint("eles",!0),t.redrawHint("drag",!0),t.redraw()},100);nt.setupDequeueing=iv.setupDequeueing({deqRedrawThreshold:Yp,deqCost:Qp,deqAvgCost:Jp,deqNoDrawCost:jp,deqFastCost:ey,deq:v(function(e,r){return e.dequeue(r)},"deq"),onDeqd:Ui,shouldRedraw:ml,priority:v(function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq},"priority")});var lv={},Os;function uv(t,e){for(var r=0;r<e.length;r++){var a=e[r];t.lineTo(a.x,a.y)}}v(uv,"polygon");function vv(t,e,r){for(var a,n=0;n<e.length;n++){var i=e[n];n===0&&(a=i),t.lineTo(i.x,i.y)}t.quadraticCurveTo(r.x,r.y,a.x,a.y)}v(vv,"triangleBackcurve");function Mi(t,e,r){t.beginPath&&t.beginPath();for(var a=e,n=0;n<a.length;n++){var i=a[n];t.lineTo(i.x,i.y)}var o=r,s=r[0];t.moveTo(s.x,s.y);for(var n=1;n<o.length;n++){var i=o[n];t.lineTo(i.x,i.y)}t.closePath&&t.closePath()}v(Mi,"triangleTee");function cv(t,e,r,a,n){t.beginPath&&t.beginPath(),t.arc(r,a,n,0,Math.PI*2,!1);var i=e,o=i[0];t.moveTo(o.x,o.y);for(var s=0;s<i.length;s++){var u=i[s];t.lineTo(u.x,u.y)}t.closePath&&t.closePath()}v(cv,"circleTriangle");function fv(t,e,r,a){t.arc(e,r,a,0,Math.PI*2,!1)}v(fv,"circle");lv.arrowShapeImpl=function(t){return(Os||(Os={polygon:uv,"triangle-backcurve":vv,"triangle-tee":Mi,"circle-triangle":cv,"triangle-cross":Mi,circle:fv}))[t]};var Kt={};Kt.drawElement=function(t,e,r,a,n,i){var o=this;e.isNode()?o.drawNode(t,e,r,a,n,i):o.drawEdge(t,e,r,a,n,i)};Kt.drawElementOverlay=function(t,e){var r=this;e.isNode()?r.drawNodeOverlay(t,e):r.drawEdgeOverlay(t,e)};Kt.drawElementUnderlay=function(t,e){var r=this;e.isNode()?r.drawNodeUnderlay(t,e):r.drawEdgeUnderlay(t,e)};Kt.drawCachedElementPortion=function(t,e,r,a,n,i,o,s){var u=this,l=r.getBoundingBox(e);if(!(l.w===0||l.h===0)){var c=r.getElement(e,l,a,n,i);if(c!=null){var f=s(u,e);if(f===0)return;var d=o(u,e),g=l.x1,h=l.y1,m=l.w,y=l.h,p,b,w,x,S;if(d!==0){var C=r.getRotationPoint(e);w=C.x,x=C.y,t.translate(w,x),t.rotate(d),S=u.getImgSmoothing(t),S||u.setImgSmoothing(t,!0);var E=r.getRotationOffset(e);p=E.x,b=E.y}else p=g,b=h;var D;f!==1&&(D=t.globalAlpha,t.globalAlpha=D*f),t.drawImage(c.texture.canvas,c.x,0,c.width,c.height,p,b,m,y),f!==1&&(t.globalAlpha=D),d!==0&&(t.rotate(-d),t.translate(-w,-x),S||u.setImgSmoothing(t,!1))}else r.drawElement(t,e)}};var iy=v(function(){return 0},"getZeroRotation"),oy=v(function(e,r){return e.getTextAngle(r,null)},"getLabelRotation"),sy=v(function(e,r){return e.getTextAngle(r,"source")},"getSourceLabelRotation"),ly=v(function(e,r){return e.getTextAngle(r,"target")},"getTargetLabelRotation"),uy=v(function(e,r){return r.effectiveOpacity()},"getOpacity"),vi=v(function(e,r){return r.pstyle("text-opacity").pfValue*r.effectiveOpacity()},"getTextOpacity");Kt.drawCachedElement=function(t,e,r,a,n,i){var o=this,s=o.data,u=s.eleTxrCache,l=s.lblTxrCache,c=s.slbTxrCache,f=s.tlbTxrCache,d=e.boundingBox(),g=i===!0?u.reasons.highQuality:null;if(!(d.w===0||d.h===0||!e.visible())&&(!a||Yi(d,a))){var h=e.isEdge(),m=e.element()._private.rscratch.badLine;o.drawElementUnderlay(t,e),o.drawCachedElementPortion(t,e,u,r,n,g,iy,uy),(!h||!m)&&o.drawCachedElementPortion(t,e,l,r,n,g,oy,vi),h&&!m&&(o.drawCachedElementPortion(t,e,c,r,n,g,sy,vi),o.drawCachedElementPortion(t,e,f,r,n,g,ly,vi)),o.drawElementOverlay(t,e)}};Kt.drawElements=function(t,e){for(var r=this,a=0;a<e.length;a++){var n=e[a];r.drawElement(t,n)}};Kt.drawCachedElements=function(t,e,r,a){for(var n=this,i=0;i<e.length;i++){var o=e[i];n.drawCachedElement(t,o,r,a)}};Kt.drawCachedNodes=function(t,e,r,a){for(var n=this,i=0;i<e.length;i++){var o=e[i];o.isNode()&&n.drawCachedElement(t,o,r,a)}};Kt.drawLayeredElements=function(t,e,r,a){var n=this,i=n.data.lyrTxrCache.getLayers(e,r);if(i)for(var o=0;o<i.length;o++){var s=i[o],u=s.bb;u.w===0||u.h===0||t.drawImage(s.canvas,u.x1,u.y1,u.w,u.h)}else n.drawCachedElements(t,e,r,a)};var Qt={};Qt.drawEdge=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,o=this,s=e._private.rscratch;if(!(i&&!e.visible())&&!(s.badLine||s.allpts==null||isNaN(s.allpts[0]))){var u;r&&(u=r,t.translate(-u.x1,-u.y1));var l=i?e.pstyle("opacity").value:1,c=i?e.pstyle("line-opacity").value:1,f=e.pstyle("curve-style").value,d=e.pstyle("line-style").value,g=e.pstyle("width").pfValue,h=e.pstyle("line-cap").value,m=e.pstyle("line-outline-width").value,y=e.pstyle("line-outline-color").value,p=l*c,b=l*c,w=v(function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p;f==="straight-triangle"?(o.eleStrokeStyle(t,e,M),o.drawEdgeTrianglePath(e,t,s.allpts)):(t.lineWidth=g,t.lineCap=h,o.eleStrokeStyle(t,e,M),o.drawEdgePath(e,t,s.allpts,d),t.lineCap="butt")},"drawLine"),x=v(function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p;if(t.lineWidth=g+m,t.lineCap=h,m>0)o.colorStrokeStyle(t,y[0],y[1],y[2],M);else{t.lineCap="butt";return}f==="straight-triangle"?o.drawEdgeTrianglePath(e,t,s.allpts):(o.drawEdgePath(e,t,s.allpts,d),t.lineCap="butt")},"drawLineOutline"),S=v(function(){n&&o.drawEdgeOverlay(t,e)},"drawOverlay"),C=v(function(){n&&o.drawEdgeUnderlay(t,e)},"drawUnderlay"),E=v(function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:b;o.drawArrowheads(t,e,M)},"drawArrows"),D=v(function(){o.drawElementText(t,e,null,a)},"drawText");t.lineJoin="round";var T=e.pstyle("ghost").value==="yes";if(T){var A=e.pstyle("ghost-offset-x").pfValue,B=e.pstyle("ghost-offset-y").pfValue,k=e.pstyle("ghost-opacity").value,L=p*k;t.translate(A,B),w(L),E(L),t.translate(-A,-B)}else x();C(),w(),E(),S(),D(),r&&t.translate(u.x1,u.y1)}};var dv=v(function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(r,a){if(a.visible()){var n=a.pstyle("".concat(e,"-opacity")).value;if(n!==0){var i=this,o=i.usePaths(),s=a._private.rscratch,u=a.pstyle("".concat(e,"-padding")).pfValue,l=2*u,c=a.pstyle("".concat(e,"-color")).value;r.lineWidth=l,s.edgeType==="self"&&!o?r.lineCap="butt":r.lineCap="round",i.colorStrokeStyle(r,c[0],c[1],c[2],n),i.drawEdgePath(a,r,s.allpts,"solid")}}}},"drawEdgeOverlayUnderlay");Qt.drawEdgeOverlay=dv("overlay");Qt.drawEdgeUnderlay=dv("underlay");Qt.drawEdgePath=function(t,e,r,a){var n=t._private.rscratch,i=e,o,s=!1,u=this.usePaths(),l=t.pstyle("line-dash-pattern").pfValue,c=t.pstyle("line-dash-offset").pfValue;if(u){var f=r.join("$"),d=n.pathCacheKey&&n.pathCacheKey===f;d?(o=e=n.pathCache,s=!0):(o=e=new Path2D,n.pathCacheKey=f,n.pathCache=o)}if(i.setLineDash)switch(a){case"dotted":i.setLineDash([1,1]);break;case"dashed":i.setLineDash(l),i.lineDashOffset=c;break;case"solid":i.setLineDash([]);break}if(!s&&!n.badLine)switch(e.beginPath&&e.beginPath(),e.moveTo(r[0],r[1]),n.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var g=2;g+3<r.length;g+=4)e.quadraticCurveTo(r[g],r[g+1],r[g+2],r[g+3]);break;case"straight":case"haystack":for(var h=2;h+1<r.length;h+=2)e.lineTo(r[h],r[h+1]);break;case"segments":if(n.isRound){var m=mt(n.roundCorners),y;try{for(m.s();!(y=m.n()).done;){var p=y.value;po(e,p)}}catch(w){m.e(w)}finally{m.f()}e.lineTo(r[r.length-2],r[r.length-1])}else for(var b=2;b+1<r.length;b+=2)e.lineTo(r[b],r[b+1]);break}e=i,u?e.stroke(o):e.stroke(),e.setLineDash&&e.setLineDash([])};Qt.drawEdgeTrianglePath=function(t,e,r){e.fillStyle=e.strokeStyle;for(var a=t.pstyle("width").pfValue,n=0;n+1<r.length;n+=2){var i=[r[n+2]-r[n],r[n+3]-r[n+1]],o=Math.sqrt(i[0]*i[0]+i[1]*i[1]),s=[i[1]/o,-i[0]/o],u=[s[0]*a/2,s[1]*a/2];e.beginPath(),e.moveTo(r[n]-u[0],r[n+1]-u[1]),e.lineTo(r[n]+u[0],r[n+1]+u[1]),e.lineTo(r[n+2],r[n+3]),e.closePath(),e.fill()}};Qt.drawArrowheads=function(t,e,r){var a=e._private.rscratch,n=a.edgeType==="haystack";n||this.drawArrowhead(t,e,"source",a.arrowStartX,a.arrowStartY,a.srcArrowAngle,r),this.drawArrowhead(t,e,"mid-target",a.midX,a.midY,a.midtgtArrowAngle,r),this.drawArrowhead(t,e,"mid-source",a.midX,a.midY,a.midsrcArrowAngle,r),n||this.drawArrowhead(t,e,"target",a.arrowEndX,a.arrowEndY,a.tgtArrowAngle,r)};Qt.drawArrowhead=function(t,e,r,a,n,i,o){if(!(isNaN(a)||a==null||isNaN(n)||n==null||isNaN(i)||i==null)){var s=this,u=e.pstyle(r+"-arrow-shape").value;if(u!=="none"){var l=e.pstyle(r+"-arrow-fill").value==="hollow"?"both":"filled",c=e.pstyle(r+"-arrow-fill").value,f=e.pstyle("width").pfValue,d=e.pstyle(r+"-arrow-width"),g=d.value==="match-line"?f:d.pfValue;d.units==="%"&&(g*=f);var h=e.pstyle("opacity").value;o===void 0&&(o=h);var m=t.globalCompositeOperation;(o!==1||c==="hollow")&&(t.globalCompositeOperation="destination-out",s.colorFillStyle(t,255,255,255,1),s.colorStrokeStyle(t,255,255,255,1),s.drawArrowShape(e,t,l,f,u,g,a,n,i),t.globalCompositeOperation=m);var y=e.pstyle(r+"-arrow-color").value;s.colorFillStyle(t,y[0],y[1],y[2],o),s.colorStrokeStyle(t,y[0],y[1],y[2],o),s.drawArrowShape(e,t,c,f,u,g,a,n,i)}}};Qt.drawArrowShape=function(t,e,r,a,n,i,o,s,u){var l=this,c=this.usePaths()&&n!=="triangle-cross",f=!1,d,g=e,h={x:o,y:s},m=t.pstyle("arrow-scale").value,y=this.getArrowWidth(a,m),p=l.arrowShapes[n];if(c){var b=l.arrowPathCache=l.arrowPathCache||[],w=ir(n),x=b[w];x!=null?(d=e=x,f=!0):(d=e=new Path2D,b[w]=d)}f||(e.beginPath&&e.beginPath(),c?p.draw(e,1,0,{x:0,y:0},1):p.draw(e,y,u,h,a),e.closePath&&e.closePath()),e=g,c&&(e.translate(o,s),e.rotate(u),e.scale(y,y)),(r==="filled"||r==="both")&&(c?e.fill(d):e.fill()),(r==="hollow"||r==="both")&&(e.lineWidth=i/(c?y:1),e.lineJoin="miter",c?e.stroke(d):e.stroke()),c&&(e.scale(1/y,1/y),e.rotate(-u),e.translate(-o,-s))};var bo={};bo.safeDrawImage=function(t,e,r,a,n,i,o,s,u,l){if(!(n<=0||i<=0||u<=0||l<=0))try{t.drawImage(e,r,a,n,i,o,s,u,l)}catch(c){Re(c)}};bo.drawInscribedImage=function(t,e,r,a,n){var i=this,o=r.position(),s=o.x,u=o.y,l=r.cy().style(),c=l.getIndexedStyle.bind(l),f=c(r,"background-fit","value",a),d=c(r,"background-repeat","value",a),g=r.width(),h=r.height(),m=r.padding()*2,y=g+(c(r,"background-width-relative-to","value",a)==="inner"?0:m),p=h+(c(r,"background-height-relative-to","value",a)==="inner"?0:m),b=r._private.rscratch,w=c(r,"background-clip","value",a),x=w==="node",S=c(r,"background-image-opacity","value",a)*n,C=c(r,"background-image-smoothing","value",a),E=r.pstyle("corner-radius").value;E!=="auto"&&(E=r.pstyle("corner-radius").pfValue);var D=e.width||e.cachedW,T=e.height||e.cachedH;(D==null||T==null)&&(document.body.appendChild(e),D=e.cachedW=e.width||e.offsetWidth,T=e.cachedH=e.height||e.offsetHeight,document.body.removeChild(e));var A=D,B=T;if(c(r,"background-width","value",a)!=="auto"&&(c(r,"background-width","units",a)==="%"?A=c(r,"background-width","pfValue",a)*y:A=c(r,"background-width","pfValue",a)),c(r,"background-height","value",a)!=="auto"&&(c(r,"background-height","units",a)==="%"?B=c(r,"background-height","pfValue",a)*p:B=c(r,"background-height","pfValue",a)),!(A===0||B===0)){if(f==="contain"){var k=Math.min(y/A,p/B);A*=k,B*=k}else if(f==="cover"){var k=Math.max(y/A,p/B);A*=k,B*=k}var L=s-y/2,R=c(r,"background-position-x","units",a),M=c(r,"background-position-x","pfValue",a);R==="%"?L+=(y-A)*M:L+=M;var I=c(r,"background-offset-x","units",a),O=c(r,"background-offset-x","pfValue",a);I==="%"?L+=(y-A)*O:L+=O;var F=u-p/2,K=c(r,"background-position-y","units",a),$=c(r,"background-position-y","pfValue",a);K==="%"?F+=(p-B)*$:F+=$;var q=c(r,"background-offset-y","units",a),G=c(r,"background-offset-y","pfValue",a);q==="%"?F+=(p-B)*G:F+=G,b.pathCache&&(L-=s,F-=u,s=0,u=0);var X=t.globalAlpha;t.globalAlpha=S;var Z=i.getImgSmoothing(t),J=!1;if(C==="no"&&Z?(i.setImgSmoothing(t,!1),J=!0):C==="yes"&&!Z&&(i.setImgSmoothing(t,!0),J=!0),d==="no-repeat")x&&(t.save(),b.pathCache?t.clip(b.pathCache):(i.nodeShapes[i.getNodeShape(r)].draw(t,s,u,y,p,E,b),t.clip())),i.safeDrawImage(t,e,0,0,D,T,L,F,A,B),x&&t.restore();else{var Q=t.createPattern(e,d);t.fillStyle=Q,i.nodeShapes[i.getNodeShape(r)].draw(t,s,u,y,p,E,b),t.translate(L,F),t.fill(),t.translate(-L,-F)}t.globalAlpha=X,J&&i.setImgSmoothing(t,Z)}};var Lr={};Lr.eleTextBiggerThanMin=function(t,e){if(!e){var r=t.cy().zoom(),a=this.getPixelRatio(),n=Math.ceil(Xi(r*a));e=Math.pow(2,n)}var i=t.pstyle("font-size").pfValue*e,o=t.pstyle("min-zoomed-font-size").pfValue;return!(i<o)};Lr.drawElementText=function(t,e,r,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,o=this;if(a==null){if(i&&!o.eleTextBiggerThanMin(e))return}else if(a===!1)return;if(e.isNode()){var s=e.pstyle("label");if(!s||!s.value)return;var u=o.getLabelJustification(e);t.textAlign=u,t.textBaseline="bottom"}else{var l=e.element()._private.rscratch.badLine,c=e.pstyle("label"),f=e.pstyle("source-label"),d=e.pstyle("target-label");if(l||(!c||!c.value)&&(!f||!f.value)&&(!d||!d.value))return;t.textAlign="center",t.textBaseline="bottom"}var g=!r,h;r&&(h=r,t.translate(-h.x1,-h.y1)),n==null?(o.drawText(t,e,null,g,i),e.isEdge()&&(o.drawText(t,e,"source",g,i),o.drawText(t,e,"target",g,i))):o.drawText(t,e,n,g,i),r&&t.translate(h.x1,h.y1)};Lr.getFontCache=function(t){var e;this.fontCaches=this.fontCaches||[];for(var r=0;r<this.fontCaches.length;r++)if(e=this.fontCaches[r],e.context===t)return e;return e={context:t},this.fontCaches.push(e),e};Lr.setupTextStyle=function(t,e){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,a=e.pstyle("font-style").strValue,n=e.pstyle("font-size").pfValue+"px",i=e.pstyle("font-family").strValue,o=e.pstyle("font-weight").strValue,s=r?e.effectiveOpacity()*e.pstyle("text-opacity").value:1,u=e.pstyle("text-outline-opacity").value*s,l=e.pstyle("color").value,c=e.pstyle("text-outline-color").value;t.font=a+" "+o+" "+n+" "+i,t.lineJoin="round",this.colorFillStyle(t,l[0],l[1],l[2],s),this.colorStrokeStyle(t,c[0],c[1],c[2],u)};function bn(t,e,r,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:5,o=arguments.length>6?arguments[6]:void 0;t.beginPath(),t.moveTo(e+i,r),t.lineTo(e+a-i,r),t.quadraticCurveTo(e+a,r,e+a,r+i),t.lineTo(e+a,r+n-i),t.quadraticCurveTo(e+a,r+n,e+a-i,r+n),t.lineTo(e+i,r+n),t.quadraticCurveTo(e,r+n,e,r+n-i),t.lineTo(e,r+i),t.quadraticCurveTo(e,r,e+i,r),t.closePath(),o?t.stroke():t.fill()}v(bn,"roundRect");Lr.getTextAngle=function(t,e){var r,a=t._private,n=a.rscratch,i=e?e+"-":"",o=t.pstyle(i+"text-rotation");if(o.strValue==="autorotate"){var s=At(n,"labelAngle",e);r=t.isEdge()?s:0}else o.strValue==="none"?r=0:r=o.pfValue;return r};Lr.drawText=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=e._private,o=i.rscratch,s=n?e.effectiveOpacity():1;if(!(n&&(s===0||e.pstyle("text-opacity").value===0))){r==="main"&&(r=null);var u=At(o,"labelX",r),l=At(o,"labelY",r),c,f,d=this.getLabelText(e,r);if(d!=null&&d!==""&&!isNaN(u)&&!isNaN(l)){this.setupTextStyle(t,e,n);var g=r?r+"-":"",h=At(o,"labelWidth",r),m=At(o,"labelHeight",r),y=e.pstyle(g+"text-margin-x").pfValue,p=e.pstyle(g+"text-margin-y").pfValue,b=e.isEdge(),w=e.pstyle("text-halign").value,x=e.pstyle("text-valign").value;b&&(w="center",x="center"),u+=y,l+=p;var S;switch(a?S=this.getTextAngle(e,r):S=0,S!==0&&(c=u,f=l,t.translate(c,f),t.rotate(S),u=0,l=0),x){case"top":break;case"center":l+=m/2;break;case"bottom":l+=m;break}var C=e.pstyle("text-background-opacity").value,E=e.pstyle("text-border-opacity").value,D=e.pstyle("text-border-width").pfValue,T=e.pstyle("text-background-padding").pfValue,A=e.pstyle("text-background-shape").strValue,B=A.indexOf("round")===0,k=2;if(C>0||D>0&&E>0){var L=u-T;switch(w){case"left":L-=h;break;case"center":L-=h/2;break}var R=l-m-T,M=h+2*T,I=m+2*T;if(C>0){var O=t.fillStyle,F=e.pstyle("text-background-color").value;t.fillStyle="rgba("+F[0]+","+F[1]+","+F[2]+","+C*s+")",B?bn(t,L,R,M,I,k):t.fillRect(L,R,M,I),t.fillStyle=O}if(D>0&&E>0){var K=t.strokeStyle,$=t.lineWidth,q=e.pstyle("text-border-color").value,G=e.pstyle("text-border-style").value;if(t.strokeStyle="rgba("+q[0]+","+q[1]+","+q[2]+","+E*s+")",t.lineWidth=D,t.setLineDash)switch(G){case"dotted":t.setLineDash([1,1]);break;case"dashed":t.setLineDash([4,2]);break;case"double":t.lineWidth=D/4,t.setLineDash([]);break;case"solid":t.setLineDash([]);break}if(B?bn(t,L,R,M,I,k,"stroke"):t.strokeRect(L,R,M,I),G==="double"){var X=D/2;B?bn(t,L+X,R+X,M-X*2,I-X*2,k,"stroke"):t.strokeRect(L+X,R+X,M-X*2,I-X*2)}t.setLineDash&&t.setLineDash([]),t.lineWidth=$,t.strokeStyle=K}}var Z=2*e.pstyle("text-outline-width").pfValue;if(Z>0&&(t.lineWidth=Z),e.pstyle("text-wrap").value==="wrap"){var J=At(o,"labelWrapCachedLines",r),Q=At(o,"labelLineHeight",r),ee=h/2,re=this.getLabelJustification(e);switch(re==="auto"||(w==="left"?re==="left"?u+=-h:re==="center"&&(u+=-ee):w==="center"?re==="left"?u+=-ee:re==="right"&&(u+=ee):w==="right"&&(re==="center"?u+=ee:re==="right"&&(u+=h))),x){case"top":l-=(J.length-1)*Q;break;case"center":case"bottom":l-=(J.length-1)*Q;break}for(var W=0;W<J.length;W++)Z>0&&t.strokeText(J[W],u,l),t.fillText(J[W],u,l),l+=Q}else Z>0&&t.strokeText(d,u,l),t.fillText(d,u,l);S!==0&&(t.rotate(-S),t.translate(-c,-f))}}};var ra={};ra.drawNode=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,o=this,s,u,l=e._private,c=l.rscratch,f=e.position();if(!(!ae(f.x)||!ae(f.y))&&!(i&&!e.visible())){var d=i?e.effectiveOpacity():1,g=o.usePaths(),h,m=!1,y=e.padding();s=e.width()+2*y,u=e.height()+2*y;var p;r&&(p=r,t.translate(-p.x1,-p.y1));for(var b=e.pstyle("background-image"),w=b.value,x=new Array(w.length),S=new Array(w.length),C=0,E=0;E<w.length;E++){var D=w[E],T=x[E]=D!=null&&D!=="none";if(T){var A=e.cy().style().getIndexedStyle(e,"background-image-crossorigin","value",E);C++,S[E]=o.getCachedImage(D,A,function(){l.backgroundTimestamp=Date.now(),e.emitAndNotify("background")})}}var B=e.pstyle("background-blacken").value,k=e.pstyle("border-width").pfValue,L=e.pstyle("background-opacity").value*d,R=e.pstyle("border-color").value,M=e.pstyle("border-style").value,I=e.pstyle("border-join").value,O=e.pstyle("border-cap").value,F=e.pstyle("border-position").value,K=e.pstyle("border-dash-pattern").pfValue,$=e.pstyle("border-dash-offset").pfValue,q=e.pstyle("border-opacity").value*d,G=e.pstyle("outline-width").pfValue,X=e.pstyle("outline-color").value,Z=e.pstyle("outline-style").value,J=e.pstyle("outline-opacity").value*d,Q=e.pstyle("outline-offset").value,ee=e.pstyle("corner-radius").value;ee!=="auto"&&(ee=e.pstyle("corner-radius").pfValue);var re=v(function(){var V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:L;o.eleFillStyle(t,e,V)},"setupShapeColor"),W=v(function(){var V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:q;o.colorStrokeStyle(t,R[0],R[1],R[2],V)},"setupBorderColor"),N=v(function(){var V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:J;o.colorStrokeStyle(t,X[0],X[1],X[2],V)},"setupOutlineColor"),U=v(function(V,H,ne,Y){var ie=o.nodePathCache=o.nodePathCache||[],de=yl(ne==="polygon"?ne+","+Y.join(","):ne,""+H,""+V,""+ee),Ee=ie[de],ce,we=!1;return Ee!=null?(ce=Ee,we=!0,c.pathCache=ce):(ce=new Path2D,ie[de]=c.pathCache=ce),{path:ce,cacheHit:we}},"getPath"),te=e.pstyle("shape").strValue,oe=e.pstyle("shape-polygon-points").pfValue;if(g){t.translate(f.x,f.y);var ue=U(s,u,te,oe);h=ue.path,m=ue.cacheHit}var Se=v(function(){if(!m){var V=f;g&&(V={x:0,y:0}),o.nodeShapes[o.getNodeShape(e)].draw(h||t,V.x,V.y,s,u,ee,c)}g?t.fill(h):t.fill()},"drawShape"),Le=v(function(){for(var V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:d,H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,ne=l.backgrounding,Y=0,ie=0;ie<S.length;ie++){var de=e.cy().style().getIndexedStyle(e,"background-image-containment","value",ie);if(H&&de==="over"||!H&&de==="inside"){Y++;continue}x[ie]&&S[ie].complete&&!S[ie].error&&(Y++,o.drawInscribedImage(t,S[ie],e,ie,V))}l.backgrounding=Y!==C,ne!==l.backgrounding&&e.updateStyle(!1)},"drawImages"),Ie=v(function(){var V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:d;o.hasPie(e)&&(o.drawPie(t,e,H),V&&(g||o.nodeShapes[o.getNodeShape(e)].draw(t,f.x,f.y,s,u,ee,c)))},"drawPie"),ve=v(function(){var V=arguments.length>0&&arguments[0]!==void 0?arguments[0]:d,H=(B>0?B:-B)*V,ne=B>0?0:255;B!==0&&(o.colorFillStyle(t,ne,ne,ne,H),g?t.fill(h):t.fill())},"darken"),le=v(function(){if(k>0){if(t.lineWidth=k,t.lineCap=O,t.lineJoin=I,t.setLineDash)switch(M){case"dotted":t.setLineDash([1,1]);break;case"dashed":t.setLineDash(K),t.lineDashOffset=$;break;case"solid":case"double":t.setLineDash([]);break}if(F!=="center"){if(t.save(),t.lineWidth*=2,F==="inside")g?t.clip(h):t.clip();else{var V=new Path2D;V.rect(-s/2-k,-u/2-k,s+2*k,u+2*k),V.addPath(h),t.clip(V,"evenodd")}g?t.stroke(h):t.stroke(),t.restore()}else g?t.stroke(h):t.stroke();if(M==="double"){t.lineWidth=k/3;var H=t.globalCompositeOperation;t.globalCompositeOperation="destination-out",g?t.stroke(h):t.stroke(),t.globalCompositeOperation=H}t.setLineDash&&t.setLineDash([])}},"drawBorder"),ye=v(function(){if(G>0){if(t.lineWidth=G,t.lineCap="butt",t.setLineDash)switch(Z){case"dotted":t.setLineDash([1,1]);break;case"dashed":t.setLineDash([4,2]);break;case"solid":case"double":t.setLineDash([]);break}var V=f;g&&(V={x:0,y:0});var H=o.getNodeShape(e),ne=k;F==="inside"&&(ne=0),F==="outside"&&(ne*=2);var Y=(s+ne+(G+Q))/s,ie=(u+ne+(G+Q))/u,de=s*Y,Ee=u*ie,ce=o.nodeShapes[H].points,we;if(g){var xe=U(de,Ee,H,ce);we=xe.path}if(H==="ellipse")o.drawEllipsePath(we||t,V.x,V.y,de,Ee);else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(H)){var pe=0,Fe=0,qe=0;H==="round-diamond"?pe=(ne+Q+G)*1.4:H==="round-heptagon"?(pe=(ne+Q+G)*1.075,qe=-(ne/2+Q+G)/35):H==="round-hexagon"?pe=(ne+Q+G)*1.12:H==="round-pentagon"?(pe=(ne+Q+G)*1.13,qe=-(ne/2+Q+G)/15):H==="round-tag"?(pe=(ne+Q+G)*1.12,Fe=(ne/2+G+Q)*.07):H==="round-triangle"&&(pe=(ne+Q+G)*(Math.PI/2),qe=-(ne+Q/2+G)/Math.PI),pe!==0&&(Y=(s+pe)/s,de=s*Y,["round-hexagon","round-tag"].includes(H)||(ie=(u+pe)/u,Ee=u*ie)),ee=ee==="auto"?kl(de,Ee):ee;for(var wt=de/2,xt=Ee/2,Ue=ee+(ne+G+Q)/2,Ze=new Array(ce.length/2),Qe=new Array(ce.length/2),ct=0;ct<ce.length/2;ct++)Ze[ct]={x:V.x+Fe+wt*ce[ct*2],y:V.y+qe+xt*ce[ct*2+1]};var ft,Lt,Ct,It,Gt=Ze.length;for(Lt=Ze[Gt-1],ft=0;ft<Gt;ft++)Ct=Ze[ft%Gt],It=Ze[(ft+1)%Gt],Qe[ft]=Xn(Lt,Ct,It,Ue),Lt=Ct,Ct=It;o.drawRoundPolygonPath(we||t,V.x+Fe,V.y+qe,s*Y,u*ie,ce,Qe)}else if(["roundrectangle","round-rectangle"].includes(H))ee=ee==="auto"?Cr(de,Ee):ee,o.drawRoundRectanglePath(we||t,V.x,V.y,de,Ee,ee+(ne+G+Q)/2);else if(["cutrectangle","cut-rectangle"].includes(H))ee=ee==="auto"?Zi():ee,o.drawCutRectanglePath(we||t,V.x,V.y,de,Ee,null,ee+(ne+G+Q)/4);else if(["bottomroundrectangle","bottom-round-rectangle"].includes(H))ee=ee==="auto"?Cr(de,Ee):ee,o.drawBottomRoundRectanglePath(we||t,V.x,V.y,de,Ee,ee+(ne+G+Q)/2);else if(H==="barrel")o.drawBarrelPath(we||t,V.x,V.y,de,Ee);else if(H.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(H)){var Ht=(ne+G+Q)/s;ce=Sn(Dn(ce,Ht)),o.drawPolygonPath(we||t,V.x,V.y,s,u,ce)}else{var it=(ne+G+Q)/s;ce=Sn(Dn(ce,-it)),o.drawPolygonPath(we||t,V.x,V.y,s,u,ce)}if(g?t.stroke(we):t.stroke(),Z==="double"){t.lineWidth=ne/3;var Je=t.globalCompositeOperation;t.globalCompositeOperation="destination-out",g?t.stroke(we):t.stroke(),t.globalCompositeOperation=Je}t.setLineDash&&t.setLineDash([])}},"drawOutline"),me=v(function(){n&&o.drawNodeOverlay(t,e,f,s,u)},"drawOverlay"),ge=v(function(){n&&o.drawNodeUnderlay(t,e,f,s,u)},"drawUnderlay"),be=v(function(){o.drawElementText(t,e,null,a)},"drawText"),Ce=e.pstyle("ghost").value==="yes";if(Ce){var De=e.pstyle("ghost-offset-x").pfValue,j=e.pstyle("ghost-offset-y").pfValue,P=e.pstyle("ghost-opacity").value,z=P*d;t.translate(De,j),N(),ye(),re(P*L),Se(),Le(z,!0),W(P*q),le(),Ie(B!==0||k!==0),Le(z,!1),ve(z),t.translate(-De,-j)}g&&t.translate(-f.x,-f.y),ge(),g&&t.translate(f.x,f.y),N(),ye(),re(),Se(),Le(d,!0),W(),le(),Ie(B!==0||k!==0),Le(d,!1),ve(),g&&t.translate(-f.x,-f.y),be(),me(),r&&t.translate(p.x1,p.y1)}};var hv=v(function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(r,a,n,i,o){var s=this;if(a.visible()){var u=a.pstyle("".concat(e,"-padding")).pfValue,l=a.pstyle("".concat(e,"-opacity")).value,c=a.pstyle("".concat(e,"-color")).value,f=a.pstyle("".concat(e,"-shape")).value,d=a.pstyle("".concat(e,"-corner-radius")).value;if(l>0){if(n=n||a.position(),i==null||o==null){var g=a.padding();i=a.width()+2*g,o=a.height()+2*g}s.colorFillStyle(r,c[0],c[1],c[2],l),s.nodeShapes[f].draw(r,n.x,n.y,i+u*2,o+u*2,d),r.fill()}}}},"drawNodeOverlayUnderlay");ra.drawNodeOverlay=hv("overlay");ra.drawNodeUnderlay=hv("underlay");ra.hasPie=function(t){return t=t[0],t._private.hasPie};ra.drawPie=function(t,e,r,a){e=e[0],a=a||e.position();var n=e.cy().style(),i=e.pstyle("pie-size"),o=a.x,s=a.y,u=e.width(),l=e.height(),c=Math.min(u,l)/2,f=0,d=this.usePaths();d&&(o=0,s=0),i.units==="%"?c=c*i.pfValue:i.pfValue!==void 0&&(c=i.pfValue/2);for(var g=1;g<=n.pieBackgroundN;g++){var h=e.pstyle("pie-"+g+"-background-size").value,m=e.pstyle("pie-"+g+"-background-color").value,y=e.pstyle("pie-"+g+"-background-opacity").value*r,p=h/100;p+f>1&&(p=1-f);var b=1.5*Math.PI+2*Math.PI*f,w=2*Math.PI*p,x=b+w;h===0||f>=1||f+p>1||(t.beginPath(),t.moveTo(o,s),t.arc(o,s,c,b,x),t.closePath(),this.colorFillStyle(t,m[0],m[1],m[2],y),t.fill(),f+=p)}};var ht={},vy=100;ht.getPixelRatio=function(){var t=this.data.contexts[0];if(this.forcedPixelRatio!=null)return this.forcedPixelRatio;var e=this.cy.window(),r=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(e.devicePixelRatio||1)/r};ht.paintCache=function(t){for(var e=this.paintCaches=this.paintCaches||[],r=!0,a,n=0;n<e.length;n++)if(a=e[n],a.context===t){r=!1;break}return r&&(a={context:t},e.push(a)),a};ht.createGradientStyleFor=function(t,e,r,a,n){var i,o=this.usePaths(),s=r.pstyle(e+"-gradient-stop-colors").value,u=r.pstyle(e+"-gradient-stop-positions").pfValue;if(a==="radial-gradient")if(r.isEdge()){var l=r.sourceEndpoint(),c=r.targetEndpoint(),f=r.midpoint(),d=Er(l,f),g=Er(c,f);i=t.createRadialGradient(f.x,f.y,0,f.x,f.y,Math.max(d,g))}else{var h=o?{x:0,y:0}:r.position(),m=r.paddedWidth(),y=r.paddedHeight();i=t.createRadialGradient(h.x,h.y,0,h.x,h.y,Math.max(m,y))}else if(r.isEdge()){var p=r.sourceEndpoint(),b=r.targetEndpoint();i=t.createLinearGradient(p.x,p.y,b.x,b.y)}else{var w=o?{x:0,y:0}:r.position(),x=r.paddedWidth(),S=r.paddedHeight(),C=x/2,E=S/2,D=r.pstyle("background-gradient-direction").value;switch(D){case"to-bottom":i=t.createLinearGradient(w.x,w.y-E,w.x,w.y+E);break;case"to-top":i=t.createLinearGradient(w.x,w.y+E,w.x,w.y-E);break;case"to-left":i=t.createLinearGradient(w.x+C,w.y,w.x-C,w.y);break;case"to-right":i=t.createLinearGradient(w.x-C,w.y,w.x+C,w.y);break;case"to-bottom-right":case"to-right-bottom":i=t.createLinearGradient(w.x-C,w.y-E,w.x+C,w.y+E);break;case"to-top-right":case"to-right-top":i=t.createLinearGradient(w.x-C,w.y+E,w.x+C,w.y-E);break;case"to-bottom-left":case"to-left-bottom":i=t.createLinearGradient(w.x+C,w.y-E,w.x-C,w.y+E);break;case"to-top-left":case"to-left-top":i=t.createLinearGradient(w.x+C,w.y+E,w.x-C,w.y-E);break}}if(!i)return null;for(var T=u.length===s.length,A=s.length,B=0;B<A;B++)i.addColorStop(T?u[B]:B/(A-1),"rgba("+s[B][0]+","+s[B][1]+","+s[B][2]+","+n+")");return i};ht.gradientFillStyle=function(t,e,r,a){var n=this.createGradientStyleFor(t,"background",e,r,a);if(!n)return null;t.fillStyle=n};ht.colorFillStyle=function(t,e,r,a,n){t.fillStyle="rgba("+e+","+r+","+a+","+n+")"};ht.eleFillStyle=function(t,e,r){var a=e.pstyle("background-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientFillStyle(t,e,a,r);else{var n=e.pstyle("background-color").value;this.colorFillStyle(t,n[0],n[1],n[2],r)}};ht.gradientStrokeStyle=function(t,e,r,a){var n=this.createGradientStyleFor(t,"line",e,r,a);if(!n)return null;t.strokeStyle=n};ht.colorStrokeStyle=function(t,e,r,a,n){t.strokeStyle="rgba("+e+","+r+","+a+","+n+")"};ht.eleStrokeStyle=function(t,e,r){var a=e.pstyle("line-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientStrokeStyle(t,e,a,r);else{var n=e.pstyle("line-color").value;this.colorStrokeStyle(t,n[0],n[1],n[2],r)}};ht.matchCanvasSize=function(t){var e=this,r=e.data,a=e.findContainerClientCoords(),n=a[2],i=a[3],o=e.getPixelRatio(),s=e.motionBlurPxRatio;(t===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE]||t===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG])&&(o=s);var u=n*o,l=i*o,c;if(!(u===e.canvasWidth&&l===e.canvasHeight)){e.fontCaches=null;var f=r.canvasContainer;f.style.width=n+"px",f.style.height=i+"px";for(var d=0;d<e.CANVAS_LAYERS;d++)c=r.canvases[d],c.width=u,c.height=l,c.style.width=n+"px",c.style.height=i+"px";for(var d=0;d<e.BUFFER_COUNT;d++)c=r.bufferCanvases[d],c.width=u,c.height=l,c.style.width=n+"px",c.style.height=i+"px";e.textureMult=1,o<=1&&(c=r.bufferCanvases[e.TEXTURE_BUFFER],e.textureMult=2,c.width=u*e.textureMult,c.height=l*e.textureMult),e.canvasWidth=u,e.canvasHeight=l,e.pixelRatio=o}};ht.renderTo=function(t,e,r,a){this.render({forcedContext:t,forcedZoom:e,forcedPan:r,drawAllLayers:!0,forcedPxRatio:a})};ht.clearCanvas=function(){var t=this,e=t.data;function r(a){a.clearRect(0,0,t.canvasWidth,t.canvasHeight)}v(r,"clear"),r(e.contexts[t.NODE]),r(e.contexts[t.DRAG])};ht.render=function(t){var e=this;t=t||xl();var r=e.cy,a=t.forcedContext,n=t.drawAllLayers,i=t.drawOnlyNodeLayer,o=t.forcedZoom,s=t.forcedPan,u=t.forcedPxRatio===void 0?this.getPixelRatio():t.forcedPxRatio,l=e.data,c=l.canvasNeedsRedraw,f=e.textureOnViewport&&!a&&(e.pinching||e.hoverData.dragging||e.swipePanning||e.data.wheelZooming),d=t.motionBlur!==void 0?t.motionBlur:e.motionBlur,g=e.motionBlurPxRatio,h=r.hasCompoundNodes(),m=e.hoverData.draggingEles,y=!!(e.hoverData.selecting||e.touchData.selecting);d=d&&!a&&e.motionBlurEnabled&&!y;var p=d;a||(e.prevPxRatio!==u&&(e.invalidateContainerClientCoordsCache(),e.matchCanvasSize(e.container),e.redrawHint("eles",!0),e.redrawHint("drag",!0)),e.prevPxRatio=u),!a&&e.motionBlurTimeout&&clearTimeout(e.motionBlurTimeout),d&&(e.mbFrames==null&&(e.mbFrames=0),e.mbFrames++,e.mbFrames<3&&(p=!1),e.mbFrames>e.minMbLowQualFrames&&(e.motionBlurPxRatio=e.mbPxRBlurry)),e.clearingMotionBlur&&(e.motionBlurPxRatio=1),e.textureDrawLastFrame&&!f&&(c[e.NODE]=!0,c[e.SELECT_BOX]=!0);var b=r.style(),w=r.zoom(),x=o!==void 0?o:w,S=r.pan(),C={x:S.x,y:S.y},E={zoom:w,pan:{x:S.x,y:S.y}},D=e.prevViewport,T=D===void 0||E.zoom!==D.zoom||E.pan.x!==D.pan.x||E.pan.y!==D.pan.y;!T&&!(m&&!h)&&(e.motionBlurPxRatio=1),s&&(C=s),x*=u,C.x*=u,C.y*=u;var A=e.getCachedZSortedEles();function B(W,N,U,te,oe){var ue=W.globalCompositeOperation;W.globalCompositeOperation="destination-out",e.colorFillStyle(W,255,255,255,e.motionBlurTransparency),W.fillRect(N,U,te,oe),W.globalCompositeOperation=ue}v(B,"mbclear");function k(W,N){var U,te,oe,ue;!e.clearingMotionBlur&&(W===l.bufferContexts[e.MOTIONBLUR_BUFFER_NODE]||W===l.bufferContexts[e.MOTIONBLUR_BUFFER_DRAG])?(U={x:S.x*g,y:S.y*g},te=w*g,oe=e.canvasWidth*g,ue=e.canvasHeight*g):(U=C,te=x,oe=e.canvasWidth,ue=e.canvasHeight),W.setTransform(1,0,0,1,0,0),N==="motionBlur"?B(W,0,0,oe,ue):!a&&(N===void 0||N)&&W.clearRect(0,0,oe,ue),n||(W.translate(U.x,U.y),W.scale(te,te)),s&&W.translate(s.x,s.y),o&&W.scale(o,o)}if(v(k,"setContextTransform"),f||(e.textureDrawLastFrame=!1),f){if(e.textureDrawLastFrame=!0,!e.textureCache){e.textureCache={},e.textureCache.bb=r.mutableElements().boundingBox(),e.textureCache.texture=e.data.bufferCanvases[e.TEXTURE_BUFFER];var L=e.data.bufferContexts[e.TEXTURE_BUFFER];L.setTransform(1,0,0,1,0,0),L.clearRect(0,0,e.canvasWidth*e.textureMult,e.canvasHeight*e.textureMult),e.render({forcedContext:L,drawOnlyNodeLayer:!0,forcedPxRatio:u*e.textureMult});var E=e.textureCache.viewport={zoom:r.zoom(),pan:r.pan(),width:e.canvasWidth,height:e.canvasHeight};E.mpan={x:(0-E.pan.x)/E.zoom,y:(0-E.pan.y)/E.zoom}}c[e.DRAG]=!1,c[e.NODE]=!1;var R=l.contexts[e.NODE],M=e.textureCache.texture,E=e.textureCache.viewport;R.setTransform(1,0,0,1,0,0),d?B(R,0,0,E.width,E.height):R.clearRect(0,0,E.width,E.height);var I=b.core("outside-texture-bg-color").value,O=b.core("outside-texture-bg-opacity").value;e.colorFillStyle(R,I[0],I[1],I[2],O),R.fillRect(0,0,E.width,E.height);var w=r.zoom();k(R,!1),R.clearRect(E.mpan.x,E.mpan.y,E.width/E.zoom/u,E.height/E.zoom/u),R.drawImage(M,E.mpan.x,E.mpan.y,E.width/E.zoom/u,E.height/E.zoom/u)}else e.textureOnViewport&&!a&&(e.textureCache=null);var F=r.extent(),K=e.pinching||e.hoverData.dragging||e.swipePanning||e.data.wheelZooming||e.hoverData.draggingEles||e.cy.animated(),$=e.hideEdgesOnViewport&&K,q=[];if(q[e.NODE]=!c[e.NODE]&&d&&!e.clearedForMotionBlur[e.NODE]||e.clearingMotionBlur,q[e.NODE]&&(e.clearedForMotionBlur[e.NODE]=!0),q[e.DRAG]=!c[e.DRAG]&&d&&!e.clearedForMotionBlur[e.DRAG]||e.clearingMotionBlur,q[e.DRAG]&&(e.clearedForMotionBlur[e.DRAG]=!0),c[e.NODE]||n||i||q[e.NODE]){var G=d&&!q[e.NODE]&&g!==1,R=a||(G?e.data.bufferContexts[e.MOTIONBLUR_BUFFER_NODE]:l.contexts[e.NODE]),X=d&&!G?"motionBlur":void 0;k(R,X),$?e.drawCachedNodes(R,A.nondrag,u,F):e.drawLayeredElements(R,A.nondrag,u,F),e.debug&&e.drawDebugPoints(R,A.nondrag),!n&&!d&&(c[e.NODE]=!1)}if(!i&&(c[e.DRAG]||n||q[e.DRAG])){var G=d&&!q[e.DRAG]&&g!==1,R=a||(G?e.data.bufferContexts[e.MOTIONBLUR_BUFFER_DRAG]:l.contexts[e.DRAG]);k(R,d&&!G?"motionBlur":void 0),$?e.drawCachedNodes(R,A.drag,u,F):e.drawCachedElements(R,A.drag,u,F),e.debug&&e.drawDebugPoints(R,A.drag),!n&&!d&&(c[e.DRAG]=!1)}if(this.drawSelectionRectangle(t,k),d&&g!==1){var Z=l.contexts[e.NODE],J=e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE],Q=l.contexts[e.DRAG],ee=e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG],re=v(function(N,U,te){N.setTransform(1,0,0,1,0,0),te||!p?N.clearRect(0,0,e.canvasWidth,e.canvasHeight):B(N,0,0,e.canvasWidth,e.canvasHeight);var oe=g;N.drawImage(U,0,0,e.canvasWidth*oe,e.canvasHeight*oe,0,0,e.canvasWidth,e.canvasHeight)},"drawMotionBlur");(c[e.NODE]||q[e.NODE])&&(re(Z,J,q[e.NODE]),c[e.NODE]=!1),(c[e.DRAG]||q[e.DRAG])&&(re(Q,ee,q[e.DRAG]),c[e.DRAG]=!1)}e.prevViewport=E,e.clearingMotionBlur&&(e.clearingMotionBlur=!1,e.motionBlurCleared=!0,e.motionBlur=!0),d&&(e.motionBlurTimeout=setTimeout(function(){e.motionBlurTimeout=null,e.clearedForMotionBlur[e.NODE]=!1,e.clearedForMotionBlur[e.DRAG]=!1,e.motionBlur=!1,e.clearingMotionBlur=!f,e.mbFrames=0,c[e.NODE]=!0,c[e.DRAG]=!0,e.redraw()},vy)),a||r.emit("render")};var la;ht.drawSelectionRectangle=function(t,e){var r=this,a=r.cy,n=r.data,i=a.style(),o=t.drawOnlyNodeLayer,s=t.drawAllLayers,u=n.canvasNeedsRedraw,l=t.forcedContext;if(r.showFps||!o&&u[r.SELECT_BOX]&&!s){var c=l||n.contexts[r.SELECT_BOX];if(e(c),r.selection[4]==1&&(r.hoverData.selecting||r.touchData.selecting)){var f=r.cy.zoom(),d=i.core("selection-box-border-width").value/f;c.lineWidth=d,c.fillStyle="rgba("+i.core("selection-box-color").value[0]+","+i.core("selection-box-color").value[1]+","+i.core("selection-box-color").value[2]+","+i.core("selection-box-opacity").value+")",c.fillRect(r.selection[0],r.selection[1],r.selection[2]-r.selection[0],r.selection[3]-r.selection[1]),d>0&&(c.strokeStyle="rgba("+i.core("selection-box-border-color").value[0]+","+i.core("selection-box-border-color").value[1]+","+i.core("selection-box-border-color").value[2]+","+i.core("selection-box-opacity").value+")",c.strokeRect(r.selection[0],r.selection[1],r.selection[2]-r.selection[0],r.selection[3]-r.selection[1]))}if(n.bgActivePosistion&&!r.hoverData.selecting){var f=r.cy.zoom(),g=n.bgActivePosistion;c.fillStyle="rgba("+i.core("active-bg-color").value[0]+","+i.core("active-bg-color").value[1]+","+i.core("active-bg-color").value[2]+","+i.core("active-bg-opacity").value+")",c.beginPath(),c.arc(g.x,g.y,i.core("active-bg-size").pfValue/f,0,2*Math.PI),c.fill()}var h=r.lastRedrawTime;if(r.showFps&&h){h=Math.round(h);var m=Math.round(1e3/h),y="1 frame = "+h+" ms = "+m+" fps";if(c.setTransform(1,0,0,1,0,0),c.fillStyle="rgba(255, 0, 0, 0.75)",c.strokeStyle="rgba(255, 0, 0, 0.75)",c.font="30px Arial",!la){var p=c.measureText(y);la=p.actualBoundingBoxAscent}c.fillText(y,0,la);var b=60;c.strokeRect(0,la+10,250,20),c.fillRect(0,la+10,250*Math.min(m/b,1),20)}s||(u[r.SELECT_BOX]=!1)}};function Oi(t,e,r){var a=t.createShader(e);if(t.shaderSource(a,r),t.compileShader(a),!t.getShaderParameter(a,t.COMPILE_STATUS))throw new Error(t.getShaderInfoLog(a));return a}v(Oi,"compileShader");function gv(t,e,r){var a=Oi(t,t.VERTEX_SHADER,e),n=Oi(t,t.FRAGMENT_SHADER,r),i=t.createProgram();if(t.attachShader(i,a),t.attachShader(i,n),t.linkProgram(i),!t.getProgramParameter(i,t.LINK_STATUS))throw new Error("Could not initialize shaders");return i}v(gv,"createProgram");function pv(t,e,r){r===void 0&&(r=e);var a=t.makeOffscreenCanvas(e,r),n=a.context=a.getContext("2d");return a.clear=function(){return n.clearRect(0,0,a.width,a.height)},a.clear(),a}v(pv,"createTextureCanvas");function Qn(t){var e=t.pixelRatio,r=t.cy.zoom(),a=t.cy.pan();return{zoom:r*e,pan:{x:a.x*e,y:a.y*e}}}v(Qn,"getEffectivePanZoom");function wn(t,e,r,a,n){var i=a*r+e.x,o=n*r+e.y;return o=Math.round(t.canvasHeight-o),[i,o]}v(wn,"modelToRenderedPosition");function pa(t,e,r){var a=t[0]/255,n=t[1]/255,i=t[2]/255,o=e,s=r||new Array(4);return s[0]=a*o,s[1]=n*o,s[2]=i*o,s[3]=o,s}v(pa,"toWebGLColor");function ya(t,e){var r=e||new Array(4);return r[0]=(t>>0&255)/255,r[1]=(t>>8&255)/255,r[2]=(t>>16&255)/255,r[3]=(t>>24&255)/255,r}v(ya,"indexToVec4");function yv(t){return t[0]+(t[1]<<8)+(t[2]<<16)+(t[3]<<24)}v(yv,"vec4ToIndex");function mv(t,e){var r=t.createTexture();return r.buffer=function(a){t.bindTexture(t.TEXTURE_2D,r),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.LINEAR_MIPMAP_NEAREST),t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,a),t.generateMipmap(t.TEXTURE_2D),t.bindTexture(t.TEXTURE_2D,null)},r.deleteTexture=function(){t.deleteTexture(r)},r}v(mv,"createTexture");function wo(t,e){switch(e){case"float":return[1,t.FLOAT,4];case"vec2":return[2,t.FLOAT,4];case"vec3":return[3,t.FLOAT,4];case"vec4":return[4,t.FLOAT,4];case"int":return[1,t.INT,4];case"ivec2":return[2,t.INT,4]}}v(wo,"getTypeInfo");function xo(t,e,r){switch(e){case t.FLOAT:return new Float32Array(r);case t.INT:return new Int32Array(r)}}v(xo,"createTypedArray");function bv(t,e,r,a,n,i){switch(e){case t.FLOAT:return new Float32Array(r.buffer,i*a,n);case t.INT:return new Int32Array(r.buffer,i*a,n)}}v(bv,"createTypedArrayView");function wv(t,e,r,a){var n=wo(t,e),i=We(n,2),o=i[0],s=i[1],u=xo(t,s,a),l=t.createBuffer();return t.bindBuffer(t.ARRAY_BUFFER,l),t.bufferData(t.ARRAY_BUFFER,u,t.STATIC_DRAW),s===t.FLOAT?t.vertexAttribPointer(r,o,s,!1,0,0):s===t.INT&&t.vertexAttribIPointer(r,o,s,0,0),t.enableVertexAttribArray(r),t.bindBuffer(t.ARRAY_BUFFER,null),l}v(wv,"createBufferStaticDraw");function pt(t,e,r,a){var n=wo(t,r),i=We(n,3),o=i[0],s=i[1],u=i[2],l=xo(t,s,e*o),c=o*u,f=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,f),t.bufferData(t.ARRAY_BUFFER,e*c,t.DYNAMIC_DRAW),t.enableVertexAttribArray(a),s===t.FLOAT?t.vertexAttribPointer(a,o,s,!1,c,0):s===t.INT&&t.vertexAttribIPointer(a,o,s,c,0),t.vertexAttribDivisor(a,1),t.bindBuffer(t.ARRAY_BUFFER,null);for(var d=new Array(e),g=0;g<e;g++)d[g]=bv(t,s,l,c,o,g);return f.dataArray=l,f.stride=c,f.size=o,f.getView=function(h){return d[h]},f.setPoint=function(h,m,y){var p=d[h];p[0]=m,p[1]=y},f.bufferSubData=function(h){t.bindBuffer(t.ARRAY_BUFFER,f),h?t.bufferSubData(t.ARRAY_BUFFER,0,l,0,h*o):t.bufferSubData(t.ARRAY_BUFFER,0,l)},f}v(pt,"createBufferDynamicDraw");function xv(t){var e=t.createFramebuffer();t.bindFramebuffer(t.FRAMEBUFFER,e);var r=t.createTexture();return t.bindTexture(t.TEXTURE_2D,r),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,r,0),t.bindFramebuffer(t.FRAMEBUFFER,null),e.setFramebufferAttachmentSizes=function(a,n){t.bindTexture(t.TEXTURE_2D,r),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,a,n,0,t.RGBA,t.UNSIGNED_BYTE,null)},e}v(xv,"createPickingFrameBuffer");var Fs=typeof Float32Array<"u"?Float32Array:Array;Math.hypot||(Math.hypot=function(){for(var t=0,e=arguments.length;e--;)t+=arguments[e]*arguments[e];return Math.sqrt(t)});function $r(){var t=new Fs(9);return Fs!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[5]=0,t[6]=0,t[7]=0),t[0]=1,t[4]=1,t[8]=1,t}v($r,"create");function Eo(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}v(Eo,"identity");function Ev(t,e,r){var a=e[0],n=e[1],i=e[2],o=e[3],s=e[4],u=e[5],l=e[6],c=e[7],f=e[8],d=r[0],g=r[1],h=r[2],m=r[3],y=r[4],p=r[5],b=r[6],w=r[7],x=r[8];return t[0]=d*a+g*o+h*l,t[1]=d*n+g*s+h*c,t[2]=d*i+g*u+h*f,t[3]=m*a+y*o+p*l,t[4]=m*n+y*s+p*c,t[5]=m*i+y*u+p*f,t[6]=b*a+w*o+x*l,t[7]=b*n+w*s+x*c,t[8]=b*i+w*u+x*f,t}v(Ev,"multiply");function La(t,e,r){var a=e[0],n=e[1],i=e[2],o=e[3],s=e[4],u=e[5],l=e[6],c=e[7],f=e[8],d=r[0],g=r[1];return t[0]=a,t[1]=n,t[2]=i,t[3]=o,t[4]=s,t[5]=u,t[6]=d*a+g*o+l,t[7]=d*n+g*s+c,t[8]=d*i+g*u+f,t}v(La,"translate");function Co(t,e,r){var a=e[0],n=e[1],i=e[2],o=e[3],s=e[4],u=e[5],l=e[6],c=e[7],f=e[8],d=Math.sin(r),g=Math.cos(r);return t[0]=g*a+d*o,t[1]=g*n+d*s,t[2]=g*i+d*u,t[3]=g*o-d*a,t[4]=g*s-d*n,t[5]=g*u-d*i,t[6]=l,t[7]=c,t[8]=f,t}v(Co,"rotate");function Jn(t,e,r){var a=r[0],n=r[1];return t[0]=a*e[0],t[1]=a*e[1],t[2]=a*e[2],t[3]=n*e[3],t[4]=n*e[4],t[5]=n*e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t}v(Jn,"scale");function Cv(t,e,r){return t[0]=2/e,t[1]=0,t[2]=0,t[3]=0,t[4]=-2/r,t[5]=0,t[6]=-1,t[7]=1,t[8]=1,t}v(Cv,"projection");var wa={SCREEN:{name:"screen",screen:!0},PICKING:{name:"picking",picking:!0}},ua=et({getKey:null,drawElement:null,getBoundingBox:null,getRotation:null,getRotationPoint:null,getRotationOffset:null,isVisible:null,getPadding:null}),cy=(function(){function t(e,r){Xt(this,t),this.debugID=Math.floor(Math.random()*1e4),this.r=e,this.atlasSize=r.webglTexSize,this.rows=r.webglTexRows,this.enableWrapping=r.enableWrapping,this.texHeight=Math.floor(this.atlasSize/this.rows),this.maxTexWidth=this.atlasSize,this.texture=null,this.canvas=null,this.needsBuffer=!0,this.freePointer={x:0,row:0},this.keyToLocation=new Map,this.canvas=r.createTextureCanvas(e,this.atlasSize,this.atlasSize),this.scratch=r.createTextureCanvas(e,this.atlasSize,this.texHeight,"scratch")}return v(t,"Atlas"),Yt(t,[{key:"getKeys",value:v(function(){return new Set(this.keyToLocation.keys())},"getKeys")},{key:"getScale",value:v(function(r){var a=r.w,n=r.h,i=this.texHeight,o=this.maxTexWidth,s=i/n,u=a*s,l=n*s;return u>o&&(s=o/a,u=a*s,l=n*s),{scale:s,texW:u,texH:l}},"getScale")},{key:"draw",value:v(function(r,a,n){var i=this,o=this.atlasSize,s=this.rows,u=this.texHeight,l=this.getScale(a),c=l.scale,f=l.texW,d=l.texH,g=[null,null],h=v(function(w,x){if(n&&x){var S=x.context,C=w.x,E=w.row,D=C,T=u*E;S.save(),S.translate(D,T),S.scale(c,c),n(S,a),S.restore()}},"drawAt"),m=v(function(){h(i.freePointer,i.canvas),g[0]={x:i.freePointer.x,y:i.freePointer.row*u,w:f,h:d},g[1]={x:i.freePointer.x+f,y:i.freePointer.row*u,w:0,h:d},i.freePointer.x+=f,i.freePointer.x==o&&(i.freePointer.x=0,i.freePointer.row++)},"drawNormal"),y=v(function(){var w=i.scratch,x=i.canvas;w.clear(),h({x:0,row:0},w);var S=o-i.freePointer.x,C=f-S,E=u;{var D=i.freePointer.x,T=i.freePointer.row*u,A=S;x.context.drawImage(w,0,0,A,E,D,T,A,E),g[0]={x:D,y:T,w:A,h:d}}{var B=S,k=(i.freePointer.row+1)*u,L=C;x&&x.context.drawImage(w,B,0,L,E,0,k,L,E),g[1]={x:0,y:k,w:L,h:d}}i.freePointer.x=C,i.freePointer.row++},"drawWrapped"),p=v(function(){i.freePointer.x=0,i.freePointer.row++},"moveToStartOfNextRow");if(this.freePointer.x+f<=o)m();else{if(this.freePointer.row>=s-1)return!1;this.freePointer.x===o?(p(),m()):this.enableWrapping?y():(p(),m())}return this.keyToLocation.set(r,g),this.needsBuffer=!0,g},"draw")},{key:"getOffsets",value:v(function(r){return this.keyToLocation.get(r)},"getOffsets")},{key:"isEmpty",value:v(function(){return this.freePointer.x===0&&this.freePointer.row===0},"isEmpty")},{key:"canFit",value:v(function(r){var a=this.atlasSize,n=this.rows,i=this.getScale(r),o=i.texW;return this.freePointer.x+o>a?this.freePointer.row<n-1:!0},"canFit")},{key:"bufferIfNeeded",value:v(function(r){this.texture||(this.texture=mv(r,this.debugID)),this.needsBuffer&&(this.texture.buffer(this.canvas),this.needsBuffer=!1)},"bufferIfNeeded")},{key:"dispose",value:v(function(){this.texture&&(this.texture.deleteTexture(),this.texture=null,this.needsBuffer=!0)},"dispose")}]),t})(),fy=(function(){function t(e,r){Xt(this,t),this.r=e,this.opts=r,this.keyToIds=new Map,this.idToKey=new Map,this.atlases=[],this.styleKeyToAtlas=new Map,this.styleKeyNeedsRedraw=new Set,this.forceGC=!1}return v(t,"AtlasCollection"),Yt(t,[{key:"getKeys",value:v(function(){return new Set(this.styleKeyToAtlas.keys())},"getKeys")},{key:"getIdsFor",value:v(function(r){var a=this.keyToIds.get(r);return a||(a=new Set,this.keyToIds.set(r,a)),a},"getIdsFor")},{key:"_createAtlas",value:v(function(){var r=this.r,a=this.opts;return new cy(r,a)},"_createAtlas")},{key:"_getScratchCanvas",value:v(function(){if(!this.scratch){var r=this.r,a=this.opts,n=a.webglTexSize,i=Math.floor(n/a.webglTexRows);this.scratch=a.createTextureCanvas(r,n,i,"scratch")}return this.scratch},"_getScratchCanvas")},{key:"draw",value:v(function(r,a,n,i){if(this.styleKeyNeedsRedraw.has(a)){this.styleKeyNeedsRedraw.delete(a),this.deleteKey(r,a);var o=this.styleKeyToAtlas.get(a);o&&(o.forceGC=!0),this.styleKeyToAtlas.delete(a)}var s=this.styleKeyToAtlas.get(a);return s||(s=this.atlases[this.atlases.length-1],(!s||!s.canFit(n))&&(s=this._createAtlas(),this.atlases.push(s)),s.draw(a,n,i),this.styleKeyToAtlas.set(a,s),this.getIdsFor(a).add(r),this.idToKey.set(r,a)),s},"draw")},{key:"getAtlas",value:v(function(r){return this.styleKeyToAtlas.get(r)},"getAtlas")},{key:"hasAtlas",value:v(function(r){return this.styleKeyToAtlas.has(r)},"hasAtlas")},{key:"deleteKey",value:v(function(r,a){this.idToKey.delete(r),this.getIdsFor(a).delete(r)},"deleteKey")},{key:"checkKeyIsInvalid",value:v(function(r,a){if(!this.idToKey.has(r))return!1;var n=this.idToKey.get(r);return n!=a?(this.deleteKey(r,n),!0):!1},"checkKeyIsInvalid")},{key:"_getKeysToCollect",value:v(function(){var r=new Set,a=mt(this.styleKeyToAtlas.keys()),n;try{for(a.s();!(n=a.n()).done;){var i=n.value;this.getIdsFor(i).size==0&&r.add(i)}}catch(o){a.e(o)}finally{a.f()}return r},"_getKeysToCollect")},{key:"gc",value:v(function(){var r=this,a=this.atlases.some(function(d){return d.forceGC}),n=this._getKeysToCollect();if(n.size===0&&!a){console.log("nothing to garbage collect");return}var i=[],o=new Map,s=null,u=mt(this.atlases),l;try{var c=v(function(){var g=l.value,h=g.getKeys(),m=Tv(n,h);if(m.size===0&&!g.forceGC)return i.push(g),h.forEach(function(E){return o.set(E,g)}),"continue";s||(s=r._createAtlas(),i.push(s));var y=mt(h),p;try{for(y.s();!(p=y.n()).done;){var b=p.value;if(!m.has(b)){var w=g.getOffsets(b),x=We(w,2),S=x[0],C=x[1];s.canFit({w:S.w+C.w,h:S.h})||(s=r._createAtlas(),i.push(s)),r._copyTextureToNewAtlas(b,g,s),o.set(b,s)}}}catch(E){y.e(E)}finally{y.f()}},"_loop");for(u.s();!(l=u.n()).done;)var f=c()}catch(d){u.e(d)}finally{u.f()}this.atlases=i,this.styleKeyToAtlas=o},"gc")},{key:"_copyTextureToNewAtlas",value:v(function(r,a,n){var i=a.getOffsets(r),o=We(i,2),s=o[0],u=o[1];if(u.w===0)n.draw(r,s,function(d){d.drawImage(a.canvas,s.x,s.y,s.w,s.h,0,0,s.w,s.h)});else{var l=this._getScratchCanvas();l.clear(),l.context.drawImage(a.canvas,s.x,s.y,s.w,s.h,0,0,s.w,s.h),l.context.drawImage(a.canvas,u.x,u.y,u.w,u.h,s.w,0,u.w,u.h);var c=s.w+u.w,f=s.h;n.draw(r,{w:c,h:f},function(d){d.drawImage(l,0,0,c,f,0,0,c,f)})}},"_copyTextureToNewAtlas")},{key:"getCounts",value:v(function(){return{keyCount:this.styleKeyToAtlas.size,atlasCount:new Set(this.styleKeyToAtlas.values()).size}},"getCounts")}]),t})();function Tv(t,e){return t.intersection?t.intersection(e):new Set(Gi(t).filter(function(r){return e.has(r)}))}v(Tv,"intersection");var dy=(function(){function t(e,r){Xt(this,t),this.r=e;var a=r;this.globalOptions=a,this.maxAtlases=a.webglTexPerBatch,this.atlasSize=a.webglTexSize,this.renderTypes=new Map,this.maxAtlasesPerBatch=r.webglTexPerBatch,this.batchAtlases=[],this._cacheScratchCanvas(a)}return v(t,"AtlasManager"),Yt(t,[{key:"_cacheScratchCanvas",value:v(function(r){var a=-1,n=-1,i=null,o=r.createTextureCanvas;r.createTextureCanvas=function(s,u,l,c){return c?((!i||u!=a||l!=n)&&(a=u,n=l,i=o(s,u,l)),i):o(s,u,l)}},"_cacheScratchCanvas")},{key:"addRenderType",value:v(function(r,a){var n=new fy(this.r,this.globalOptions),i=a;this.renderTypes.set(r,he({type:r,atlasCollection:n},i))},"addRenderType")},{key:"getRenderTypes",value:v(function(){return Gi(this.renderTypes.values())},"getRenderTypes")},{key:"getRenderTypeOpts",value:v(function(r){return this.renderTypes.get(r)},"getRenderTypeOpts")},{key:"invalidate",value:v(function(r){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=a.forceRedraw,i=n===void 0?!1:n,o=a.filterEle,s=o===void 0?function(){return!0}:o,u=a.filterType,l=u===void 0?function(){return!0}:u,c=!1,f=mt(r),d;try{for(f.s();!(d=f.n()).done;){var g=d.value;if(s(g)){var h=g.id(),m=mt(this.getRenderTypes()),y;try{for(m.s();!(y=m.n()).done;){var p=y.value;if(l(p.type)){var b=p.getKey(g);i?(p.atlasCollection.deleteKey(h,b),p.atlasCollection.styleKeyNeedsRedraw.add(b),c=!0):c|=p.atlasCollection.checkKeyIsInvalid(h,b)}}}catch(w){m.e(w)}finally{m.f()}}}}catch(w){f.e(w)}finally{f.f()}return c},"invalidate")},{key:"gc",value:v(function(){var r=mt(this.getRenderTypes()),a;try{for(r.s();!(a=r.n()).done;){var n=a.value;n.atlasCollection.gc()}}catch(i){r.e(i)}finally{r.f()}},"gc")},{key:"isRenderable",value:v(function(r,a){var n=this.getRenderTypeOpts(a);return n&&n.isVisible(r)},"isRenderable")},{key:"startBatch",value:v(function(){this.batchAtlases=[]},"startBatch")},{key:"getAtlasCount",value:v(function(){return this.batchAtlases.length},"getAtlasCount")},{key:"getAtlases",value:v(function(){return this.batchAtlases},"getAtlases")},{key:"getOrCreateAtlas",value:v(function(r,a,n){var i=this.renderTypes.get(n),o=i.getKey(r),s=r.id();return i.atlasCollection.draw(s,o,a,function(u){i.drawElement(u,r,a,!0,!0)})},"getOrCreateAtlas")},{key:"getAtlasIndexForBatch",value:v(function(r){var a=this.batchAtlases.indexOf(r);if(a<0){if(this.batchAtlases.length===this.maxAtlasesPerBatch)return;this.batchAtlases.push(r),a=this.batchAtlases.length-1}return a},"getAtlasIndexForBatch")},{key:"getIndexArray",value:v(function(){return Array.from({length:this.maxAtlases},function(r,a){return a})},"getIndexArray")},{key:"getAtlasInfo",value:v(function(r,a){var n=this.renderTypes.get(a),i=n.getBoundingBox(r),o=this.getOrCreateAtlas(r,i,a),s=this.getAtlasIndexForBatch(o);if(s!==void 0){var u=n.getKey(r),l=o.getOffsets(u),c=We(l,2),f=c[0],d=c[1];return{atlasID:s,tex:f,tex1:f,tex2:d,bb:i,type:a,styleKey:u}}},"getAtlasInfo")},{key:"canAddToCurrentBatch",value:v(function(r,a){if(this.batchAtlases.length===this.maxAtlasesPerBatch){var n=this.renderTypes.get(a),i=n.getKey(r),o=n.atlasCollection.getAtlas(i);return o&&this.batchAtlases.includes(o)}return!0},"canAddToCurrentBatch")},{key:"setTransformMatrix",value:v(function(r,a,n){var i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,o=a.bb,s=a.type,u=a.tex1,l=a.tex2,c=this.getRenderTypeOpts(s),f=c.getPadding?c.getPadding(n):0,d=u.w/(u.w+l.w);i||(d=1-d);var g=this.getAdjustedBB(o,f,i,d),h,m;Eo(r);var y=c.getRotation?c.getRotation(n):0;if(y!==0){var p=c.getRotationPoint(n),b=p.x,w=p.y;La(r,r,[b,w]),Co(r,r,y);var x=c.getRotationOffset(n);h=x.x+g.xOffset,m=x.y}else h=g.x1,m=g.y1;La(r,r,[h,m]),Jn(r,r,[g.w,g.h])},"setTransformMatrix")},{key:"getTransformMatrix",value:v(function(r,a){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,i=$r();return this.setTransformMatrix(i,r,a,n),i},"getTransformMatrix")},{key:"getAdjustedBB",value:v(function(r,a,n,i){var o=r.x1,s=r.y1,u=r.w,l=r.h;a&&(o-=a,s-=a,u+=2*a,l+=2*a);var c=0,f=u*i;return n&&i<1?u=f:!n&&i<1&&(c=u-f,o+=c,u=f),{x1:o,y1:s,w:u,h:l,xOffset:c}},"getAdjustedBB")},{key:"getDebugInfo",value:v(function(){var r=[],a=mt(this.renderTypes),n;try{for(a.s();!(n=a.n()).done;){var i=We(n.value,2),o=i[0],s=i[1],u=s.atlasCollection.getCounts(),l=u.keyCount,c=u.atlasCount;r.push({type:o,keyCount:l,atlasCount:c})}}catch(f){a.e(f)}finally{a.f()}return r},"getDebugInfo")}]),t})(),ci=0,Ns=1,zs=2,fi=3,hy=(function(){function t(e,r,a){Xt(this,t),this.r=e,this.gl=r,this.maxInstances=a.webglBatchSize,this.maxAtlases=a.webglTexPerBatch,this.atlasSize=a.webglTexSize,this.bgColor=a.bgColor,a.enableWrapping=!0,a.createTextureCanvas=pv,this.atlasManager=new dy(e,a),this.program=this.createShaderProgram(wa.SCREEN),this.pickingProgram=this.createShaderProgram(wa.PICKING),this.vao=this.createVAO(),this.debugInfo=[]}return v(t,"ElementDrawingWebGL"),Yt(t,[{key:"addTextureRenderType",value:v(function(r,a){this.atlasManager.addRenderType(r,a)},"addTextureRenderType")},{key:"invalidate",value:v(function(r){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=a.type,i=this.atlasManager;return n?i.invalidate(r,{filterType:v(function(s){return s===n},"filterType"),forceRedraw:!0}):i.invalidate(r)},"invalidate")},{key:"gc",value:v(function(){this.atlasManager.gc()},"gc")},{key:"createShaderProgram",value:v(function(r){var a=this.gl,n=`#version 300 es
      precision highp float;

      uniform mat3 uPanZoomMatrix;
      uniform int  uAtlasSize;
      
      // instanced
      in vec2 aPosition; 

      // what are we rendering?
      in int aVertType;

      // for picking
      in vec4 aIndex;
      
      // For textures
      in int aAtlasId; // which shader unit/atlas to use
      in vec4 aTex1; // x/y/w/h of texture in atlas
      in vec4 aTex2; 

      // for any transforms that are needed
      in vec4 aScaleRotate1;  // vectors use fewer attributes than matrices
      in vec2 aTranslate1;
      in vec4 aScaleRotate2;
      in vec2 aTranslate2;

      // for edges
      in vec4 aPointAPointB;
      in vec4 aPointCPointD;
      in float aLineWidth;
      in vec4 aEdgeColor;

      out vec2 vTexCoord;
      out vec4 vEdgeColor;
      flat out int vAtlasId;
      flat out vec4 vIndex;
      flat out int vVertType;

      void main(void) {
        int vid = gl_VertexID;
        vec2 position = aPosition;

        if(aVertType == `.concat(ci,`) {
          float texX;
          float texY;
          float texW;
          float texH;
          mat3  texMatrix;

          int vid = gl_VertexID;
          if(vid <= 5) {
            texX = aTex1.x;
            texY = aTex1.y;
            texW = aTex1.z;
            texH = aTex1.w;
            texMatrix = mat3(
              vec3(aScaleRotate1.xy, 0.0),
              vec3(aScaleRotate2.zw, 0.0),
              vec3(aTranslate1,      1.0)
            );
          } else {
            texX = aTex2.x;
            texY = aTex2.y;
            texW = aTex2.z;
            texH = aTex2.w;
            texMatrix = mat3(
              vec3(aScaleRotate2.xy, 0.0),
              vec3(aScaleRotate2.zw, 0.0),
              vec3(aTranslate2,      1.0)
            );
          }

          if(vid == 1 || vid == 2 || vid == 4 || vid == 7 || vid == 8 || vid == 10) {
            texX += texW;
          }
          if(vid == 2 || vid == 4 || vid == 5 || vid == 8 || vid == 10 || vid == 11) {
            texY += texH;
          }

          float d = float(uAtlasSize);
          vTexCoord = vec2(texX / d, texY / d); // tex coords must be between 0 and 1

          gl_Position = vec4(uPanZoomMatrix * texMatrix * vec3(position, 1.0), 1.0);
        } 
        else if(aVertType == `).concat(Ns,` && vid < 6) {
          vec2 source = aPointAPointB.xy;
          vec2 target = aPointAPointB.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          vec2 xBasis = target - source;
          vec2 yBasis = normalize(vec2(-xBasis.y, xBasis.x));
          vec2 point = source + xBasis * position.x + yBasis * aLineWidth * position.y;

          gl_Position = vec4(uPanZoomMatrix * vec3(point, 1.0), 1.0);
          vEdgeColor = aEdgeColor;
        } 
        else if(aVertType == `).concat(zs,` && vid < 6) {
          vec2 pointA = aPointAPointB.xy;
          vec2 pointB = aPointAPointB.zw;
          vec2 pointC = aPointCPointD.xy;
          vec2 pointD = aPointCPointD.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          vec2 p0 = pointA;
          vec2 p1 = pointB;
          vec2 p2 = pointC;
          vec2 pos = position;
          if(position.x == 1.0) {
            p0 = pointD;
            p1 = pointC;
            p2 = pointB;
            pos = vec2(0.0, -position.y);
          }

          vec2 p01 = p1 - p0;
          vec2 p12 = p2 - p1;
          vec2 p21 = p1 - p2;

          // Find the normal vector.
          vec2 tangent = normalize(normalize(p12) + normalize(p01));
          vec2 normal = vec2(-tangent.y, tangent.x);

          // Find the vector perpendicular to p0 -> p1.
          vec2 p01Norm = normalize(vec2(-p01.y, p01.x));

          // Determine the bend direction.
          float sigma = sign(dot(p01 + p21, normal));
          float width = aLineWidth;

          if(sign(pos.y) == -sigma) {
            // This is an intersecting vertex. Adjust the position so that there's no overlap.
            vec2 point = 0.5 * width * normal * -sigma / dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          } else {
            // This is a non-intersecting vertex. Treat it like a mitre join.
            vec2 point = 0.5 * width * normal * sigma * dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          }

          vEdgeColor = aEdgeColor;
        } 
        else if(aVertType == `).concat(fi,` && vid < 3) {
          // massage the first triangle into an edge arrow
          if(vid == 0)
            position = vec2(-0.15, -0.3);
          if(vid == 1)
            position = vec2( 0.0,   0.0);
          if(vid == 2)
            position = vec2( 0.15, -0.3);

          mat3 transform = mat3(
            vec3(aScaleRotate1.xy, 0.0),
            vec3(aScaleRotate1.zw, 0.0),
            vec3(aTranslate1,      1.0)
          );
          gl_Position = vec4(uPanZoomMatrix * transform * vec3(position, 1.0), 1.0);
          vEdgeColor = aEdgeColor;
        } else {
          gl_Position = vec4(2.0, 0.0, 0.0, 1.0); // discard vertex by putting it outside webgl clip space
        }

        vAtlasId = aAtlasId;
        vIndex = aIndex;
        vVertType = aVertType;
      }
    `),i=this.atlasManager.getIndexArray(),o=`#version 300 es
      precision highp float;

      // define texture unit for each node in the batch
      `.concat(i.map(function(l){return"uniform sampler2D uTexture".concat(l,";")}).join(`
	`),`

      uniform vec4 uBGColor;

      in vec2 vTexCoord;
      in vec4 vEdgeColor;
      flat in int vAtlasId;
      flat in vec4 vIndex;
      flat in int vVertType;

      out vec4 outColor;

      void main(void) {
        if(vVertType == `).concat(ci,`) {
          `).concat(i.map(function(l){return"if(vAtlasId == ".concat(l,") outColor = texture(uTexture").concat(l,", vTexCoord);")}).join(`
	else `),`
        } else if(vVertType == `).concat(fi,`) {
          // blend arrow color with background (using premultiplied alpha)
          outColor.rgb = vEdgeColor.rgb + (uBGColor.rgb * (1.0 - vEdgeColor.a)); 
          outColor.a = 1.0; // make opaque, masks out line under arrow
        } else {
          outColor = vEdgeColor;
        }

        `).concat(r.picking?`if(outColor.a == 0.0) discard;
             else outColor = vIndex;`:"",`
      }
    `),s=gv(a,n,o);s.aPosition=a.getAttribLocation(s,"aPosition"),s.aIndex=a.getAttribLocation(s,"aIndex"),s.aVertType=a.getAttribLocation(s,"aVertType"),s.aAtlasId=a.getAttribLocation(s,"aAtlasId"),s.aTex1=a.getAttribLocation(s,"aTex1"),s.aTex2=a.getAttribLocation(s,"aTex2"),s.aScaleRotate1=a.getAttribLocation(s,"aScaleRotate1"),s.aTranslate1=a.getAttribLocation(s,"aTranslate1"),s.aScaleRotate2=a.getAttribLocation(s,"aScaleRotate2"),s.aTranslate2=a.getAttribLocation(s,"aTranslate2"),s.aPointAPointB=a.getAttribLocation(s,"aPointAPointB"),s.aPointCPointD=a.getAttribLocation(s,"aPointCPointD"),s.aLineWidth=a.getAttribLocation(s,"aLineWidth"),s.aEdgeColor=a.getAttribLocation(s,"aEdgeColor"),s.uPanZoomMatrix=a.getUniformLocation(s,"uPanZoomMatrix"),s.uAtlasSize=a.getUniformLocation(s,"uAtlasSize"),s.uBGColor=a.getUniformLocation(s,"uBGColor"),s.uTextures=[];for(var u=0;u<this.atlasManager.maxAtlases;u++)s.uTextures.push(a.getUniformLocation(s,"uTexture".concat(u)));return s},"createShaderProgram")},{key:"createVAO",value:v(function(){var r=[0,0,1,0,1,1,0,0,1,1,0,1],a=[].concat(r,r);this.vertexCount=a.length/2;var n=this.maxInstances,i=this.gl,o=this.program,s=i.createVertexArray();return i.bindVertexArray(s),wv(i,"vec2",o.aPosition,a),this.indexBuffer=pt(i,n,"vec4",o.aIndex),this.vertTypeBuffer=pt(i,n,"int",o.aVertType),this.atlasIdBuffer=pt(i,n,"int",o.aAtlasId),this.tex1Buffer=pt(i,n,"vec4",o.aTex1),this.tex2Buffer=pt(i,n,"vec4",o.aTex2),this.scaleRotate1Buffer=pt(i,n,"vec4",o.aScaleRotate1),this.translate1Buffer=pt(i,n,"vec2",o.aTranslate1),this.scaleRotate2Buffer=pt(i,n,"vec4",o.aScaleRotate2),this.translate2Buffer=pt(i,n,"vec2",o.aTranslate2),this.pointAPointBBuffer=pt(i,n,"vec4",o.aPointAPointB),this.pointCPointDBuffer=pt(i,n,"vec4",o.aPointCPointD),this.lineWidthBuffer=pt(i,n,"float",o.aLineWidth),this.edgeColorBuffer=pt(i,n,"vec4",o.aEdgeColor),i.bindVertexArray(null),s},"createVAO")},{key:"buffers",get:v(function(){var r=this;return this._buffers||(this._buffers=Object.keys(this).filter(function(a){return a.endsWith("Buffer")}).map(function(a){return r[a]})),this._buffers},"get")},{key:"startFrame",value:v(function(r,a){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:wa.SCREEN;this.panZoomMatrix=r,this.debugInfo=a,this.renderTarget=n,this.startBatch()},"startFrame")},{key:"startBatch",value:v(function(){this.instanceCount=0,this.atlasManager.startBatch()},"startBatch")},{key:"endFrame",value:v(function(){this.endBatch()},"endFrame")},{key:"getTempMatrix",value:v(function(){return this.tempMatrix=this.tempMatrix||$r()},"getTempMatrix")},{key:"drawTexture",value:v(function(r,a,n){var i=this.atlasManager;if(i.isRenderable(r,n)){i.canAddToCurrentBatch(r,n)||this.endBatch();var o=this.instanceCount;this.vertTypeBuffer.getView(o)[0]=ci;var s=this.indexBuffer.getView(o);ya(a,s);var u=i.getAtlasInfo(r,n,u),l=u.atlasID,c=u.tex1,f=u.tex2,d=this.atlasIdBuffer.getView(o);d[0]=l;var g=this.tex1Buffer.getView(o);g[0]=c.x,g[1]=c.y,g[2]=c.w,g[3]=c.h;var h=this.tex2Buffer.getView(o);h[0]=f.x,h[1]=f.y,h[2]=f.w,h[3]=f.h;for(var m=this.getTempMatrix(),y=0,p=[1,2];y<p.length;y++){var b=p[y];i.setTransformMatrix(m,u,r,b===1);var w=this["scaleRotate".concat(b,"Buffer")].getView(o);w[0]=m[0],w[1]=m[1],w[2]=m[3],w[3]=m[4];var x=this["translate".concat(b,"Buffer")].getView(o);x[0]=m[6],x[1]=m[7]}this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}},"drawTexture")},{key:"drawEdgeArrow",value:v(function(r,a,n){var i=r._private.rscratch,o,s,u;if(n==="source"?(o=i.arrowStartX,s=i.arrowStartY,u=i.srcArrowAngle):(o=i.arrowEndX,s=i.arrowEndY,u=i.tgtArrowAngle),!(isNaN(o)||o==null||isNaN(s)||s==null||isNaN(u)||u==null)){var l=r.pstyle(n+"-arrow-shape").value;if(l!=="none"){var c=r.pstyle(n+"-arrow-color").value,f=r.pstyle("opacity").value,d=r.pstyle("line-opacity").value,g=f*d,h=r.pstyle("width").pfValue,m=r.pstyle("arrow-scale").value,y=this.r.getArrowWidth(h,m),p=this.getTempMatrix();Eo(p),La(p,p,[o,s]),Jn(p,p,[y,y]),Co(p,p,u);var b=this.instanceCount;this.vertTypeBuffer.getView(b)[0]=fi;var w=this.indexBuffer.getView(b);ya(a,w);var x=this.edgeColorBuffer.getView(b);pa(c,g,x);var S=this.scaleRotate1Buffer.getView(b);S[0]=p[0],S[1]=p[1],S[2]=p[3],S[3]=p[4];var C=this.translate1Buffer.getView(b);C[0]=p[6],C[1]=p[7],this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}},"drawEdgeArrow")},{key:"drawEdgeLine",value:v(function(r,a){var n=r.pstyle("opacity").value,i=r.pstyle("line-opacity").value,o=r.pstyle("width").pfValue,s=r.pstyle("line-color").value,u=n*i,l=this.getEdgePoints(r);if(l.length/2+this.instanceCount>this.maxInstances&&this.endBatch(),l.length==4){var c=this.instanceCount;this.vertTypeBuffer.getView(c)[0]=Ns;var f=this.indexBuffer.getView(c);ya(a,f);var d=this.edgeColorBuffer.getView(c);pa(s,u,d);var g=this.lineWidthBuffer.getView(c);g[0]=o;var h=this.pointAPointBBuffer.getView(c);h[0]=l[0],h[1]=l[1],h[2]=l[2],h[3]=l[3],this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}else for(var m=0;m<l.length-2;m+=2){var y=this.instanceCount;this.vertTypeBuffer.getView(y)[0]=zs;var p=this.indexBuffer.getView(y);ya(a,p);var b=this.edgeColorBuffer.getView(y);pa(s,u,b);var w=this.lineWidthBuffer.getView(y);w[0]=o;var x=l[m-2],S=l[m-1],C=l[m],E=l[m+1],D=l[m+2],T=l[m+3],A=l[m+4],B=l[m+5];m==0&&(x=2*C-D+.001,S=2*E-T+.001),m==l.length-4&&(A=2*D-C+.001,B=2*T-E+.001);var k=this.pointAPointBBuffer.getView(y);k[0]=x,k[1]=S,k[2]=C,k[3]=E;var L=this.pointCPointDBuffer.getView(y);L[0]=D,L[1]=T,L[2]=A,L[3]=B,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}},"drawEdgeLine")},{key:"getEdgePoints",value:v(function(r){var a=r._private.rscratch,n=a.allpts;if(n.length==4)return n;var i=this.getNumSegments(r);return this.getCurveSegmentPoints(n,i)},"getEdgePoints")},{key:"getNumSegments",value:v(function(r){var a=15;return Math.min(Math.max(a,5),this.maxInstances)},"getNumSegments")},{key:"getCurveSegmentPoints",value:v(function(r,a){if(r.length==4)return r;for(var n=Array((a+1)*2),i=0;i<=a;i++)if(i==0)n[0]=r[0],n[1]=r[1];else if(i==a)n[i*2]=r[r.length-2],n[i*2+1]=r[r.length-1];else{var o=i/a;this.setCurvePoint(r,o,n,i*2)}return n},"getCurveSegmentPoints")},{key:"setCurvePoint",value:v(function(r,a,n,i){if(r.length<=2)n[i]=r[0],n[i+1]=r[1];else{for(var o=Array(r.length-2),s=0;s<o.length;s+=2){var u=(1-a)*r[s]+a*r[s+2],l=(1-a)*r[s+1]+a*r[s+3];o[s]=u,o[s+1]=l}return this.setCurvePoint(o,a,n,i)}},"setCurvePoint")},{key:"endBatch",value:v(function(){var r=this.gl,a=this.vao,n=this.vertexCount,i=this.instanceCount;if(i!==0){var o=this.renderTarget.picking?this.pickingProgram:this.program;r.useProgram(o),r.bindVertexArray(a);var s=mt(this.buffers),u;try{for(s.s();!(u=s.n()).done;){var l=u.value;l.bufferSubData(i)}}catch(h){s.e(h)}finally{s.f()}for(var c=this.atlasManager.getAtlases(),f=0;f<c.length;f++)c[f].bufferIfNeeded(r);for(var d=0;d<c.length;d++)r.activeTexture(r.TEXTURE0+d),r.bindTexture(r.TEXTURE_2D,c[d].texture),r.uniform1i(o.uTextures[d],d);r.uniformMatrix3fv(o.uPanZoomMatrix,!1,this.panZoomMatrix),r.uniform1i(o.uAtlasSize,this.atlasManager.atlasSize);var g=pa(this.bgColor,1);r.uniform4fv(o.uBGColor,g),r.drawArraysInstanced(r.TRIANGLES,0,n,i),r.bindVertexArray(null),r.bindTexture(r.TEXTURE_2D,null),this.debugInfo&&this.debugInfo.push({count:i,atlasCount:c.length}),this.startBatch()}},"endBatch")},{key:"getDebugInfo",value:v(function(){return this.debugInfo},"getDebugInfo")},{key:"getAtlasDebugInfo",value:v(function(){return this.atlasManager.getDebugInfo()},"getAtlasDebugInfo")}]),t})();function Fi(t,e){return"rgba(".concat(t[0],", ").concat(t[1],", ").concat(t[2],", ").concat(e,")")}v(Fi,"fillStyle");var gy=(function(){function t(e){Xt(this,t),this.r=e}return v(t,"OverlayUnderlayRenderer"),Yt(t,[{key:"getStyleKey",value:v(function(r,a){var n=this.getStyle(r,a),i=n.shape,o=n.opacity,s=n.color;if(!i)return null;var u=a.width(),l=a.height(),c=Fi(s,o);return ir("".concat(i,"-").concat(u,"-").concat(l,"-").concat(c))},"getStyleKey")},{key:"isVisible",value:v(function(r,a){var n=a.pstyle("".concat(r,"-opacity")).value;return n>0},"isVisible")},{key:"getStyle",value:v(function(r,a){var n=a.pstyle("".concat(r,"-opacity")).value,i=a.pstyle("".concat(r,"-color")).value,o=a.pstyle("".concat(r,"-shape")).value;return{opacity:n,color:i,shape:o}},"getStyle")},{key:"getPadding",value:v(function(r,a){return a.pstyle("".concat(r,"-padding")).pfValue},"getPadding")},{key:"draw",value:v(function(r,a,n,i){if(this.isVisible(r,n)){var o=this.r,s=i.w,u=i.h,l=s/2,c=u/2,f=this.getStyle(r,n),d=f.shape,g=f.color,h=f.opacity;a.save(),a.fillStyle=Fi(g,h),d==="round-rectangle"||d==="roundrectangle"?o.drawRoundRectanglePath(a,l,c,s,u,"auto"):d==="ellipse"&&o.drawEllipsePath(a,l,c,s,u),a.fill(),a.restore()}},"draw")}]),t})();function Sv(t){var e=t&&t.style&&t.style.backgroundColor||"white";return Js(e)}v(Sv,"getBGColor");var Dv={};Dv.initWebgl=function(t,e){var r=this,a=r.data.contexts[r.WEBGL],n=t.cy.container();t.bgColor=Sv(n),t.webglTexSize=Math.min(t.webglTexSize,a.getParameter(a.MAX_TEXTURE_SIZE)),t.webglTexRows=Math.min(t.webglTexRows,54),t.webglBatchSize=Math.min(t.webglBatchSize,16384),t.webglTexPerBatch=Math.min(t.webglTexPerBatch,a.getParameter(a.MAX_TEXTURE_IMAGE_UNITS)),r.webglDebug=t.webglDebug,r.webglDebugShowAtlases=t.webglDebugShowAtlases,console.log("max texture units",a.getParameter(a.MAX_TEXTURE_IMAGE_UNITS)),console.log("max texture size",a.getParameter(a.MAX_TEXTURE_SIZE)),console.log("webgl options",t),r.pickingFrameBuffer=xv(a),r.pickingFrameBuffer.needsDraw=!0;var i=v(function(c){return r.getTextAngle(c,null)},"getLabelRotation"),o=v(function(c){var f=c.pstyle("label");return f&&f.value},"isLabelVisible");r.eleDrawing=new hy(r,a,t);var s=new gy(r);r.eleDrawing.addTextureRenderType("node-body",ua({getKey:e.getStyleKey,getBoundingBox:e.getElementBox,drawElement:e.drawElement,isVisible:v(function(c){return c.visible()},"isVisible")})),r.eleDrawing.addTextureRenderType("node-label",ua({getKey:e.getLabelKey,getBoundingBox:e.getLabelBox,drawElement:e.drawLabel,getRotation:i,getRotationPoint:e.getLabelRotationPoint,getRotationOffset:e.getLabelRotationOffset,isVisible:o})),r.eleDrawing.addTextureRenderType("node-overlay",ua({getBoundingBox:e.getElementBox,getKey:v(function(c){return s.getStyleKey("overlay",c)},"getKey"),drawElement:v(function(c,f,d){return s.draw("overlay",c,f,d)},"drawElement"),isVisible:v(function(c){return s.isVisible("overlay",c)},"isVisible"),getPadding:v(function(c){return s.getPadding("overlay",c)},"getPadding")})),r.eleDrawing.addTextureRenderType("node-underlay",ua({getBoundingBox:e.getElementBox,getKey:v(function(c){return s.getStyleKey("underlay",c)},"getKey"),drawElement:v(function(c,f,d){return s.draw("underlay",c,f,d)},"drawElement"),isVisible:v(function(c){return s.isVisible("underlay",c)},"isVisible"),getPadding:v(function(c){return s.getPadding("underlay",c)},"getPadding")})),r.eleDrawing.addTextureRenderType("edge-label",ua({getKey:e.getLabelKey,getBoundingBox:e.getLabelBox,drawElement:e.drawLabel,getRotation:i,getRotationPoint:e.getLabelRotationPoint,getRotationOffset:e.getLabelRotationOffset,isVisible:o}));var u=Fa(function(){console.log("garbage collect flag set"),r.data.gc=!0},1e4);r.onUpdateEleCalcs(function(l,c){var f=!1;c&&c.length>0&&(f|=r.eleDrawing.invalidate(c)),f&&u()}),Pv(r)};function Pv(t){{var e=t.render;t.render=function(i){i=i||{};var o=t.cy;t.webgl&&(o.zoom()>ov?(kv(t),e.call(t,i)):(Bv(t),So(t,i,wa.SCREEN)))}}{var r=t.matchCanvasSize;t.matchCanvasSize=function(i){r.call(t,i),t.pickingFrameBuffer.setFramebufferAttachmentSizes(t.canvasWidth,t.canvasHeight),t.pickingFrameBuffer.needsDraw=!0}}t.findNearestElements=function(i,o,s,u){return Ov(t,i,o)};{var a=t.invalidateCachedZSortedEles;t.invalidateCachedZSortedEles=function(){a.call(t),t.pickingFrameBuffer.needsDraw=!0}}{var n=t.notify;t.notify=function(i,o){n.call(t,i,o),i==="viewport"||i==="bounds"?t.pickingFrameBuffer.needsDraw=!0:i==="background"&&t.eleDrawing.invalidate(o,{type:"node-body"})}}}v(Pv,"overrideCanvasRendererFunctions");function kv(t){var e=t.data.contexts[t.WEBGL];e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT)}v(kv,"clearWebgl");function Bv(t){var e=v(function(a){a.save(),a.setTransform(1,0,0,1,0,0),a.clearRect(0,0,t.canvasWidth,t.canvasHeight),a.restore()},"clear");e(t.data.contexts[t.NODE]),e(t.data.contexts[t.DRAG])}v(Bv,"clearCanvas");function Av(t){var e=t.canvasWidth,r=t.canvasHeight,a=Qn(t),n=a.pan,i=a.zoom,o=$r();La(o,o,[n.x,n.y]),Jn(o,o,[i,i]);var s=$r();Cv(s,e,r);var u=$r();return Ev(u,s,o),u}v(Av,"createPanZoomMatrix");function To(t,e){var r=t.canvasWidth,a=t.canvasHeight,n=Qn(t),i=n.pan,o=n.zoom;e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,r,a),e.translate(i.x,i.y),e.scale(o,o)}v(To,"setContextTransform");function Rv(t,e){t.drawSelectionRectangle(e,function(r){return To(t,r)})}v(Rv,"drawSelectionRectangle");function Lv(t){var e=t.data.contexts[t.NODE];e.save(),To(t,e),e.strokeStyle="rgba(0, 0, 0, 0.3)",e.beginPath(),e.moveTo(-1e3,0),e.lineTo(1e3,0),e.stroke(),e.beginPath(),e.moveTo(0,-1e3),e.lineTo(0,1e3),e.stroke(),e.restore()}v(Lv,"drawAxes");function Iv(t){var e=v(function(n,i,o){for(var s=n.atlasManager.getRenderTypeOpts(i),u=t.data.contexts[t.NODE],l=.125,c=s.atlasCollection.atlases,f=0;f<c.length;f++){var d=c[f],g=d.canvas,h=g.width,m=g.height,y=h*f,p=g.height*o;u.save(),u.scale(l,l),u.drawImage(g,y,p),u.strokeStyle="black",u.rect(y,p,h,m),u.stroke(),u.restore()}},"draw"),r=0;e(t.eleDrawing,"node-body",r++),e(t.eleDrawing,"node-label",r++)}v(Iv,"drawAtlases");function Mv(t,e,r,a,n){var i,o,s,u,l=Qn(t),c=l.pan,f=l.zoom;if(a===void 0||n===void 0){var d=wn(t,c,f,e,r),g=We(d,2),h=g[0],m=g[1],y=6;i=h-y/2,o=m-y/2,s=y,u=y}else{var p=wn(t,c,f,e,r),b=We(p,2),w=b[0],x=b[1],S=wn(t,c,f,a,n),C=We(S,2),E=C[0],D=C[1];i=w,o=D,s=Math.abs(E-w),u=Math.abs(D-x)}if(s===0||u===0)return[];var T=t.data.contexts[t.WEBGL];T.bindFramebuffer(T.FRAMEBUFFER,t.pickingFrameBuffer),t.pickingFrameBuffer.needsDraw&&(T.viewport(0,0,T.canvas.width,T.canvas.height),So(t,null,wa.PICKING),t.pickingFrameBuffer.needsDraw=!1);var A=s*u,B=new Uint8Array(A*4);T.readPixels(i,o,s,u,T.RGBA,T.UNSIGNED_BYTE,B),T.bindFramebuffer(T.FRAMEBUFFER,null);for(var k=new Set,L=0;L<A;L++){var R=B.slice(L*4,L*4+4),M=yv(R)-1;M>=0&&k.add(M)}return k}v(Mv,"getPickingIndexes");function Ov(t,e,r){var a=Mv(t,e,r),n=t.getCachedZSortedEles(),i,o,s=mt(a),u;try{for(s.s();!(u=s.n()).done;){var l=u.value,c=n[l];if(!i&&c.isNode()&&(i=c),!o&&c.isEdge()&&(o=c),i&&o)break}}catch(f){s.e(f)}finally{s.f()}return[i,o].filter(Boolean)}v(Ov,"findNearestElementsWebgl");function So(t,e,r){var a,n;t.webglDebug&&(n=[],a=performance.now());var i=t.eleDrawing,o=0;if(r.screen&&t.data.canvasNeedsRedraw[t.SELECT_BOX]&&Rv(t,e),t.data.canvasNeedsRedraw[t.NODE]||r.picking){var s=v(function(k,L){L+=1,k.isNode()?(i.drawTexture(k,L,"node-underlay"),i.drawTexture(k,L,"node-body"),i.drawTexture(k,L,"node-label"),i.drawTexture(k,L,"node-overlay")):(i.drawEdgeLine(k,L),i.drawEdgeArrow(k,L,"source"),i.drawEdgeArrow(k,L,"target"),i.drawTexture(k,L,"edge-label"))},"draw"),u=t.data.contexts[t.WEBGL];r.screen?(u.clearColor(0,0,0,0),u.enable(u.BLEND),u.blendFunc(u.ONE,u.ONE_MINUS_SRC_ALPHA)):u.disable(u.BLEND),u.clear(u.COLOR_BUFFER_BIT|u.DEPTH_BUFFER_BIT),u.viewport(0,0,u.canvas.width,u.canvas.height);var l=Av(t),c=t.getCachedZSortedEles();if(o=c.length,i.startFrame(l,n,r),r.screen){for(var f=0;f<c.nondrag.length;f++)s(c.nondrag[f],f);for(var d=0;d<c.drag.length;d++)s(c.drag[d],-1)}else if(r.picking)for(var g=0;g<c.length;g++)s(c[g],g);i.endFrame(),t.data.gc&&(console.log("Garbage Collect!"),t.data.gc=!1,i.gc()),r.screen&&t.webglDebugShowAtlases&&(Lv(t),Iv(t)),t.data.canvasNeedsRedraw[t.NODE]=!1,t.data.canvasNeedsRedraw[t.DRAG]=!1}if(t.webglDebug){var h=performance.now(),m=!0,y=0,p=0,b=mt(n),w;try{for(b.s();!(w=b.n()).done;){var x=w.value;y++,p+=x.count}}catch(B){b.e(B)}finally{b.f()}var S=Math.ceil(h-a),C="".concat(o," elements, ").concat(p," rectangles, ").concat(y," batches");if(m)console.log("WebGL (".concat(r.name,") - ").concat(C));else{console.log("WebGL render (".concat(r.name,") - frame time ").concat(S,"ms")),console.log("  ".concat(C)),console.log("Texture Atlases Used:");var E=i.getAtlasDebugInfo(),D=mt(E),T;try{for(D.s();!(T=D.n()).done;){var A=T.value;console.log("  ".concat(A.type,": ").concat(A.keyCount," keys, ").concat(A.atlasCount," atlases"))}}catch(B){D.e(B)}finally{D.f()}console.log("")}}}v(So,"renderWebgl");var cr={};cr.drawPolygonPath=function(t,e,r,a,n,i){var o=a/2,s=n/2;t.beginPath&&t.beginPath(),t.moveTo(e+o*i[0],r+s*i[1]);for(var u=1;u<i.length/2;u++)t.lineTo(e+o*i[u*2],r+s*i[u*2+1]);t.closePath()};cr.drawRoundPolygonPath=function(t,e,r,a,n,i,o){o.forEach(function(s){return po(t,s)}),t.closePath()};cr.drawRoundRectanglePath=function(t,e,r,a,n,i){var o=a/2,s=n/2,u=i==="auto"?Cr(a,n):Math.min(i,s,o);t.beginPath&&t.beginPath(),t.moveTo(e,r-s),t.arcTo(e+o,r-s,e+o,r,u),t.arcTo(e+o,r+s,e,r+s,u),t.arcTo(e-o,r+s,e-o,r,u),t.arcTo(e-o,r-s,e,r-s,u),t.lineTo(e,r-s),t.closePath()};cr.drawBottomRoundRectanglePath=function(t,e,r,a,n,i){var o=a/2,s=n/2,u=i==="auto"?Cr(a,n):i;t.beginPath&&t.beginPath(),t.moveTo(e,r-s),t.lineTo(e+o,r-s),t.lineTo(e+o,r),t.arcTo(e+o,r+s,e,r+s,u),t.arcTo(e-o,r+s,e-o,r,u),t.lineTo(e-o,r-s),t.lineTo(e,r-s),t.closePath()};cr.drawCutRectanglePath=function(t,e,r,a,n,i,o){var s=a/2,u=n/2,l=o==="auto"?Zi():o;t.beginPath&&t.beginPath(),t.moveTo(e-s+l,r-u),t.lineTo(e+s-l,r-u),t.lineTo(e+s,r-u+l),t.lineTo(e+s,r+u-l),t.lineTo(e+s-l,r+u),t.lineTo(e-s+l,r+u),t.lineTo(e-s,r+u-l),t.lineTo(e-s,r-u+l),t.closePath()};cr.drawBarrelPath=function(t,e,r,a,n){var i=a/2,o=n/2,s=e-i,u=e+i,l=r-o,c=r+o,f=gi(a,n),d=f.widthOffset,g=f.heightOffset,h=f.ctrlPtOffsetPct*d;t.beginPath&&t.beginPath(),t.moveTo(s,l+g),t.lineTo(s,c-g),t.quadraticCurveTo(s+h,c,s+d,c),t.lineTo(u-d,c),t.quadraticCurveTo(u-h,c,u,c-g),t.lineTo(u,l+g),t.quadraticCurveTo(u-h,l,u-d,l),t.lineTo(s+d,l),t.quadraticCurveTo(s+h,l,s,l+g),t.closePath()};var Vs=Math.sin(0),qs=Math.cos(0),Ni={},zi={},Fv=Math.PI/40;for(dr=0*Math.PI;dr<2*Math.PI;dr+=Fv)Ni[dr]=Math.sin(dr),zi[dr]=Math.cos(dr);var dr;cr.drawEllipsePath=function(t,e,r,a,n){if(t.beginPath&&t.beginPath(),t.ellipse)t.ellipse(e,r,a/2,n/2,0,0,2*Math.PI);else for(var i,o,s=a/2,u=n/2,l=0*Math.PI;l<2*Math.PI;l+=Fv)i=e-s*Ni[l]*Vs+s*zi[l]*qs,o=r+u*zi[l]*Vs+u*Ni[l]*qs,l===0?t.moveTo(i,o):t.lineTo(i,o);t.closePath()};var _a={};_a.createBuffer=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,[r,r.getContext("2d")]};_a.bufferCanvasImage=function(t){var e=this.cy,r=e.mutableElements(),a=r.boundingBox(),n=this.findContainerClientCoords(),i=t.full?Math.ceil(a.w):n[2],o=t.full?Math.ceil(a.h):n[3],s=ae(t.maxWidth)||ae(t.maxHeight),u=this.getPixelRatio(),l=1;if(t.scale!==void 0)i*=t.scale,o*=t.scale,l=t.scale;else if(s){var c=1/0,f=1/0;ae(t.maxWidth)&&(c=l*t.maxWidth/i),ae(t.maxHeight)&&(f=l*t.maxHeight/o),l=Math.min(c,f),i*=l,o*=l}s||(i*=u,o*=u,l*=u);var d=document.createElement("canvas");d.width=i,d.height=o,d.style.width=i+"px",d.style.height=o+"px";var g=d.getContext("2d");if(i>0&&o>0){g.clearRect(0,0,i,o),g.globalCompositeOperation="source-over";var h=this.getCachedZSortedEles();if(t.full)g.translate(-a.x1*l,-a.y1*l),g.scale(l,l),this.drawElements(g,h),g.scale(1/l,1/l),g.translate(a.x1*l,a.y1*l);else{var m=e.pan(),y={x:m.x*l,y:m.y*l};l*=e.zoom(),g.translate(y.x,y.y),g.scale(l,l),this.drawElements(g,h),g.scale(1/l,1/l),g.translate(-y.x,-y.y)}t.bg&&(g.globalCompositeOperation="destination-over",g.fillStyle=t.bg,g.rect(0,0,i,o),g.fill())}return d};function Nv(t,e){for(var r=atob(t),a=new ArrayBuffer(r.length),n=new Uint8Array(a),i=0;i<r.length;i++)n[i]=r.charCodeAt(i);return new Blob([a],{type:e})}v(Nv,"b64ToBlob");function Vi(t){var e=t.indexOf(",");return t.substr(e+1)}v(Vi,"b64UriToB64");function Do(t,e,r){var a=v(function(){return e.toDataURL(r,t.quality)},"getB64Uri");switch(t.output){case"blob-promise":return new Jr(function(n,i){try{e.toBlob(function(o){o!=null?n(o):i(new Error("`canvas.toBlob()` sent a null value in its callback"))},r,t.quality)}catch(o){i(o)}});case"blob":return Nv(Vi(a()),r);case"base64":return Vi(a());case"base64uri":default:return a()}}v(Do,"output");_a.png=function(t){return Do(t,this.bufferCanvasImage(t),"image/png")};_a.jpg=function(t){return Do(t,this.bufferCanvasImage(t),"image/jpeg")};var zv={};zv.nodeShapeImpl=function(t,e,r,a,n,i,o,s){switch(t){case"ellipse":return this.drawEllipsePath(e,r,a,n,i);case"polygon":return this.drawPolygonPath(e,r,a,n,i,o);case"round-polygon":return this.drawRoundPolygonPath(e,r,a,n,i,o,s);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(e,r,a,n,i,s);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(e,r,a,n,i,o,s);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(e,r,a,n,i,s);case"barrel":return this.drawBarrelPath(e,r,a,n,i)}};var py=Po,Te=Po.prototype;Te.CANVAS_LAYERS=3;Te.SELECT_BOX=0;Te.DRAG=1;Te.NODE=2;Te.WEBGL=3;Te.CANVAS_TYPES=["2d","2d","2d","webgl2"];Te.BUFFER_COUNT=3;Te.TEXTURE_BUFFER=0;Te.MOTIONBLUR_BUFFER_NODE=1;Te.MOTIONBLUR_BUFFER_DRAG=2;function Po(t){var e=this,r=e.cy.window(),a=r.document;t.webgl&&(Te.CANVAS_LAYERS=e.CANVAS_LAYERS=4,console.log("webgl rendering enabled")),e.data={canvases:new Array(Te.CANVAS_LAYERS),contexts:new Array(Te.CANVAS_LAYERS),canvasNeedsRedraw:new Array(Te.CANVAS_LAYERS),bufferCanvases:new Array(Te.BUFFER_COUNT),bufferContexts:new Array(Te.CANVAS_LAYERS)};var n="-webkit-tap-highlight-color",i="rgba(0,0,0,0)";e.data.canvasContainer=a.createElement("div");var o=e.data.canvasContainer.style;e.data.canvasContainer.style[n]=i,o.position="relative",o.zIndex="0",o.overflow="hidden";var s=t.cy.container();s.appendChild(e.data.canvasContainer),s.style[n]=i;var u={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};lc()&&(u["-ms-touch-action"]="none",u["touch-action"]="none");for(var l=0;l<Te.CANVAS_LAYERS;l++){var c=e.data.canvases[l]=a.createElement("canvas"),f=Te.CANVAS_TYPES[l];e.data.contexts[l]=c.getContext(f),e.data.contexts[l]||Ke("Could not create canvas of type "+f),Object.keys(u).forEach(function(W){c.style[W]=u[W]}),c.style.position="absolute",c.setAttribute("data-id","layer"+l),c.style.zIndex=String(Te.CANVAS_LAYERS-l),e.data.canvasContainer.appendChild(c),e.data.canvasNeedsRedraw[l]=!1}e.data.topCanvas=e.data.canvases[0],e.data.canvases[Te.NODE].setAttribute("data-id","layer"+Te.NODE+"-node"),e.data.canvases[Te.SELECT_BOX].setAttribute("data-id","layer"+Te.SELECT_BOX+"-selectbox"),e.data.canvases[Te.DRAG].setAttribute("data-id","layer"+Te.DRAG+"-drag"),e.data.canvases[Te.WEBGL]&&e.data.canvases[Te.WEBGL].setAttribute("data-id","layer"+Te.WEBGL+"-webgl");for(var l=0;l<Te.BUFFER_COUNT;l++)e.data.bufferCanvases[l]=a.createElement("canvas"),e.data.bufferContexts[l]=e.data.bufferCanvases[l].getContext("2d"),e.data.bufferCanvases[l].style.position="absolute",e.data.bufferCanvases[l].setAttribute("data-id","buffer"+l),e.data.bufferCanvases[l].style.zIndex=String(-l-1),e.data.bufferCanvases[l].style.visibility="hidden";e.pathsEnabled=!0;var d=bt(),g=v(function(N){return{x:(N.x1+N.x2)/2,y:(N.y1+N.y2)/2}},"getBoxCenter"),h=v(function(N){return{x:-N.w/2,y:-N.h/2}},"getCenterOffset"),m=v(function(N){var U=N[0]._private,te=U.oldBackgroundTimestamp===U.backgroundTimestamp;return!te},"backgroundTimestampHasChanged"),y=v(function(N){return N[0]._private.nodeKey},"getStyleKey"),p=v(function(N){return N[0]._private.labelStyleKey},"getLabelKey"),b=v(function(N){return N[0]._private.sourceLabelStyleKey},"getSourceLabelKey"),w=v(function(N){return N[0]._private.targetLabelStyleKey},"getTargetLabelKey"),x=v(function(N,U,te,oe,ue){return e.drawElement(N,U,te,!1,!1,ue)},"drawElement"),S=v(function(N,U,te,oe,ue){return e.drawElementText(N,U,te,oe,"main",ue)},"drawLabel"),C=v(function(N,U,te,oe,ue){return e.drawElementText(N,U,te,oe,"source",ue)},"drawSourceLabel"),E=v(function(N,U,te,oe,ue){return e.drawElementText(N,U,te,oe,"target",ue)},"drawTargetLabel"),D=v(function(N){return N.boundingBox(),N[0]._private.bodyBounds},"getElementBox"),T=v(function(N){return N.boundingBox(),N[0]._private.labelBounds.main||d},"getLabelBox"),A=v(function(N){return N.boundingBox(),N[0]._private.labelBounds.source||d},"getSourceLabelBox"),B=v(function(N){return N.boundingBox(),N[0]._private.labelBounds.target||d},"getTargetLabelBox"),k=v(function(N,U){return U},"isLabelVisibleAtScale"),L=v(function(N){return g(D(N))},"getElementRotationPoint"),R=v(function(N,U,te){var oe=N?N+"-":"";return{x:U.x+te.pstyle(oe+"text-margin-x").pfValue,y:U.y+te.pstyle(oe+"text-margin-y").pfValue}},"addTextMargin"),M=v(function(N,U,te){var oe=N[0]._private.rscratch;return{x:oe[U],y:oe[te]}},"getRsPt"),I=v(function(N){return R("",M(N,"labelX","labelY"),N)},"getLabelRotationPoint"),O=v(function(N){return R("source",M(N,"sourceLabelX","sourceLabelY"),N)},"getSourceLabelRotationPoint"),F=v(function(N){return R("target",M(N,"targetLabelX","targetLabelY"),N)},"getTargetLabelRotationPoint"),K=v(function(N){return h(D(N))},"getElementRotationOffset"),$=v(function(N){return h(A(N))},"getSourceLabelRotationOffset"),q=v(function(N){return h(B(N))},"getTargetLabelRotationOffset"),G=v(function(N){var U=T(N),te=h(T(N));if(N.isNode()){switch(N.pstyle("text-halign").value){case"left":te.x=-U.w-(U.leftPad||0);break;case"right":te.x=-(U.rightPad||0);break}switch(N.pstyle("text-valign").value){case"top":te.y=-U.h-(U.topPad||0);break;case"bottom":te.y=-(U.botPad||0);break}}return te},"getLabelRotationOffset"),X=e.data.eleTxrCache=new ga(e,{getKey:y,doesEleInvalidateKey:m,drawElement:x,getBoundingBox:D,getRotationPoint:L,getRotationOffset:K,allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),Z=e.data.lblTxrCache=new ga(e,{getKey:p,drawElement:S,getBoundingBox:T,getRotationPoint:I,getRotationOffset:G,isVisible:k}),J=e.data.slbTxrCache=new ga(e,{getKey:b,drawElement:C,getBoundingBox:A,getRotationPoint:O,getRotationOffset:$,isVisible:k}),Q=e.data.tlbTxrCache=new ga(e,{getKey:w,drawElement:E,getBoundingBox:B,getRotationPoint:F,getRotationOffset:q,isVisible:k}),ee=e.data.lyrTxrCache=new sv(e);e.onUpdateEleCalcs(v(function(N,U){X.invalidateElements(U),Z.invalidateElements(U),J.invalidateElements(U),Q.invalidateElements(U),ee.invalidateElements(U);for(var te=0;te<U.length;te++){var oe=U[te]._private;oe.oldBackgroundTimestamp=oe.backgroundTimestamp}},"invalidateTextureCaches"));var re=v(function(N){for(var U=0;U<N.length;U++)ee.enqueueElementRefinement(N[U].ele)},"refineInLayers");X.onDequeue(re),Z.onDequeue(re),J.onDequeue(re),Q.onDequeue(re),t.webgl&&e.initWebgl(t,{getStyleKey:y,getLabelKey:p,drawElement:x,drawLabel:S,getElementBox:D,getLabelBox:T,getElementRotationPoint:L,getElementRotationOffset:K,getLabelRotationPoint:I,getLabelRotationOffset:G})}v(Po,"CanvasRenderer");Te.redrawHint=function(t,e){var r=this;switch(t){case"eles":r.data.canvasNeedsRedraw[Te.NODE]=e;break;case"drag":r.data.canvasNeedsRedraw[Te.DRAG]=e;break;case"select":r.data.canvasNeedsRedraw[Te.SELECT_BOX]=e;break;case"gc":r.data.gc=!0;break}};var yy=typeof Path2D<"u";Te.path2dEnabled=function(t){if(t===void 0)return this.pathsEnabled;this.pathsEnabled=!!t};Te.usePaths=function(){return yy&&this.pathsEnabled};Te.setImgSmoothing=function(t,e){t.imageSmoothingEnabled!=null?t.imageSmoothingEnabled=e:(t.webkitImageSmoothingEnabled=e,t.mozImageSmoothingEnabled=e,t.msImageSmoothingEnabled=e)};Te.getImgSmoothing=function(t){return t.imageSmoothingEnabled!=null?t.imageSmoothingEnabled:t.webkitImageSmoothingEnabled||t.mozImageSmoothingEnabled||t.msImageSmoothingEnabled};Te.makeOffscreenCanvas=function(t,e){var r;if((typeof OffscreenCanvas>"u"?"undefined":$e(OffscreenCanvas))!=="undefined")r=new OffscreenCanvas(t,e);else{var a=this.cy.window(),n=a.document;r=n.createElement("canvas"),r.width=t,r.height=e}return r};[lv,Kt,Qt,bo,Lr,ra,ht,Dv,cr,_a,zv].forEach(function(t){he(Te,t)});var my=[{name:"null",impl:ho},{name:"base",impl:nv},{name:"canvas",impl:py}],by=[{type:"layout",extensions:Pp},{type:"renderer",extensions:my}],Vv={},qv={};function ko(t,e,r){var a=r,n=v(function(D){Re("Can not register `"+e+"` for `"+t+"` since `"+D+"` already exists in the prototype and can not be overridden")},"overrideErr");if(t==="core"){if(Ra.prototype[e])return n(e);Ra.prototype[e]=r}else if(t==="collection"){if(at.prototype[e])return n(e);at.prototype[e]=r}else if(t==="layout"){for(var i=v(function(D){this.options=D,r.call(this,D),ke(this._private)||(this._private={}),this._private.cy=D.cy,this._private.listeners=[],this.createEmitter()},"Layout"),o=i.prototype=Object.create(r.prototype),s=[],u=0;u<s.length;u++){var l=s[u];o[l]=o[l]||function(){return this}}o.start&&!o.run?o.run=function(){return this.start(),this}:!o.start&&o.run&&(o.start=function(){return this.run(),this});var c=r.prototype.stop;o.stop=function(){var E=this.options;if(E&&E.animate){var D=this.animations;if(D)for(var T=0;T<D.length;T++)D[T].stop()}return c?c.call(this):this.emit("layoutstop"),this},o.destroy||(o.destroy=function(){return this}),o.cy=function(){return this._private.cy};var f=v(function(D){return D._private.cy},"getCy"),d={addEventFields:v(function(D,T){T.layout=D,T.cy=f(D),T.target=D},"addEventFields"),bubble:v(function(){return!0},"bubble"),parent:v(function(D){return f(D)},"parent")};he(o,{createEmitter:v(function(){return this._private.emitter=new qa(d,this),this},"createEmitter"),emitter:v(function(){return this._private.emitter},"emitter"),on:v(function(D,T){return this.emitter().on(D,T),this},"on"),one:v(function(D,T){return this.emitter().one(D,T),this},"one"),once:v(function(D,T){return this.emitter().one(D,T),this},"once"),removeListener:v(function(D,T){return this.emitter().removeListener(D,T),this},"removeListener"),removeAllListeners:v(function(){return this.emitter().removeAllListeners(),this},"removeAllListeners"),emit:v(function(D,T){return this.emitter().emit(D,T),this},"emit")}),Ae.eventAliasesOn(o),a=i}else if(t==="renderer"&&e!=="null"&&e!=="base"){var g=Bo("renderer","base"),h=g.prototype,m=r,y=r.prototype,p=v(function(){g.apply(this,arguments),m.apply(this,arguments)},"Renderer"),b=p.prototype;for(var w in h){var x=h[w],S=y[w]!=null;if(S)return n(w);b[w]=x}for(var C in y)b[C]=y[C];h.clientFunctions.forEach(function(E){b[E]=b[E]||function(){Ke("Renderer does not implement `renderer."+E+"()` on its prototype")}}),a=p}else if(t==="__proto__"||t==="constructor"||t==="prototype")return Ke(t+" is an illegal type to be registered, possibly lead to prototype pollutions");return js({map:Vv,keys:[t,e],value:a})}v(ko,"setExtension");function Bo(t,e){return el({map:Vv,keys:[t,e]})}v(Bo,"getExtension");function Kv(t,e,r,a,n){return js({map:qv,keys:[t,e,r,a],value:n})}v(Kv,"setModule");function Gv(t,e,r,a){return el({map:qv,keys:[t,e,r,a]})}v(Gv,"getModule");var qi=v(function(){if(arguments.length===2)return Bo.apply(null,arguments);if(arguments.length===3)return ko.apply(null,arguments);if(arguments.length===4)return Gv.apply(null,arguments);if(arguments.length===5)return Kv.apply(null,arguments);Ke("Invalid extension access syntax")},"extension");Ra.prototype.extension=qi;by.forEach(function(t){t.extensions.forEach(function(e){ko(t.type,e.name,e.impl)})});var Hv=v(function t(){if(!(this instanceof t))return new t;this.length=0},"Stylesheet"),Dr=Hv.prototype;Dr.instanceString=function(){return"stylesheet"};Dr.selector=function(t){var e=this.length++;return this[e]={selector:t,properties:[]},this};Dr.css=function(t,e){var r=this.length-1;if(fe(t))this[r].properties.push({name:t,value:e});else if(ke(t))for(var a=t,n=Object.keys(a),i=0;i<n.length;i++){var o=n[i],s=a[o];if(s!=null){var u=ot.properties[o]||ot.properties[Mn(o)];if(u!=null){var l=u.name,c=s;this[r].properties.push({name:l,value:c})}}}return this};Dr.style=Dr.css;Dr.generateStyle=function(t){var e=new ot(t);return this.appendToStyle(e)};Dr.appendToStyle=function(t){for(var e=0;e<this.length;e++){var r=this[e],a=r.selector,n=r.properties;t.selector(a);for(var i=0;i<n.length;i++){var o=n[i];t.css(o.name,o.value)}}return t};var wy="3.31.0",Zr=v(function(e){if(e===void 0&&(e={}),ke(e))return new Ra(e);if(fe(e))return qi.apply(qi,arguments)},"cytoscape");Zr.use=function(t){var e=Array.prototype.slice.call(arguments,1);return e.unshift(Zr),t.apply(null,e),this};Zr.warnings=function(t){return bl(t)};Zr.version=wy;Zr.stylesheet=Zr.Stylesheet=Hv;/*! Bundled license information:

cytoscape/dist/cytoscape.esm.mjs:
  (*!
  Embeddable Minimum Strictly-Compliant Promises/A+ 1.1.1 Thenable
  Copyright (c) 2013-2014 Ralf S. Engelschall (http://engelschall.com)
  Licensed under The MIT License (http://opensource.org/licenses/MIT)
  *)
  (*!
  Event object based on jQuery events, MIT license
  
  https://jquery.org/license/
  https://tldrlegal.com/license/mit-license
  https://github.com/jquery/jquery/blob/master/src/event.js
  *)
  (*! Bezier curve function generator. Copyright Gaetan Renaudeau. MIT License: http://en.wikipedia.org/wiki/MIT_License *)
  (*! Runge-Kutta spring physics function generator. Adapted from Framer.js, copyright Koen Bok. MIT License: http://en.wikipedia.org/wiki/MIT_License *)
*/export{Zr as c};
