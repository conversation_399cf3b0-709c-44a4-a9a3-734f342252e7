import{c as ct}from"./chunk-COUQ6RZ3-CV_BEwpd.js";import{_ as A,l as Z,c as it,a6 as dt,ap as vt,H as tt,i as J,aq as yt,ar as Et,as as Lt,d as Tt,au as mt,a8 as Nt,j as Dt,a5 as nt}from"./app-BQZQgfaL.js";var ht=nt({"../../node_modules/.pnpm/layout-base@1.0.2/node_modules/layout-base/layout-base.js"(C,x){A(function(D,d){typeof C=="object"&&typeof x=="object"?x.exports=d():typeof define=="function"&&define.amd?define([],d):typeof C=="object"?C.layoutBase=d():D.layoutBase=d()},"webpackUniversalModuleDefinition")(C,function(){return(function(u){var D={};function d(i){if(D[i])return D[i].exports;var t=D[i]={i,l:!1,exports:{}};return u[i].call(t.exports,t,t.exports,d),t.l=!0,t.exports}return A(d,"__webpack_require__"),d.m=u,d.c=D,d.i=function(i){return i},d.d=function(i,t,e){d.o(i,t)||Object.defineProperty(i,t,{configurable:!1,enumerable:!0,get:e})},d.n=function(i){var t=i&&i.__esModule?A(function(){return i.default},"getDefault"):A(function(){return i},"getModuleExports");return d.d(t,"a",t),t},d.o=function(i,t){return Object.prototype.hasOwnProperty.call(i,t)},d.p="",d(d.s=26)})([function(u,D,d){function i(){}A(i,"LayoutConstants"),i.QUALITY=1,i.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,i.DEFAULT_INCREMENTAL=!1,i.DEFAULT_ANIMATION_ON_LAYOUT=!0,i.DEFAULT_ANIMATION_DURING_LAYOUT=!1,i.DEFAULT_ANIMATION_PERIOD=50,i.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,i.DEFAULT_GRAPH_MARGIN=15,i.NODE_DIMENSIONS_INCLUDE_LABELS=!1,i.SIMPLE_NODE_SIZE=40,i.SIMPLE_NODE_HALF_SIZE=i.SIMPLE_NODE_SIZE/2,i.EMPTY_COMPOUND_NODE_SIZE=40,i.MIN_EDGE_LENGTH=1,i.WORLD_BOUNDARY=1e6,i.INITIAL_WORLD_BOUNDARY=i.WORLD_BOUNDARY/1e3,i.WORLD_CENTER_X=1200,i.WORLD_CENTER_Y=900,u.exports=i},function(u,D,d){var i=d(2),t=d(8),e=d(9);function r(g,a,y){i.call(this,y),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=y,this.bendpoints=[],this.source=g,this.target=a}A(r,"LEdge"),r.prototype=Object.create(i.prototype);for(var o in i)r[o]=i[o];r.prototype.getSource=function(){return this.source},r.prototype.getTarget=function(){return this.target},r.prototype.isInterGraph=function(){return this.isInterGraph},r.prototype.getLength=function(){return this.length},r.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},r.prototype.getBendpoints=function(){return this.bendpoints},r.prototype.getLca=function(){return this.lca},r.prototype.getSourceInLca=function(){return this.sourceInLca},r.prototype.getTargetInLca=function(){return this.targetInLca},r.prototype.getOtherEnd=function(g){if(this.source===g)return this.target;if(this.target===g)return this.source;throw"Node is not incident with this edge"},r.prototype.getOtherEndInGraph=function(g,a){for(var y=this.getOtherEnd(g),n=a.getGraphManager().getRoot();;){if(y.getOwner()==a)return y;if(y.getOwner()==n)break;y=y.getOwner().getParent()}return null},r.prototype.updateLength=function(){var g=new Array(4);this.isOverlapingSourceAndTarget=t.getIntersection(this.target.getRect(),this.source.getRect(),g),this.isOverlapingSourceAndTarget||(this.lengthX=g[0]-g[2],this.lengthY=g[1]-g[3],Math.abs(this.lengthX)<1&&(this.lengthX=e.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=e.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},r.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=e.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=e.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},u.exports=r},function(u,D,d){function i(t){this.vGraphObject=t}A(i,"LGraphObject"),u.exports=i},function(u,D,d){var i=d(2),t=d(10),e=d(13),r=d(0),o=d(16),g=d(4);function a(n,h,l,E){l==null&&E==null&&(E=h),i.call(this,E),n.graphManager!=null&&(n=n.graphManager),this.estimatedSize=t.MIN_VALUE,this.inclusionTreeDepth=t.MAX_VALUE,this.vGraphObject=E,this.edges=[],this.graphManager=n,l!=null&&h!=null?this.rect=new e(h.x,h.y,l.width,l.height):this.rect=new e}A(a,"LNode"),a.prototype=Object.create(i.prototype);for(var y in i)a[y]=i[y];a.prototype.getEdges=function(){return this.edges},a.prototype.getChild=function(){return this.child},a.prototype.getOwner=function(){return this.owner},a.prototype.getWidth=function(){return this.rect.width},a.prototype.setWidth=function(n){this.rect.width=n},a.prototype.getHeight=function(){return this.rect.height},a.prototype.setHeight=function(n){this.rect.height=n},a.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},a.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},a.prototype.getCenter=function(){return new g(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},a.prototype.getLocation=function(){return new g(this.rect.x,this.rect.y)},a.prototype.getRect=function(){return this.rect},a.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},a.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},a.prototype.setRect=function(n,h){this.rect.x=n.x,this.rect.y=n.y,this.rect.width=h.width,this.rect.height=h.height},a.prototype.setCenter=function(n,h){this.rect.x=n-this.rect.width/2,this.rect.y=h-this.rect.height/2},a.prototype.setLocation=function(n,h){this.rect.x=n,this.rect.y=h},a.prototype.moveBy=function(n,h){this.rect.x+=n,this.rect.y+=h},a.prototype.getEdgeListToNode=function(n){var h=[],l=this;return l.edges.forEach(function(E){if(E.target==n){if(E.source!=l)throw"Incorrect edge source!";h.push(E)}}),h},a.prototype.getEdgesBetween=function(n){var h=[],l=this;return l.edges.forEach(function(E){if(!(E.source==l||E.target==l))throw"Incorrect edge source and/or target";(E.target==n||E.source==n)&&h.push(E)}),h},a.prototype.getNeighborsList=function(){var n=new Set,h=this;return h.edges.forEach(function(l){if(l.source==h)n.add(l.target);else{if(l.target!=h)throw"Incorrect incidency!";n.add(l.source)}}),n},a.prototype.withChildren=function(){var n=new Set,h,l;if(n.add(this),this.child!=null)for(var E=this.child.getNodes(),T=0;T<E.length;T++)h=E[T],l=h.withChildren(),l.forEach(function(N){n.add(N)});return n},a.prototype.getNoOfChildren=function(){var n=0,h;if(this.child==null)n=1;else for(var l=this.child.getNodes(),E=0;E<l.length;E++)h=l[E],n+=h.getNoOfChildren();return n==0&&(n=1),n},a.prototype.getEstimatedSize=function(){if(this.estimatedSize==t.MIN_VALUE)throw"assert failed";return this.estimatedSize},a.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},a.prototype.scatter=function(){var n,h,l=-r.INITIAL_WORLD_BOUNDARY,E=r.INITIAL_WORLD_BOUNDARY;n=r.WORLD_CENTER_X+o.nextDouble()*(E-l)+l;var T=-r.INITIAL_WORLD_BOUNDARY,N=r.INITIAL_WORLD_BOUNDARY;h=r.WORLD_CENTER_Y+o.nextDouble()*(N-T)+T,this.rect.x=n,this.rect.y=h},a.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var n=this.getChild();if(n.updateBounds(!0),this.rect.x=n.getLeft(),this.rect.y=n.getTop(),this.setWidth(n.getRight()-n.getLeft()),this.setHeight(n.getBottom()-n.getTop()),r.NODE_DIMENSIONS_INCLUDE_LABELS){var h=n.getRight()-n.getLeft(),l=n.getBottom()-n.getTop();this.labelWidth>h&&(this.rect.x-=(this.labelWidth-h)/2,this.setWidth(this.labelWidth)),this.labelHeight>l&&(this.labelPos=="center"?this.rect.y-=(this.labelHeight-l)/2:this.labelPos=="top"&&(this.rect.y-=this.labelHeight-l),this.setHeight(this.labelHeight))}}},a.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==t.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},a.prototype.transform=function(n){var h=this.rect.x;h>r.WORLD_BOUNDARY?h=r.WORLD_BOUNDARY:h<-r.WORLD_BOUNDARY&&(h=-r.WORLD_BOUNDARY);var l=this.rect.y;l>r.WORLD_BOUNDARY?l=r.WORLD_BOUNDARY:l<-r.WORLD_BOUNDARY&&(l=-r.WORLD_BOUNDARY);var E=new g(h,l),T=n.inverseTransformPoint(E);this.setLocation(T.x,T.y)},a.prototype.getLeft=function(){return this.rect.x},a.prototype.getRight=function(){return this.rect.x+this.rect.width},a.prototype.getTop=function(){return this.rect.y},a.prototype.getBottom=function(){return this.rect.y+this.rect.height},a.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},u.exports=a},function(u,D,d){function i(t,e){t==null&&e==null?(this.x=0,this.y=0):(this.x=t,this.y=e)}A(i,"PointD"),i.prototype.getX=function(){return this.x},i.prototype.getY=function(){return this.y},i.prototype.setX=function(t){this.x=t},i.prototype.setY=function(t){this.y=t},i.prototype.getDifference=function(t){return new DimensionD(this.x-t.x,this.y-t.y)},i.prototype.getCopy=function(){return new i(this.x,this.y)},i.prototype.translate=function(t){return this.x+=t.width,this.y+=t.height,this},u.exports=i},function(u,D,d){var i=d(2),t=d(10),e=d(0),r=d(6),o=d(3),g=d(1),a=d(13),y=d(12),n=d(11);function h(E,T,N){i.call(this,N),this.estimatedSize=t.MIN_VALUE,this.margin=e.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=E,T!=null&&T instanceof r?this.graphManager=T:T!=null&&T instanceof Layout&&(this.graphManager=T.graphManager)}A(h,"LGraph"),h.prototype=Object.create(i.prototype);for(var l in i)h[l]=i[l];h.prototype.getNodes=function(){return this.nodes},h.prototype.getEdges=function(){return this.edges},h.prototype.getGraphManager=function(){return this.graphManager},h.prototype.getParent=function(){return this.parent},h.prototype.getLeft=function(){return this.left},h.prototype.getRight=function(){return this.right},h.prototype.getTop=function(){return this.top},h.prototype.getBottom=function(){return this.bottom},h.prototype.isConnected=function(){return this.isConnected},h.prototype.add=function(E,T,N){if(T==null&&N==null){var L=E;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(L)>-1)throw"Node already in graph!";return L.owner=this,this.getNodes().push(L),L}else{var O=E;if(!(this.getNodes().indexOf(T)>-1&&this.getNodes().indexOf(N)>-1))throw"Source or target not in graph!";if(!(T.owner==N.owner&&T.owner==this))throw"Both owners must be this graph!";return T.owner!=N.owner?null:(O.source=T,O.target=N,O.isInterGraph=!1,this.getEdges().push(O),T.edges.push(O),N!=T&&N.edges.push(O),O)}},h.prototype.remove=function(E){var T=E;if(E instanceof o){if(T==null)throw"Node is null!";if(!(T.owner!=null&&T.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var N=T.edges.slice(),L,O=N.length,v=0;v<O;v++)L=N[v],L.isInterGraph?this.graphManager.remove(L):L.source.owner.remove(L);var m=this.nodes.indexOf(T);if(m==-1)throw"Node not in owner node list!";this.nodes.splice(m,1)}else if(E instanceof g){var L=E;if(L==null)throw"Edge is null!";if(!(L.source!=null&&L.target!=null))throw"Source and/or target is null!";if(!(L.source.owner!=null&&L.target.owner!=null&&L.source.owner==this&&L.target.owner==this))throw"Source and/or target owner is invalid!";var s=L.source.edges.indexOf(L),c=L.target.edges.indexOf(L);if(!(s>-1&&c>-1))throw"Source and/or target doesn't know this edge!";L.source.edges.splice(s,1),L.target!=L.source&&L.target.edges.splice(c,1);var m=L.source.owner.getEdges().indexOf(L);if(m==-1)throw"Not in owner's edge list!";L.source.owner.getEdges().splice(m,1)}},h.prototype.updateLeftTop=function(){for(var E=t.MAX_VALUE,T=t.MAX_VALUE,N,L,O,v=this.getNodes(),m=v.length,s=0;s<m;s++){var c=v[s];N=c.getTop(),L=c.getLeft(),E>N&&(E=N),T>L&&(T=L)}return E==t.MAX_VALUE?null:(v[0].getParent().paddingLeft!=null?O=v[0].getParent().paddingLeft:O=this.margin,this.left=T-O,this.top=E-O,new y(this.left,this.top))},h.prototype.updateBounds=function(E){for(var T=t.MAX_VALUE,N=-t.MAX_VALUE,L=t.MAX_VALUE,O=-t.MAX_VALUE,v,m,s,c,f,p=this.nodes,I=p.length,R=0;R<I;R++){var M=p[R];E&&M.child!=null&&M.updateBounds(),v=M.getLeft(),m=M.getRight(),s=M.getTop(),c=M.getBottom(),T>v&&(T=v),N<m&&(N=m),L>s&&(L=s),O<c&&(O=c)}var w=new a(T,L,N-T,O-L);T==t.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),p[0].getParent().paddingLeft!=null?f=p[0].getParent().paddingLeft:f=this.margin,this.left=w.x-f,this.right=w.x+w.width+f,this.top=w.y-f,this.bottom=w.y+w.height+f},h.calculateBounds=function(E){for(var T=t.MAX_VALUE,N=-t.MAX_VALUE,L=t.MAX_VALUE,O=-t.MAX_VALUE,v,m,s,c,f=E.length,p=0;p<f;p++){var I=E[p];v=I.getLeft(),m=I.getRight(),s=I.getTop(),c=I.getBottom(),T>v&&(T=v),N<m&&(N=m),L>s&&(L=s),O<c&&(O=c)}var R=new a(T,L,N-T,O-L);return R},h.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},h.prototype.getEstimatedSize=function(){if(this.estimatedSize==t.MIN_VALUE)throw"assert failed";return this.estimatedSize},h.prototype.calcEstimatedSize=function(){for(var E=0,T=this.nodes,N=T.length,L=0;L<N;L++){var O=T[L];E+=O.calcEstimatedSize()}return E==0?this.estimatedSize=e.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=E/Math.sqrt(this.nodes.length),this.estimatedSize},h.prototype.updateConnected=function(){var E=this;if(this.nodes.length==0){this.isConnected=!0;return}var T=new n,N=new Set,L=this.nodes[0],O,v,m=L.withChildren();for(m.forEach(function(R){T.push(R),N.add(R)});T.length!==0;){L=T.shift(),O=L.getEdges();for(var s=O.length,c=0;c<s;c++){var f=O[c];if(v=f.getOtherEndInGraph(L,this),v!=null&&!N.has(v)){var p=v.withChildren();p.forEach(function(R){T.push(R),N.add(R)})}}}if(this.isConnected=!1,N.size>=this.nodes.length){var I=0;N.forEach(function(R){R.owner==E&&I++}),I==this.nodes.length&&(this.isConnected=!0)}},u.exports=h},function(u,D,d){var i,t=d(1);function e(r){i=d(5),this.layout=r,this.graphs=[],this.edges=[]}A(e,"LGraphManager"),e.prototype.addRoot=function(){var r=this.layout.newGraph(),o=this.layout.newNode(null),g=this.add(r,o);return this.setRootGraph(g),this.rootGraph},e.prototype.add=function(r,o,g,a,y){if(g==null&&a==null&&y==null){if(r==null)throw"Graph is null!";if(o==null)throw"Parent node is null!";if(this.graphs.indexOf(r)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(r),r.parent!=null)throw"Already has a parent!";if(o.child!=null)throw"Already has a child!";return r.parent=o,o.child=r,r}else{y=g,a=o,g=r;var n=a.getOwner(),h=y.getOwner();if(!(n!=null&&n.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(h!=null&&h.getGraphManager()==this))throw"Target not in this graph mgr!";if(n==h)return g.isInterGraph=!1,n.add(g,a,y);if(g.isInterGraph=!0,g.source=a,g.target=y,this.edges.indexOf(g)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(g),!(g.source!=null&&g.target!=null))throw"Edge source and/or target is null!";if(!(g.source.edges.indexOf(g)==-1&&g.target.edges.indexOf(g)==-1))throw"Edge already in source and/or target incidency list!";return g.source.edges.push(g),g.target.edges.push(g),g}},e.prototype.remove=function(r){if(r instanceof i){var o=r;if(o.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(o==this.rootGraph||o.parent!=null&&o.parent.graphManager==this))throw"Invalid parent node!";var g=[];g=g.concat(o.getEdges());for(var a,y=g.length,n=0;n<y;n++)a=g[n],o.remove(a);var h=[];h=h.concat(o.getNodes());var l;y=h.length;for(var n=0;n<y;n++)l=h[n],o.remove(l);o==this.rootGraph&&this.setRootGraph(null);var E=this.graphs.indexOf(o);this.graphs.splice(E,1),o.parent=null}else if(r instanceof t){if(a=r,a==null)throw"Edge is null!";if(!a.isInterGraph)throw"Not an inter-graph edge!";if(!(a.source!=null&&a.target!=null))throw"Source and/or target is null!";if(!(a.source.edges.indexOf(a)!=-1&&a.target.edges.indexOf(a)!=-1))throw"Source and/or target doesn't know this edge!";var E=a.source.edges.indexOf(a);if(a.source.edges.splice(E,1),E=a.target.edges.indexOf(a),a.target.edges.splice(E,1),!(a.source.owner!=null&&a.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(a.source.owner.getGraphManager().edges.indexOf(a)==-1)throw"Not in owner graph manager's edge list!";var E=a.source.owner.getGraphManager().edges.indexOf(a);a.source.owner.getGraphManager().edges.splice(E,1)}},e.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},e.prototype.getGraphs=function(){return this.graphs},e.prototype.getAllNodes=function(){if(this.allNodes==null){for(var r=[],o=this.getGraphs(),g=o.length,a=0;a<g;a++)r=r.concat(o[a].getNodes());this.allNodes=r}return this.allNodes},e.prototype.resetAllNodes=function(){this.allNodes=null},e.prototype.resetAllEdges=function(){this.allEdges=null},e.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},e.prototype.getAllEdges=function(){if(this.allEdges==null){var r=[],o=this.getGraphs();o.length;for(var g=0;g<o.length;g++)r=r.concat(o[g].getEdges());r=r.concat(this.edges),this.allEdges=r}return this.allEdges},e.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},e.prototype.setAllNodesToApplyGravitation=function(r){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=r},e.prototype.getRoot=function(){return this.rootGraph},e.prototype.setRootGraph=function(r){if(r.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=r,r.parent==null&&(r.parent=this.layout.newNode("Root node"))},e.prototype.getLayout=function(){return this.layout},e.prototype.isOneAncestorOfOther=function(r,o){if(!(r!=null&&o!=null))throw"assert failed";if(r==o)return!0;var g=r.getOwner(),a;do{if(a=g.getParent(),a==null)break;if(a==o)return!0;if(g=a.getOwner(),g==null)break}while(!0);g=o.getOwner();do{if(a=g.getParent(),a==null)break;if(a==r)return!0;if(g=a.getOwner(),g==null)break}while(!0);return!1},e.prototype.calcLowestCommonAncestors=function(){for(var r,o,g,a,y,n=this.getAllEdges(),h=n.length,l=0;l<h;l++){if(r=n[l],o=r.source,g=r.target,r.lca=null,r.sourceInLca=o,r.targetInLca=g,o==g){r.lca=o.getOwner();continue}for(a=o.getOwner();r.lca==null;){for(r.targetInLca=g,y=g.getOwner();r.lca==null;){if(y==a){r.lca=y;break}if(y==this.rootGraph)break;if(r.lca!=null)throw"assert failed";r.targetInLca=y.getParent(),y=r.targetInLca.getOwner()}if(a==this.rootGraph)break;r.lca==null&&(r.sourceInLca=a.getParent(),a=r.sourceInLca.getOwner())}if(r.lca==null)throw"assert failed"}},e.prototype.calcLowestCommonAncestor=function(r,o){if(r==o)return r.getOwner();var g=r.getOwner();do{if(g==null)break;var a=o.getOwner();do{if(a==null)break;if(a==g)return a;a=a.getParent().getOwner()}while(!0);g=g.getParent().getOwner()}while(!0);return g},e.prototype.calcInclusionTreeDepths=function(r,o){r==null&&o==null&&(r=this.rootGraph,o=1);for(var g,a=r.getNodes(),y=a.length,n=0;n<y;n++)g=a[n],g.inclusionTreeDepth=o,g.child!=null&&this.calcInclusionTreeDepths(g.child,o+1)},e.prototype.includesInvalidEdge=function(){for(var r,o=this.edges.length,g=0;g<o;g++)if(r=this.edges[g],this.isOneAncestorOfOther(r.source,r.target))return!0;return!1},u.exports=e},function(u,D,d){var i=d(0);function t(){}A(t,"FDLayoutConstants");for(var e in i)t[e]=i[e];t.MAX_ITERATIONS=2500,t.DEFAULT_EDGE_LENGTH=50,t.DEFAULT_SPRING_STRENGTH=.45,t.DEFAULT_REPULSION_STRENGTH=4500,t.DEFAULT_GRAVITY_STRENGTH=.4,t.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,t.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,t.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,t.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,t.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,t.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,t.COOLING_ADAPTATION_FACTOR=.33,t.ADAPTATION_LOWER_NODE_LIMIT=1e3,t.ADAPTATION_UPPER_NODE_LIMIT=5e3,t.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,t.MAX_NODE_DISPLACEMENT=t.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,t.MIN_REPULSION_DIST=t.DEFAULT_EDGE_LENGTH/10,t.CONVERGENCE_CHECK_PERIOD=100,t.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,t.MIN_EDGE_LENGTH=1,t.GRID_CALCULATION_CHECK_PERIOD=10,u.exports=t},function(u,D,d){var i=d(12);function t(){}A(t,"IGeometry"),t.calcSeparationAmount=function(e,r,o,g){if(!e.intersects(r))throw"assert failed";var a=new Array(2);this.decideDirectionsForOverlappingNodes(e,r,a),o[0]=Math.min(e.getRight(),r.getRight())-Math.max(e.x,r.x),o[1]=Math.min(e.getBottom(),r.getBottom())-Math.max(e.y,r.y),e.getX()<=r.getX()&&e.getRight()>=r.getRight()?o[0]+=Math.min(r.getX()-e.getX(),e.getRight()-r.getRight()):r.getX()<=e.getX()&&r.getRight()>=e.getRight()&&(o[0]+=Math.min(e.getX()-r.getX(),r.getRight()-e.getRight())),e.getY()<=r.getY()&&e.getBottom()>=r.getBottom()?o[1]+=Math.min(r.getY()-e.getY(),e.getBottom()-r.getBottom()):r.getY()<=e.getY()&&r.getBottom()>=e.getBottom()&&(o[1]+=Math.min(e.getY()-r.getY(),r.getBottom()-e.getBottom()));var y=Math.abs((r.getCenterY()-e.getCenterY())/(r.getCenterX()-e.getCenterX()));r.getCenterY()===e.getCenterY()&&r.getCenterX()===e.getCenterX()&&(y=1);var n=y*o[0],h=o[1]/y;o[0]<h?h=o[0]:n=o[1],o[0]=-1*a[0]*(h/2+g),o[1]=-1*a[1]*(n/2+g)},t.decideDirectionsForOverlappingNodes=function(e,r,o){e.getCenterX()<r.getCenterX()?o[0]=-1:o[0]=1,e.getCenterY()<r.getCenterY()?o[1]=-1:o[1]=1},t.getIntersection2=function(e,r,o){var g=e.getCenterX(),a=e.getCenterY(),y=r.getCenterX(),n=r.getCenterY();if(e.intersects(r))return o[0]=g,o[1]=a,o[2]=y,o[3]=n,!0;var h=e.getX(),l=e.getY(),E=e.getRight(),T=e.getX(),N=e.getBottom(),L=e.getRight(),O=e.getWidthHalf(),v=e.getHeightHalf(),m=r.getX(),s=r.getY(),c=r.getRight(),f=r.getX(),p=r.getBottom(),I=r.getRight(),R=r.getWidthHalf(),M=r.getHeightHalf(),w=!1,G=!1;if(g===y){if(a>n)return o[0]=g,o[1]=l,o[2]=y,o[3]=p,!1;if(a<n)return o[0]=g,o[1]=N,o[2]=y,o[3]=s,!1}else if(a===n){if(g>y)return o[0]=h,o[1]=a,o[2]=c,o[3]=n,!1;if(g<y)return o[0]=E,o[1]=a,o[2]=m,o[3]=n,!1}else{var U=e.height/e.width,X=r.height/r.width,_=(n-a)/(y-g),S=void 0,F=void 0,b=void 0,Y=void 0,k=void 0,H=void 0;if(-U===_?g>y?(o[0]=T,o[1]=N,w=!0):(o[0]=E,o[1]=l,w=!0):U===_&&(g>y?(o[0]=h,o[1]=l,w=!0):(o[0]=L,o[1]=N,w=!0)),-X===_?y>g?(o[2]=f,o[3]=p,G=!0):(o[2]=c,o[3]=s,G=!0):X===_&&(y>g?(o[2]=m,o[3]=s,G=!0):(o[2]=I,o[3]=p,G=!0)),w&&G)return!1;if(g>y?a>n?(S=this.getCardinalDirection(U,_,4),F=this.getCardinalDirection(X,_,2)):(S=this.getCardinalDirection(-U,_,3),F=this.getCardinalDirection(-X,_,1)):a>n?(S=this.getCardinalDirection(-U,_,1),F=this.getCardinalDirection(-X,_,3)):(S=this.getCardinalDirection(U,_,2),F=this.getCardinalDirection(X,_,4)),!w)switch(S){case 1:Y=l,b=g+-v/_,o[0]=b,o[1]=Y;break;case 2:b=L,Y=a+O*_,o[0]=b,o[1]=Y;break;case 3:Y=N,b=g+v/_,o[0]=b,o[1]=Y;break;case 4:b=T,Y=a+-O*_,o[0]=b,o[1]=Y;break}if(!G)switch(F){case 1:H=s,k=y+-M/_,o[2]=k,o[3]=H;break;case 2:k=I,H=n+R*_,o[2]=k,o[3]=H;break;case 3:H=p,k=y+M/_,o[2]=k,o[3]=H;break;case 4:k=f,H=n+-R*_,o[2]=k,o[3]=H;break}}return!1},t.getCardinalDirection=function(e,r,o){return e>r?o:1+o%4},t.getIntersection=function(e,r,o,g){if(g==null)return this.getIntersection2(e,r,o);var a=e.x,y=e.y,n=r.x,h=r.y,l=o.x,E=o.y,T=g.x,N=g.y,L=void 0,O=void 0,v=void 0,m=void 0,s=void 0,c=void 0,f=void 0,p=void 0,I=void 0;return v=h-y,s=a-n,f=n*y-a*h,m=N-E,c=l-T,p=T*E-l*N,I=v*c-m*s,I===0?null:(L=(s*p-c*f)/I,O=(m*f-v*p)/I,new i(L,O))},t.angleOfVector=function(e,r,o,g){var a=void 0;return e!==o?(a=Math.atan((g-r)/(o-e)),o<e?a+=Math.PI:g<r&&(a+=this.TWO_PI)):g<r?a=this.ONE_AND_HALF_PI:a=this.HALF_PI,a},t.doIntersect=function(e,r,o,g){var a=e.x,y=e.y,n=r.x,h=r.y,l=o.x,E=o.y,T=g.x,N=g.y,L=(n-a)*(N-E)-(T-l)*(h-y);if(L===0)return!1;var O=((N-E)*(T-a)+(l-T)*(N-y))/L,v=((y-h)*(T-a)+(n-a)*(N-y))/L;return 0<O&&O<1&&0<v&&v<1},t.HALF_PI=.5*Math.PI,t.ONE_AND_HALF_PI=1.5*Math.PI,t.TWO_PI=2*Math.PI,t.THREE_PI=3*Math.PI,u.exports=t},function(u,D,d){function i(){}A(i,"IMath"),i.sign=function(t){return t>0?1:t<0?-1:0},i.floor=function(t){return t<0?Math.ceil(t):Math.floor(t)},i.ceil=function(t){return t<0?Math.floor(t):Math.ceil(t)},u.exports=i},function(u,D,d){function i(){}A(i,"Integer"),i.MAX_VALUE=2147483647,i.MIN_VALUE=-2147483648,u.exports=i},function(u,D,d){var i=(function(){function a(y,n){for(var h=0;h<n.length;h++){var l=n[h];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(y,l.key,l)}}return A(a,"defineProperties"),function(y,n,h){return n&&a(y.prototype,n),h&&a(y,h),y}})();function t(a,y){if(!(a instanceof y))throw new TypeError("Cannot call a class as a function")}A(t,"_classCallCheck");var e=A(function(y){return{value:y,next:null,prev:null}},"nodeFrom"),r=A(function(y,n,h,l){return y!==null?y.next=n:l.head=n,h!==null?h.prev=n:l.tail=n,n.prev=y,n.next=h,l.length++,n},"add"),o=A(function(y,n){var h=y.prev,l=y.next;return h!==null?h.next=l:n.head=l,l!==null?l.prev=h:n.tail=h,y.prev=y.next=null,n.length--,y},"_remove"),g=(function(){function a(y){var n=this;t(this,a),this.length=0,this.head=null,this.tail=null,y?.forEach(function(h){return n.push(h)})}return A(a,"LinkedList"),i(a,[{key:"size",value:A(function(){return this.length},"size")},{key:"insertBefore",value:A(function(n,h){return r(h.prev,e(n),h,this)},"insertBefore")},{key:"insertAfter",value:A(function(n,h){return r(h,e(n),h.next,this)},"insertAfter")},{key:"insertNodeBefore",value:A(function(n,h){return r(h.prev,n,h,this)},"insertNodeBefore")},{key:"insertNodeAfter",value:A(function(n,h){return r(h,n,h.next,this)},"insertNodeAfter")},{key:"push",value:A(function(n){return r(this.tail,e(n),null,this)},"push")},{key:"unshift",value:A(function(n){return r(null,e(n),this.head,this)},"unshift")},{key:"remove",value:A(function(n){return o(n,this)},"remove")},{key:"pop",value:A(function(){return o(this.tail,this).value},"pop")},{key:"popNode",value:A(function(){return o(this.tail,this)},"popNode")},{key:"shift",value:A(function(){return o(this.head,this).value},"shift")},{key:"shiftNode",value:A(function(){return o(this.head,this)},"shiftNode")},{key:"get_object_at",value:A(function(n){if(n<=this.length()){for(var h=1,l=this.head;h<n;)l=l.next,h++;return l.value}},"get_object_at")},{key:"set_object_at",value:A(function(n,h){if(n<=this.length()){for(var l=1,E=this.head;l<n;)E=E.next,l++;E.value=h}},"set_object_at")}]),a})();u.exports=g},function(u,D,d){function i(t,e,r){this.x=null,this.y=null,t==null&&e==null&&r==null?(this.x=0,this.y=0):typeof t=="number"&&typeof e=="number"&&r==null?(this.x=t,this.y=e):t.constructor.name=="Point"&&e==null&&r==null&&(r=t,this.x=r.x,this.y=r.y)}A(i,"Point"),i.prototype.getX=function(){return this.x},i.prototype.getY=function(){return this.y},i.prototype.getLocation=function(){return new i(this.x,this.y)},i.prototype.setLocation=function(t,e,r){t.constructor.name=="Point"&&e==null&&r==null?(r=t,this.setLocation(r.x,r.y)):typeof t=="number"&&typeof e=="number"&&r==null&&(parseInt(t)==t&&parseInt(e)==e?this.move(t,e):(this.x=Math.floor(t+.5),this.y=Math.floor(e+.5)))},i.prototype.move=function(t,e){this.x=t,this.y=e},i.prototype.translate=function(t,e){this.x+=t,this.y+=e},i.prototype.equals=function(t){if(t.constructor.name=="Point"){var e=t;return this.x==e.x&&this.y==e.y}return this==t},i.prototype.toString=function(){return new i().constructor.name+"[x="+this.x+",y="+this.y+"]"},u.exports=i},function(u,D,d){function i(t,e,r,o){this.x=0,this.y=0,this.width=0,this.height=0,t!=null&&e!=null&&r!=null&&o!=null&&(this.x=t,this.y=e,this.width=r,this.height=o)}A(i,"RectangleD"),i.prototype.getX=function(){return this.x},i.prototype.setX=function(t){this.x=t},i.prototype.getY=function(){return this.y},i.prototype.setY=function(t){this.y=t},i.prototype.getWidth=function(){return this.width},i.prototype.setWidth=function(t){this.width=t},i.prototype.getHeight=function(){return this.height},i.prototype.setHeight=function(t){this.height=t},i.prototype.getRight=function(){return this.x+this.width},i.prototype.getBottom=function(){return this.y+this.height},i.prototype.intersects=function(t){return!(this.getRight()<t.x||this.getBottom()<t.y||t.getRight()<this.x||t.getBottom()<this.y)},i.prototype.getCenterX=function(){return this.x+this.width/2},i.prototype.getMinX=function(){return this.getX()},i.prototype.getMaxX=function(){return this.getX()+this.width},i.prototype.getCenterY=function(){return this.y+this.height/2},i.prototype.getMinY=function(){return this.getY()},i.prototype.getMaxY=function(){return this.getY()+this.height},i.prototype.getWidthHalf=function(){return this.width/2},i.prototype.getHeightHalf=function(){return this.height/2},u.exports=i},function(u,D,d){var i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function t(){}A(t,"UniqueIDGeneretor"),t.lastID=0,t.createID=function(e){return t.isPrimitive(e)?e:(e.uniqueID!=null||(e.uniqueID=t.getString(),t.lastID++),e.uniqueID)},t.getString=function(e){return e==null&&(e=t.lastID),"Object#"+e},t.isPrimitive=function(e){var r=typeof e>"u"?"undefined":i(e);return e==null||r!="object"&&r!="function"},u.exports=t},function(u,D,d){function i(l){if(Array.isArray(l)){for(var E=0,T=Array(l.length);E<l.length;E++)T[E]=l[E];return T}else return Array.from(l)}A(i,"_toConsumableArray");var t=d(0),e=d(6),r=d(3),o=d(1),g=d(5),a=d(4),y=d(17),n=d(27);function h(l){n.call(this),this.layoutQuality=t.QUALITY,this.createBendsAsNeeded=t.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=t.DEFAULT_INCREMENTAL,this.animationOnLayout=t.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=t.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=t.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=t.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new e(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,l!=null&&(this.isRemoteUse=l)}A(h,"Layout"),h.RANDOM_SEED=1,h.prototype=Object.create(n.prototype),h.prototype.getGraphManager=function(){return this.graphManager},h.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},h.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},h.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},h.prototype.newGraphManager=function(){var l=new e(this);return this.graphManager=l,l},h.prototype.newGraph=function(l){return new g(null,this.graphManager,l)},h.prototype.newNode=function(l){return new r(this.graphManager,l)},h.prototype.newEdge=function(l){return new o(null,null,l)},h.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},h.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var l;return this.checkLayoutSuccess()?l=!1:l=this.layout(),t.ANIMATE==="during"?!1:(l&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,l)},h.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},h.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var l=this.graphManager.getAllEdges(),E=0;E<l.length;E++)l[E];for(var T=this.graphManager.getRoot().getNodes(),E=0;E<T.length;E++)T[E];this.update(this.graphManager.getRoot())}},h.prototype.update=function(l){if(l==null)this.update2();else if(l instanceof r){var E=l;if(E.getChild()!=null)for(var T=E.getChild().getNodes(),N=0;N<T.length;N++)update(T[N]);if(E.vGraphObject!=null){var L=E.vGraphObject;L.update(E)}}else if(l instanceof o){var O=l;if(O.vGraphObject!=null){var v=O.vGraphObject;v.update(O)}}else if(l instanceof g){var m=l;if(m.vGraphObject!=null){var s=m.vGraphObject;s.update(m)}}},h.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=t.QUALITY,this.animationDuringLayout=t.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=t.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=t.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=t.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=t.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=t.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},h.prototype.transform=function(l){if(l==null)this.transform(new a(0,0));else{var E=new y,T=this.graphManager.getRoot().updateLeftTop();if(T!=null){E.setWorldOrgX(l.x),E.setWorldOrgY(l.y),E.setDeviceOrgX(T.x),E.setDeviceOrgY(T.y);for(var N=this.getAllNodes(),L,O=0;O<N.length;O++)L=N[O],L.transform(E)}}},h.prototype.positionNodesRandomly=function(l){if(l==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var E,T,N=l.getNodes(),L=0;L<N.length;L++)E=N[L],T=E.getChild(),T==null||T.getNodes().length==0?E.scatter():(this.positionNodesRandomly(T),E.updateBounds())},h.prototype.getFlatForest=function(){for(var l=[],E=!0,T=this.graphManager.getRoot().getNodes(),N=!0,L=0;L<T.length;L++)T[L].getChild()!=null&&(N=!1);if(!N)return l;var O=new Set,v=[],m=new Map,s=[];for(s=s.concat(T);s.length>0&&E;){for(v.push(s[0]);v.length>0&&E;){var c=v[0];v.splice(0,1),O.add(c);for(var f=c.getEdges(),L=0;L<f.length;L++){var p=f[L].getOtherEnd(c);if(m.get(c)!=p)if(!O.has(p))v.push(p),m.set(p,c);else{E=!1;break}}}if(!E)l=[];else{var I=[].concat(i(O));l.push(I);for(var L=0;L<I.length;L++){var R=I[L],M=s.indexOf(R);M>-1&&s.splice(M,1)}O=new Set,m=new Map}}return l},h.prototype.createDummyNodesForBendpoints=function(l){for(var E=[],T=l.source,N=this.graphManager.calcLowestCommonAncestor(l.source,l.target),L=0;L<l.bendpoints.length;L++){var O=this.newNode(null);O.setRect(new Point(0,0),new Dimension(1,1)),N.add(O);var v=this.newEdge(null);this.graphManager.add(v,T,O),E.add(O),T=O}var v=this.newEdge(null);return this.graphManager.add(v,T,l.target),this.edgeToDummyNodes.set(l,E),l.isInterGraph()?this.graphManager.remove(l):N.remove(l),E},h.prototype.createBendpointsFromDummyNodes=function(){var l=[];l=l.concat(this.graphManager.getAllEdges()),l=[].concat(i(this.edgeToDummyNodes.keys())).concat(l);for(var E=0;E<l.length;E++){var T=l[E];if(T.bendpoints.length>0){for(var N=this.edgeToDummyNodes.get(T),L=0;L<N.length;L++){var O=N[L],v=new a(O.getCenterX(),O.getCenterY()),m=T.bendpoints.get(L);m.x=v.x,m.y=v.y,O.getOwner().remove(O)}this.graphManager.add(T,T.source,T.target)}}},h.transform=function(l,E,T,N){if(T!=null&&N!=null){var L=E;if(l<=50){var O=E/T;L-=(E-O)/50*(50-l)}else{var v=E*N;L+=(v-E)/50*(l-50)}return L}else{var m,s;return l<=50?(m=9*E/500,s=E/10):(m=9*E/50,s=-8*E),m*l+s}},h.findCenterOfTree=function(l){var E=[];E=E.concat(l);var T=[],N=new Map,L=!1,O=null;(E.length==1||E.length==2)&&(L=!0,O=E[0]);for(var v=0;v<E.length;v++){var m=E[v],s=m.getNeighborsList().size;N.set(m,m.getNeighborsList().size),s==1&&T.push(m)}var c=[];for(c=c.concat(T);!L;){var f=[];f=f.concat(c),c=[];for(var v=0;v<E.length;v++){var m=E[v],p=E.indexOf(m);p>=0&&E.splice(p,1);var I=m.getNeighborsList();I.forEach(function(w){if(T.indexOf(w)<0){var G=N.get(w),U=G-1;U==1&&c.push(w),N.set(w,U)}})}T=T.concat(c),(E.length==1||E.length==2)&&(L=!0,O=E[0])}return O},h.prototype.setGraphManager=function(l){this.graphManager=l},u.exports=h},function(u,D,d){function i(){}A(i,"RandomSeed"),i.seed=1,i.x=0,i.nextDouble=function(){return i.x=Math.sin(i.seed++)*1e4,i.x-Math.floor(i.x)},u.exports=i},function(u,D,d){var i=d(4);function t(e,r){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}A(t,"Transform"),t.prototype.getWorldOrgX=function(){return this.lworldOrgX},t.prototype.setWorldOrgX=function(e){this.lworldOrgX=e},t.prototype.getWorldOrgY=function(){return this.lworldOrgY},t.prototype.setWorldOrgY=function(e){this.lworldOrgY=e},t.prototype.getWorldExtX=function(){return this.lworldExtX},t.prototype.setWorldExtX=function(e){this.lworldExtX=e},t.prototype.getWorldExtY=function(){return this.lworldExtY},t.prototype.setWorldExtY=function(e){this.lworldExtY=e},t.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},t.prototype.setDeviceOrgX=function(e){this.ldeviceOrgX=e},t.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},t.prototype.setDeviceOrgY=function(e){this.ldeviceOrgY=e},t.prototype.getDeviceExtX=function(){return this.ldeviceExtX},t.prototype.setDeviceExtX=function(e){this.ldeviceExtX=e},t.prototype.getDeviceExtY=function(){return this.ldeviceExtY},t.prototype.setDeviceExtY=function(e){this.ldeviceExtY=e},t.prototype.transformX=function(e){var r=0,o=this.lworldExtX;return o!=0&&(r=this.ldeviceOrgX+(e-this.lworldOrgX)*this.ldeviceExtX/o),r},t.prototype.transformY=function(e){var r=0,o=this.lworldExtY;return o!=0&&(r=this.ldeviceOrgY+(e-this.lworldOrgY)*this.ldeviceExtY/o),r},t.prototype.inverseTransformX=function(e){var r=0,o=this.ldeviceExtX;return o!=0&&(r=this.lworldOrgX+(e-this.ldeviceOrgX)*this.lworldExtX/o),r},t.prototype.inverseTransformY=function(e){var r=0,o=this.ldeviceExtY;return o!=0&&(r=this.lworldOrgY+(e-this.ldeviceOrgY)*this.lworldExtY/o),r},t.prototype.inverseTransformPoint=function(e){var r=new i(this.inverseTransformX(e.x),this.inverseTransformY(e.y));return r},u.exports=t},function(u,D,d){function i(n){if(Array.isArray(n)){for(var h=0,l=Array(n.length);h<n.length;h++)l[h]=n[h];return l}else return Array.from(n)}A(i,"_toConsumableArray");var t=d(15),e=d(7),r=d(0),o=d(8),g=d(9);function a(){t.call(this),this.useSmartIdealEdgeLengthCalculation=e.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.idealEdgeLength=e.DEFAULT_EDGE_LENGTH,this.springConstant=e.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=e.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=e.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=e.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=e.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=e.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*e.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=e.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=e.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=e.MAX_ITERATIONS}A(a,"FDLayout"),a.prototype=Object.create(t.prototype);for(var y in t)a[y]=t[y];a.prototype.initParameters=function(){t.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=e.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},a.prototype.calcIdealEdgeLengths=function(){for(var n,h,l,E,T,N,L=this.getGraphManager().getAllEdges(),O=0;O<L.length;O++)n=L[O],n.idealLength=this.idealEdgeLength,n.isInterGraph&&(l=n.getSource(),E=n.getTarget(),T=n.getSourceInLca().getEstimatedSize(),N=n.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(n.idealLength+=T+N-2*r.SIMPLE_NODE_SIZE),h=n.getLca().getInclusionTreeDepth(),n.idealLength+=e.DEFAULT_EDGE_LENGTH*e.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(l.getInclusionTreeDepth()+E.getInclusionTreeDepth()-2*h))},a.prototype.initSpringEmbedder=function(){var n=this.getAllNodes().length;this.incremental?(n>e.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*e.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(n-e.ADAPTATION_LOWER_NODE_LIMIT)/(e.ADAPTATION_UPPER_NODE_LIMIT-e.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-e.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=e.MAX_NODE_DISPLACEMENT_INCREMENTAL):(n>e.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(e.COOLING_ADAPTATION_FACTOR,1-(n-e.ADAPTATION_LOWER_NODE_LIMIT)/(e.ADAPTATION_UPPER_NODE_LIMIT-e.ADAPTATION_LOWER_NODE_LIMIT)*(1-e.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=e.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},a.prototype.calcSpringForces=function(){for(var n=this.getAllEdges(),h,l=0;l<n.length;l++)h=n[l],this.calcSpringForce(h,h.idealLength)},a.prototype.calcRepulsionForces=function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l,E,T,N,L=this.getAllNodes(),O;if(this.useFRGridVariant)for(this.totalIterations%e.GRID_CALCULATION_CHECK_PERIOD==1&&n&&this.updateGrid(),O=new Set,l=0;l<L.length;l++)T=L[l],this.calculateRepulsionForceOfANode(T,O,n,h),O.add(T);else for(l=0;l<L.length;l++)for(T=L[l],E=l+1;E<L.length;E++)N=L[E],T.getOwner()==N.getOwner()&&this.calcRepulsionForce(T,N)},a.prototype.calcGravitationalForces=function(){for(var n,h=this.getAllNodesToApplyGravitation(),l=0;l<h.length;l++)n=h[l],this.calcGravitationalForce(n)},a.prototype.moveNodes=function(){for(var n=this.getAllNodes(),h,l=0;l<n.length;l++)h=n[l],h.move()},a.prototype.calcSpringForce=function(n,h){var l=n.getSource(),E=n.getTarget(),T,N,L,O;if(this.uniformLeafNodeSizes&&l.getChild()==null&&E.getChild()==null)n.updateLengthSimple();else if(n.updateLength(),n.isOverlapingSourceAndTarget)return;T=n.getLength(),T!=0&&(N=this.springConstant*(T-h),L=N*(n.lengthX/T),O=N*(n.lengthY/T),l.springForceX+=L,l.springForceY+=O,E.springForceX-=L,E.springForceY-=O)},a.prototype.calcRepulsionForce=function(n,h){var l=n.getRect(),E=h.getRect(),T=new Array(2),N=new Array(4),L,O,v,m,s,c,f;if(l.intersects(E)){o.calcSeparationAmount(l,E,T,e.DEFAULT_EDGE_LENGTH/2),c=2*T[0],f=2*T[1];var p=n.noOfChildren*h.noOfChildren/(n.noOfChildren+h.noOfChildren);n.repulsionForceX-=p*c,n.repulsionForceY-=p*f,h.repulsionForceX+=p*c,h.repulsionForceY+=p*f}else this.uniformLeafNodeSizes&&n.getChild()==null&&h.getChild()==null?(L=E.getCenterX()-l.getCenterX(),O=E.getCenterY()-l.getCenterY()):(o.getIntersection(l,E,N),L=N[2]-N[0],O=N[3]-N[1]),Math.abs(L)<e.MIN_REPULSION_DIST&&(L=g.sign(L)*e.MIN_REPULSION_DIST),Math.abs(O)<e.MIN_REPULSION_DIST&&(O=g.sign(O)*e.MIN_REPULSION_DIST),v=L*L+O*O,m=Math.sqrt(v),s=this.repulsionConstant*n.noOfChildren*h.noOfChildren/v,c=s*L/m,f=s*O/m,n.repulsionForceX-=c,n.repulsionForceY-=f,h.repulsionForceX+=c,h.repulsionForceY+=f},a.prototype.calcGravitationalForce=function(n){var h,l,E,T,N,L,O,v;h=n.getOwner(),l=(h.getRight()+h.getLeft())/2,E=(h.getTop()+h.getBottom())/2,T=n.getCenterX()-l,N=n.getCenterY()-E,L=Math.abs(T)+n.getWidth()/2,O=Math.abs(N)+n.getHeight()/2,n.getOwner()==this.graphManager.getRoot()?(v=h.getEstimatedSize()*this.gravityRangeFactor,(L>v||O>v)&&(n.gravitationForceX=-this.gravityConstant*T,n.gravitationForceY=-this.gravityConstant*N)):(v=h.getEstimatedSize()*this.compoundGravityRangeFactor,(L>v||O>v)&&(n.gravitationForceX=-this.gravityConstant*T*this.compoundGravityConstant,n.gravitationForceY=-this.gravityConstant*N*this.compoundGravityConstant))},a.prototype.isConverged=function(){var n,h=!1;return this.totalIterations>this.maxIterations/3&&(h=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),n=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,n||h},a.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},a.prototype.calcNoOfChildrenForAllNodes=function(){for(var n,h=this.graphManager.getAllNodes(),l=0;l<h.length;l++)n=h[l],n.noOfChildren=n.getNoOfChildren()},a.prototype.calcGrid=function(n){var h=0,l=0;h=parseInt(Math.ceil((n.getRight()-n.getLeft())/this.repulsionRange)),l=parseInt(Math.ceil((n.getBottom()-n.getTop())/this.repulsionRange));for(var E=new Array(h),T=0;T<h;T++)E[T]=new Array(l);for(var T=0;T<h;T++)for(var N=0;N<l;N++)E[T][N]=new Array;return E},a.prototype.addNodeToGrid=function(n,h,l){var E=0,T=0,N=0,L=0;E=parseInt(Math.floor((n.getRect().x-h)/this.repulsionRange)),T=parseInt(Math.floor((n.getRect().width+n.getRect().x-h)/this.repulsionRange)),N=parseInt(Math.floor((n.getRect().y-l)/this.repulsionRange)),L=parseInt(Math.floor((n.getRect().height+n.getRect().y-l)/this.repulsionRange));for(var O=E;O<=T;O++)for(var v=N;v<=L;v++)this.grid[O][v].push(n),n.setGridCoordinates(E,T,N,L)},a.prototype.updateGrid=function(){var n,h,l=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),n=0;n<l.length;n++)h=l[n],this.addNodeToGrid(h,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},a.prototype.calculateRepulsionForceOfANode=function(n,h,l,E){if(this.totalIterations%e.GRID_CALCULATION_CHECK_PERIOD==1&&l||E){var T=new Set;n.surrounding=new Array;for(var N,L=this.grid,O=n.startX-1;O<n.finishX+2;O++)for(var v=n.startY-1;v<n.finishY+2;v++)if(!(O<0||v<0||O>=L.length||v>=L[0].length)){for(var m=0;m<L[O][v].length;m++)if(N=L[O][v][m],!(n.getOwner()!=N.getOwner()||n==N)&&!h.has(N)&&!T.has(N)){var s=Math.abs(n.getCenterX()-N.getCenterX())-(n.getWidth()/2+N.getWidth()/2),c=Math.abs(n.getCenterY()-N.getCenterY())-(n.getHeight()/2+N.getHeight()/2);s<=this.repulsionRange&&c<=this.repulsionRange&&T.add(N)}}n.surrounding=[].concat(i(T))}for(O=0;O<n.surrounding.length;O++)this.calcRepulsionForce(n,n.surrounding[O])},a.prototype.calcRepulsionRange=function(){return 0},u.exports=a},function(u,D,d){var i=d(1),t=d(7);function e(o,g,a){i.call(this,o,g,a),this.idealLength=t.DEFAULT_EDGE_LENGTH}A(e,"FDLayoutEdge"),e.prototype=Object.create(i.prototype);for(var r in i)e[r]=i[r];u.exports=e},function(u,D,d){var i=d(3);function t(r,o,g,a){i.call(this,r,o,g,a),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}A(t,"FDLayoutNode"),t.prototype=Object.create(i.prototype);for(var e in i)t[e]=i[e];t.prototype.setGridCoordinates=function(r,o,g,a){this.startX=r,this.finishX=o,this.startY=g,this.finishY=a},u.exports=t},function(u,D,d){function i(t,e){this.width=0,this.height=0,t!==null&&e!==null&&(this.height=e,this.width=t)}A(i,"DimensionD"),i.prototype.getWidth=function(){return this.width},i.prototype.setWidth=function(t){this.width=t},i.prototype.getHeight=function(){return this.height},i.prototype.setHeight=function(t){this.height=t},u.exports=i},function(u,D,d){var i=d(14);function t(){this.map={},this.keys=[]}A(t,"HashMap"),t.prototype.put=function(e,r){var o=i.createID(e);this.contains(o)||(this.map[o]=r,this.keys.push(e))},t.prototype.contains=function(e){return i.createID(e),this.map[e]!=null},t.prototype.get=function(e){var r=i.createID(e);return this.map[r]},t.prototype.keySet=function(){return this.keys},u.exports=t},function(u,D,d){var i=d(14);function t(){this.set={}}A(t,"HashSet"),t.prototype.add=function(e){var r=i.createID(e);this.contains(r)||(this.set[r]=e)},t.prototype.remove=function(e){delete this.set[i.createID(e)]},t.prototype.clear=function(){this.set={}},t.prototype.contains=function(e){return this.set[i.createID(e)]==e},t.prototype.isEmpty=function(){return this.size()===0},t.prototype.size=function(){return Object.keys(this.set).length},t.prototype.addAllTo=function(e){for(var r=Object.keys(this.set),o=r.length,g=0;g<o;g++)e.push(this.set[r[g]])},t.prototype.size=function(){return Object.keys(this.set).length},t.prototype.addAll=function(e){for(var r=e.length,o=0;o<r;o++){var g=e[o];this.add(g)}},u.exports=t},function(u,D,d){var i=(function(){function o(g,a){for(var y=0;y<a.length;y++){var n=a[y];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(g,n.key,n)}}return A(o,"defineProperties"),function(g,a,y){return a&&o(g.prototype,a),y&&o(g,y),g}})();function t(o,g){if(!(o instanceof g))throw new TypeError("Cannot call a class as a function")}A(t,"_classCallCheck");var e=d(11),r=(function(){function o(g,a){t(this,o),(a!==null||a!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var y=void 0;g instanceof e?y=g.size():y=g.length,this._quicksort(g,0,y-1)}return A(o,"Quicksort"),i(o,[{key:"_quicksort",value:A(function(a,y,n){if(y<n){var h=this._partition(a,y,n);this._quicksort(a,y,h),this._quicksort(a,h+1,n)}},"_quicksort")},{key:"_partition",value:A(function(a,y,n){for(var h=this._get(a,y),l=y,E=n;;){for(;this.compareFunction(h,this._get(a,E));)E--;for(;this.compareFunction(this._get(a,l),h);)l++;if(l<E)this._swap(a,l,E),l++,E--;else return E}},"_partition")},{key:"_get",value:A(function(a,y){return a instanceof e?a.get_object_at(y):a[y]},"_get")},{key:"_set",value:A(function(a,y,n){a instanceof e?a.set_object_at(y,n):a[y]=n},"_set")},{key:"_swap",value:A(function(a,y,n){var h=this._get(a,y);this._set(a,y,this._get(a,n)),this._set(a,n,h)},"_swap")},{key:"_defaultCompareFunction",value:A(function(a,y){return y>a},"_defaultCompareFunction")}]),o})();u.exports=r},function(u,D,d){var i=(function(){function r(o,g){for(var a=0;a<g.length;a++){var y=g[a];y.enumerable=y.enumerable||!1,y.configurable=!0,"value"in y&&(y.writable=!0),Object.defineProperty(o,y.key,y)}}return A(r,"defineProperties"),function(o,g,a){return g&&r(o.prototype,g),a&&r(o,a),o}})();function t(r,o){if(!(r instanceof o))throw new TypeError("Cannot call a class as a function")}A(t,"_classCallCheck");var e=(function(){function r(o,g){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,y=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;t(this,r),this.sequence1=o,this.sequence2=g,this.match_score=a,this.mismatch_penalty=y,this.gap_penalty=n,this.iMax=o.length+1,this.jMax=g.length+1,this.grid=new Array(this.iMax);for(var h=0;h<this.iMax;h++){this.grid[h]=new Array(this.jMax);for(var l=0;l<this.jMax;l++)this.grid[h][l]=0}this.tracebackGrid=new Array(this.iMax);for(var E=0;E<this.iMax;E++){this.tracebackGrid[E]=new Array(this.jMax);for(var T=0;T<this.jMax;T++)this.tracebackGrid[E][T]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return A(r,"NeedlemanWunsch"),i(r,[{key:"getScore",value:A(function(){return this.score},"getScore")},{key:"getAlignments",value:A(function(){return this.alignments},"getAlignments")},{key:"computeGrids",value:A(function(){for(var g=1;g<this.jMax;g++)this.grid[0][g]=this.grid[0][g-1]+this.gap_penalty,this.tracebackGrid[0][g]=[!1,!1,!0];for(var a=1;a<this.iMax;a++)this.grid[a][0]=this.grid[a-1][0]+this.gap_penalty,this.tracebackGrid[a][0]=[!1,!0,!1];for(var y=1;y<this.iMax;y++)for(var n=1;n<this.jMax;n++){var h=void 0;this.sequence1[y-1]===this.sequence2[n-1]?h=this.grid[y-1][n-1]+this.match_score:h=this.grid[y-1][n-1]+this.mismatch_penalty;var l=this.grid[y-1][n]+this.gap_penalty,E=this.grid[y][n-1]+this.gap_penalty,T=[h,l,E],N=this.arrayAllMaxIndexes(T);this.grid[y][n]=T[N[0]],this.tracebackGrid[y][n]=[N.includes(0),N.includes(1),N.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]},"computeGrids")},{key:"alignmentTraceback",value:A(function(){var g=[];for(g.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});g[0];){var a=g[0],y=this.tracebackGrid[a.pos[0]][a.pos[1]];y[0]&&g.push({pos:[a.pos[0]-1,a.pos[1]-1],seq1:this.sequence1[a.pos[0]-1]+a.seq1,seq2:this.sequence2[a.pos[1]-1]+a.seq2}),y[1]&&g.push({pos:[a.pos[0]-1,a.pos[1]],seq1:this.sequence1[a.pos[0]-1]+a.seq1,seq2:"-"+a.seq2}),y[2]&&g.push({pos:[a.pos[0],a.pos[1]-1],seq1:"-"+a.seq1,seq2:this.sequence2[a.pos[1]-1]+a.seq2}),a.pos[0]===0&&a.pos[1]===0&&this.alignments.push({sequence1:a.seq1,sequence2:a.seq2}),g.shift()}return this.alignments},"alignmentTraceback")},{key:"getAllIndexes",value:A(function(g,a){for(var y=[],n=-1;(n=g.indexOf(a,n+1))!==-1;)y.push(n);return y},"getAllIndexes")},{key:"arrayAllMaxIndexes",value:A(function(g){return this.getAllIndexes(g,Math.max.apply(null,g))},"arrayAllMaxIndexes")}]),r})();u.exports=e},function(u,D,d){var i=A(function(){},"layoutBase");i.FDLayout=d(18),i.FDLayoutConstants=d(7),i.FDLayoutEdge=d(19),i.FDLayoutNode=d(20),i.DimensionD=d(21),i.HashMap=d(22),i.HashSet=d(23),i.IGeometry=d(8),i.IMath=d(9),i.Integer=d(10),i.Point=d(12),i.PointD=d(4),i.RandomSeed=d(16),i.RectangleD=d(13),i.Transform=d(17),i.UniqueIDGeneretor=d(14),i.Quicksort=d(24),i.LinkedList=d(11),i.LGraphObject=d(2),i.LGraph=d(5),i.LEdge=d(1),i.LGraphManager=d(6),i.LNode=d(3),i.Layout=d(15),i.LayoutConstants=d(0),i.NeedlemanWunsch=d(25),u.exports=i},function(u,D,d){function i(){this.listeners=[]}A(i,"Emitter");var t=i.prototype;t.addListener=function(e,r){this.listeners.push({event:e,callback:r})},t.removeListener=function(e,r){for(var o=this.listeners.length;o>=0;o--){var g=this.listeners[o];g.event===e&&g.callback===r&&this.listeners.splice(o,1)}},t.emit=function(e,r){for(var o=0;o<this.listeners.length;o++){var g=this.listeners[o];e===g.event&&g.callback(r)}},u.exports=i}])})}}),lt=nt({"../../node_modules/.pnpm/cose-base@1.0.3/node_modules/cose-base/cose-base.js"(C,x){A(function(D,d){typeof C=="object"&&typeof x=="object"?x.exports=d(ht()):typeof define=="function"&&define.amd?define(["layout-base"],d):typeof C=="object"?C.coseBase=d(ht()):D.coseBase=d(D.layoutBase)},"webpackUniversalModuleDefinition")(C,function(u){return(function(D){var d={};function i(t){if(d[t])return d[t].exports;var e=d[t]={i:t,l:!1,exports:{}};return D[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}return A(i,"__webpack_require__"),i.m=D,i.c=d,i.i=function(t){return t},i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:r})},i.n=function(t){var e=t&&t.__esModule?A(function(){return t.default},"getDefault"):A(function(){return t},"getModuleExports");return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=7)})([function(D,d){D.exports=u},function(D,d,i){var t=i(0).FDLayoutConstants;function e(){}A(e,"CoSEConstants");for(var r in t)e[r]=t[r];e.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,e.DEFAULT_RADIAL_SEPARATION=t.DEFAULT_EDGE_LENGTH,e.DEFAULT_COMPONENT_SEPERATION=60,e.TILE=!0,e.TILING_PADDING_VERTICAL=10,e.TILING_PADDING_HORIZONTAL=10,e.TREE_REDUCTION_ON_INCREMENTAL=!1,D.exports=e},function(D,d,i){var t=i(0).FDLayoutEdge;function e(o,g,a){t.call(this,o,g,a)}A(e,"CoSEEdge"),e.prototype=Object.create(t.prototype);for(var r in t)e[r]=t[r];D.exports=e},function(D,d,i){var t=i(0).LGraph;function e(o,g,a){t.call(this,o,g,a)}A(e,"CoSEGraph"),e.prototype=Object.create(t.prototype);for(var r in t)e[r]=t[r];D.exports=e},function(D,d,i){var t=i(0).LGraphManager;function e(o){t.call(this,o)}A(e,"CoSEGraphManager"),e.prototype=Object.create(t.prototype);for(var r in t)e[r]=t[r];D.exports=e},function(D,d,i){var t=i(0).FDLayoutNode,e=i(0).IMath;function r(g,a,y,n){t.call(this,g,a,y,n)}A(r,"CoSENode"),r.prototype=Object.create(t.prototype);for(var o in t)r[o]=t[o];r.prototype.move=function(){var g=this.graphManager.getLayout();this.displacementX=g.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY=g.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren,Math.abs(this.displacementX)>g.coolingFactor*g.maxNodeDisplacement&&(this.displacementX=g.coolingFactor*g.maxNodeDisplacement*e.sign(this.displacementX)),Math.abs(this.displacementY)>g.coolingFactor*g.maxNodeDisplacement&&(this.displacementY=g.coolingFactor*g.maxNodeDisplacement*e.sign(this.displacementY)),this.child==null?this.moveBy(this.displacementX,this.displacementY):this.child.getNodes().length==0?this.moveBy(this.displacementX,this.displacementY):this.propogateDisplacementToChildren(this.displacementX,this.displacementY),g.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},r.prototype.propogateDisplacementToChildren=function(g,a){for(var y=this.getChild().getNodes(),n,h=0;h<y.length;h++)n=y[h],n.getChild()==null?(n.moveBy(g,a),n.displacementX+=g,n.displacementY+=a):n.propogateDisplacementToChildren(g,a)},r.prototype.setPred1=function(g){this.pred1=g},r.prototype.getPred1=function(){return pred1},r.prototype.getPred2=function(){return pred2},r.prototype.setNext=function(g){this.next=g},r.prototype.getNext=function(){return next},r.prototype.setProcessed=function(g){this.processed=g},r.prototype.isProcessed=function(){return processed},D.exports=r},function(D,d,i){var t=i(0).FDLayout,e=i(4),r=i(3),o=i(5),g=i(2),a=i(1),y=i(0).FDLayoutConstants,n=i(0).LayoutConstants,h=i(0).Point,l=i(0).PointD,E=i(0).Layout,T=i(0).Integer,N=i(0).IGeometry,L=i(0).LGraph,O=i(0).Transform;function v(){t.call(this),this.toBeTiled={}}A(v,"CoSELayout"),v.prototype=Object.create(t.prototype);for(var m in t)v[m]=t[m];v.prototype.newGraphManager=function(){var s=new e(this);return this.graphManager=s,s},v.prototype.newGraph=function(s){return new r(null,this.graphManager,s)},v.prototype.newNode=function(s){return new o(this.graphManager,s)},v.prototype.newEdge=function(s){return new g(null,null,s)},v.prototype.initParameters=function(){t.prototype.initParameters.call(this,arguments),this.isSubLayout||(a.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=a.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=a.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.springConstant=y.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=y.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=y.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=y.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=y.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=y.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1,this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/y.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=y.CONVERGENCE_CHECK_PERIOD/this.maxIterations,this.coolingAdjuster=1)},v.prototype.layout=function(){var s=n.DEFAULT_CREATE_BENDS_AS_NEEDED;return s&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},v.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(a.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var c=new Set(this.getAllNodes()),f=this.nodesWithGravity.filter(function(R){return c.has(R)});this.graphManager.setAllNodesToApplyGravitation(f)}}else{var s=this.getFlatForest();if(s.length>0)this.positionNodesRadially(s);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var c=new Set(this.getAllNodes()),f=this.nodesWithGravity.filter(function(p){return c.has(p)});this.graphManager.setAllNodesToApplyGravitation(f),this.positionNodesRandomly()}}return this.initSpringEmbedder(),this.runSpringEmbedder(),!0},v.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%y.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var s=new Set(this.getAllNodes()),c=this.nodesWithGravity.filter(function(I){return s.has(I)});this.graphManager.setAllNodesToApplyGravitation(c),this.graphManager.updateBounds(),this.updateGrid(),this.coolingFactor=y.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),this.coolingFactor=y.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var f=!this.isTreeGrowing&&!this.isGrowthFinished,p=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(f,p),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},v.prototype.getPositionsData=function(){for(var s=this.graphManager.getAllNodes(),c={},f=0;f<s.length;f++){var p=s[f].rect,I=s[f].id;c[I]={id:I,x:p.getCenterX(),y:p.getCenterY(),w:p.width,h:p.height}}return c},v.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var s=!1;if(y.ANIMATE==="during")this.emit("layoutstarted");else{for(;!s;)s=this.tick();this.graphManager.updateBounds()}},v.prototype.calculateNodesToApplyGravitationTo=function(){var s=[],c,f=this.graphManager.getGraphs(),p=f.length,I;for(I=0;I<p;I++)c=f[I],c.updateConnected(),c.isConnected||(s=s.concat(c.getNodes()));return s},v.prototype.createBendpoints=function(){var s=[];s=s.concat(this.graphManager.getAllEdges());var c=new Set,f;for(f=0;f<s.length;f++){var p=s[f];if(!c.has(p)){var I=p.getSource(),R=p.getTarget();if(I==R)p.getBendpoints().push(new l),p.getBendpoints().push(new l),this.createDummyNodesForBendpoints(p),c.add(p);else{var M=[];if(M=M.concat(I.getEdgeListToNode(R)),M=M.concat(R.getEdgeListToNode(I)),!c.has(M[0])){if(M.length>1){var w;for(w=0;w<M.length;w++){var G=M[w];G.getBendpoints().push(new l),this.createDummyNodesForBendpoints(G)}}M.forEach(function(U){c.add(U)})}}}if(c.size==s.length)break}},v.prototype.positionNodesRadially=function(s){for(var c=new h(0,0),f=Math.ceil(Math.sqrt(s.length)),p=0,I=0,R=0,M=new l(0,0),w=0;w<s.length;w++){w%f==0&&(R=0,I=p,w!=0&&(I+=a.DEFAULT_COMPONENT_SEPERATION),p=0);var G=s[w],U=E.findCenterOfTree(G);c.x=R,c.y=I,M=v.radialLayout(G,U,c),M.y>p&&(p=Math.floor(M.y)),R=Math.floor(M.x+a.DEFAULT_COMPONENT_SEPERATION)}this.transform(new l(n.WORLD_CENTER_X-M.x/2,n.WORLD_CENTER_Y-M.y/2))},v.radialLayout=function(s,c,f){var p=Math.max(this.maxDiagonalInTree(s),a.DEFAULT_RADIAL_SEPARATION);v.branchRadialLayout(c,null,0,359,0,p);var I=L.calculateBounds(s),R=new O;R.setDeviceOrgX(I.getMinX()),R.setDeviceOrgY(I.getMinY()),R.setWorldOrgX(f.x),R.setWorldOrgY(f.y);for(var M=0;M<s.length;M++){var w=s[M];w.transform(R)}var G=new l(I.getMaxX(),I.getMaxY());return R.inverseTransformPoint(G)},v.branchRadialLayout=function(s,c,f,p,I,R){var M=(p-f+1)/2;M<0&&(M+=180);var w=(M+f)%360,G=w*N.TWO_PI/360,U=I*Math.cos(G),X=I*Math.sin(G);s.setCenter(U,X);var _=[];_=_.concat(s.getEdges());var S=_.length;c!=null&&S--;for(var F=0,b=_.length,Y,k=s.getEdgesBetween(c);k.length>1;){var H=k[0];k.splice(0,1);var P=_.indexOf(H);P>=0&&_.splice(P,1),b--,S--}c!=null?Y=(_.indexOf(k[0])+1)%b:Y=0;for(var W=Math.abs(p-f)/S,$=Y;F!=S;$=++$%b){var Q=_[$].getOtherEnd(s);if(Q!=c){var V=(f+F*W)%360,z=(V+W)%360;v.branchRadialLayout(Q,s,V,z,I+R,R),F++}}},v.maxDiagonalInTree=function(s){for(var c=T.MIN_VALUE,f=0;f<s.length;f++){var p=s[f],I=p.getDiagonal();I>c&&(c=I)}return c},v.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},v.prototype.groupZeroDegreeMembers=function(){var s=this,c={};this.memberGroups={},this.idToDummyNode={};for(var f=[],p=this.graphManager.getAllNodes(),I=0;I<p.length;I++){var R=p[I],M=R.getParent();this.getNodeDegreeWithChildren(R)===0&&(M.id==null||!this.getToBeTiled(M))&&f.push(R)}for(var I=0;I<f.length;I++){var R=f[I],w=R.getParent().id;typeof c[w]>"u"&&(c[w]=[]),c[w]=c[w].concat(R)}Object.keys(c).forEach(function(G){if(c[G].length>1){var U="DummyCompound_"+G;s.memberGroups[U]=c[G];var X=c[G][0].getParent(),_=new o(s.graphManager);_.id=U,_.paddingLeft=X.paddingLeft||0,_.paddingRight=X.paddingRight||0,_.paddingBottom=X.paddingBottom||0,_.paddingTop=X.paddingTop||0,s.idToDummyNode[U]=_;var S=s.getGraphManager().add(s.newGraph(),_),F=X.getChild();F.add(_);for(var b=0;b<c[G].length;b++){var Y=c[G][b];F.remove(Y),S.add(Y)}}})},v.prototype.clearCompounds=function(){var s={},c={};this.performDFSOnCompounds();for(var f=0;f<this.compoundOrder.length;f++)c[this.compoundOrder[f].id]=this.compoundOrder[f],s[this.compoundOrder[f].id]=[].concat(this.compoundOrder[f].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[f].getChild()),this.compoundOrder[f].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(s,c)},v.prototype.clearZeroDegreeMembers=function(){var s=this,c=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(f){var p=s.idToDummyNode[f];c[f]=s.tileNodes(s.memberGroups[f],p.paddingLeft+p.paddingRight),p.rect.width=c[f].width,p.rect.height=c[f].height})},v.prototype.repopulateCompounds=function(){for(var s=this.compoundOrder.length-1;s>=0;s--){var c=this.compoundOrder[s],f=c.id,p=c.paddingLeft,I=c.paddingTop;this.adjustLocations(this.tiledMemberPack[f],c.rect.x,c.rect.y,p,I)}},v.prototype.repopulateZeroDegreeMembers=function(){var s=this,c=this.tiledZeroDegreePack;Object.keys(c).forEach(function(f){var p=s.idToDummyNode[f],I=p.paddingLeft,R=p.paddingTop;s.adjustLocations(c[f],p.rect.x,p.rect.y,I,R)})},v.prototype.getToBeTiled=function(s){var c=s.id;if(this.toBeTiled[c]!=null)return this.toBeTiled[c];var f=s.getChild();if(f==null)return this.toBeTiled[c]=!1,!1;for(var p=f.getNodes(),I=0;I<p.length;I++){var R=p[I];if(this.getNodeDegree(R)>0)return this.toBeTiled[c]=!1,!1;if(R.getChild()==null){this.toBeTiled[R.id]=!1;continue}if(!this.getToBeTiled(R))return this.toBeTiled[c]=!1,!1}return this.toBeTiled[c]=!0,!0},v.prototype.getNodeDegree=function(s){s.id;for(var c=s.getEdges(),f=0,p=0;p<c.length;p++){var I=c[p];I.getSource().id!==I.getTarget().id&&(f=f+1)}return f},v.prototype.getNodeDegreeWithChildren=function(s){var c=this.getNodeDegree(s);if(s.getChild()==null)return c;for(var f=s.getChild().getNodes(),p=0;p<f.length;p++){var I=f[p];c+=this.getNodeDegreeWithChildren(I)}return c},v.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},v.prototype.fillCompexOrderByDFS=function(s){for(var c=0;c<s.length;c++){var f=s[c];f.getChild()!=null&&this.fillCompexOrderByDFS(f.getChild().getNodes()),this.getToBeTiled(f)&&this.compoundOrder.push(f)}},v.prototype.adjustLocations=function(s,c,f,p,I){c+=p,f+=I;for(var R=c,M=0;M<s.rows.length;M++){var w=s.rows[M];c=R;for(var G=0,U=0;U<w.length;U++){var X=w[U];X.rect.x=c,X.rect.y=f,c+=X.rect.width+s.horizontalPadding,X.rect.height>G&&(G=X.rect.height)}f+=G+s.verticalPadding}},v.prototype.tileCompoundMembers=function(s,c){var f=this;this.tiledMemberPack=[],Object.keys(s).forEach(function(p){var I=c[p];f.tiledMemberPack[p]=f.tileNodes(s[p],I.paddingLeft+I.paddingRight),I.rect.width=f.tiledMemberPack[p].width,I.rect.height=f.tiledMemberPack[p].height})},v.prototype.tileNodes=function(s,c){var f=a.TILING_PADDING_VERTICAL,p=a.TILING_PADDING_HORIZONTAL,I={rows:[],rowWidth:[],rowHeight:[],width:0,height:c,verticalPadding:f,horizontalPadding:p};s.sort(function(w,G){return w.rect.width*w.rect.height>G.rect.width*G.rect.height?-1:w.rect.width*w.rect.height<G.rect.width*G.rect.height?1:0});for(var R=0;R<s.length;R++){var M=s[R];I.rows.length==0?this.insertNodeToRow(I,M,0,c):this.canAddHorizontal(I,M.rect.width,M.rect.height)?this.insertNodeToRow(I,M,this.getShortestRowIndex(I),c):this.insertNodeToRow(I,M,I.rows.length,c),this.shiftToLastRow(I)}return I},v.prototype.insertNodeToRow=function(s,c,f,p){var I=p;if(f==s.rows.length){var R=[];s.rows.push(R),s.rowWidth.push(I),s.rowHeight.push(0)}var M=s.rowWidth[f]+c.rect.width;s.rows[f].length>0&&(M+=s.horizontalPadding),s.rowWidth[f]=M,s.width<M&&(s.width=M);var w=c.rect.height;f>0&&(w+=s.verticalPadding);var G=0;w>s.rowHeight[f]&&(G=s.rowHeight[f],s.rowHeight[f]=w,G=s.rowHeight[f]-G),s.height+=G,s.rows[f].push(c)},v.prototype.getShortestRowIndex=function(s){for(var c=-1,f=Number.MAX_VALUE,p=0;p<s.rows.length;p++)s.rowWidth[p]<f&&(c=p,f=s.rowWidth[p]);return c},v.prototype.getLongestRowIndex=function(s){for(var c=-1,f=Number.MIN_VALUE,p=0;p<s.rows.length;p++)s.rowWidth[p]>f&&(c=p,f=s.rowWidth[p]);return c},v.prototype.canAddHorizontal=function(s,c,f){var p=this.getShortestRowIndex(s);if(p<0)return!0;var I=s.rowWidth[p];if(I+s.horizontalPadding+c<=s.width)return!0;var R=0;s.rowHeight[p]<f&&p>0&&(R=f+s.verticalPadding-s.rowHeight[p]);var M;s.width-I>=c+s.horizontalPadding?M=(s.height+R)/(I+c+s.horizontalPadding):M=(s.height+R)/s.width,R=f+s.verticalPadding;var w;return s.width<c?w=(s.height+R)/c:w=(s.height+R)/s.width,w<1&&(w=1/w),M<1&&(M=1/M),M<w},v.prototype.shiftToLastRow=function(s){var c=this.getLongestRowIndex(s),f=s.rowWidth.length-1,p=s.rows[c],I=p[p.length-1],R=I.width+s.horizontalPadding;if(s.width-s.rowWidth[f]>R&&c!=f){p.splice(-1,1),s.rows[f].push(I),s.rowWidth[c]=s.rowWidth[c]-R,s.rowWidth[f]=s.rowWidth[f]+R,s.width=s.rowWidth[instance.getLongestRowIndex(s)];for(var M=Number.MIN_VALUE,w=0;w<p.length;w++)p[w].height>M&&(M=p[w].height);c>0&&(M+=s.verticalPadding);var G=s.rowHeight[c]+s.rowHeight[f];s.rowHeight[c]=M,s.rowHeight[f]<I.height+s.verticalPadding&&(s.rowHeight[f]=I.height+s.verticalPadding);var U=s.rowHeight[c]+s.rowHeight[f];s.height+=U-G,this.shiftToLastRow(s)}},v.prototype.tilingPreLayout=function(){a.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},v.prototype.tilingPostLayout=function(){a.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},v.prototype.reduceTrees=function(){for(var s=[],c=!0,f;c;){var p=this.graphManager.getAllNodes(),I=[];c=!1;for(var R=0;R<p.length;R++)f=p[R],f.getEdges().length==1&&!f.getEdges()[0].isInterGraph&&f.getChild()==null&&(I.push([f,f.getEdges()[0],f.getOwner()]),c=!0);if(c==!0){for(var M=[],w=0;w<I.length;w++)I[w][0].getEdges().length==1&&(M.push(I[w]),I[w][0].getOwner().remove(I[w][0]));s.push(M),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=s},v.prototype.growTree=function(s){for(var c=s.length,f=s[c-1],p,I=0;I<f.length;I++)p=f[I],this.findPlaceforPrunedNode(p),p[2].add(p[0]),p[2].add(p[1],p[1].source,p[1].target);s.splice(s.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},v.prototype.findPlaceforPrunedNode=function(s){var c,f,p=s[0];p==s[1].source?f=s[1].target:f=s[1].source;var I=f.startX,R=f.finishX,M=f.startY,w=f.finishY,G=0,U=0,X=0,_=0,S=[G,X,U,_];if(M>0)for(var F=I;F<=R;F++)S[0]+=this.grid[F][M-1].length+this.grid[F][M].length-1;if(R<this.grid.length-1)for(var F=M;F<=w;F++)S[1]+=this.grid[R+1][F].length+this.grid[R][F].length-1;if(w<this.grid[0].length-1)for(var F=I;F<=R;F++)S[2]+=this.grid[F][w+1].length+this.grid[F][w].length-1;if(I>0)for(var F=M;F<=w;F++)S[3]+=this.grid[I-1][F].length+this.grid[I][F].length-1;for(var b=T.MAX_VALUE,Y,k,H=0;H<S.length;H++)S[H]<b?(b=S[H],Y=1,k=H):S[H]==b&&Y++;if(Y==3&&b==0)S[0]==0&&S[1]==0&&S[2]==0?c=1:S[0]==0&&S[1]==0&&S[3]==0?c=0:S[0]==0&&S[2]==0&&S[3]==0?c=3:S[1]==0&&S[2]==0&&S[3]==0&&(c=2);else if(Y==2&&b==0){var P=Math.floor(Math.random()*2);S[0]==0&&S[1]==0?P==0?c=0:c=1:S[0]==0&&S[2]==0?P==0?c=0:c=2:S[0]==0&&S[3]==0?P==0?c=0:c=3:S[1]==0&&S[2]==0?P==0?c=1:c=2:S[1]==0&&S[3]==0?P==0?c=1:c=3:P==0?c=2:c=3}else if(Y==4&&b==0){var P=Math.floor(Math.random()*4);c=P}else c=k;c==0?p.setCenter(f.getCenterX(),f.getCenterY()-f.getHeight()/2-y.DEFAULT_EDGE_LENGTH-p.getHeight()/2):c==1?p.setCenter(f.getCenterX()+f.getWidth()/2+y.DEFAULT_EDGE_LENGTH+p.getWidth()/2,f.getCenterY()):c==2?p.setCenter(f.getCenterX(),f.getCenterY()+f.getHeight()/2+y.DEFAULT_EDGE_LENGTH+p.getHeight()/2):p.setCenter(f.getCenterX()-f.getWidth()/2-y.DEFAULT_EDGE_LENGTH-p.getWidth()/2,f.getCenterY())},D.exports=v},function(D,d,i){var t={};t.layoutBase=i(0),t.CoSEConstants=i(1),t.CoSEEdge=i(2),t.CoSEGraph=i(3),t.CoSEGraphManager=i(4),t.CoSELayout=i(6),t.CoSENode=i(5),D.exports=t}])})}}),At=nt({"../../node_modules/.pnpm/cytoscape-cose-bilkent@4.1.0_cytoscape@3.31.0/node_modules/cytoscape-cose-bilkent/cytoscape-cose-bilkent.js"(C,x){A(function(D,d){typeof C=="object"&&typeof x=="object"?x.exports=d(lt()):typeof define=="function"&&define.amd?define(["cose-base"],d):typeof C=="object"?C.cytoscapeCoseBilkent=d(lt()):D.cytoscapeCoseBilkent=d(D.coseBase)},"webpackUniversalModuleDefinition")(C,function(u){return(function(D){var d={};function i(t){if(d[t])return d[t].exports;var e=d[t]={i:t,l:!1,exports:{}};return D[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}return A(i,"__webpack_require__"),i.m=D,i.c=d,i.i=function(t){return t},i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:r})},i.n=function(t){var e=t&&t.__esModule?A(function(){return t.default},"getDefault"):A(function(){return t},"getModuleExports");return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=1)})([function(D,d){D.exports=u},function(D,d,i){var t=i(0).layoutBase.LayoutConstants,e=i(0).layoutBase.FDLayoutConstants,r=i(0).CoSEConstants,o=i(0).CoSELayout,g=i(0).CoSENode,a=i(0).layoutBase.PointD,y=i(0).layoutBase.DimensionD,n={ready:A(function(){},"ready"),stop:A(function(){},"stop"),quality:"default",nodeDimensionsIncludeLabels:!1,refresh:30,fit:!0,padding:10,randomize:!0,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function h(N,L){var O={};for(var v in N)O[v]=N[v];for(var v in L)O[v]=L[v];return O}A(h,"extend");function l(N){this.options=h(n,N),E(this.options)}A(l,"_CoSELayout");var E=A(function(L){L.nodeRepulsion!=null&&(r.DEFAULT_REPULSION_STRENGTH=e.DEFAULT_REPULSION_STRENGTH=L.nodeRepulsion),L.idealEdgeLength!=null&&(r.DEFAULT_EDGE_LENGTH=e.DEFAULT_EDGE_LENGTH=L.idealEdgeLength),L.edgeElasticity!=null&&(r.DEFAULT_SPRING_STRENGTH=e.DEFAULT_SPRING_STRENGTH=L.edgeElasticity),L.nestingFactor!=null&&(r.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=e.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=L.nestingFactor),L.gravity!=null&&(r.DEFAULT_GRAVITY_STRENGTH=e.DEFAULT_GRAVITY_STRENGTH=L.gravity),L.numIter!=null&&(r.MAX_ITERATIONS=e.MAX_ITERATIONS=L.numIter),L.gravityRange!=null&&(r.DEFAULT_GRAVITY_RANGE_FACTOR=e.DEFAULT_GRAVITY_RANGE_FACTOR=L.gravityRange),L.gravityCompound!=null&&(r.DEFAULT_COMPOUND_GRAVITY_STRENGTH=e.DEFAULT_COMPOUND_GRAVITY_STRENGTH=L.gravityCompound),L.gravityRangeCompound!=null&&(r.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=e.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=L.gravityRangeCompound),L.initialEnergyOnIncremental!=null&&(r.DEFAULT_COOLING_FACTOR_INCREMENTAL=e.DEFAULT_COOLING_FACTOR_INCREMENTAL=L.initialEnergyOnIncremental),L.quality=="draft"?t.QUALITY=0:L.quality=="proof"?t.QUALITY=2:t.QUALITY=1,r.NODE_DIMENSIONS_INCLUDE_LABELS=e.NODE_DIMENSIONS_INCLUDE_LABELS=t.NODE_DIMENSIONS_INCLUDE_LABELS=L.nodeDimensionsIncludeLabels,r.DEFAULT_INCREMENTAL=e.DEFAULT_INCREMENTAL=t.DEFAULT_INCREMENTAL=!L.randomize,r.ANIMATE=e.ANIMATE=t.ANIMATE=L.animate,r.TILE=L.tile,r.TILING_PADDING_VERTICAL=typeof L.tilingPaddingVertical=="function"?L.tilingPaddingVertical.call():L.tilingPaddingVertical,r.TILING_PADDING_HORIZONTAL=typeof L.tilingPaddingHorizontal=="function"?L.tilingPaddingHorizontal.call():L.tilingPaddingHorizontal},"getUserOptions");l.prototype.run=function(){var N,L,O=this.options;this.idToLNode={};var v=this.layout=new o,m=this;m.stopped=!1,this.cy=this.options.cy,this.cy.trigger({type:"layoutstart",layout:this});var s=v.newGraphManager();this.gm=s;var c=this.options.eles.nodes(),f=this.options.eles.edges();this.root=s.addRoot(),this.processChildrenList(this.root,this.getTopMostNodes(c),v);for(var p=0;p<f.length;p++){var I=f[p],R=this.idToLNode[I.data("source")],M=this.idToLNode[I.data("target")];if(R!==M&&R.getEdgesBetween(M).length==0){var w=s.add(v.newEdge(),R,M);w.id=I.id()}}var G=A(function(_,S){typeof _=="number"&&(_=S);var F=_.data("id"),b=m.idToLNode[F];return{x:b.getRect().getCenterX(),y:b.getRect().getCenterY()}},"getPositions"),U=A(function X(){for(var _=A(function(){O.fit&&O.cy.fit(O.eles,O.padding),N||(N=!0,m.cy.one("layoutready",O.ready),m.cy.trigger({type:"layoutready",layout:m}))},"afterReposition"),S=m.options.refresh,F,b=0;b<S&&!F;b++)F=m.stopped||m.layout.tick();if(F){v.checkLayoutSuccess()&&!v.isSubLayout&&v.doPostLayout(),v.tilingPostLayout&&v.tilingPostLayout(),v.isLayoutFinished=!0,m.options.eles.nodes().positions(G),_(),m.cy.one("layoutstop",m.options.stop),m.cy.trigger({type:"layoutstop",layout:m}),L&&cancelAnimationFrame(L),N=!1;return}var Y=m.layout.getPositionsData();O.eles.nodes().positions(function(k,H){if(typeof k=="number"&&(k=H),!k.isParent()){for(var P=k.id(),W=Y[P],$=k;W==null&&(W=Y[$.data("parent")]||Y["DummyCompound_"+$.data("parent")],Y[P]=W,$=$.parent()[0],$!=null););return W!=null?{x:W.x,y:W.y}:{x:k.position("x"),y:k.position("y")}}}),_(),L=requestAnimationFrame(X)},"iterateAnimated");return v.addListener("layoutstarted",function(){m.options.animate==="during"&&(L=requestAnimationFrame(U))}),v.runLayout(),this.options.animate!=="during"&&(m.options.eles.nodes().not(":parent").layoutPositions(m,m.options,G),N=!1),this},l.prototype.getTopMostNodes=function(N){for(var L={},O=0;O<N.length;O++)L[N[O].id()]=!0;var v=N.filter(function(m,s){typeof m=="number"&&(m=s);for(var c=m.parent()[0];c!=null;){if(L[c.id()])return!1;c=c.parent()[0]}return!0});return v},l.prototype.processChildrenList=function(N,L,O){for(var v=L.length,m=0;m<v;m++){var s=L[m],c=s.children(),f,p=s.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if(s.outerWidth()!=null&&s.outerHeight()!=null?f=N.add(new g(O.graphManager,new a(s.position("x")-p.w/2,s.position("y")-p.h/2),new y(parseFloat(p.w),parseFloat(p.h)))):f=N.add(new g(this.graphManager)),f.id=s.data("id"),f.paddingLeft=parseInt(s.css("padding")),f.paddingTop=parseInt(s.css("padding")),f.paddingRight=parseInt(s.css("padding")),f.paddingBottom=parseInt(s.css("padding")),this.options.nodeDimensionsIncludeLabels&&s.isParent()){var I=s.boundingBox({includeLabels:!0,includeNodes:!1}).w,R=s.boundingBox({includeLabels:!0,includeNodes:!1}).h,M=s.css("text-halign");f.labelWidth=I,f.labelHeight=R,f.labelPos=M}if(this.idToLNode[s.data("id")]=f,isNaN(f.rect.x)&&(f.rect.x=0),isNaN(f.rect.y)&&(f.rect.y=0),c!=null&&c.length>0){var w;w=O.getGraphManager().add(O.newGraph(),f),this.processChildrenList(w,c,O)}}},l.prototype.stop=function(){return this.stopped=!0,this};var T=A(function(L){L("layout","cose-bilkent",l)},"register");typeof cytoscape<"u"&&T(cytoscape),D.exports=T}])})}}),rt=(function(){var C=A(function(O,v,m,s){for(m=m||{},s=O.length;s--;m[O[s]]=v);return m},"o"),x=[1,4],u=[1,13],D=[1,12],d=[1,15],i=[1,16],t=[1,20],e=[1,19],r=[6,7,8],o=[1,26],g=[1,24],a=[1,25],y=[6,7,11],n=[1,6,13,15,16,19,22],h=[1,33],l=[1,34],E=[1,6,7,11,13,15,16,19,22],T={trace:A(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:A(function(v,m,s,c,f,p,I){var R=p.length-1;switch(f){case 6:case 7:return c;case 8:c.getLogger().trace("Stop NL ");break;case 9:c.getLogger().trace("Stop EOF ");break;case 11:c.getLogger().trace("Stop NL2 ");break;case 12:c.getLogger().trace("Stop EOF2 ");break;case 15:c.getLogger().info("Node: ",p[R].id),c.addNode(p[R-1].length,p[R].id,p[R].descr,p[R].type);break;case 16:c.getLogger().trace("Icon: ",p[R]),c.decorateNode({icon:p[R]});break;case 17:case 21:c.decorateNode({class:p[R]});break;case 18:c.getLogger().trace("SPACELIST");break;case 19:c.getLogger().trace("Node: ",p[R].id),c.addNode(0,p[R].id,p[R].descr,p[R].type);break;case 20:c.decorateNode({icon:p[R]});break;case 25:c.getLogger().trace("node found ..",p[R-2]),this.$={id:p[R-1],descr:p[R-1],type:c.getType(p[R-2],p[R])};break;case 26:this.$={id:p[R],descr:p[R],type:c.nodeType.DEFAULT};break;case 27:c.getLogger().trace("node found ..",p[R-3]),this.$={id:p[R-3],descr:p[R-1],type:c.getType(p[R-2],p[R])};break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:x},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:x},{6:u,7:[1,10],9:9,12:11,13:D,14:14,15:d,16:i,17:17,18:18,19:t,22:e},C(r,[2,3]),{1:[2,2]},C(r,[2,4]),C(r,[2,5]),{1:[2,6],6:u,12:21,13:D,14:14,15:d,16:i,17:17,18:18,19:t,22:e},{6:u,9:22,12:11,13:D,14:14,15:d,16:i,17:17,18:18,19:t,22:e},{6:o,7:g,10:23,11:a},C(y,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:t,22:e}),C(y,[2,18]),C(y,[2,19]),C(y,[2,20]),C(y,[2,21]),C(y,[2,23]),C(y,[2,24]),C(y,[2,26],{19:[1,30]}),{20:[1,31]},{6:o,7:g,10:32,11:a},{1:[2,7],6:u,12:21,13:D,14:14,15:d,16:i,17:17,18:18,19:t,22:e},C(n,[2,14],{7:h,11:l}),C(E,[2,8]),C(E,[2,9]),C(E,[2,10]),C(y,[2,15]),C(y,[2,16]),C(y,[2,17]),{20:[1,35]},{21:[1,36]},C(n,[2,13],{7:h,11:l}),C(E,[2,11]),C(E,[2,12]),{21:[1,37]},C(y,[2,25]),C(y,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:A(function(v,m){if(m.recoverable)this.trace(v);else{var s=new Error(v);throw s.hash=m,s}},"parseError"),parse:A(function(v){var m=this,s=[0],c=[],f=[null],p=[],I=this.table,R="",M=0,w=0,G=2,U=1,X=p.slice.call(arguments,1),_=Object.create(this.lexer),S={yy:{}};for(var F in this.yy)Object.prototype.hasOwnProperty.call(this.yy,F)&&(S.yy[F]=this.yy[F]);_.setInput(v,S.yy),S.yy.lexer=_,S.yy.parser=this,typeof _.yylloc>"u"&&(_.yylloc={});var b=_.yylloc;p.push(b);var Y=_.options&&_.options.ranges;typeof S.yy.parseError=="function"?this.parseError=S.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function k(B){s.length=s.length-2*B,f.length=f.length-B,p.length=p.length-B}A(k,"popStack");function H(){var B;return B=c.pop()||_.lex()||U,typeof B!="number"&&(B instanceof Array&&(c=B,B=c.pop()),B=m.symbols_[B]||B),B}A(H,"lex");for(var P,W,$,Q,V={},z,j,ot,q;;){if(W=s[s.length-1],this.defaultActions[W]?$=this.defaultActions[W]:((P===null||typeof P>"u")&&(P=H()),$=I[W]&&I[W][P]),typeof $>"u"||!$.length||!$[0]){var et="";q=[];for(z in I[W])this.terminals_[z]&&z>G&&q.push("'"+this.terminals_[z]+"'");_.showPosition?et="Parse error on line "+(M+1)+`:
`+_.showPosition()+`
Expecting `+q.join(", ")+", got '"+(this.terminals_[P]||P)+"'":et="Parse error on line "+(M+1)+": Unexpected "+(P==U?"end of input":"'"+(this.terminals_[P]||P)+"'"),this.parseError(et,{text:_.match,token:this.terminals_[P]||P,line:_.yylineno,loc:b,expected:q})}if($[0]instanceof Array&&$.length>1)throw new Error("Parse Error: multiple actions possible at state: "+W+", token: "+P);switch($[0]){case 1:s.push(P),f.push(_.yytext),p.push(_.yylloc),s.push($[1]),P=null,w=_.yyleng,R=_.yytext,M=_.yylineno,b=_.yylloc;break;case 2:if(j=this.productions_[$[1]][1],V.$=f[f.length-j],V._$={first_line:p[p.length-(j||1)].first_line,last_line:p[p.length-1].last_line,first_column:p[p.length-(j||1)].first_column,last_column:p[p.length-1].last_column},Y&&(V._$.range=[p[p.length-(j||1)].range[0],p[p.length-1].range[1]]),Q=this.performAction.apply(V,[R,w,M,S.yy,$[1],f,p].concat(X)),typeof Q<"u")return Q;j&&(s=s.slice(0,-1*j*2),f=f.slice(0,-1*j),p=p.slice(0,-1*j)),s.push(this.productions_[$[1]][0]),f.push(V.$),p.push(V._$),ot=I[s[s.length-2]][s[s.length-1]],s.push(ot);break;case 3:return!0}}return!0},"parse")},N=(function(){var O={EOF:1,parseError:A(function(m,s){if(this.yy.parser)this.yy.parser.parseError(m,s);else throw new Error(m)},"parseError"),setInput:A(function(v,m){return this.yy=m||this.yy||{},this._input=v,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:A(function(){var v=this._input[0];this.yytext+=v,this.yyleng++,this.offset++,this.match+=v,this.matched+=v;var m=v.match(/(?:\r\n?|\n).*/g);return m?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),v},"input"),unput:A(function(v){var m=v.length,s=v.split(/(?:\r\n?|\n)/g);this._input=v+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-m),this.offset-=m;var c=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),s.length-1&&(this.yylineno-=s.length-1);var f=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===c.length?this.yylloc.first_column:0)+c[c.length-s.length].length-s[0].length:this.yylloc.first_column-m},this.options.ranges&&(this.yylloc.range=[f[0],f[0]+this.yyleng-m]),this.yyleng=this.yytext.length,this},"unput"),more:A(function(){return this._more=!0,this},"more"),reject:A(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:A(function(v){this.unput(this.match.slice(v))},"less"),pastInput:A(function(){var v=this.matched.substr(0,this.matched.length-this.match.length);return(v.length>20?"...":"")+v.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:A(function(){var v=this.match;return v.length<20&&(v+=this._input.substr(0,20-v.length)),(v.substr(0,20)+(v.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:A(function(){var v=this.pastInput(),m=new Array(v.length+1).join("-");return v+this.upcomingInput()+`
`+m+"^"},"showPosition"),test_match:A(function(v,m){var s,c,f;if(this.options.backtrack_lexer&&(f={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(f.yylloc.range=this.yylloc.range.slice(0))),c=v[0].match(/(?:\r\n?|\n).*/g),c&&(this.yylineno+=c.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:c?c[c.length-1].length-c[c.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+v[0].length},this.yytext+=v[0],this.match+=v[0],this.matches=v,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(v[0].length),this.matched+=v[0],s=this.performAction.call(this,this.yy,this,m,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),s)return s;if(this._backtrack){for(var p in f)this[p]=f[p];return!1}return!1},"test_match"),next:A(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var v,m,s,c;this._more||(this.yytext="",this.match="");for(var f=this._currentRules(),p=0;p<f.length;p++)if(s=this._input.match(this.rules[f[p]]),s&&(!m||s[0].length>m[0].length)){if(m=s,c=p,this.options.backtrack_lexer){if(v=this.test_match(s,f[p]),v!==!1)return v;if(this._backtrack){m=!1;continue}else return!1}else if(!this.options.flex)break}return m?(v=this.test_match(m,f[c]),v!==!1?v:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:A(function(){var m=this.next();return m||this.lex()},"lex"),begin:A(function(m){this.conditionStack.push(m)},"begin"),popState:A(function(){var m=this.conditionStack.length-1;return m>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:A(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:A(function(m){return m=this.conditionStack.length-1-Math.abs(m||0),m>=0?this.conditionStack[m]:"INITIAL"},"topState"),pushState:A(function(m){this.begin(m)},"pushState"),stateStackSize:A(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:A(function(m,s,c,f){switch(c){case 0:return m.getLogger().trace("Found comment",s.yytext),6;case 1:return 8;case 2:this.begin("CLASS");break;case 3:return this.popState(),16;case 4:this.popState();break;case 5:m.getLogger().trace("Begin icon"),this.begin("ICON");break;case 6:return m.getLogger().trace("SPACELINE"),6;case 7:return 7;case 8:return 15;case 9:m.getLogger().trace("end icon"),this.popState();break;case 10:return m.getLogger().trace("Exploding node"),this.begin("NODE"),19;case 11:return m.getLogger().trace("Cloud"),this.begin("NODE"),19;case 12:return m.getLogger().trace("Explosion Bang"),this.begin("NODE"),19;case 13:return m.getLogger().trace("Cloud Bang"),this.begin("NODE"),19;case 14:return this.begin("NODE"),19;case 15:return this.begin("NODE"),19;case 16:return this.begin("NODE"),19;case 17:return this.begin("NODE"),19;case 18:return 13;case 19:return 22;case 20:return 11;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";case 23:this.popState();break;case 24:m.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 25:return m.getLogger().trace("description:",s.yytext),"NODE_DESCR";case 26:this.popState();break;case 27:return this.popState(),m.getLogger().trace("node end ))"),"NODE_DEND";case 28:return this.popState(),m.getLogger().trace("node end )"),"NODE_DEND";case 29:return this.popState(),m.getLogger().trace("node end ...",s.yytext),"NODE_DEND";case 30:return this.popState(),m.getLogger().trace("node end (("),"NODE_DEND";case 31:return this.popState(),m.getLogger().trace("node end (-"),"NODE_DEND";case 32:return this.popState(),m.getLogger().trace("node end (-"),"NODE_DEND";case 33:return this.popState(),m.getLogger().trace("node end (("),"NODE_DEND";case 34:return this.popState(),m.getLogger().trace("node end (("),"NODE_DEND";case 35:return m.getLogger().trace("Long description:",s.yytext),20;case 36:return m.getLogger().trace("Long description:",s.yytext),20}},"anonymous"),rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:!1},ICON:{rules:[8,9],inclusive:!1},NSTR2:{rules:[22,23],inclusive:!1},NSTR:{rules:[25,26],inclusive:!1},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:!1},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:!0}}};return O})();T.lexer=N;function L(){this.yy={}}return A(L,"Parser"),L.prototype=T,T.Parser=L,new L})();rt.parser=rt;var Ot=rt,It={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},K,Ct=(K=class{constructor(){this.nodes=[],this.count=0,this.elements={},this.getLogger=this.getLogger.bind(this),this.nodeType=It,this.clear(),this.getType=this.getType.bind(this),this.getMindmap=this.getMindmap.bind(this),this.getElementById=this.getElementById.bind(this),this.getParent=this.getParent.bind(this),this.getMindmap=this.getMindmap.bind(this),this.addNode=this.addNode.bind(this),this.decorateNode=this.decorateNode.bind(this)}clear(){this.nodes=[],this.count=0,this.elements={}}getParent(x){for(let u=this.nodes.length-1;u>=0;u--)if(this.nodes[u].level<x)return this.nodes[u];return null}getMindmap(){return this.nodes.length>0?this.nodes[0]:null}addNode(x,u,D,d){Z.info("addNode",x,u,D,d);const i=it();let t=i.mindmap?.padding??tt.mindmap.padding;switch(d){case this.nodeType.ROUNDED_RECT:case this.nodeType.RECT:case this.nodeType.HEXAGON:t*=2;break}const e={id:this.count++,nodeId:J(u,i),level:x,descr:J(D,i),type:d,children:[],width:i.mindmap?.maxNodeWidth??tt.mindmap.maxNodeWidth,padding:t},r=this.getParent(x);if(r)r.children.push(e),this.nodes.push(e);else if(this.nodes.length===0)this.nodes.push(e);else throw new Error(`There can be only one root. No parent could be found for ("${e.descr}")`)}getType(x,u){switch(Z.debug("In get type",x,u),x){case"[":return this.nodeType.RECT;case"(":return u===")"?this.nodeType.ROUNDED_RECT:this.nodeType.CLOUD;case"((":return this.nodeType.CIRCLE;case")":return this.nodeType.CLOUD;case"))":return this.nodeType.BANG;case"{{":return this.nodeType.HEXAGON;default:return this.nodeType.DEFAULT}}setElementForId(x,u){this.elements[x]=u}getElementById(x){return this.elements[x]}decorateNode(x){if(!x)return;const u=it(),D=this.nodes[this.nodes.length-1];x.icon&&(D.icon=J(x.icon,u)),x.class&&(D.class=J(x.class,u))}type2Str(x){switch(x){case this.nodeType.DEFAULT:return"no-border";case this.nodeType.RECT:return"rect";case this.nodeType.ROUNDED_RECT:return"rounded-rect";case this.nodeType.CIRCLE:return"circle";case this.nodeType.CLOUD:return"cloud";case this.nodeType.BANG:return"bang";case this.nodeType.HEXAGON:return"hexgon";default:return"no-border"}}getLogger(){return Z}},A(K,"MindmapDB"),K),Rt=Dt(At()),Mt=12,wt=A(function(C,x,u,D){x.append("path").attr("id","node-"+u.id).attr("class","node-bkg node-"+C.type2Str(u.type)).attr("d",`M0 ${u.height-5} v${-u.height+10} q0,-5 5,-5 h${u.width-10} q5,0 5,5 v${u.height-5} H0 Z`),x.append("line").attr("class","node-line-"+D).attr("x1",0).attr("y1",u.height).attr("x2",u.width).attr("y2",u.height)},"defaultBkg"),xt=A(function(C,x,u){x.append("rect").attr("id","node-"+u.id).attr("class","node-bkg node-"+C.type2Str(u.type)).attr("height",u.height).attr("width",u.width)},"rectBkg"),_t=A(function(C,x,u){const D=u.width,d=u.height,i=.15*D,t=.25*D,e=.35*D,r=.2*D;x.append("path").attr("id","node-"+u.id).attr("class","node-bkg node-"+C.type2Str(u.type)).attr("d",`M0 0 a${i},${i} 0 0,1 ${D*.25},${-1*D*.1}
      a${e},${e} 1 0,1 ${D*.4},${-1*D*.1}
      a${t},${t} 1 0,1 ${D*.35},${1*D*.2}

      a${i},${i} 1 0,1 ${D*.15},${1*d*.35}
      a${r},${r} 1 0,1 ${-1*D*.15},${1*d*.65}

      a${t},${i} 1 0,1 ${-1*D*.25},${D*.15}
      a${e},${e} 1 0,1 ${-1*D*.5},0
      a${i},${i} 1 0,1 ${-1*D*.25},${-1*D*.15}

      a${i},${i} 1 0,1 ${-1*D*.1},${-1*d*.35}
      a${r},${r} 1 0,1 ${D*.1},${-1*d*.65}

    H0 V0 Z`)},"cloudBkg"),St=A(function(C,x,u){const D=u.width,d=u.height,i=.15*D;x.append("path").attr("id","node-"+u.id).attr("class","node-bkg node-"+C.type2Str(u.type)).attr("d",`M0 0 a${i},${i} 1 0,0 ${D*.25},${-1*d*.1}
      a${i},${i} 1 0,0 ${D*.25},0
      a${i},${i} 1 0,0 ${D*.25},0
      a${i},${i} 1 0,0 ${D*.25},${1*d*.1}

      a${i},${i} 1 0,0 ${D*.15},${1*d*.33}
      a${i*.8},${i*.8} 1 0,0 0,${1*d*.34}
      a${i},${i} 1 0,0 ${-1*D*.15},${1*d*.33}

      a${i},${i} 1 0,0 ${-1*D*.25},${d*.15}
      a${i},${i} 1 0,0 ${-1*D*.25},0
      a${i},${i} 1 0,0 ${-1*D*.25},0
      a${i},${i} 1 0,0 ${-1*D*.25},${-1*d*.15}

      a${i},${i} 1 0,0 ${-1*D*.1},${-1*d*.33}
      a${i*.8},${i*.8} 1 0,0 0,${-1*d*.34}
      a${i},${i} 1 0,0 ${D*.1},${-1*d*.33}

    H0 V0 Z`)},"bangBkg"),Gt=A(function(C,x,u){x.append("circle").attr("id","node-"+u.id).attr("class","node-bkg node-"+C.type2Str(u.type)).attr("r",u.width/2)},"circleBkg");function gt(C,x,u,D,d){return C.insert("polygon",":first-child").attr("points",D.map(function(i){return i.x+","+i.y}).join(" ")).attr("transform","translate("+(d.width-x)/2+", "+u+")")}A(gt,"insertPolygonShape");var Ft=A(function(C,x,u){const D=u.height,i=D/4,t=u.width-u.padding+2*i,e=[{x:i,y:0},{x:t-i,y:0},{x:t,y:-D/2},{x:t-i,y:-D},{x:i,y:-D},{x:0,y:-D/2}];gt(x,t,D,e,u)},"hexagonBkg"),bt=A(function(C,x,u){x.append("rect").attr("id","node-"+u.id).attr("class","node-bkg node-"+C.type2Str(u.type)).attr("height",u.height).attr("rx",u.padding).attr("ry",u.padding).attr("width",u.width)},"roundedRectBkg"),Ut=A(async function(C,x,u,D,d){const i=d.htmlLabels,t=D%(Mt-1),e=x.append("g");u.section=t;let r="section-"+t;t<0&&(r+=" section-root"),e.attr("class",(u.class?u.class+" ":"")+"mindmap-node "+r);const o=e.append("g"),g=e.append("g"),a=u.descr.replace(/(<br\/*>)/g,`
`);await mt(g,a,{useHtmlLabels:i,width:u.width,classes:"mindmap-node-label"},d),i||g.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");const y=g.node().getBBox(),[n]=Nt(d.fontSize);if(u.height=y.height+n*1.1*.5+u.padding,u.width=y.width+2*u.padding,u.icon)if(u.type===C.nodeType.CIRCLE)u.height+=50,u.width+=50,e.append("foreignObject").attr("height","50px").attr("width",u.width).attr("style","text-align: center;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+t+" "+u.icon),g.attr("transform","translate("+u.width/2+", "+(u.height/2-1.5*u.padding)+")");else{u.width+=50;const h=u.height;u.height=Math.max(h,60);const l=Math.abs(u.height-h);e.append("foreignObject").attr("width","60px").attr("height",u.height).attr("style","text-align: center;margin-top:"+l/2+"px;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+t+" "+u.icon),g.attr("transform","translate("+(25+u.width/2)+", "+(l/2+u.padding/2)+")")}else if(i){const h=(u.width-y.width)/2,l=(u.height-y.height)/2;g.attr("transform","translate("+h+", "+l+")")}else{const h=u.width/2,l=u.padding/2;g.attr("transform","translate("+h+", "+l+")")}switch(u.type){case C.nodeType.DEFAULT:wt(C,o,u,t);break;case C.nodeType.ROUNDED_RECT:bt(C,o,u,t);break;case C.nodeType.RECT:xt(C,o,u,t);break;case C.nodeType.CIRCLE:o.attr("transform","translate("+u.width/2+", "+ +u.height/2+")"),Gt(C,o,u,t);break;case C.nodeType.CLOUD:_t(C,o,u,t);break;case C.nodeType.BANG:St(C,o,u,t);break;case C.nodeType.HEXAGON:Ft(C,o,u,t);break}return C.setElementForId(u.id,e),u.height},"drawNode"),Pt=A(function(C,x){const u=C.getElementById(x.id),D=x.x||0,d=x.y||0;u.attr("transform","translate("+D+","+d+")")},"positionNode");ct.use(Rt.default);async function st(C,x,u,D,d){await Ut(C,x,u,D,d),u.children&&await Promise.all(u.children.map((i,t)=>st(C,x,i,D<0?t:D,d)))}A(st,"drawNodes");function ut(C,x){x.edges().map((u,D)=>{const d=u.data();if(u[0]._private.bodyBounds){const i=u[0]._private.rscratch;Z.trace("Edge: ",D,d),C.insert("path").attr("d",`M ${i.startX},${i.startY} L ${i.midX},${i.midY} L${i.endX},${i.endY} `).attr("class","edge section-edge-"+d.section+" edge-depth-"+d.depth)}})}A(ut,"drawEdges");function at(C,x,u,D){x.add({group:"nodes",data:{id:C.id.toString(),labelText:C.descr,height:C.height,width:C.width,level:D,nodeId:C.id,padding:C.padding,type:C.type},position:{x:C.x,y:C.y}}),C.children&&C.children.forEach(d=>{at(d,x,u,D+1),x.add({group:"edges",data:{id:`${C.id}_${d.id}`,source:C.id,target:d.id,depth:D,section:d.section}})})}A(at,"addNodes");function ft(C,x){return new Promise(u=>{const D=Tt("body").append("div").attr("id","cy").attr("style","display:none"),d=ct({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});D.remove(),at(C,d,x,0),d.nodes().forEach(function(i){i.layoutDimensions=()=>{const t=i.data();return{w:t.width,h:t.height}}}),d.layout({name:"cose-bilkent",quality:"proof",styleEnabled:!1,animate:!1}).run(),d.ready(i=>{Z.info("Ready",i),u(d)})})}A(ft,"layoutMindmap");function pt(C,x){x.nodes().map((u,D)=>{const d=u.data();d.x=u.position().x,d.y=u.position().y,Pt(C,d);const i=C.getElementById(d.nodeId);Z.info("id:",D,"Position: (",u.position().x,", ",u.position().y,")",d),i.attr("transform",`translate(${u.position().x-d.width/2}, ${u.position().y-d.height/2})`),i.attr("attr",`apa-${D})`)})}A(pt,"positionNodes");var Yt=A(async(C,x,u,D)=>{Z.debug(`Rendering mindmap diagram
`+C);const d=D.db,i=d.getMindmap();if(!i)return;const t=it();t.htmlLabels=!1;const e=dt(x),r=e.append("g");r.attr("class","mindmap-edges");const o=e.append("g");o.attr("class","mindmap-nodes"),await st(d,o,i,-1,t);const g=await ft(i,t);ut(r,g),pt(d,g),vt(void 0,e,t.mindmap?.padding??tt.mindmap.padding,t.mindmap?.useMaxWidth??tt.mindmap.useMaxWidth)},"draw"),Xt={draw:Yt},kt=A(C=>{let x="";for(let u=0;u<C.THEME_COLOR_LIMIT;u++)C["lineColor"+u]=C["lineColor"+u]||C["cScaleInv"+u],yt(C["lineColor"+u])?C["lineColor"+u]=Et(C["lineColor"+u],20):C["lineColor"+u]=Lt(C["lineColor"+u],20);for(let u=0;u<C.THEME_COLOR_LIMIT;u++){const D=""+(17-3*u);x+=`
    .section-${u-1} rect, .section-${u-1} path, .section-${u-1} circle, .section-${u-1} polygon, .section-${u-1} path  {
      fill: ${C["cScale"+u]};
    }
    .section-${u-1} text {
     fill: ${C["cScaleLabel"+u]};
    }
    .node-icon-${u-1} {
      font-size: 40px;
      color: ${C["cScaleLabel"+u]};
    }
    .section-edge-${u-1}{
      stroke: ${C["cScale"+u]};
    }
    .edge-depth-${u-1}{
      stroke-width: ${D};
    }
    .section-${u-1} line {
      stroke: ${C["cScaleInv"+u]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return x},"genSections"),Ht=A(C=>`
  .edge {
    stroke-width: 3;
  }
  ${kt(C)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${C.git0};
  }
  .section-root text {
    fill: ${C.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .mindmap-node-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,"getStyles"),$t=Ht,Vt={get db(){return new Ct},renderer:Xt,parser:Ot,styles:$t};export{Vt as diagram};
