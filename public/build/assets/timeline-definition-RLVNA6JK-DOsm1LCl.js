import{_ as s,c as xt,l as E,d as j,ap as kt,aq as _t,ar as vt,as as bt,a9 as nt,D as wt,at as St,z as Et}from"./app-BQZQgfaL.js";var Q=(function(){var n=s(function(x,r,a,c){for(a=a||{},c=x.length;c--;a[x[c]]=r);return a},"o"),t=[6,8,10,11,12,14,16,17,20,21],e=[1,9],l=[1,10],i=[1,11],d=[1,12],h=[1,13],f=[1,16],m=[1,17],p={trace:s(function(){},"trace"),yy:{},symbols_:{error:2,start:3,timeline:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,title:11,acc_title:12,acc_title_value:13,acc_descr:14,acc_descr_value:15,acc_descr_multiline_value:16,section:17,period_statement:18,event_statement:19,period:20,event:21,$accept:0,$end:1},terminals_:{2:"error",4:"timeline",6:"EOF",8:"SPACE",10:"NEWLINE",11:"title",12:"acc_title",13:"acc_title_value",14:"acc_descr",15:"acc_descr_value",16:"acc_descr_multiline_value",17:"section",20:"period",21:"event"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[18,1],[19,1]],performAction:s(function(r,a,c,u,y,o,w){var _=o.length-1;switch(y){case 1:return o[_-1];case 2:this.$=[];break;case 3:o[_-1].push(o[_]),this.$=o[_-1];break;case 4:case 5:this.$=o[_];break;case 6:case 7:this.$=[];break;case 8:u.getCommonDb().setDiagramTitle(o[_].substr(6)),this.$=o[_].substr(6);break;case 9:this.$=o[_].trim(),u.getCommonDb().setAccTitle(this.$);break;case 10:case 11:this.$=o[_].trim(),u.getCommonDb().setAccDescription(this.$);break;case 12:u.addSection(o[_].substr(8)),this.$=o[_].substr(8);break;case 15:u.addTask(o[_],0,""),this.$=o[_];break;case 16:u.addEvent(o[_].substr(2)),this.$=o[_];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},n(t,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:e,12:l,14:i,16:d,17:h,18:14,19:15,20:f,21:m},n(t,[2,7],{1:[2,1]}),n(t,[2,3]),{9:18,11:e,12:l,14:i,16:d,17:h,18:14,19:15,20:f,21:m},n(t,[2,5]),n(t,[2,6]),n(t,[2,8]),{13:[1,19]},{15:[1,20]},n(t,[2,11]),n(t,[2,12]),n(t,[2,13]),n(t,[2,14]),n(t,[2,15]),n(t,[2,16]),n(t,[2,4]),n(t,[2,9]),n(t,[2,10])],defaultActions:{},parseError:s(function(r,a){if(a.recoverable)this.trace(r);else{var c=new Error(r);throw c.hash=a,c}},"parseError"),parse:s(function(r){var a=this,c=[0],u=[],y=[null],o=[],w=this.table,_="",N=0,P=0,V=2,U=1,H=o.slice.call(arguments,1),g=Object.create(this.lexer),b={yy:{}};for(var L in this.yy)Object.prototype.hasOwnProperty.call(this.yy,L)&&(b.yy[L]=this.yy[L]);g.setInput(r,b.yy),b.yy.lexer=g,b.yy.parser=this,typeof g.yylloc>"u"&&(g.yylloc={});var M=g.yylloc;o.push(M);var W=g.options&&g.options.ranges;typeof b.yy.parseError=="function"?this.parseError=b.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Z(T){c.length=c.length-2*T,y.length=y.length-T,o.length=o.length-T}s(Z,"popStack");function tt(){var T;return T=u.pop()||g.lex()||U,typeof T!="number"&&(T instanceof Array&&(u=T,T=u.pop()),T=a.symbols_[T]||T),T}s(tt,"lex");for(var S,A,I,J,R={},B,$,et,O;;){if(A=c[c.length-1],this.defaultActions[A]?I=this.defaultActions[A]:((S===null||typeof S>"u")&&(S=tt()),I=w[A]&&w[A][S]),typeof I>"u"||!I.length||!I[0]){var K="";O=[];for(B in w[A])this.terminals_[B]&&B>V&&O.push("'"+this.terminals_[B]+"'");g.showPosition?K="Parse error on line "+(N+1)+`:
`+g.showPosition()+`
Expecting `+O.join(", ")+", got '"+(this.terminals_[S]||S)+"'":K="Parse error on line "+(N+1)+": Unexpected "+(S==U?"end of input":"'"+(this.terminals_[S]||S)+"'"),this.parseError(K,{text:g.match,token:this.terminals_[S]||S,line:g.yylineno,loc:M,expected:O})}if(I[0]instanceof Array&&I.length>1)throw new Error("Parse Error: multiple actions possible at state: "+A+", token: "+S);switch(I[0]){case 1:c.push(S),y.push(g.yytext),o.push(g.yylloc),c.push(I[1]),S=null,P=g.yyleng,_=g.yytext,N=g.yylineno,M=g.yylloc;break;case 2:if($=this.productions_[I[1]][1],R.$=y[y.length-$],R._$={first_line:o[o.length-($||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-($||1)].first_column,last_column:o[o.length-1].last_column},W&&(R._$.range=[o[o.length-($||1)].range[0],o[o.length-1].range[1]]),J=this.performAction.apply(R,[_,P,N,b.yy,I[1],y,o].concat(H)),typeof J<"u")return J;$&&(c=c.slice(0,-1*$*2),y=y.slice(0,-1*$),o=o.slice(0,-1*$)),c.push(this.productions_[I[1]][0]),y.push(R.$),o.push(R._$),et=w[c[c.length-2]][c[c.length-1]],c.push(et);break;case 3:return!0}}return!0},"parse")},k=(function(){var x={EOF:1,parseError:s(function(a,c){if(this.yy.parser)this.yy.parser.parseError(a,c);else throw new Error(a)},"parseError"),setInput:s(function(r,a){return this.yy=a||this.yy||{},this._input=r,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:s(function(){var r=this._input[0];this.yytext+=r,this.yyleng++,this.offset++,this.match+=r,this.matched+=r;var a=r.match(/(?:\r\n?|\n).*/g);return a?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),r},"input"),unput:s(function(r){var a=r.length,c=r.split(/(?:\r\n?|\n)/g);this._input=r+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-a),this.offset-=a;var u=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var y=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===u.length?this.yylloc.first_column:0)+u[u.length-c.length].length-c[0].length:this.yylloc.first_column-a},this.options.ranges&&(this.yylloc.range=[y[0],y[0]+this.yyleng-a]),this.yyleng=this.yytext.length,this},"unput"),more:s(function(){return this._more=!0,this},"more"),reject:s(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:s(function(r){this.unput(this.match.slice(r))},"less"),pastInput:s(function(){var r=this.matched.substr(0,this.matched.length-this.match.length);return(r.length>20?"...":"")+r.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:s(function(){var r=this.match;return r.length<20&&(r+=this._input.substr(0,20-r.length)),(r.substr(0,20)+(r.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:s(function(){var r=this.pastInput(),a=new Array(r.length+1).join("-");return r+this.upcomingInput()+`
`+a+"^"},"showPosition"),test_match:s(function(r,a){var c,u,y;if(this.options.backtrack_lexer&&(y={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(y.yylloc.range=this.yylloc.range.slice(0))),u=r[0].match(/(?:\r\n?|\n).*/g),u&&(this.yylineno+=u.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:u?u[u.length-1].length-u[u.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+r[0].length},this.yytext+=r[0],this.match+=r[0],this.matches=r,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(r[0].length),this.matched+=r[0],c=this.performAction.call(this,this.yy,this,a,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),c)return c;if(this._backtrack){for(var o in y)this[o]=y[o];return!1}return!1},"test_match"),next:s(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var r,a,c,u;this._more||(this.yytext="",this.match="");for(var y=this._currentRules(),o=0;o<y.length;o++)if(c=this._input.match(this.rules[y[o]]),c&&(!a||c[0].length>a[0].length)){if(a=c,u=o,this.options.backtrack_lexer){if(r=this.test_match(c,y[o]),r!==!1)return r;if(this._backtrack){a=!1;continue}else return!1}else if(!this.options.flex)break}return a?(r=this.test_match(a,y[u]),r!==!1?r:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:s(function(){var a=this.next();return a||this.lex()},"lex"),begin:s(function(a){this.conditionStack.push(a)},"begin"),popState:s(function(){var a=this.conditionStack.length-1;return a>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:s(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:s(function(a){return a=this.conditionStack.length-1-Math.abs(a||0),a>=0?this.conditionStack[a]:"INITIAL"},"topState"),pushState:s(function(a){this.begin(a)},"pushState"),stateStackSize:s(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:s(function(a,c,u,y){switch(u){case 0:break;case 1:break;case 2:return 10;case 3:break;case 4:break;case 5:return 4;case 6:return 11;case 7:return this.begin("acc_title"),12;case 8:return this.popState(),"acc_title_value";case 9:return this.begin("acc_descr"),14;case 10:return this.popState(),"acc_descr_value";case 11:this.begin("acc_descr_multiline");break;case 12:this.popState();break;case 13:return"acc_descr_multiline_value";case 14:return 17;case 15:return 21;case 16:return 20;case 17:return 6;case 18:return"INVALID"}},"anonymous"),rules:[/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:timeline\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:section\s[^:\n]+)/i,/^(?::\s(?:[^:\n]|:(?!\s))+)/i,/^(?:[^#:\n]+)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[12,13],inclusive:!1},acc_descr:{rules:[10],inclusive:!1},acc_title:{rules:[8],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,9,11,14,15,16,17,18],inclusive:!0}}};return x})();p.lexer=k;function v(){this.yy={}}return s(v,"Parser"),v.prototype=p,p.Parser=v,new v})();Q.parser=Q;var Tt=Q,at={};wt(at,{addEvent:()=>yt,addSection:()=>ht,addTask:()=>pt,addTaskOrg:()=>gt,clear:()=>ct,default:()=>It,getCommonDb:()=>ot,getSections:()=>dt,getTasks:()=>ut});var z="",lt=0,X=[],G=[],F=[],ot=s(()=>St,"getCommonDb"),ct=s(function(){X.length=0,G.length=0,z="",F.length=0,Et()},"clear"),ht=s(function(n){z=n,X.push(n)},"addSection"),dt=s(function(){return X},"getSections"),ut=s(function(){let n=it();const t=100;let e=0;for(;!n&&e<t;)n=it(),e++;return G.push(...F),G},"getTasks"),pt=s(function(n,t,e){const l={id:lt++,section:z,type:z,task:n,score:t||0,events:e?[e]:[]};F.push(l)},"addTask"),yt=s(function(n){F.find(e=>e.id===lt-1).events.push(n)},"addEvent"),gt=s(function(n){const t={section:z,type:z,description:n,task:n,classes:[]};G.push(t)},"addTaskOrg"),it=s(function(){const n=s(function(e){return F[e].processed},"compileTask");let t=!0;for(const[e,l]of F.entries())n(e),t=t&&l.processed;return t},"compileTasks"),It={clear:ct,getCommonDb:ot,addSection:ht,getSections:dt,getTasks:ut,addTask:pt,addTaskOrg:gt,addEvent:yt},Nt=12,q=s(function(n,t){const e=n.append("rect");return e.attr("x",t.x),e.attr("y",t.y),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("width",t.width),e.attr("height",t.height),e.attr("rx",t.rx),e.attr("ry",t.ry),t.class!==void 0&&e.attr("class",t.class),e},"drawRect"),Lt=s(function(n,t){const l=n.append("circle").attr("cx",t.cx).attr("cy",t.cy).attr("class","face").attr("r",15).attr("stroke-width",2).attr("overflow","visible"),i=n.append("g");i.append("circle").attr("cx",t.cx-15/3).attr("cy",t.cy-15/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666"),i.append("circle").attr("cx",t.cx+15/3).attr("cy",t.cy-15/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666");function d(m){const p=nt().startAngle(Math.PI/2).endAngle(3*(Math.PI/2)).innerRadius(7.5).outerRadius(6.8181818181818175);m.append("path").attr("class","mouth").attr("d",p).attr("transform","translate("+t.cx+","+(t.cy+2)+")")}s(d,"smile");function h(m){const p=nt().startAngle(3*Math.PI/2).endAngle(5*(Math.PI/2)).innerRadius(7.5).outerRadius(6.8181818181818175);m.append("path").attr("class","mouth").attr("d",p).attr("transform","translate("+t.cx+","+(t.cy+7)+")")}s(h,"sad");function f(m){m.append("line").attr("class","mouth").attr("stroke",2).attr("x1",t.cx-5).attr("y1",t.cy+7).attr("x2",t.cx+5).attr("y2",t.cy+7).attr("class","mouth").attr("stroke-width","1px").attr("stroke","#666")}return s(f,"ambivalent"),t.score>3?d(i):t.score<3?h(i):f(i),l},"drawFace"),Mt=s(function(n,t){const e=n.append("circle");return e.attr("cx",t.cx),e.attr("cy",t.cy),e.attr("class","actor-"+t.pos),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("r",t.r),e.class!==void 0&&e.attr("class",e.class),t.title!==void 0&&e.append("title").text(t.title),e},"drawCircle"),ft=s(function(n,t){const e=t.text.replace(/<br\s*\/?>/gi," "),l=n.append("text");l.attr("x",t.x),l.attr("y",t.y),l.attr("class","legend"),l.style("text-anchor",t.anchor),t.class!==void 0&&l.attr("class",t.class);const i=l.append("tspan");return i.attr("x",t.x+t.textMargin*2),i.text(e),l},"drawText"),$t=s(function(n,t){function e(i,d,h,f,m){return i+","+d+" "+(i+h)+","+d+" "+(i+h)+","+(d+f-m)+" "+(i+h-m*1.2)+","+(d+f)+" "+i+","+(d+f)}s(e,"genPoints");const l=n.append("polygon");l.attr("points",e(t.x,t.y,50,20,7)),l.attr("class","labelBox"),t.y=t.y+t.labelMargin,t.x=t.x+.5*t.labelMargin,ft(n,t)},"drawLabel"),Ht=s(function(n,t,e){const l=n.append("g"),i=Y();i.x=t.x,i.y=t.y,i.fill=t.fill,i.width=e.width,i.height=e.height,i.class="journey-section section-type-"+t.num,i.rx=3,i.ry=3,q(l,i),mt(e)(t.text,l,i.x,i.y,i.width,i.height,{class:"journey-section section-type-"+t.num},e,t.colour)},"drawSection"),rt=-1,Pt=s(function(n,t,e){const l=t.x+e.width/2,i=n.append("g");rt++,i.append("line").attr("id","task"+rt).attr("x1",l).attr("y1",t.y).attr("x2",l).attr("y2",450).attr("class","task-line").attr("stroke-width","1px").attr("stroke-dasharray","4 2").attr("stroke","#666"),Lt(i,{cx:l,cy:300+(5-t.score)*30,score:t.score});const h=Y();h.x=t.x,h.y=t.y,h.fill=t.fill,h.width=e.width,h.height=e.height,h.class="task task-type-"+t.num,h.rx=3,h.ry=3,q(i,h),mt(e)(t.task,i,h.x,h.y,h.width,h.height,{class:"task"},e,t.colour)},"drawTask"),At=s(function(n,t){q(n,{x:t.startx,y:t.starty,width:t.stopx-t.startx,height:t.stopy-t.starty,fill:t.fill,class:"rect"}).lower()},"drawBackgroundRect"),Ct=s(function(){return{x:0,y:0,fill:void 0,"text-anchor":"start",width:100,height:100,textMargin:0,rx:0,ry:0}},"getTextObj"),Y=s(function(){return{x:0,y:0,width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),mt=(function(){function n(i,d,h,f,m,p,k,v){const x=d.append("text").attr("x",h+m/2).attr("y",f+p/2+5).style("font-color",v).style("text-anchor","middle").text(i);l(x,k)}s(n,"byText");function t(i,d,h,f,m,p,k,v,x){const{taskFontSize:r,taskFontFamily:a}=v,c=i.split(/<br\s*\/?>/gi);for(let u=0;u<c.length;u++){const y=u*r-r*(c.length-1)/2,o=d.append("text").attr("x",h+m/2).attr("y",f).attr("fill",x).style("text-anchor","middle").style("font-size",r).style("font-family",a);o.append("tspan").attr("x",h+m/2).attr("dy",y).text(c[u]),o.attr("y",f+p/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),l(o,k)}}s(t,"byTspan");function e(i,d,h,f,m,p,k,v){const x=d.append("switch"),a=x.append("foreignObject").attr("x",h).attr("y",f).attr("width",m).attr("height",p).attr("position","fixed").append("xhtml:div").style("display","table").style("height","100%").style("width","100%");a.append("div").attr("class","label").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(i),t(i,x,h,f,m,p,k,v),l(a,k)}s(e,"byFo");function l(i,d){for(const h in d)h in d&&i.attr(h,d[h])}return s(l,"_setTextAttrs"),function(i){return i.textPlacement==="fo"?e:i.textPlacement==="old"?n:t}})(),Rt=s(function(n){n.append("defs").append("marker").attr("id","arrowhead").attr("refX",5).attr("refY",2).attr("markerWidth",6).attr("markerHeight",4).attr("orient","auto").append("path").attr("d","M 0,0 V 4 L6,2 Z")},"initGraphics");function D(n,t){n.each(function(){var e=j(this),l=e.text().split(/(\s+|<br>)/).reverse(),i,d=[],h=1.1,f=e.attr("y"),m=parseFloat(e.attr("dy")),p=e.text(null).append("tspan").attr("x",0).attr("y",f).attr("dy",m+"em");for(let k=0;k<l.length;k++)i=l[l.length-1-k],d.push(i),p.text(d.join(" ").trim()),(p.node().getComputedTextLength()>t||i==="<br>")&&(d.pop(),p.text(d.join(" ").trim()),i==="<br>"?d=[""]:d=[i],p=e.append("tspan").attr("x",0).attr("y",f).attr("dy",h+"em").text(i))})}s(D,"wrap");var zt=s(function(n,t,e,l){const i=e%Nt-1,d=n.append("g");t.section=i,d.attr("class",(t.class?t.class+" ":"")+"timeline-node "+("section-"+i));const h=d.append("g"),f=d.append("g"),p=f.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(D,t.width).node().getBBox(),k=l.fontSize?.replace?l.fontSize.replace("px",""):l.fontSize;return t.height=p.height+k*1.1*.5+t.padding,t.height=Math.max(t.height,t.maxHeight),t.width=t.width+2*t.padding,f.attr("transform","translate("+t.width/2+", "+t.padding/2+")"),Vt(h,t,i,l),t},"drawNode"),Ft=s(function(n,t,e){const l=n.append("g"),d=l.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(D,t.width).node().getBBox(),h=e.fontSize?.replace?e.fontSize.replace("px",""):e.fontSize;return l.remove(),d.height+h*1.1*.5+t.padding},"getVirtualNodeHeight"),Vt=s(function(n,t,e){n.append("path").attr("id","node-"+t.id).attr("class","node-bkg node-"+t.type).attr("d",`M0 ${t.height-5} v${-t.height+10} q0,-5 5,-5 h${t.width-10} q5,0 5,5 v${t.height-5} H0 Z`),n.append("line").attr("class","node-line-"+e).attr("x1",0).attr("y1",t.height).attr("x2",t.width).attr("y2",t.height)},"defaultBkg"),C={drawRect:q,drawCircle:Mt,drawSection:Ht,drawText:ft,drawLabel:$t,drawTask:Pt,drawBackgroundRect:At,getTextObj:Ct,getNoteRect:Y,initGraphics:Rt,drawNode:zt,getVirtualNodeHeight:Ft},Wt=s(function(n,t,e,l){const i=xt(),d=i.timeline?.leftMargin??50;E.debug("timeline",l.db);const h=i.securityLevel;let f;h==="sandbox"&&(f=j("#i"+t));const p=(h==="sandbox"?j(f.nodes()[0].contentDocument.body):j("body")).select("#"+t);p.append("g");const k=l.db.getTasks(),v=l.db.getCommonDb().getDiagramTitle();E.debug("task",k),C.initGraphics(p);const x=l.db.getSections();E.debug("sections",x);let r=0,a=0,c=0,u=0,y=50+d,o=50;u=50;let w=0,_=!0;x.forEach(function(H){const g={number:w,descr:H,section:w,width:150,padding:20,maxHeight:r},b=C.getVirtualNodeHeight(p,g,i);E.debug("sectionHeight before draw",b),r=Math.max(r,b+20)});let N=0,P=0;E.debug("tasks.length",k.length);for(const[H,g]of k.entries()){const b={number:H,descr:g,section:g.section,width:150,padding:20,maxHeight:a},L=C.getVirtualNodeHeight(p,b,i);E.debug("taskHeight before draw",L),a=Math.max(a,L+20),N=Math.max(N,g.events.length);let M=0;for(const W of g.events){const Z={descr:W,section:g.section,number:g.section,width:150,padding:20,maxHeight:50};M+=C.getVirtualNodeHeight(p,Z,i)}g.events.length>0&&(M+=(g.events.length-1)*10),P=Math.max(P,M)}E.debug("maxSectionHeight before draw",r),E.debug("maxTaskHeight before draw",a),x&&x.length>0?x.forEach(H=>{const g=k.filter(W=>W.section===H),b={number:w,descr:H,section:w,width:200*Math.max(g.length,1)-50,padding:20,maxHeight:r};E.debug("sectionNode",b);const L=p.append("g"),M=C.drawNode(L,b,w,i);E.debug("sectionNode output",M),L.attr("transform",`translate(${y}, ${u})`),o+=r+50,g.length>0&&st(p,g,w,y,o,a,i,N,P,r,!1),y+=200*Math.max(g.length,1),o=u,w++}):(_=!1,st(p,k,w,y,o,a,i,N,P,r,!0));const V=p.node().getBBox();E.debug("bounds",V),v&&p.append("text").text(v).attr("x",V.width/2-d).attr("font-size","4ex").attr("font-weight","bold").attr("y",20),c=_?r+a+150:a+100,p.append("g").attr("class","lineWrapper").append("line").attr("x1",d).attr("y1",c).attr("x2",V.width+3*d).attr("y2",c).attr("stroke-width",4).attr("stroke","black").attr("marker-end","url(#arrowhead)"),kt(void 0,p,i.timeline?.padding??50,i.timeline?.useMaxWidth??!1)},"draw"),st=s(function(n,t,e,l,i,d,h,f,m,p,k){for(const v of t){const x={descr:v.task,section:e,number:e,width:150,padding:20,maxHeight:d};E.debug("taskNode",x);const r=n.append("g").attr("class","taskWrapper"),c=C.drawNode(r,x,e,h).height;if(E.debug("taskHeight after draw",c),r.attr("transform",`translate(${l}, ${i})`),d=Math.max(d,c),v.events){const u=n.append("g").attr("class","lineWrapper");let y=d;i+=100,y=y+Bt(n,v.events,e,l,i,h),i-=100,u.append("line").attr("x1",l+190/2).attr("y1",i+d).attr("x2",l+190/2).attr("y2",i+d+100+m+100).attr("stroke-width",2).attr("stroke","black").attr("marker-end","url(#arrowhead)").attr("stroke-dasharray","5,5")}l=l+200,k&&!h.timeline?.disableMulticolor&&e++}i=i-10},"drawTasks"),Bt=s(function(n,t,e,l,i,d){let h=0;const f=i;i=i+100;for(const m of t){const p={descr:m,section:e,number:e,width:150,padding:20,maxHeight:50};E.debug("eventNode",p);const k=n.append("g").attr("class","eventWrapper"),x=C.drawNode(k,p,e,d).height;h=h+x,k.attr("transform",`translate(${l}, ${i})`),i=i+10+x}return i=f,h},"drawEvents"),Ot={setConf:s(()=>{},"setConf"),draw:Wt},jt=s(n=>{let t="";for(let e=0;e<n.THEME_COLOR_LIMIT;e++)n["lineColor"+e]=n["lineColor"+e]||n["cScaleInv"+e],_t(n["lineColor"+e])?n["lineColor"+e]=vt(n["lineColor"+e],20):n["lineColor"+e]=bt(n["lineColor"+e],20);for(let e=0;e<n.THEME_COLOR_LIMIT;e++){const l=""+(17-3*e);t+=`
    .section-${e-1} rect, .section-${e-1} path, .section-${e-1} circle, .section-${e-1} path  {
      fill: ${n["cScale"+e]};
    }
    .section-${e-1} text {
     fill: ${n["cScaleLabel"+e]};
    }
    .node-icon-${e-1} {
      font-size: 40px;
      color: ${n["cScaleLabel"+e]};
    }
    .section-edge-${e-1}{
      stroke: ${n["cScale"+e]};
    }
    .edge-depth-${e-1}{
      stroke-width: ${l};
    }
    .section-${e-1} line {
      stroke: ${n["cScaleInv"+e]} ;
      stroke-width: 3;
    }

    .lineWrapper line{
      stroke: ${n["cScaleLabel"+e]} ;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return t},"genSections"),Gt=s(n=>`
  .edge {
    stroke-width: 3;
  }
  ${jt(n)}
  .section-root rect, .section-root path, .section-root circle  {
    fill: ${n.git0};
  }
  .section-root text {
    fill: ${n.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .eventWrapper  {
   filter: brightness(120%);
  }
`,"getStyles"),qt=Gt,Zt={db:at,renderer:Ot,parser:Tt,styles:qt};export{Zt as diagram};
