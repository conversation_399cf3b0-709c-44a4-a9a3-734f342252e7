import{p as re}from"./chunk-K2ZEYYM2-CnH50PEr.js";import{p as oe}from"./treemap-KMMF4GRG-5JCI3IDA-Dr_hM2q7.js";import{_ as h,F as q,G as J,a6 as ie,e as ce,aI as P,l as I,aa as B,aJ as de,aK as pe,aL as T,d as E,b as he,a as ue,q as me,t as fe,g as ye,s as ge,H as Se,aM as xe,z as be}from"./app-BQZQgfaL.js";import{s as ve}from"./chunk-DYEWET5N-BxpfYBxX.js";import"./chunk-TGZYFRKZ-DbNMVLyI.js";var F,K=(F=class{constructor(){this.nodes=[],this.levels=new Map,this.outerNodes=[],this.classes=new Map,this.setAccTitle=he,this.getAccTitle=ue,this.setDiagramTitle=me,this.getDiagramTitle=fe,this.getAccDescription=ye,this.setAccDescription=ge}getNodes(){return this.nodes}getConfig(){const a=Se,o=J();return q({...a.treemap,...o.treemap??{}})}addNode(a,o){this.nodes.push(a),this.levels.set(a,o),o===0&&(this.outerNodes.push(a),this.root??=a)}getRoot(){return{name:"",children:this.outerNodes}}addClass(a,o){const r=this.classes.get(a)??{id:a,styles:[],textStyles:[]},c=o.replace(/\\,/g,"§§§").replace(/,/g,";").replace(/§§§/g,",").split(";");c&&c.forEach(s=>{xe(s)&&(r?.textStyles?r.textStyles.push(s):r.textStyles=[s]),r?.styles?r.styles.push(s):r.styles=[s]}),this.classes.set(a,r)}getClasses(){return this.classes}getStylesForClass(a){return this.classes.get(a)?.styles??[]}clear(){be(),this.nodes=[],this.levels=new Map,this.outerNodes=[],this.classes=new Map,this.root=void 0}},h(F,"TreeMapDB"),F);function U(d){if(!d.length)return[];const a=[],o=[];return d.forEach(r=>{const c={name:r.name,children:r.type==="Leaf"?void 0:[]};for(c.classSelector=r?.classSelector,r?.cssCompiledStyles&&(c.cssCompiledStyles=[r.cssCompiledStyles]),r.type==="Leaf"&&r.value!==void 0&&(c.value=r.value);o.length>0&&o[o.length-1].level>=r.level;)o.pop();if(o.length===0)a.push(c);else{const s=o[o.length-1].node;s.children?s.children.push(c):s.children=[c]}r.type!=="Leaf"&&o.push({node:c,level:r.level})}),a}h(U,"buildHierarchy");var Ce=h((d,a)=>{re(d,a);const o=[];for(const s of d.TreemapRows??[])s.$type==="ClassDefStatement"&&a.addClass(s.className??"",s.styleText??"");for(const s of d.TreemapRows??[]){const p=s.item;if(!p)continue;const m=s.indent?parseInt(s.indent):0,V=we(p),l=p.classSelector?a.getStylesForClass(p.classSelector):[],z=l.length>0?l.join(";"):void 0,b={level:m,name:V,type:p.$type,value:p.value,classSelector:p.classSelector,cssCompiledStyles:z};o.push(b)}const r=U(o),c=h((s,p)=>{for(const m of s)a.addNode(m,p),m.children&&m.children.length>0&&c(m.children,p+1)},"addNodesRecursively");c(r,0)},"populate"),we=h(d=>d.name?String(d.name):"","getItemName"),Q={parser:{yy:void 0},parse:h(async d=>{try{const o=await oe("treemap",d);I.debug("Treemap AST:",o);const r=Q.parser?.yy;if(!(r instanceof K))throw new Error("parser.parser?.yy was not a TreemapDB. This is due to a bug within Mermaid, please report this issue at https://github.com/mermaid-js/mermaid/issues.");Ce(o,r)}catch(a){throw I.error("Error parsing treemap:",a),a}},"parse")},Le=10,$=10,D=25,Te=h((d,a,o,r)=>{const c=r.db,s=c.getConfig(),p=s.padding??Le,m=c.getDiagramTitle(),V=c.getRoot(),{themeVariables:l}=J();if(!V)return;const z=m?30:0,b=ie(a),G=s.nodeWidth?s.nodeWidth*$:960,O=s.nodeHeight?s.nodeHeight*$:500,H=G,X=O+z;b.attr("viewBox",`0 0 ${H} ${X}`),ce(b,X,H,s.useMaxWidth);let v;try{const e=s.valueFormat||",";if(e==="$0,0")v=h(t=>"$"+P(",")(t),"valueFormat");else if(e.startsWith("$")&&e.includes(",")){const t=/\.\d+/.exec(e),n=t?t[0]:"";v=h(u=>"$"+P(","+n)(u),"valueFormat")}else if(e.startsWith("$")){const t=e.substring(1);v=h(n=>"$"+P(t||"")(n),"valueFormat")}else v=P(e)}catch(e){I.error("Error creating format function:",e),v=P(",")}const N=B().range(["transparent",l.cScale0,l.cScale1,l.cScale2,l.cScale3,l.cScale4,l.cScale5,l.cScale6,l.cScale7,l.cScale8,l.cScale9,l.cScale10,l.cScale11]),Z=B().range(["transparent",l.cScalePeer0,l.cScalePeer1,l.cScalePeer2,l.cScalePeer3,l.cScalePeer4,l.cScalePeer5,l.cScalePeer6,l.cScalePeer7,l.cScalePeer8,l.cScalePeer9,l.cScalePeer10,l.cScalePeer11]),W=B().range([l.cScaleLabel0,l.cScaleLabel1,l.cScaleLabel2,l.cScaleLabel3,l.cScaleLabel4,l.cScaleLabel5,l.cScaleLabel6,l.cScaleLabel7,l.cScaleLabel8,l.cScaleLabel9,l.cScaleLabel10,l.cScaleLabel11]);m&&b.append("text").attr("x",H/2).attr("y",z/2).attr("class","treemapTitle").attr("text-anchor","middle").attr("dominant-baseline","middle").text(m);const Y=b.append("g").attr("transform",`translate(0, ${z})`).attr("class","treemapContainer"),ee=de(V).sum(e=>e.value??0).sort((e,t)=>(t.value??0)-(e.value??0)),j=pe().size([G,O]).paddingTop(e=>e.children&&e.children.length>0?D+$:0).paddingInner(p).paddingLeft(e=>e.children&&e.children.length>0?$:0).paddingRight(e=>e.children&&e.children.length>0?$:0).paddingBottom(e=>e.children&&e.children.length>0?$:0).round(!0)(ee),te=j.descendants().filter(e=>e.children&&e.children.length>0),A=Y.selectAll(".treemapSection").data(te).enter().append("g").attr("class","treemapSection").attr("transform",e=>`translate(${e.x0},${e.y0})`);A.append("rect").attr("width",e=>e.x1-e.x0).attr("height",D).attr("class","treemapSectionHeader").attr("fill","none").attr("fill-opacity",.6).attr("stroke-width",.6).attr("style",e=>e.depth===0?"display: none;":""),A.append("clipPath").attr("id",(e,t)=>`clip-section-${a}-${t}`).append("rect").attr("width",e=>Math.max(0,e.x1-e.x0-12)).attr("height",D),A.append("rect").attr("width",e=>e.x1-e.x0).attr("height",e=>e.y1-e.y0).attr("class",(e,t)=>`treemapSection section${t}`).attr("fill",e=>N(e.data.name)).attr("fill-opacity",.6).attr("stroke",e=>Z(e.data.name)).attr("stroke-width",2).attr("stroke-opacity",.4).attr("style",e=>{if(e.depth===0)return"display: none;";const t=T({cssCompiledStyles:e.data.cssCompiledStyles});return t.nodeStyles+";"+t.borderStyles.join(";")}),A.append("text").attr("class","treemapSectionLabel").attr("x",6).attr("y",D/2).attr("dominant-baseline","middle").text(e=>e.depth===0?"":e.data.name).attr("font-weight","bold").attr("style",e=>{if(e.depth===0)return"display: none;";const t="dominant-baseline: middle; font-size: 12px; fill:"+W(e.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",n=T({cssCompiledStyles:e.data.cssCompiledStyles});return t+n.labelStyles.replace("color:","fill:")}).each(function(e){if(e.depth===0)return;const t=E(this),n=e.data.name;t.text(n);const u=e.x1-e.x0,y=6;let g;s.showValues!==!1&&e.value?g=u-10-30-10-y:g=u-y-6;const S=Math.max(15,g),i=t.node();if(i.getComputedTextLength()>S){let f=n;for(;f.length>0;){if(f=n.substring(0,f.length-1),f.length===0){t.text("..."),i.getComputedTextLength()>S&&t.text("");break}if(t.text(f+"..."),i.getComputedTextLength()<=S)break}}}),s.showValues!==!1&&A.append("text").attr("class","treemapSectionValue").attr("x",e=>e.x1-e.x0-10).attr("y",D/2).attr("text-anchor","end").attr("dominant-baseline","middle").text(e=>e.value?v(e.value):"").attr("font-style","italic").attr("style",e=>{if(e.depth===0)return"display: none;";const t="text-anchor: end; dominant-baseline: middle; font-size: 10px; fill:"+W(e.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",n=T({cssCompiledStyles:e.data.cssCompiledStyles});return t+n.labelStyles.replace("color:","fill:")});const ae=j.leaves(),_=Y.selectAll(".treemapLeafGroup").data(ae).enter().append("g").attr("class",(e,t)=>`treemapNode treemapLeafGroup leaf${t}${e.data.classSelector?` ${e.data.classSelector}`:""}x`).attr("transform",e=>`translate(${e.x0},${e.y0})`);_.append("rect").attr("width",e=>e.x1-e.x0).attr("height",e=>e.y1-e.y0).attr("class","treemapLeaf").attr("fill",e=>e.parent?N(e.parent.data.name):N(e.data.name)).attr("style",e=>T({cssCompiledStyles:e.data.cssCompiledStyles}).nodeStyles).attr("fill-opacity",.3).attr("stroke",e=>e.parent?N(e.parent.data.name):N(e.data.name)).attr("stroke-width",3),_.append("clipPath").attr("id",(e,t)=>`clip-${a}-${t}`).append("rect").attr("width",e=>Math.max(0,e.x1-e.x0-4)).attr("height",e=>Math.max(0,e.y1-e.y0-4)),_.append("text").attr("class","treemapLabel").attr("x",e=>(e.x1-e.x0)/2).attr("y",e=>(e.y1-e.y0)/2).attr("style",e=>{const t="text-anchor: middle; dominant-baseline: middle; font-size: 38px;fill:"+W(e.data.name)+";",n=T({cssCompiledStyles:e.data.cssCompiledStyles});return t+n.labelStyles.replace("color:","fill:")}).attr("clip-path",(e,t)=>`url(#clip-${a}-${t})`).text(e=>e.data.name).each(function(e){const t=E(this),n=e.x1-e.x0,u=e.y1-e.y0,y=t.node(),g=4,L=n-2*g,S=u-2*g;if(L<10||S<10){t.style("display","none");return}let i=parseInt(t.style("font-size"),10);const C=8,x=28,f=.6,w=6,k=2;for(;y.getComputedTextLength()>L&&i>C;)i--,t.style("font-size",`${i}px`);let M=Math.max(w,Math.min(x,Math.round(i*f))),R=i+k+M;for(;R>S&&i>C&&(i--,M=Math.max(w,Math.min(x,Math.round(i*f))),!(M<w&&i===C));)t.style("font-size",`${i}px`),R=i+k+M;t.style("font-size",`${i}px`),(y.getComputedTextLength()>L||i<C||S<i)&&t.style("display","none")}),s.showValues!==!1&&_.append("text").attr("class","treemapValue").attr("x",t=>(t.x1-t.x0)/2).attr("y",function(t){return(t.y1-t.y0)/2}).attr("style",t=>{const n="text-anchor: middle; dominant-baseline: hanging; font-size: 28px;fill:"+W(t.data.name)+";",u=T({cssCompiledStyles:t.data.cssCompiledStyles});return n+u.labelStyles.replace("color:","fill:")}).attr("clip-path",(t,n)=>`url(#clip-${a}-${n})`).text(t=>t.value?v(t.value):"").each(function(t){const n=E(this),u=this.parentNode;if(!u){n.style("display","none");return}const y=E(u).select(".treemapLabel");if(y.empty()||y.style("display")==="none"){n.style("display","none");return}const g=parseFloat(y.style("font-size")),L=28,S=.6,i=6,C=2,x=Math.max(i,Math.min(L,Math.round(g*S)));n.style("font-size",`${x}px`);const w=(t.y1-t.y0)/2+g/2+C;n.attr("y",w);const k=t.x1-t.x0,se=t.y1-t.y0-4,ne=k-8;n.node().getComputedTextLength()>ne||w+x>se||x<i?n.style("display","none"):n.style("display",null)});const le=s.diagramPadding??8;ve(b,le,"flowchart",s?.useMaxWidth||!1)},"draw"),$e=h(function(d,a){return a.db.getClasses()},"getClasses"),Fe={draw:Te,getClasses:$e},ze={sectionStrokeColor:"black",sectionStrokeWidth:"1",sectionFillColor:"#efefef",leafStrokeColor:"black",leafStrokeWidth:"1",leafFillColor:"#efefef",labelColor:"black",labelFontSize:"12px",valueFontSize:"10px",valueColor:"black",titleColor:"black",titleFontSize:"14px"},Ne=h(({treemap:d}={})=>{const a=q(ze,d);return`
  .treemapNode.section {
    stroke: ${a.sectionStrokeColor};
    stroke-width: ${a.sectionStrokeWidth};
    fill: ${a.sectionFillColor};
  }
  .treemapNode.leaf {
    stroke: ${a.leafStrokeColor};
    stroke-width: ${a.leafStrokeWidth};
    fill: ${a.leafFillColor};
  }
  .treemapLabel {
    fill: ${a.labelColor};
    font-size: ${a.labelFontSize};
  }
  .treemapValue {
    fill: ${a.valueColor};
    font-size: ${a.valueFontSize};
  }
  .treemapTitle {
    fill: ${a.titleColor};
    font-size: ${a.titleFontSize};
  }
  `},"getStyles"),Ae=Ne,Ee={parser:Q,get db(){return new K},renderer:Fe,styles:Ae};export{Ee as diagram};
