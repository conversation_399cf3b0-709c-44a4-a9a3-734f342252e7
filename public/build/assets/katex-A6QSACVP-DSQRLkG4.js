import{_ as d}from"./app-BQZQgfaL.js";var Z0,b0=(Z0=class{constructor(e,t,a){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=a}static range(e,t){return t?!e||!e.loc||!t.loc||e.loc.lexer!==t.loc.lexer?null:new Z0(e.loc.lexer,e.loc.start,t.loc.end):e&&e.loc}},d(Z0,"SourceLocation"),Z0),K0,A0=(K0=class{constructor(e,t){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=t}range(e,t){return new K0(t,b0.range(this,e))}},d(K0,"Token"),K0),J0,M=(J0=class{constructor(e,t){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;var a="KaTeX parse error: "+e,i,l,u=t&&t.loc;if(u&&u.start<=u.end){var h=u.lexer.input;i=u.start,l=u.end,i===h.length?a+=" at end of input: ":a+=" at position "+(i+1)+": ";var c=h.slice(i,l).replace(/[^]/g,"$&̲"),v;i>15?v="…"+h.slice(i-15,i):v=h.slice(0,i);var b;l+15<h.length?b=h.slice(l,l+15)+"…":b=h.slice(l),a+=v+c+b}var x=new Error(a);return x.name="ParseError",x.__proto__=J0.prototype,x.position=i,i!=null&&l!=null&&(x.length=l-i),x.rawMessage=e,x}},d(J0,"ParseError"),J0);M.prototype.__proto__=Error.prototype;var $1=d(function(e,t){return e.indexOf(t)!==-1},"contains"),Y1=d(function(e,t){return e===void 0?t:e},"deflt"),X1=/([A-Z])/g,W1=d(function(e){return e.replace(X1,"-$1").toLowerCase()},"hyphenate"),j1={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},Z1=/[&><"']/g;function Gr(r){return String(r).replace(Z1,e=>j1[e])}d(Gr,"escape");var Vr=d(function r(e){return e.type==="ordgroup"||e.type==="color"?e.body.length===1?r(e.body[0]):e:e.type==="font"?r(e.body):e},"getBaseElem"),K1=d(function(e){var t=Vr(e);return t.type==="mathord"||t.type==="textord"||t.type==="atom"},"isCharacterBox"),J1=d(function(e){if(!e)throw new Error("Expected non-null, but got "+String(e));return e},"assert"),Q1=d(function(e){var t=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(e);return t?t[2]!==":"||!/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(t[1])?null:t[1].toLowerCase():"_relative"},"protocolFromUrl"),N={contains:$1,deflt:Y1,escape:Gr,hyphenate:W1,getBaseElem:Vr,isCharacterBox:K1,protocolFromUrl:Q1},Qe={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:d(r=>"#"+r,"cliProcessor")},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:d((r,e)=>(e.push(r),e),"cliProcessor")},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:d(r=>Math.max(0,r),"processor"),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:d(r=>Math.max(0,r),"processor"),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:d(r=>Math.max(0,r),"processor"),cli:"-e, --max-expand <n>",cliProcessor:d(r=>r==="Infinity"?1/0:parseInt(r),"cliProcessor")},globalGroup:{type:"boolean",cli:!1}};function Ur(r){if(r.default)return r.default;var e=r.type,t=Array.isArray(e)?e[0]:e;if(typeof t!="string")return t.enum[0];switch(t){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}d(Ur,"getDefaultValue");var le,Ut=(le=class{constructor(e){this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{};for(var t in Qe)if(Qe.hasOwnProperty(t)){var a=Qe[t];this[t]=e[t]!==void 0?a.processor?a.processor(e[t]):e[t]:Ur(a)}}reportNonstrict(e,t,a){var i=this.strict;if(typeof i=="function"&&(i=i(e,t,a)),!(!i||i==="ignore")){if(i===!0||i==="error")throw new M("LaTeX-incompatible input and strict mode is set to 'error': "+(t+" ["+e+"]"),a);i==="warn"?typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" ["+e+"]")):typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+i+"': "+t+" ["+e+"]"))}}useStrictBehavior(e,t,a){var i=this.strict;if(typeof i=="function")try{i=i(e,t,a)}catch{i="error"}return!i||i==="ignore"?!1:i===!0||i==="error"?!0:i==="warn"?(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" ["+e+"]")),!1):(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+i+"': "+t+" ["+e+"]")),!1)}isTrusted(e){if(e.url&&!e.protocol){var t=N.protocolFromUrl(e.url);if(t==null)return!1;e.protocol=t}var a=typeof this.trust=="function"?this.trust(e):this.trust;return!!a}},d(le,"Settings"),le),se,P0=(se=class{constructor(e,t,a){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=a}sup(){return w0[_1[this.id]]}sub(){return w0[ea[this.id]]}fracNum(){return w0[ta[this.id]]}fracDen(){return w0[ra[this.id]]}cramp(){return w0[aa[this.id]]}text(){return w0[ia[this.id]]}isTight(){return this.size>=2}},d(se,"Style"),se),$t=0,tt=1,ne=2,E0=3,De=4,p0=5,ke=6,n0=7,w0=[new P0($t,0,!1),new P0(tt,0,!0),new P0(ne,1,!1),new P0(E0,1,!0),new P0(De,2,!1),new P0(p0,2,!0),new P0(ke,3,!1),new P0(n0,3,!0)],_1=[De,p0,De,p0,ke,n0,ke,n0],ea=[p0,p0,p0,p0,n0,n0,n0,n0],ta=[ne,E0,De,p0,ke,n0,ke,n0],ra=[E0,E0,p0,p0,n0,n0,n0,n0],aa=[tt,tt,E0,E0,p0,p0,n0,n0],ia=[$t,tt,ne,E0,ne,E0,ne,E0],R={DISPLAY:w0[$t],TEXT:w0[ne],SCRIPT:w0[De],SCRIPTSCRIPT:w0[ke]},Dt=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];function $r(r){for(var e=0;e<Dt.length;e++)for(var t=Dt[e],a=0;a<t.blocks.length;a++){var i=t.blocks[a];if(r>=i[0]&&r<=i[1])return t.name}return null}d($r,"scriptFromCodepoint");var _e=[];Dt.forEach(r=>r.blocks.forEach(e=>_e.push(...e)));function Yt(r){for(var e=0;e<_e.length;e+=2)if(r>=_e[e]&&r<=_e[e+1])return!0;return!1}d(Yt,"supportedCodepoint");var ie=80,na=d(function(e,t){return"M95,"+(622+e+t)+`
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l`+e/2.075+" -"+e+`
c5.3,-9.3,12,-14,20,-14
H400000v`+(40+e)+`H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M`+(834+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},"sqrtMain"),la=d(function(e,t){return"M263,"+(601+e+t)+`c0.7,0,18,39.7,52,119
c34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120
c340,-704.7,510.7,-1060.3,512,-1067
l`+e/2.084+" -"+e+`
c4.7,-7.3,11,-11,19,-11
H40000v`+(40+e)+`H1012.3
s-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232
c-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1
s-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26
c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},"sqrtSize1"),sa=d(function(e,t){return"M983 "+(10+e+t)+`
l`+e/3.13+" -"+e+`
c4,-6.7,10,-10,18,-10 H400000v`+(40+e)+`
H1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7
s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744
c-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30
c26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722
c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5
c53.7,-170.3,84.5,-266.8,92.5,-289.5z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},"sqrtSize2"),ua=d(function(e,t){return"M424,"+(2398+e+t)+`
c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514
c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20
s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121
s209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081
l`+e/4.223+" -"+e+`c4,-6.7,10,-10,18,-10 H400000
v`+(40+e)+`H1014.6
s-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185
c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2z M`+(1001+e)+" "+t+`
h400000v`+(40+e)+"h-400000z"},"sqrtSize3"),oa=d(function(e,t){return"M473,"+(2713+e+t)+`
c339.3,-1799.3,509.3,-2700,510,-2702 l`+e/5.298+" -"+e+`
c3.3,-7.3,9.3,-11,18,-11 H400000v`+(40+e)+`H1017.7
s-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200
c0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26
s76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,
606zM`+(1001+e)+" "+t+"h400000v"+(40+e)+"H1017.7z"},"sqrtSize4"),ha=d(function(e){var t=e/2;return"M400000 "+e+" H0 L"+t+" 0 l65 45 L145 "+(e-80)+" H400000z"},"phasePath"),ma=d(function(e,t,a){var i=a-54-t-e;return"M702 "+(e+t)+"H400000"+(40+e)+`
H742v`+i+`l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1
h-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170
c-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667
219 661 l218 661zM702 `+t+"H400000v"+(40+e)+"H742z"},"sqrtTall"),ca=d(function(e,t,a){t=1e3*t;var i="";switch(e){case"sqrtMain":i=na(t,ie);break;case"sqrtSize1":i=la(t,ie);break;case"sqrtSize2":i=sa(t,ie);break;case"sqrtSize3":i=ua(t,ie);break;case"sqrtSize4":i=oa(t,ie);break;case"sqrtTall":i=ma(t,ie,a)}return i},"sqrtPath"),da=d(function(e,t){switch(e){case"⎜":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"∣":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"∥":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z"+("M367 0 H410 V"+t+" H367z M367 0 H410 V"+t+" H367z");case"⎟":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"⎢":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"⎥":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"⎪":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"⏐":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"‖":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257z"+("M478 0 H521 V"+t+" H478z M478 0 H521 V"+t+" H478z");default:return""}},"innerPath"),dr={doubleleftarrow:`M262 157
l10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3
 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28
 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5
c2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5
 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87
-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7
-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z
m8 0v40h399730v-40zm0 194v40h399730v-40z`,doublerightarrow:`M399738 392l
-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5
 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88
-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68
-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18
-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782
c-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3
-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z`,leftarrow:`M400000 241H110l3-3c68.7-52.7 113.7-120
 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8
-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247
c-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208
 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3
 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202
 l-3-3h399890zM100 241v40h399900v-40z`,leftbrace:`M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117
-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7
 5-6 9-10 13-.7 1-7.3 1-20 1H6z`,leftbraceunder:`M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13
 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688
 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7
-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z`,leftgroup:`M400000 80
H435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0
 435 0h399565z`,leftgroupunder:`M400000 262
H435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219
 435 219h399565z`,leftharpoon:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3
-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5
-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7
-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z`,leftharpoonplus:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5
 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3
-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7
-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z
m0 0v40h400000v-40z`,leftharpoondown:`M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333
 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5
 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667
-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z`,leftharpoondownplus:`M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12
 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7
-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0
v40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z`,lefthook:`M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5
-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3
-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21
 71.5 23h399859zM103 281v-40h399897v40z`,leftlinesegment:`M40 281 V428 H0 V94 H40 V241 H400000 v40z
M40 281 V428 H0 V94 H40 V241 H400000 v40z`,leftmapsto:`M40 281 V448H0V74H40V241H400000v40z
M40 281 V448H0V74H40V241H400000v40z`,leftToFrom:`M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23
-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8
c28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3
 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z`,longequal:`M0 50 h400000 v40H0z m0 194h40000v40H0z
M0 50 h400000 v40H0z m0 194h40000v40H0z`,midbrace:`M200428 334
c-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14
-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7
 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11
 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z`,midbraceunder:`M199572 214
c100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14
 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3
 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0
-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z`,oiintSize1:`M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6
-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z
m368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8
60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z`,oiintSize2:`M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8
-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z
m502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2
c0 110 84 276 504 276s502.4-166 502.4-276z`,oiiintSize1:`M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6
-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z
m525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0
85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z`,oiiintSize2:`M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8
-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z
m770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1
c0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z`,rightarrow:`M0 241v40h399891c-47.3 35.3-84 78-110 128
-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20
 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7
 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85
-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
 151.7 139 205zm0 0v40h399900v-40z`,rightbrace:`M400000 542l
-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5
s-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1
c124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z`,rightbraceunder:`M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3
 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237
-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z`,rightgroup:`M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0
 3-1 3-3v-38c-76-158-257-219-435-219H0z`,rightgroupunder:`M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18
 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z`,rightharpoon:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3
-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2
-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58
 69.2 92 94.5zm0 0v40h399900v-40z`,rightharpoonplus:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11
-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7
 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z
m0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z`,rightharpoondown:`M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8
 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5
-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95
-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z`,rightharpoondownplus:`M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8
 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3
 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3
-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z
m0-194v40h400000v-40zm0 0v40h400000v-40z`,righthook:`M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3
 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0
-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21
 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z`,rightlinesegment:`M399960 241 V94 h40 V428 h-40 V281 H0 v-40z
M399960 241 V94 h40 V428 h-40 V281 H0 v-40z`,rightToFrom:`M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23
 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32
-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142
-167z M100 147v40h399900v-40zM0 341v40h399900v-40z`,twoheadleftarrow:`M0 167c68 40
 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69
-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3
-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19
-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101
 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z`,twoheadrightarrow:`M400000 167
c-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3
 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42
 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333
-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70
 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z`,tilde1:`M200 55.538c-77 0-168 73.953-177 73.953-3 0-7
-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0
 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0
 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128
-68.267.847-113-73.952-191-73.952z`,tilde2:`M344 55.266c-142 0-300.638 81.316-311.5 86.418
-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9
 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114
c1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751
 181.476 676 181.476c-149 0-189-126.21-332-126.21z`,tilde3:`M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457
-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0
 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697
 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696
 -338 0-409-156.573-744-156.573z`,tilde4:`M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345
-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409
 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9
 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409
 -175.236-744-175.236z`,vec:`M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5
3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11
10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63
-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1
-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59
H213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359
c-16-25.333-24-45-24-59z`,widehat1:`M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22
c-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z`,widehat2:`M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat3:`M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat4:`M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widecheck1:`M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,
-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z`,widecheck2:`M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck3:`M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck4:`M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,baraboveleftarrow:`M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202
c4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5
c-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130
s-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47
121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6
s2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11
c0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z
M100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z`,rightarrowabovebar:`M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32
-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0
13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39
-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5
-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z`,baraboveshortleftharpoon:`M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17
c2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21
c-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40
c-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z
M0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z`,rightharpoonaboveshortbar:`M0,241 l0,40c399126,0,399993,0,399993,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z`,shortbaraboveleftharpoon:`M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,
1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,
-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z
M93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z`,shortrightharpoonabovebar:`M53,241l0,40c398570,0,399437,0,399437,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`},pa=d(function(e,t){switch(e){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+` v1759 h347 v-84
H403z M403 1759 V0 H319 V1759 v`+t+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+` v1759 H0 v84 H347z
M347 1759 V0 H263 V1759 v`+t+" v1759 h84z";case"vert":return"M145 15 v585 v"+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+t+" v585 h43z";case"doublevert":return"M145 15 v585 v"+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+t+` v585 h43z
M367 15 v585 v`+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M410 15 H367 v585 v`+t+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+t+` v1715 h263 v84 H319z
MM319 602 V0 H403 V602 v`+t+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+t+` v1799 H0 v-84 H319z
MM319 602 V0 H403 V602 v`+t+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+` v602 h84z
M403 1759 V0 H319 V1759 v`+t+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+` v602 h84z
M347 1759 V0 h-84 V1759 v`+t+" v602 h84z";case"lparen":return`M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1
c-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,
-36,557 l0,`+(t+84)+`c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,
949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9
c0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,
-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189
l0,-`+(t+92)+`c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,
-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z`;case"rparen":return`M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,
63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5
c11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,`+(t+9)+`
c-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664
c-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11
c0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17
c242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558
l0,-`+(t+144)+`c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,
-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z`;default:throw new Error("Unknown stretchy delimiter.")}},"tallDelim"),ue,Ne=(ue=class{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return N.contains(this.classes,e)}toNode(){for(var e=document.createDocumentFragment(),t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){for(var e="",t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e}toText(){var e=d(t=>t.toText(),"toText");return this.children.map(e).join("")}},d(ue,"DocumentFragment"),ue),k0={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},Ve={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},pr={Å:"A",Ð:"D",Þ:"o",å:"a",ð:"d",þ:"o",А:"A",Б:"B",В:"B",Г:"F",Д:"A",Е:"E",Ж:"K",З:"3",И:"N",Й:"N",К:"K",Л:"N",М:"M",Н:"H",О:"O",П:"N",Р:"P",С:"C",Т:"T",У:"y",Ф:"O",Х:"X",Ц:"U",Ч:"h",Ш:"W",Щ:"W",Ъ:"B",Ы:"X",Ь:"B",Э:"3",Ю:"X",Я:"R",а:"a",б:"b",в:"a",г:"r",д:"y",е:"e",ж:"m",з:"e",и:"n",й:"n",к:"n",л:"n",м:"m",н:"n",о:"o",п:"n",р:"p",с:"c",т:"o",у:"y",ф:"b",х:"x",ц:"n",ч:"n",ш:"w",щ:"w",ъ:"a",ы:"m",ь:"a",э:"e",ю:"m",я:"r"};function Yr(r,e){k0[r]=e}d(Yr,"setFontMetrics");function st(r,e,t){if(!k0[e])throw new Error("Font metrics not found for font: "+e+".");var a=r.charCodeAt(0),i=k0[e][a];if(!i&&r[0]in pr&&(a=pr[r[0]].charCodeAt(0),i=k0[e][a]),!i&&t==="text"&&Yt(a)&&(i=k0[e][77]),i)return{depth:i[0],height:i[1],italic:i[2],skew:i[3],width:i[4]}}d(st,"getCharacterMetrics");var yt={};function Xr(r){var e;if(r>=5?e=0:r>=3?e=1:e=2,!yt[e]){var t=yt[e]={cssEmPerMu:Ve.quad[e]/18};for(var a in Ve)Ve.hasOwnProperty(a)&&(t[a]=Ve[a][e])}return yt[e]}d(Xr,"getGlobalMetrics");var fa=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],fr=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],vr=d(function(e,t){return t.size<2?e:fa[e-1][t.size-1]},"sizeAtStyle"),d0,Wr=(d0=class{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||d0.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=fr[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){var t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);return new d0(t)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:vr(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:fr[e-1]})}havingBaseStyle(e){e=e||this.style.text();var t=vr(d0.BASESIZE,e);return this.size===t&&this.textSize===d0.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})}havingBaseSizing(){var e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==d0.BASESIZE?["sizing","reset-size"+this.size,"size"+d0.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=Xr(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}},d(d0,"Options"),d0);Wr.BASESIZE=6;var Ft={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:803/800,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:803/800},va={ex:!0,em:!0,mu:!0},jr=d(function(e){return typeof e!="string"&&(e=e.unit),e in Ft||e in va||e==="ex"},"validUnit"),Q=d(function(e,t){var a;if(e.unit in Ft)a=Ft[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if(e.unit==="mu")a=t.fontMetrics().cssEmPerMu;else{var i;if(t.style.isTight()?i=t.havingStyle(t.style.text()):i=t,e.unit==="ex")a=i.fontMetrics().xHeight;else if(e.unit==="em")a=i.fontMetrics().quad;else throw new M("Invalid unit: '"+e.unit+"'");i!==t&&(a*=i.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*a,t.maxSize)},"calculateSize"),z=d(function(e){return+e.toFixed(4)+"em"},"makeEm"),U0=d(function(e){return e.filter(t=>t).join(" ")},"createClass"),Zr=d(function(e,t,a){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=a||{},t){t.style.isTight()&&this.classes.push("mtight");var i=t.getColor();i&&(this.style.color=i)}},"initNode"),Kr=d(function(e){var t=document.createElement(e);t.className=U0(this.classes);for(var a in this.style)this.style.hasOwnProperty(a)&&(t.style[a]=this.style[a]);for(var i in this.attributes)this.attributes.hasOwnProperty(i)&&t.setAttribute(i,this.attributes[i]);for(var l=0;l<this.children.length;l++)t.appendChild(this.children[l].toNode());return t},"toNode"),ga=/[\s"'>/=\x00-\x1f]/,Jr=d(function(e){var t="<"+e;this.classes.length&&(t+=' class="'+N.escape(U0(this.classes))+'"');var a="";for(var i in this.style)this.style.hasOwnProperty(i)&&(a+=N.hyphenate(i)+":"+this.style[i]+";");a&&(t+=' style="'+N.escape(a)+'"');for(var l in this.attributes)if(this.attributes.hasOwnProperty(l)){if(ga.test(l))throw new M("Invalid attribute name '"+l+"'");t+=" "+l+'="'+N.escape(this.attributes[l])+'"'}t+=">";for(var u=0;u<this.children.length;u++)t+=this.children[u].toMarkup();return t+="</"+e+">",t},"toMarkup"),oe,qe=(oe=class{constructor(e,t,a,i){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,Zr.call(this,e,a,i),this.children=t||[]}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return N.contains(this.classes,e)}toNode(){return Kr.call(this,"span")}toMarkup(){return Jr.call(this,"span")}},d(oe,"Span"),oe),he,Xt=(he=class{constructor(e,t,a,i){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,Zr.call(this,t,i),this.children=a||[],this.setAttribute("href",e)}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return N.contains(this.classes,e)}toNode(){return Kr.call(this,"a")}toMarkup(){return Jr.call(this,"a")}},d(he,"Anchor"),he),me,ba=(me=class{constructor(e,t,a){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=t,this.src=e,this.classes=["mord"],this.style=a}hasClass(e){return N.contains(this.classes,e)}toNode(){var e=document.createElement("img");e.src=this.src,e.alt=this.alt,e.className="mord";for(var t in this.style)this.style.hasOwnProperty(t)&&(e.style[t]=this.style[t]);return e}toMarkup(){var e='<img src="'+N.escape(this.src)+'"'+(' alt="'+N.escape(this.alt)+'"'),t="";for(var a in this.style)this.style.hasOwnProperty(a)&&(t+=N.hyphenate(a)+":"+this.style[a]+";");return t&&(e+=' style="'+N.escape(t)+'"'),e+="'/>",e}},d(me,"Img"),me),ya={î:"ı̂",ï:"ı̈",í:"ı́",ì:"ı̀"},ce,f0=(ce=class{constructor(e,t,a,i,l,u,h,c){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=a||0,this.italic=i||0,this.skew=l||0,this.width=u||0,this.classes=h||[],this.style=c||{},this.maxFontSize=0;var v=$r(this.text.charCodeAt(0));v&&this.classes.push(v+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=ya[this.text])}hasClass(e){return N.contains(this.classes,e)}toNode(){var e=document.createTextNode(this.text),t=null;this.italic>0&&(t=document.createElement("span"),t.style.marginRight=z(this.italic)),this.classes.length>0&&(t=t||document.createElement("span"),t.className=U0(this.classes));for(var a in this.style)this.style.hasOwnProperty(a)&&(t=t||document.createElement("span"),t.style[a]=this.style[a]);return t?(t.appendChild(e),t):e}toMarkup(){var e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="',t+=N.escape(U0(this.classes)),t+='"');var a="";this.italic>0&&(a+="margin-right:"+this.italic+"em;");for(var i in this.style)this.style.hasOwnProperty(i)&&(a+=N.hyphenate(i)+":"+this.style[i]+";");a&&(e=!0,t+=' style="'+N.escape(a)+'"');var l=N.escape(this.text);return e?(t+=">",t+=l,t+="</span>",t):l}},d(ce,"SymbolNode"),ce),de,F0=(de=class{constructor(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"svg");for(var a in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,a)&&t.setAttribute(a,this.attributes[a]);for(var i=0;i<this.children.length;i++)t.appendChild(this.children[i].toNode());return t}toMarkup(){var e='<svg xmlns="http://www.w3.org/2000/svg"';for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+N.escape(this.attributes[t])+'"');e+=">";for(var a=0;a<this.children.length;a++)e+=this.children[a].toMarkup();return e+="</svg>",e}},d(de,"SvgNode"),de),pe,$0=(pe=class{constructor(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"path");return this.alternate?t.setAttribute("d",this.alternate):t.setAttribute("d",dr[this.pathName]),t}toMarkup(){return this.alternate?'<path d="'+N.escape(this.alternate)+'"/>':'<path d="'+N.escape(dr[this.pathName])+'"/>'}},d(pe,"PathNode"),pe),fe,Nt=(fe=class{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"line");for(var a in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,a)&&t.setAttribute(a,this.attributes[a]);return t}toMarkup(){var e="<line";for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+N.escape(this.attributes[t])+'"');return e+="/>",e}},d(fe,"LineNode"),fe);function qt(r){if(r instanceof f0)return r;throw new Error("Expected symbolNode but got "+String(r)+".")}d(qt,"assertSymbolDomNode");function Qr(r){if(r instanceof qe)return r;throw new Error("Expected span<HtmlDomNode> but got "+String(r)+".")}d(Qr,"assertSpan");var xa={bin:1,close:1,inner:1,open:1,punct:1,rel:1},wa={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},W={math:{},text:{}};function n(r,e,t,a,i,l){W[r][i]={font:e,group:t,replace:a},l&&a&&(W[r][a]=W[r][i])}d(n,"defineSymbol");var s="math",S="text",o="main",p="ams",j="accent-token",E="bin",l0="close",Se="inner",q="mathord",e0="op-token",m0="open",ut="punct",f="rel",R0="spacing",g="textord";n(s,o,f,"≡","\\equiv",!0);n(s,o,f,"≺","\\prec",!0);n(s,o,f,"≻","\\succ",!0);n(s,o,f,"∼","\\sim",!0);n(s,o,f,"⊥","\\perp");n(s,o,f,"⪯","\\preceq",!0);n(s,o,f,"⪰","\\succeq",!0);n(s,o,f,"≃","\\simeq",!0);n(s,o,f,"∣","\\mid",!0);n(s,o,f,"≪","\\ll",!0);n(s,o,f,"≫","\\gg",!0);n(s,o,f,"≍","\\asymp",!0);n(s,o,f,"∥","\\parallel");n(s,o,f,"⋈","\\bowtie",!0);n(s,o,f,"⌣","\\smile",!0);n(s,o,f,"⊑","\\sqsubseteq",!0);n(s,o,f,"⊒","\\sqsupseteq",!0);n(s,o,f,"≐","\\doteq",!0);n(s,o,f,"⌢","\\frown",!0);n(s,o,f,"∋","\\ni",!0);n(s,o,f,"∝","\\propto",!0);n(s,o,f,"⊢","\\vdash",!0);n(s,o,f,"⊣","\\dashv",!0);n(s,o,f,"∋","\\owns");n(s,o,ut,".","\\ldotp");n(s,o,ut,"⋅","\\cdotp");n(s,o,g,"#","\\#");n(S,o,g,"#","\\#");n(s,o,g,"&","\\&");n(S,o,g,"&","\\&");n(s,o,g,"ℵ","\\aleph",!0);n(s,o,g,"∀","\\forall",!0);n(s,o,g,"ℏ","\\hbar",!0);n(s,o,g,"∃","\\exists",!0);n(s,o,g,"∇","\\nabla",!0);n(s,o,g,"♭","\\flat",!0);n(s,o,g,"ℓ","\\ell",!0);n(s,o,g,"♮","\\natural",!0);n(s,o,g,"♣","\\clubsuit",!0);n(s,o,g,"℘","\\wp",!0);n(s,o,g,"♯","\\sharp",!0);n(s,o,g,"♢","\\diamondsuit",!0);n(s,o,g,"ℜ","\\Re",!0);n(s,o,g,"♡","\\heartsuit",!0);n(s,o,g,"ℑ","\\Im",!0);n(s,o,g,"♠","\\spadesuit",!0);n(s,o,g,"§","\\S",!0);n(S,o,g,"§","\\S");n(s,o,g,"¶","\\P",!0);n(S,o,g,"¶","\\P");n(s,o,g,"†","\\dag");n(S,o,g,"†","\\dag");n(S,o,g,"†","\\textdagger");n(s,o,g,"‡","\\ddag");n(S,o,g,"‡","\\ddag");n(S,o,g,"‡","\\textdaggerdbl");n(s,o,l0,"⎱","\\rmoustache",!0);n(s,o,m0,"⎰","\\lmoustache",!0);n(s,o,l0,"⟯","\\rgroup",!0);n(s,o,m0,"⟮","\\lgroup",!0);n(s,o,E,"∓","\\mp",!0);n(s,o,E,"⊖","\\ominus",!0);n(s,o,E,"⊎","\\uplus",!0);n(s,o,E,"⊓","\\sqcap",!0);n(s,o,E,"∗","\\ast");n(s,o,E,"⊔","\\sqcup",!0);n(s,o,E,"◯","\\bigcirc",!0);n(s,o,E,"∙","\\bullet",!0);n(s,o,E,"‡","\\ddagger");n(s,o,E,"≀","\\wr",!0);n(s,o,E,"⨿","\\amalg");n(s,o,E,"&","\\And");n(s,o,f,"⟵","\\longleftarrow",!0);n(s,o,f,"⇐","\\Leftarrow",!0);n(s,o,f,"⟸","\\Longleftarrow",!0);n(s,o,f,"⟶","\\longrightarrow",!0);n(s,o,f,"⇒","\\Rightarrow",!0);n(s,o,f,"⟹","\\Longrightarrow",!0);n(s,o,f,"↔","\\leftrightarrow",!0);n(s,o,f,"⟷","\\longleftrightarrow",!0);n(s,o,f,"⇔","\\Leftrightarrow",!0);n(s,o,f,"⟺","\\Longleftrightarrow",!0);n(s,o,f,"↦","\\mapsto",!0);n(s,o,f,"⟼","\\longmapsto",!0);n(s,o,f,"↗","\\nearrow",!0);n(s,o,f,"↩","\\hookleftarrow",!0);n(s,o,f,"↪","\\hookrightarrow",!0);n(s,o,f,"↘","\\searrow",!0);n(s,o,f,"↼","\\leftharpoonup",!0);n(s,o,f,"⇀","\\rightharpoonup",!0);n(s,o,f,"↙","\\swarrow",!0);n(s,o,f,"↽","\\leftharpoondown",!0);n(s,o,f,"⇁","\\rightharpoondown",!0);n(s,o,f,"↖","\\nwarrow",!0);n(s,o,f,"⇌","\\rightleftharpoons",!0);n(s,p,f,"≮","\\nless",!0);n(s,p,f,"","\\@nleqslant");n(s,p,f,"","\\@nleqq");n(s,p,f,"⪇","\\lneq",!0);n(s,p,f,"≨","\\lneqq",!0);n(s,p,f,"","\\@lvertneqq");n(s,p,f,"⋦","\\lnsim",!0);n(s,p,f,"⪉","\\lnapprox",!0);n(s,p,f,"⊀","\\nprec",!0);n(s,p,f,"⋠","\\npreceq",!0);n(s,p,f,"⋨","\\precnsim",!0);n(s,p,f,"⪹","\\precnapprox",!0);n(s,p,f,"≁","\\nsim",!0);n(s,p,f,"","\\@nshortmid");n(s,p,f,"∤","\\nmid",!0);n(s,p,f,"⊬","\\nvdash",!0);n(s,p,f,"⊭","\\nvDash",!0);n(s,p,f,"⋪","\\ntriangleleft");n(s,p,f,"⋬","\\ntrianglelefteq",!0);n(s,p,f,"⊊","\\subsetneq",!0);n(s,p,f,"","\\@varsubsetneq");n(s,p,f,"⫋","\\subsetneqq",!0);n(s,p,f,"","\\@varsubsetneqq");n(s,p,f,"≯","\\ngtr",!0);n(s,p,f,"","\\@ngeqslant");n(s,p,f,"","\\@ngeqq");n(s,p,f,"⪈","\\gneq",!0);n(s,p,f,"≩","\\gneqq",!0);n(s,p,f,"","\\@gvertneqq");n(s,p,f,"⋧","\\gnsim",!0);n(s,p,f,"⪊","\\gnapprox",!0);n(s,p,f,"⊁","\\nsucc",!0);n(s,p,f,"⋡","\\nsucceq",!0);n(s,p,f,"⋩","\\succnsim",!0);n(s,p,f,"⪺","\\succnapprox",!0);n(s,p,f,"≆","\\ncong",!0);n(s,p,f,"","\\@nshortparallel");n(s,p,f,"∦","\\nparallel",!0);n(s,p,f,"⊯","\\nVDash",!0);n(s,p,f,"⋫","\\ntriangleright");n(s,p,f,"⋭","\\ntrianglerighteq",!0);n(s,p,f,"","\\@nsupseteqq");n(s,p,f,"⊋","\\supsetneq",!0);n(s,p,f,"","\\@varsupsetneq");n(s,p,f,"⫌","\\supsetneqq",!0);n(s,p,f,"","\\@varsupsetneqq");n(s,p,f,"⊮","\\nVdash",!0);n(s,p,f,"⪵","\\precneqq",!0);n(s,p,f,"⪶","\\succneqq",!0);n(s,p,f,"","\\@nsubseteqq");n(s,p,E,"⊴","\\unlhd");n(s,p,E,"⊵","\\unrhd");n(s,p,f,"↚","\\nleftarrow",!0);n(s,p,f,"↛","\\nrightarrow",!0);n(s,p,f,"⇍","\\nLeftarrow",!0);n(s,p,f,"⇏","\\nRightarrow",!0);n(s,p,f,"↮","\\nleftrightarrow",!0);n(s,p,f,"⇎","\\nLeftrightarrow",!0);n(s,p,f,"△","\\vartriangle");n(s,p,g,"ℏ","\\hslash");n(s,p,g,"▽","\\triangledown");n(s,p,g,"◊","\\lozenge");n(s,p,g,"Ⓢ","\\circledS");n(s,p,g,"®","\\circledR");n(S,p,g,"®","\\circledR");n(s,p,g,"∡","\\measuredangle",!0);n(s,p,g,"∄","\\nexists");n(s,p,g,"℧","\\mho");n(s,p,g,"Ⅎ","\\Finv",!0);n(s,p,g,"⅁","\\Game",!0);n(s,p,g,"‵","\\backprime");n(s,p,g,"▲","\\blacktriangle");n(s,p,g,"▼","\\blacktriangledown");n(s,p,g,"■","\\blacksquare");n(s,p,g,"⧫","\\blacklozenge");n(s,p,g,"★","\\bigstar");n(s,p,g,"∢","\\sphericalangle",!0);n(s,p,g,"∁","\\complement",!0);n(s,p,g,"ð","\\eth",!0);n(S,o,g,"ð","ð");n(s,p,g,"╱","\\diagup");n(s,p,g,"╲","\\diagdown");n(s,p,g,"□","\\square");n(s,p,g,"□","\\Box");n(s,p,g,"◊","\\Diamond");n(s,p,g,"¥","\\yen",!0);n(S,p,g,"¥","\\yen",!0);n(s,p,g,"✓","\\checkmark",!0);n(S,p,g,"✓","\\checkmark");n(s,p,g,"ℶ","\\beth",!0);n(s,p,g,"ℸ","\\daleth",!0);n(s,p,g,"ℷ","\\gimel",!0);n(s,p,g,"ϝ","\\digamma",!0);n(s,p,g,"ϰ","\\varkappa");n(s,p,m0,"┌","\\@ulcorner",!0);n(s,p,l0,"┐","\\@urcorner",!0);n(s,p,m0,"└","\\@llcorner",!0);n(s,p,l0,"┘","\\@lrcorner",!0);n(s,p,f,"≦","\\leqq",!0);n(s,p,f,"⩽","\\leqslant",!0);n(s,p,f,"⪕","\\eqslantless",!0);n(s,p,f,"≲","\\lesssim",!0);n(s,p,f,"⪅","\\lessapprox",!0);n(s,p,f,"≊","\\approxeq",!0);n(s,p,E,"⋖","\\lessdot");n(s,p,f,"⋘","\\lll",!0);n(s,p,f,"≶","\\lessgtr",!0);n(s,p,f,"⋚","\\lesseqgtr",!0);n(s,p,f,"⪋","\\lesseqqgtr",!0);n(s,p,f,"≑","\\doteqdot");n(s,p,f,"≓","\\risingdotseq",!0);n(s,p,f,"≒","\\fallingdotseq",!0);n(s,p,f,"∽","\\backsim",!0);n(s,p,f,"⋍","\\backsimeq",!0);n(s,p,f,"⫅","\\subseteqq",!0);n(s,p,f,"⋐","\\Subset",!0);n(s,p,f,"⊏","\\sqsubset",!0);n(s,p,f,"≼","\\preccurlyeq",!0);n(s,p,f,"⋞","\\curlyeqprec",!0);n(s,p,f,"≾","\\precsim",!0);n(s,p,f,"⪷","\\precapprox",!0);n(s,p,f,"⊲","\\vartriangleleft");n(s,p,f,"⊴","\\trianglelefteq");n(s,p,f,"⊨","\\vDash",!0);n(s,p,f,"⊪","\\Vvdash",!0);n(s,p,f,"⌣","\\smallsmile");n(s,p,f,"⌢","\\smallfrown");n(s,p,f,"≏","\\bumpeq",!0);n(s,p,f,"≎","\\Bumpeq",!0);n(s,p,f,"≧","\\geqq",!0);n(s,p,f,"⩾","\\geqslant",!0);n(s,p,f,"⪖","\\eqslantgtr",!0);n(s,p,f,"≳","\\gtrsim",!0);n(s,p,f,"⪆","\\gtrapprox",!0);n(s,p,E,"⋗","\\gtrdot");n(s,p,f,"⋙","\\ggg",!0);n(s,p,f,"≷","\\gtrless",!0);n(s,p,f,"⋛","\\gtreqless",!0);n(s,p,f,"⪌","\\gtreqqless",!0);n(s,p,f,"≖","\\eqcirc",!0);n(s,p,f,"≗","\\circeq",!0);n(s,p,f,"≜","\\triangleq",!0);n(s,p,f,"∼","\\thicksim");n(s,p,f,"≈","\\thickapprox");n(s,p,f,"⫆","\\supseteqq",!0);n(s,p,f,"⋑","\\Supset",!0);n(s,p,f,"⊐","\\sqsupset",!0);n(s,p,f,"≽","\\succcurlyeq",!0);n(s,p,f,"⋟","\\curlyeqsucc",!0);n(s,p,f,"≿","\\succsim",!0);n(s,p,f,"⪸","\\succapprox",!0);n(s,p,f,"⊳","\\vartriangleright");n(s,p,f,"⊵","\\trianglerighteq");n(s,p,f,"⊩","\\Vdash",!0);n(s,p,f,"∣","\\shortmid");n(s,p,f,"∥","\\shortparallel");n(s,p,f,"≬","\\between",!0);n(s,p,f,"⋔","\\pitchfork",!0);n(s,p,f,"∝","\\varpropto");n(s,p,f,"◀","\\blacktriangleleft");n(s,p,f,"∴","\\therefore",!0);n(s,p,f,"∍","\\backepsilon");n(s,p,f,"▶","\\blacktriangleright");n(s,p,f,"∵","\\because",!0);n(s,p,f,"⋘","\\llless");n(s,p,f,"⋙","\\gggtr");n(s,p,E,"⊲","\\lhd");n(s,p,E,"⊳","\\rhd");n(s,p,f,"≂","\\eqsim",!0);n(s,o,f,"⋈","\\Join");n(s,p,f,"≑","\\Doteq",!0);n(s,p,E,"∔","\\dotplus",!0);n(s,p,E,"∖","\\smallsetminus");n(s,p,E,"⋒","\\Cap",!0);n(s,p,E,"⋓","\\Cup",!0);n(s,p,E,"⩞","\\doublebarwedge",!0);n(s,p,E,"⊟","\\boxminus",!0);n(s,p,E,"⊞","\\boxplus",!0);n(s,p,E,"⋇","\\divideontimes",!0);n(s,p,E,"⋉","\\ltimes",!0);n(s,p,E,"⋊","\\rtimes",!0);n(s,p,E,"⋋","\\leftthreetimes",!0);n(s,p,E,"⋌","\\rightthreetimes",!0);n(s,p,E,"⋏","\\curlywedge",!0);n(s,p,E,"⋎","\\curlyvee",!0);n(s,p,E,"⊝","\\circleddash",!0);n(s,p,E,"⊛","\\circledast",!0);n(s,p,E,"⋅","\\centerdot");n(s,p,E,"⊺","\\intercal",!0);n(s,p,E,"⋒","\\doublecap");n(s,p,E,"⋓","\\doublecup");n(s,p,E,"⊠","\\boxtimes",!0);n(s,p,f,"⇢","\\dashrightarrow",!0);n(s,p,f,"⇠","\\dashleftarrow",!0);n(s,p,f,"⇇","\\leftleftarrows",!0);n(s,p,f,"⇆","\\leftrightarrows",!0);n(s,p,f,"⇚","\\Lleftarrow",!0);n(s,p,f,"↞","\\twoheadleftarrow",!0);n(s,p,f,"↢","\\leftarrowtail",!0);n(s,p,f,"↫","\\looparrowleft",!0);n(s,p,f,"⇋","\\leftrightharpoons",!0);n(s,p,f,"↶","\\curvearrowleft",!0);n(s,p,f,"↺","\\circlearrowleft",!0);n(s,p,f,"↰","\\Lsh",!0);n(s,p,f,"⇈","\\upuparrows",!0);n(s,p,f,"↿","\\upharpoonleft",!0);n(s,p,f,"⇃","\\downharpoonleft",!0);n(s,o,f,"⊶","\\origof",!0);n(s,o,f,"⊷","\\imageof",!0);n(s,p,f,"⊸","\\multimap",!0);n(s,p,f,"↭","\\leftrightsquigarrow",!0);n(s,p,f,"⇉","\\rightrightarrows",!0);n(s,p,f,"⇄","\\rightleftarrows",!0);n(s,p,f,"↠","\\twoheadrightarrow",!0);n(s,p,f,"↣","\\rightarrowtail",!0);n(s,p,f,"↬","\\looparrowright",!0);n(s,p,f,"↷","\\curvearrowright",!0);n(s,p,f,"↻","\\circlearrowright",!0);n(s,p,f,"↱","\\Rsh",!0);n(s,p,f,"⇊","\\downdownarrows",!0);n(s,p,f,"↾","\\upharpoonright",!0);n(s,p,f,"⇂","\\downharpoonright",!0);n(s,p,f,"⇝","\\rightsquigarrow",!0);n(s,p,f,"⇝","\\leadsto");n(s,p,f,"⇛","\\Rrightarrow",!0);n(s,p,f,"↾","\\restriction");n(s,o,g,"‘","`");n(s,o,g,"$","\\$");n(S,o,g,"$","\\$");n(S,o,g,"$","\\textdollar");n(s,o,g,"%","\\%");n(S,o,g,"%","\\%");n(s,o,g,"_","\\_");n(S,o,g,"_","\\_");n(S,o,g,"_","\\textunderscore");n(s,o,g,"∠","\\angle",!0);n(s,o,g,"∞","\\infty",!0);n(s,o,g,"′","\\prime");n(s,o,g,"△","\\triangle");n(s,o,g,"Γ","\\Gamma",!0);n(s,o,g,"Δ","\\Delta",!0);n(s,o,g,"Θ","\\Theta",!0);n(s,o,g,"Λ","\\Lambda",!0);n(s,o,g,"Ξ","\\Xi",!0);n(s,o,g,"Π","\\Pi",!0);n(s,o,g,"Σ","\\Sigma",!0);n(s,o,g,"Υ","\\Upsilon",!0);n(s,o,g,"Φ","\\Phi",!0);n(s,o,g,"Ψ","\\Psi",!0);n(s,o,g,"Ω","\\Omega",!0);n(s,o,g,"A","Α");n(s,o,g,"B","Β");n(s,o,g,"E","Ε");n(s,o,g,"Z","Ζ");n(s,o,g,"H","Η");n(s,o,g,"I","Ι");n(s,o,g,"K","Κ");n(s,o,g,"M","Μ");n(s,o,g,"N","Ν");n(s,o,g,"O","Ο");n(s,o,g,"P","Ρ");n(s,o,g,"T","Τ");n(s,o,g,"X","Χ");n(s,o,g,"¬","\\neg",!0);n(s,o,g,"¬","\\lnot");n(s,o,g,"⊤","\\top");n(s,o,g,"⊥","\\bot");n(s,o,g,"∅","\\emptyset");n(s,p,g,"∅","\\varnothing");n(s,o,q,"α","\\alpha",!0);n(s,o,q,"β","\\beta",!0);n(s,o,q,"γ","\\gamma",!0);n(s,o,q,"δ","\\delta",!0);n(s,o,q,"ϵ","\\epsilon",!0);n(s,o,q,"ζ","\\zeta",!0);n(s,o,q,"η","\\eta",!0);n(s,o,q,"θ","\\theta",!0);n(s,o,q,"ι","\\iota",!0);n(s,o,q,"κ","\\kappa",!0);n(s,o,q,"λ","\\lambda",!0);n(s,o,q,"μ","\\mu",!0);n(s,o,q,"ν","\\nu",!0);n(s,o,q,"ξ","\\xi",!0);n(s,o,q,"ο","\\omicron",!0);n(s,o,q,"π","\\pi",!0);n(s,o,q,"ρ","\\rho",!0);n(s,o,q,"σ","\\sigma",!0);n(s,o,q,"τ","\\tau",!0);n(s,o,q,"υ","\\upsilon",!0);n(s,o,q,"ϕ","\\phi",!0);n(s,o,q,"χ","\\chi",!0);n(s,o,q,"ψ","\\psi",!0);n(s,o,q,"ω","\\omega",!0);n(s,o,q,"ε","\\varepsilon",!0);n(s,o,q,"ϑ","\\vartheta",!0);n(s,o,q,"ϖ","\\varpi",!0);n(s,o,q,"ϱ","\\varrho",!0);n(s,o,q,"ς","\\varsigma",!0);n(s,o,q,"φ","\\varphi",!0);n(s,o,E,"∗","*",!0);n(s,o,E,"+","+");n(s,o,E,"−","-",!0);n(s,o,E,"⋅","\\cdot",!0);n(s,o,E,"∘","\\circ",!0);n(s,o,E,"÷","\\div",!0);n(s,o,E,"±","\\pm",!0);n(s,o,E,"×","\\times",!0);n(s,o,E,"∩","\\cap",!0);n(s,o,E,"∪","\\cup",!0);n(s,o,E,"∖","\\setminus",!0);n(s,o,E,"∧","\\land");n(s,o,E,"∨","\\lor");n(s,o,E,"∧","\\wedge",!0);n(s,o,E,"∨","\\vee",!0);n(s,o,g,"√","\\surd");n(s,o,m0,"⟨","\\langle",!0);n(s,o,m0,"∣","\\lvert");n(s,o,m0,"∥","\\lVert");n(s,o,l0,"?","?");n(s,o,l0,"!","!");n(s,o,l0,"⟩","\\rangle",!0);n(s,o,l0,"∣","\\rvert");n(s,o,l0,"∥","\\rVert");n(s,o,f,"=","=");n(s,o,f,":",":");n(s,o,f,"≈","\\approx",!0);n(s,o,f,"≅","\\cong",!0);n(s,o,f,"≥","\\ge");n(s,o,f,"≥","\\geq",!0);n(s,o,f,"←","\\gets");n(s,o,f,">","\\gt",!0);n(s,o,f,"∈","\\in",!0);n(s,o,f,"","\\@not");n(s,o,f,"⊂","\\subset",!0);n(s,o,f,"⊃","\\supset",!0);n(s,o,f,"⊆","\\subseteq",!0);n(s,o,f,"⊇","\\supseteq",!0);n(s,p,f,"⊈","\\nsubseteq",!0);n(s,p,f,"⊉","\\nsupseteq",!0);n(s,o,f,"⊨","\\models");n(s,o,f,"←","\\leftarrow",!0);n(s,o,f,"≤","\\le");n(s,o,f,"≤","\\leq",!0);n(s,o,f,"<","\\lt",!0);n(s,o,f,"→","\\rightarrow",!0);n(s,o,f,"→","\\to");n(s,p,f,"≱","\\ngeq",!0);n(s,p,f,"≰","\\nleq",!0);n(s,o,R0," ","\\ ");n(s,o,R0," ","\\space");n(s,o,R0," ","\\nobreakspace");n(S,o,R0," ","\\ ");n(S,o,R0," "," ");n(S,o,R0," ","\\space");n(S,o,R0," ","\\nobreakspace");n(s,o,R0,null,"\\nobreak");n(s,o,R0,null,"\\allowbreak");n(s,o,ut,",",",");n(s,o,ut,";",";");n(s,p,E,"⊼","\\barwedge",!0);n(s,p,E,"⊻","\\veebar",!0);n(s,o,E,"⊙","\\odot",!0);n(s,o,E,"⊕","\\oplus",!0);n(s,o,E,"⊗","\\otimes",!0);n(s,o,g,"∂","\\partial",!0);n(s,o,E,"⊘","\\oslash",!0);n(s,p,E,"⊚","\\circledcirc",!0);n(s,p,E,"⊡","\\boxdot",!0);n(s,o,E,"△","\\bigtriangleup");n(s,o,E,"▽","\\bigtriangledown");n(s,o,E,"†","\\dagger");n(s,o,E,"⋄","\\diamond");n(s,o,E,"⋆","\\star");n(s,o,E,"◃","\\triangleleft");n(s,o,E,"▹","\\triangleright");n(s,o,m0,"{","\\{");n(S,o,g,"{","\\{");n(S,o,g,"{","\\textbraceleft");n(s,o,l0,"}","\\}");n(S,o,g,"}","\\}");n(S,o,g,"}","\\textbraceright");n(s,o,m0,"{","\\lbrace");n(s,o,l0,"}","\\rbrace");n(s,o,m0,"[","\\lbrack",!0);n(S,o,g,"[","\\lbrack",!0);n(s,o,l0,"]","\\rbrack",!0);n(S,o,g,"]","\\rbrack",!0);n(s,o,m0,"(","\\lparen",!0);n(s,o,l0,")","\\rparen",!0);n(S,o,g,"<","\\textless",!0);n(S,o,g,">","\\textgreater",!0);n(s,o,m0,"⌊","\\lfloor",!0);n(s,o,l0,"⌋","\\rfloor",!0);n(s,o,m0,"⌈","\\lceil",!0);n(s,o,l0,"⌉","\\rceil",!0);n(s,o,g,"\\","\\backslash");n(s,o,g,"∣","|");n(s,o,g,"∣","\\vert");n(S,o,g,"|","\\textbar",!0);n(s,o,g,"∥","\\|");n(s,o,g,"∥","\\Vert");n(S,o,g,"∥","\\textbardbl");n(S,o,g,"~","\\textasciitilde");n(S,o,g,"\\","\\textbackslash");n(S,o,g,"^","\\textasciicircum");n(s,o,f,"↑","\\uparrow",!0);n(s,o,f,"⇑","\\Uparrow",!0);n(s,o,f,"↓","\\downarrow",!0);n(s,o,f,"⇓","\\Downarrow",!0);n(s,o,f,"↕","\\updownarrow",!0);n(s,o,f,"⇕","\\Updownarrow",!0);n(s,o,e0,"∐","\\coprod");n(s,o,e0,"⋁","\\bigvee");n(s,o,e0,"⋀","\\bigwedge");n(s,o,e0,"⨄","\\biguplus");n(s,o,e0,"⋂","\\bigcap");n(s,o,e0,"⋃","\\bigcup");n(s,o,e0,"∫","\\int");n(s,o,e0,"∫","\\intop");n(s,o,e0,"∬","\\iint");n(s,o,e0,"∭","\\iiint");n(s,o,e0,"∏","\\prod");n(s,o,e0,"∑","\\sum");n(s,o,e0,"⨂","\\bigotimes");n(s,o,e0,"⨁","\\bigoplus");n(s,o,e0,"⨀","\\bigodot");n(s,o,e0,"∮","\\oint");n(s,o,e0,"∯","\\oiint");n(s,o,e0,"∰","\\oiiint");n(s,o,e0,"⨆","\\bigsqcup");n(s,o,e0,"∫","\\smallint");n(S,o,Se,"…","\\textellipsis");n(s,o,Se,"…","\\mathellipsis");n(S,o,Se,"…","\\ldots",!0);n(s,o,Se,"…","\\ldots",!0);n(s,o,Se,"⋯","\\@cdots",!0);n(s,o,Se,"⋱","\\ddots",!0);n(s,o,g,"⋮","\\varvdots");n(S,o,g,"⋮","\\varvdots");n(s,o,j,"ˊ","\\acute");n(s,o,j,"ˋ","\\grave");n(s,o,j,"¨","\\ddot");n(s,o,j,"~","\\tilde");n(s,o,j,"ˉ","\\bar");n(s,o,j,"˘","\\breve");n(s,o,j,"ˇ","\\check");n(s,o,j,"^","\\hat");n(s,o,j,"⃗","\\vec");n(s,o,j,"˙","\\dot");n(s,o,j,"˚","\\mathring");n(s,o,q,"","\\@imath");n(s,o,q,"","\\@jmath");n(s,o,g,"ı","ı");n(s,o,g,"ȷ","ȷ");n(S,o,g,"ı","\\i",!0);n(S,o,g,"ȷ","\\j",!0);n(S,o,g,"ß","\\ss",!0);n(S,o,g,"æ","\\ae",!0);n(S,o,g,"œ","\\oe",!0);n(S,o,g,"ø","\\o",!0);n(S,o,g,"Æ","\\AE",!0);n(S,o,g,"Œ","\\OE",!0);n(S,o,g,"Ø","\\O",!0);n(S,o,j,"ˊ","\\'");n(S,o,j,"ˋ","\\`");n(S,o,j,"ˆ","\\^");n(S,o,j,"˜","\\~");n(S,o,j,"ˉ","\\=");n(S,o,j,"˘","\\u");n(S,o,j,"˙","\\.");n(S,o,j,"¸","\\c");n(S,o,j,"˚","\\r");n(S,o,j,"ˇ","\\v");n(S,o,j,"¨",'\\"');n(S,o,j,"˝","\\H");n(S,o,j,"◯","\\textcircled");var _r={"--":!0,"---":!0,"``":!0,"''":!0};n(S,o,g,"–","--",!0);n(S,o,g,"–","\\textendash");n(S,o,g,"—","---",!0);n(S,o,g,"—","\\textemdash");n(S,o,g,"‘","`",!0);n(S,o,g,"‘","\\textquoteleft");n(S,o,g,"’","'",!0);n(S,o,g,"’","\\textquoteright");n(S,o,g,"“","``",!0);n(S,o,g,"“","\\textquotedblleft");n(S,o,g,"”","''",!0);n(S,o,g,"”","\\textquotedblright");n(s,o,g,"°","\\degree",!0);n(S,o,g,"°","\\degree");n(S,o,g,"°","\\textdegree",!0);n(s,o,g,"£","\\pounds");n(s,o,g,"£","\\mathsterling",!0);n(S,o,g,"£","\\pounds");n(S,o,g,"£","\\textsterling",!0);n(s,p,g,"✠","\\maltese");n(S,p,g,"✠","\\maltese");var gr='0123456789/@."';for(Ue=0;Ue<gr.length;Ue++)xt=gr.charAt(Ue),n(s,o,g,xt,xt);var xt,Ue,br='0123456789!@*()-=+";:?/.,';for($e=0;$e<br.length;$e++)wt=br.charAt($e),n(S,o,g,wt,wt);var wt,$e,rt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";for(Ye=0;Ye<rt.length;Ye++)Be=rt.charAt(Ye),n(s,o,q,Be,Be),n(S,o,g,Be,Be);var Be,Ye;n(s,p,g,"C","ℂ");n(S,p,g,"C","ℂ");n(s,p,g,"H","ℍ");n(S,p,g,"H","ℍ");n(s,p,g,"N","ℕ");n(S,p,g,"N","ℕ");n(s,p,g,"P","ℙ");n(S,p,g,"P","ℙ");n(s,p,g,"Q","ℚ");n(S,p,g,"Q","ℚ");n(s,p,g,"R","ℝ");n(S,p,g,"R","ℝ");n(s,p,g,"Z","ℤ");n(S,p,g,"Z","ℤ");n(s,o,q,"h","ℎ");n(S,o,q,"h","ℎ");var I="";for(a0=0;a0<rt.length;a0++)K=rt.charAt(a0),I=String.fromCharCode(55349,56320+a0),n(s,o,q,K,I),n(S,o,g,K,I),I=String.fromCharCode(55349,56372+a0),n(s,o,q,K,I),n(S,o,g,K,I),I=String.fromCharCode(55349,56424+a0),n(s,o,q,K,I),n(S,o,g,K,I),I=String.fromCharCode(55349,56580+a0),n(s,o,q,K,I),n(S,o,g,K,I),I=String.fromCharCode(55349,56684+a0),n(s,o,q,K,I),n(S,o,g,K,I),I=String.fromCharCode(55349,56736+a0),n(s,o,q,K,I),n(S,o,g,K,I),I=String.fromCharCode(55349,56788+a0),n(s,o,q,K,I),n(S,o,g,K,I),I=String.fromCharCode(55349,56840+a0),n(s,o,q,K,I),n(S,o,g,K,I),I=String.fromCharCode(55349,56944+a0),n(s,o,q,K,I),n(S,o,g,K,I),a0<26&&(I=String.fromCharCode(55349,56632+a0),n(s,o,q,K,I),n(S,o,g,K,I),I=String.fromCharCode(55349,56476+a0),n(s,o,q,K,I),n(S,o,g,K,I));var K,a0;I="𝕜";n(s,o,q,"k",I);n(S,o,g,"k",I);for(G0=0;G0<10;G0++)C0=G0.toString(),I=String.fromCharCode(55349,57294+G0),n(s,o,q,C0,I),n(S,o,g,C0,I),I=String.fromCharCode(55349,57314+G0),n(s,o,q,C0,I),n(S,o,g,C0,I),I=String.fromCharCode(55349,57324+G0),n(s,o,q,C0,I),n(S,o,g,C0,I),I=String.fromCharCode(55349,57334+G0),n(s,o,q,C0,I),n(S,o,g,C0,I);var C0,G0,Rt="ÐÞþ";for(Xe=0;Xe<Rt.length;Xe++)ze=Rt.charAt(Xe),n(s,o,q,ze,ze),n(S,o,g,ze,ze);var ze,Xe,We=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],yr=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],ka=d(function(e,t){var a=e.charCodeAt(0),i=e.charCodeAt(1),l=(a-55296)*1024+(i-56320)+65536,u=t==="math"?0:1;if(119808<=l&&l<120484){var h=Math.floor((l-119808)/26);return[We[h][2],We[h][u]]}else if(120782<=l&&l<=120831){var c=Math.floor((l-120782)/10);return[yr[c][2],yr[c][u]]}else{if(l===120485||l===120486)return[We[0][2],We[0][u]];if(120486<l&&l<120782)return["",""];throw new M("Unsupported character: "+e)}},"wideCharacterFont"),ot=d(function(e,t,a){return W[a][e]&&W[a][e].replace&&(e=W[a][e].replace),{value:e,metrics:st(e,t,a)}},"lookupSymbol"),y0=d(function(e,t,a,i,l){var u=ot(e,t,a),h=u.metrics;e=u.value;var c;if(h){var v=h.italic;(a==="text"||i&&i.font==="mathit")&&(v=0),c=new f0(e,h.height,h.depth,v,h.skew,h.width,l)}else typeof console<"u"&&console.warn("No character metrics "+("for '"+e+"' in style '"+t+"' and mode '"+a+"'")),c=new f0(e,0,0,0,0,0,l);if(i){c.maxFontSize=i.sizeMultiplier,i.style.isTight()&&c.classes.push("mtight");var b=i.getColor();b&&(c.style.color=b)}return c},"makeSymbol"),Sa=d(function(e,t,a,i){return i===void 0&&(i=[]),a.font==="boldsymbol"&&ot(e,"Main-Bold",t).metrics?y0(e,"Main-Bold",t,a,i.concat(["mathbf"])):e==="\\"||W[t][e].font==="main"?y0(e,"Main-Regular",t,a,i):y0(e,"AMS-Regular",t,a,i.concat(["amsrm"]))},"mathsym"),Aa=d(function(e,t,a,i,l){return l!=="textord"&&ot(e,"Math-BoldItalic",t).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}},"boldsymbol"),Ma=d(function(e,t,a){var i=e.mode,l=e.text,u=["mord"],h=i==="math"||i==="text"&&t.font,c=h?t.font:t.fontFamily,v="",b="";if(l.charCodeAt(0)===55349&&([v,b]=ka(l,i)),v.length>0)return y0(l,v,i,t,u.concat(b));if(c){var x,k;if(c==="boldsymbol"){var w=Aa(l,i,t,u,a);x=w.fontName,k=[w.fontClass]}else h?(x=r1[c].fontName,k=[c]):(x=je(c,t.fontWeight,t.fontShape),k=[c,t.fontWeight,t.fontShape]);if(ot(l,x,i).metrics)return y0(l,x,i,t,u.concat(k));if(_r.hasOwnProperty(l)&&x.slice(0,10)==="Typewriter"){for(var B=[],C=0;C<l.length;C++)B.push(y0(l[C],x,i,t,u.concat(k)));return t1(B)}}if(a==="mathord")return y0(l,"Math-Italic",i,t,u.concat(["mathnormal"]));if(a==="textord"){var D=W[i][l]&&W[i][l].font;if(D==="ams"){var F=je("amsrm",t.fontWeight,t.fontShape);return y0(l,F,i,t,u.concat("amsrm",t.fontWeight,t.fontShape))}else if(D==="main"||!D){var L=je("textrm",t.fontWeight,t.fontShape);return y0(l,L,i,t,u.concat(t.fontWeight,t.fontShape))}else{var O=je(D,t.fontWeight,t.fontShape);return y0(l,O,i,t,u.concat(O,t.fontWeight,t.fontShape))}}else throw new Error("unexpected type: "+a+" in makeOrd")},"makeOrd"),Ba=d((r,e)=>{if(U0(r.classes)!==U0(e.classes)||r.skew!==e.skew||r.maxFontSize!==e.maxFontSize)return!1;if(r.classes.length===1){var t=r.classes[0];if(t==="mbin"||t==="mord")return!1}for(var a in r.style)if(r.style.hasOwnProperty(a)&&r.style[a]!==e.style[a])return!1;for(var i in e.style)if(e.style.hasOwnProperty(i)&&r.style[i]!==e.style[i])return!1;return!0},"canCombine"),za=d(r=>{for(var e=0;e<r.length-1;e++){var t=r[e],a=r[e+1];t instanceof f0&&a instanceof f0&&Ba(t,a)&&(t.text+=a.text,t.height=Math.max(t.height,a.height),t.depth=Math.max(t.depth,a.depth),t.italic=a.italic,r.splice(e+1,1),e--)}return r},"tryCombineChars"),Wt=d(function(e){for(var t=0,a=0,i=0,l=0;l<e.children.length;l++){var u=e.children[l];u.height>t&&(t=u.height),u.depth>a&&(a=u.depth),u.maxFontSize>i&&(i=u.maxFontSize)}e.height=t,e.depth=a,e.maxFontSize=i},"sizeElementFromChildren"),u0=d(function(e,t,a,i){var l=new qe(e,t,a,i);return Wt(l),l},"makeSpan"),e1=d((r,e,t,a)=>new qe(r,e,t,a),"makeSvgSpan"),Ca=d(function(e,t,a){var i=u0([e],[],t);return i.height=Math.max(a||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),i.style.borderBottomWidth=z(i.height),i.maxFontSize=1,i},"makeLineSpan"),Ta=d(function(e,t,a,i){var l=new Xt(e,t,a,i);return Wt(l),l},"makeAnchor"),t1=d(function(e){var t=new Ne(e);return Wt(t),t},"makeFragment"),Ea=d(function(e,t){return e instanceof Ne?u0([],[e],t):e},"wrapFragment"),Da=d(function(e){if(e.positionType==="individualShift"){for(var t=e.children,a=[t[0]],i=-t[0].shift-t[0].elem.depth,l=i,u=1;u<t.length;u++){var h=-t[u].shift-l-t[u].elem.depth,c=h-(t[u-1].elem.height+t[u-1].elem.depth);l=l+h,a.push({type:"kern",size:c}),a.push(t[u])}return{children:a,depth:i}}var v;if(e.positionType==="top"){for(var b=e.positionData,x=0;x<e.children.length;x++){var k=e.children[x];b-=k.type==="kern"?k.size:k.elem.height+k.elem.depth}v=b}else if(e.positionType==="bottom")v=-e.positionData;else{var w=e.children[0];if(w.type!=="elem")throw new Error('First child must have type "elem".');if(e.positionType==="shift")v=-w.elem.depth-e.positionData;else if(e.positionType==="firstBaseline")v=-w.elem.depth;else throw new Error("Invalid positionType "+e.positionType+".")}return{children:e.children,depth:v}},"getVListChildrenAndDepth"),Fa=d(function(e,t){for(var{children:a,depth:i}=Da(e),l=0,u=0;u<a.length;u++){var h=a[u];if(h.type==="elem"){var c=h.elem;l=Math.max(l,c.maxFontSize,c.height)}}l+=2;var v=u0(["pstrut"],[]);v.style.height=z(l);for(var b=[],x=i,k=i,w=i,B=0;B<a.length;B++){var C=a[B];if(C.type==="kern")w+=C.size;else{var D=C.elem,F=C.wrapperClasses||[],L=C.wrapperStyle||{},O=u0(F,[v,D],void 0,L);O.style.top=z(-l-w-D.depth),C.marginLeft&&(O.style.marginLeft=C.marginLeft),C.marginRight&&(O.style.marginRight=C.marginRight),b.push(O),w+=D.height+D.depth}x=Math.min(x,w),k=Math.max(k,w)}var G=u0(["vlist"],b);G.style.height=z(k);var P;if(x<0){var $=u0([],[]),U=u0(["vlist"],[$]);U.style.height=z(-x);var Z=u0(["vlist-s"],[new f0("​")]);P=[u0(["vlist-r"],[G,Z]),u0(["vlist-r"],[U])]}else P=[u0(["vlist-r"],[G])];var Y=u0(["vlist-t"],P);return P.length===2&&Y.classes.push("vlist-t2"),Y.height=k,Y.depth=-x,Y},"makeVList"),Na=d((r,e)=>{var t=u0(["mspace"],[],e),a=Q(r,e);return t.style.marginRight=z(a),t},"makeGlue"),je=d(function(e,t,a){var i="";switch(e){case"amsrm":i="AMS";break;case"textrm":i="Main";break;case"textsf":i="SansSerif";break;case"texttt":i="Typewriter";break;default:i=e}var l;return t==="textbf"&&a==="textit"?l="BoldItalic":t==="textbf"?l="Bold":t==="textit"?l="Italic":l="Regular",i+"-"+l},"retrieveTextFontName"),r1={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathsfit:{variant:"sans-serif-italic",fontName:"SansSerif-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},a1={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},qa=d(function(e,t){var[a,i,l]=a1[e],u=new $0(a),h=new F0([u],{width:z(i),height:z(l),style:"width:"+z(i),viewBox:"0 0 "+1e3*i+" "+1e3*l,preserveAspectRatio:"xMinYMin"}),c=e1(["overlay"],[h],t);return c.height=l,c.style.height=z(l),c.style.width=z(i),c},"staticSvg"),y={fontMap:r1,makeSymbol:y0,mathsym:Sa,makeSpan:u0,makeSvgSpan:e1,makeLineSpan:Ca,makeAnchor:Ta,makeFragment:t1,wrapFragment:Ea,makeVList:Fa,makeOrd:Ma,makeGlue:Na,staticSvg:qa,svgData:a1,tryCombineChars:za},J={number:3,unit:"mu"},j0={number:4,unit:"mu"},T0={number:5,unit:"mu"},Ra={mord:{mop:J,mbin:j0,mrel:T0,minner:J},mop:{mord:J,mop:J,mrel:T0,minner:J},mbin:{mord:j0,mop:j0,mopen:j0,minner:j0},mrel:{mord:T0,mop:T0,mopen:T0,minner:T0},mopen:{},mclose:{mop:J,mbin:j0,mrel:T0,minner:J},mpunct:{mord:J,mop:J,mrel:T0,mopen:J,mclose:J,mpunct:J,minner:J},minner:{mord:J,mop:J,mbin:j0,mrel:T0,mopen:J,mpunct:J,minner:J}},Ia={mord:{mop:J},mop:{mord:J,mop:J},mbin:{},mrel:{},mopen:{},mclose:{mop:J},mpunct:{},minner:{mop:J}},i1={},at={},it={};function T(r){for(var{type:e,names:t,props:a,handler:i,htmlBuilder:l,mathmlBuilder:u}=r,h={type:e,numArgs:a.numArgs,argTypes:a.argTypes,allowedInArgument:!!a.allowedInArgument,allowedInText:!!a.allowedInText,allowedInMath:a.allowedInMath===void 0?!0:a.allowedInMath,numOptionalArgs:a.numOptionalArgs||0,infix:!!a.infix,primitive:!!a.primitive,handler:i},c=0;c<t.length;++c)i1[t[c]]=h;e&&(l&&(at[e]=l),u&&(it[e]=u))}d(T,"defineFunction");function X0(r){var{type:e,htmlBuilder:t,mathmlBuilder:a}=r;T({type:e,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:t,mathmlBuilder:a})}d(X0,"defineFunctionBuilders");var nt=d(function(e){return e.type==="ordgroup"&&e.body.length===1?e.body[0]:e},"normalizeArgument"),_=d(function(e){return e.type==="ordgroup"?e.body:[e]},"ordargument"),N0=y.makeSpan,La=["leftmost","mbin","mopen","mrel","mop","mpunct"],Oa=["rightmost","mrel","mclose","mpunct"],Ha={display:R.DISPLAY,text:R.TEXT,script:R.SCRIPT,scriptscript:R.SCRIPTSCRIPT},Pa={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},r0=d(function(e,t,a,i){i===void 0&&(i=[null,null]);for(var l=[],u=0;u<e.length;u++){var h=V(e[u],t);if(h instanceof Ne){var c=h.children;l.push(...c)}else l.push(h)}if(y.tryCombineChars(l),!a)return l;var v=t;if(e.length===1){var b=e[0];b.type==="sizing"?v=t.havingSize(b.size):b.type==="styling"&&(v=t.havingStyle(Ha[b.style]))}var x=N0([i[0]||"leftmost"],[],t),k=N0([i[1]||"rightmost"],[],t),w=a==="root";return xr(l,(B,C)=>{var D=C.classes[0],F=B.classes[0];D==="mbin"&&N.contains(Oa,F)?C.classes[0]="mord":F==="mbin"&&N.contains(La,D)&&(B.classes[0]="mord")},{node:x},k,w),xr(l,(B,C)=>{var D=It(C),F=It(B),L=D&&F?B.hasClass("mtight")?Ia[D][F]:Ra[D][F]:null;if(L)return y.makeGlue(L,v)},{node:x},k,w),l},"buildExpression"),xr=d(function r(e,t,a,i,l){i&&e.push(i);for(var u=0;u<e.length;u++){var h=e[u],c=n1(h);if(c){r(c.children,t,a,null,l);continue}var v=!h.hasClass("mspace");if(v){var b=t(h,a.node);b&&(a.insertAfter?a.insertAfter(b):(e.unshift(b),u++))}v?a.node=h:l&&h.hasClass("newline")&&(a.node=N0(["leftmost"])),a.insertAfter=(x=>k=>{e.splice(x+1,0,k),u++})(u)}i&&e.pop()},"traverseNonSpaceNodes"),n1=d(function(e){return e instanceof Ne||e instanceof Xt||e instanceof qe&&e.hasClass("enclosing")?e:null},"checkPartialGroup"),Ga=d(function r(e,t){var a=n1(e);if(a){var i=a.children;if(i.length){if(t==="right")return r(i[i.length-1],"right");if(t==="left")return r(i[0],"left")}}return e},"getOutermostNode"),It=d(function(e,t){return e?(t&&(e=Ga(e,t)),Pa[e.classes[0]]||null):null},"getTypeOfDomTree"),Fe=d(function(e,t){var a=["nulldelimiter"].concat(e.baseSizingClasses());return N0(t.concat(a))},"makeNullDelimiter"),V=d(function(e,t,a){if(!e)return N0();if(at[e.type]){var i=at[e.type](e,t);if(a&&t.size!==a.size){i=N0(t.sizingClasses(a),[i],t);var l=t.sizeMultiplier/a.sizeMultiplier;i.height*=l,i.depth*=l}return i}else throw new M("Got group of unknown type: '"+e.type+"'")},"buildGroup");function Ce(r,e){var t=N0(["base"],r,e),a=N0(["strut"]);return a.style.height=z(t.height+t.depth),t.depth&&(a.style.verticalAlign=z(-t.depth)),t.children.unshift(a),t}d(Ce,"buildHTMLUnbreakable");function lt(r,e){var t=null;r.length===1&&r[0].type==="tag"&&(t=r[0].tag,r=r[0].body);var a=r0(r,e,"root"),i;a.length===2&&a[1].hasClass("tag")&&(i=a.pop());for(var l=[],u=[],h=0;h<a.length;h++)if(u.push(a[h]),a[h].hasClass("mbin")||a[h].hasClass("mrel")||a[h].hasClass("allowbreak")){for(var c=!1;h<a.length-1&&a[h+1].hasClass("mspace")&&!a[h+1].hasClass("newline");)h++,u.push(a[h]),a[h].hasClass("nobreak")&&(c=!0);c||(l.push(Ce(u,e)),u=[])}else a[h].hasClass("newline")&&(u.pop(),u.length>0&&(l.push(Ce(u,e)),u=[]),l.push(a[h]));u.length>0&&l.push(Ce(u,e));var v;t?(v=Ce(r0(t,e,!0)),v.classes=["tag"],l.push(v)):i&&l.push(i);var b=N0(["katex-html"],l);if(b.setAttribute("aria-hidden","true"),v){var x=v.children[0];x.style.height=z(b.height+b.depth),b.depth&&(x.style.verticalAlign=z(-b.depth))}return b}d(lt,"buildHTML");function jt(r){return new Ne(r)}d(jt,"newDocumentFragment");var ve,h0=(ve=class{constructor(e,t,a){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=t||[],this.classes=a||[]}setAttribute(e,t){this.attributes[e]=t}getAttribute(e){return this.attributes[e]}toNode(){var e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);this.classes.length>0&&(e.className=U0(this.classes));for(var a=0;a<this.children.length;a++)if(this.children[a]instanceof S0&&this.children[a+1]instanceof S0){for(var i=this.children[a].toText()+this.children[++a].toText();this.children[a+1]instanceof S0;)i+=this.children[++a].toText();e.appendChild(new S0(i).toNode())}else e.appendChild(this.children[a].toNode());return e}toMarkup(){var e="<"+this.type;for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="',e+=N.escape(this.attributes[t]),e+='"');this.classes.length>0&&(e+=' class ="'+N.escape(U0(this.classes))+'"'),e+=">";for(var a=0;a<this.children.length;a++)e+=this.children[a].toMarkup();return e+="</"+this.type+">",e}toText(){return this.children.map(e=>e.toText()).join("")}},d(ve,"MathNode"),ve),ge,S0=(ge=class{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return N.escape(this.toText())}toText(){return this.text}},d(ge,"TextNode"),ge),be,Va=(be=class{constructor(e){this.width=void 0,this.character=void 0,this.width=e,e>=.05555&&e<=.05556?this.character=" ":e>=.1666&&e<=.1667?this.character=" ":e>=.2222&&e<=.2223?this.character=" ":e>=.2777&&e<=.2778?this.character="  ":e>=-.05556&&e<=-.05555?this.character=" ⁣":e>=-.1667&&e<=-.1666?this.character=" ⁣":e>=-.2223&&e<=-.2222?this.character=" ⁣":e>=-.2778&&e<=-.2777?this.character=" ⁣":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);var e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",z(this.width)),e}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+z(this.width)+'"/>'}toText(){return this.character?this.character:" "}},d(be,"SpaceNode"),be),A={MathNode:h0,TextNode:S0,SpaceNode:Va,newDocumentFragment:jt},v0=d(function(e,t,a){return W[t][e]&&W[t][e].replace&&e.charCodeAt(0)!==55349&&!(_r.hasOwnProperty(e)&&a&&(a.fontFamily&&a.fontFamily.slice(4,6)==="tt"||a.font&&a.font.slice(4,6)==="tt"))&&(e=W[t][e].replace),new A.TextNode(e)},"makeText"),Zt=d(function(e){return e.length===1?e[0]:new A.MathNode("mrow",e)},"makeRow"),Kt=d(function(e,t){if(t.fontFamily==="texttt")return"monospace";if(t.fontFamily==="textsf")return t.fontShape==="textit"&&t.fontWeight==="textbf"?"sans-serif-bold-italic":t.fontShape==="textit"?"sans-serif-italic":t.fontWeight==="textbf"?"bold-sans-serif":"sans-serif";if(t.fontShape==="textit"&&t.fontWeight==="textbf")return"bold-italic";if(t.fontShape==="textit")return"italic";if(t.fontWeight==="textbf")return"bold";var a=t.font;if(!a||a==="mathnormal")return null;var i=e.mode;if(a==="mathit")return"italic";if(a==="boldsymbol")return e.type==="textord"?"bold":"bold-italic";if(a==="mathbf")return"bold";if(a==="mathbb")return"double-struck";if(a==="mathsfit")return"sans-serif-italic";if(a==="mathfrak")return"fraktur";if(a==="mathscr"||a==="mathcal")return"script";if(a==="mathsf")return"sans-serif";if(a==="mathtt")return"monospace";var l=e.text;if(N.contains(["\\imath","\\jmath"],l))return null;W[i][l]&&W[i][l].replace&&(l=W[i][l].replace);var u=y.fontMap[a].fontName;return st(l,u,i)?y.fontMap[a].variant:null},"getVariant");function et(r){if(!r)return!1;if(r.type==="mi"&&r.children.length===1){var e=r.children[0];return e instanceof S0&&e.text==="."}else if(r.type==="mo"&&r.children.length===1&&r.getAttribute("separator")==="true"&&r.getAttribute("lspace")==="0em"&&r.getAttribute("rspace")==="0em"){var t=r.children[0];return t instanceof S0&&t.text===","}else return!1}d(et,"isNumberPunctuation");var o0=d(function(e,t,a){if(e.length===1){var i=X(e[0],t);return a&&i instanceof h0&&i.type==="mo"&&(i.setAttribute("lspace","0em"),i.setAttribute("rspace","0em")),[i]}for(var l=[],u,h=0;h<e.length;h++){var c=X(e[h],t);if(c instanceof h0&&u instanceof h0){if(c.type==="mtext"&&u.type==="mtext"&&c.getAttribute("mathvariant")===u.getAttribute("mathvariant")){u.children.push(...c.children);continue}else if(c.type==="mn"&&u.type==="mn"){u.children.push(...c.children);continue}else if(et(c)&&u.type==="mn"){u.children.push(...c.children);continue}else if(c.type==="mn"&&et(u))c.children=[...u.children,...c.children],l.pop();else if((c.type==="msup"||c.type==="msub")&&c.children.length>=1&&(u.type==="mn"||et(u))){var v=c.children[0];v instanceof h0&&v.type==="mn"&&(v.children=[...u.children,...v.children],l.pop())}else if(u.type==="mi"&&u.children.length===1){var b=u.children[0];if(b instanceof S0&&b.text==="̸"&&(c.type==="mo"||c.type==="mi"||c.type==="mn")){var x=c.children[0];x instanceof S0&&x.text.length>0&&(x.text=x.text.slice(0,1)+"̸"+x.text.slice(1),l.pop())}}}l.push(c),u=c}return l},"buildExpression"),Y0=d(function(e,t,a){return Zt(o0(e,t,a))},"buildExpressionRow"),X=d(function(e,t){if(!e)return new A.MathNode("mrow");if(it[e.type]){var a=it[e.type](e,t);return a}else throw new M("Got group of unknown type: '"+e.type+"'")},"buildGroup");function Lt(r,e,t,a,i){var l=o0(r,t),u;l.length===1&&l[0]instanceof h0&&N.contains(["mrow","mtable"],l[0].type)?u=l[0]:u=new A.MathNode("mrow",l);var h=new A.MathNode("annotation",[new A.TextNode(e)]);h.setAttribute("encoding","application/x-tex");var c=new A.MathNode("semantics",[u,h]),v=new A.MathNode("math",[c]);v.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),a&&v.setAttribute("display","block");var b=i?"katex":"katex-mathml";return y.makeSpan([b],[v])}d(Lt,"buildMathML");var l1=d(function(e){return new Wr({style:e.displayMode?R.DISPLAY:R.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},"optionsFromSettings"),s1=d(function(e,t){if(t.displayMode){var a=["katex-display"];t.leqno&&a.push("leqno"),t.fleqn&&a.push("fleqn"),e=y.makeSpan(a,[e])}return e},"displayWrap"),Ua=d(function(e,t,a){var i=l1(a),l;if(a.output==="mathml")return Lt(e,t,i,a.displayMode,!0);if(a.output==="html"){var u=lt(e,i);l=y.makeSpan(["katex"],[u])}else{var h=Lt(e,t,i,a.displayMode,!1),c=lt(e,i);l=y.makeSpan(["katex"],[h,c])}return s1(l,a)},"buildTree"),$a=d(function(e,t,a){var i=l1(a),l=lt(e,i),u=y.makeSpan(["katex"],[l]);return s1(u,a)},"buildHTMLTree"),Ya={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},Xa=d(function(e){var t=new A.MathNode("mo",[new A.TextNode(Ya[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},"mathMLnode"),Wa={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},ja=d(function(e){return e.type==="ordgroup"?e.body.length:1},"groupLength"),Za=d(function(e,t){function a(){var h=4e5,c=e.label.slice(1);if(N.contains(["widehat","widecheck","widetilde","utilde"],c)){var v=e,b=ja(v.base),x,k,w;if(b>5)c==="widehat"||c==="widecheck"?(x=420,h=2364,w=.42,k=c+"4"):(x=312,h=2340,w=.34,k="tilde4");else{var B=[1,1,2,2,3,3][b];c==="widehat"||c==="widecheck"?(h=[0,1062,2364,2364,2364][B],x=[0,239,300,360,420][B],w=[0,.24,.3,.3,.36,.42][B],k=c+B):(h=[0,600,1033,2339,2340][B],x=[0,260,286,306,312][B],w=[0,.26,.286,.3,.306,.34][B],k="tilde"+B)}var C=new $0(k),D=new F0([C],{width:"100%",height:z(w),viewBox:"0 0 "+h+" "+x,preserveAspectRatio:"none"});return{span:y.makeSvgSpan([],[D],t),minWidth:0,height:w}}else{var F=[],L=Wa[c],[O,G,P]=L,$=P/1e3,U=O.length,Z,Y;if(U===1){var z0=L[3];Z=["hide-tail"],Y=[z0]}else if(U===2)Z=["halfarrow-left","halfarrow-right"],Y=["xMinYMin","xMaxYMin"];else if(U===3)Z=["brace-left","brace-center","brace-right"],Y=["xMinYMin","xMidYMin","xMaxYMin"];else throw new Error(`Correct katexImagesData or update code here to support
                    `+U+" children.");for(var i0=0;i0<U;i0++){var t0=new $0(O[i0]),W0=new F0([t0],{width:"400em",height:z($),viewBox:"0 0 "+h+" "+P,preserveAspectRatio:Y[i0]+" slice"}),s0=y.makeSvgSpan([Z[i0]],[W0],t);if(U===1)return{span:s0,minWidth:G,height:$};s0.style.height=z($),F.push(s0)}return{span:y.makeSpan(["stretchy"],F,t),minWidth:G,height:$}}}d(a,"buildSvgSpan_");var{span:i,minWidth:l,height:u}=a();return i.height=u,i.style.height=z(u),l>0&&(i.style.minWidth=z(l)),i},"svgSpan"),Ka=d(function(e,t,a,i,l){var u,h=e.height+e.depth+a+i;if(/fbox|color|angl/.test(t)){if(u=y.makeSpan(["stretchy",t],[],l),t==="fbox"){var c=l.color&&l.getColor();c&&(u.style.borderColor=c)}}else{var v=[];/^[bx]cancel$/.test(t)&&v.push(new Nt({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&v.push(new Nt({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var b=new F0(v,{width:"100%",height:z(h)});u=y.makeSvgSpan([],[b],l)}return u.height=h,u.style.height=z(h),u},"encloseSpan"),q0={encloseSpan:Ka,mathMLnode:Xa,svgSpan:Za};function H(r,e){if(!r||r.type!==e)throw new Error("Expected node of type "+e+", but got "+(r?"node of type "+r.type:String(r)));return r}d(H,"assertNodeType");function ht(r){var e=Re(r);if(!e)throw new Error("Expected node of symbol group type, but got "+(r?"node of type "+r.type:String(r)));return e}d(ht,"assertSymbolNodeType");function Re(r){return r&&(r.type==="atom"||wa.hasOwnProperty(r.type))?r:null}d(Re,"checkSymbolNodeType");var Jt=d((r,e)=>{var t,a,i;r&&r.type==="supsub"?(a=H(r.base,"accent"),t=a.base,r.base=t,i=Qr(V(r,e)),r.base=a):(a=H(r,"accent"),t=a.base);var l=V(t,e.havingCrampedStyle()),u=a.isShifty&&N.isCharacterBox(t),h=0;if(u){var c=N.getBaseElem(t),v=V(c,e.havingCrampedStyle());h=qt(v).skew}var b=a.label==="\\c",x=b?l.height+l.depth:Math.min(l.height,e.fontMetrics().xHeight),k;if(a.isStretchy)k=q0.svgSpan(a,e),k=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"elem",elem:k,wrapperClasses:["svg-align"],wrapperStyle:h>0?{width:"calc(100% - "+z(2*h)+")",marginLeft:z(2*h)}:void 0}]},e);else{var w,B;a.label==="\\vec"?(w=y.staticSvg("vec",e),B=y.svgData.vec[1]):(w=y.makeOrd({mode:a.mode,text:a.label},e,"textord"),w=qt(w),w.italic=0,B=w.width,b&&(x+=w.depth)),k=y.makeSpan(["accent-body"],[w]);var C=a.label==="\\textcircled";C&&(k.classes.push("accent-full"),x=l.height);var D=h;C||(D-=B/2),k.style.left=z(D),a.label==="\\textcircled"&&(k.style.top=".2em"),k=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"kern",size:-x},{type:"elem",elem:k}]},e)}var F=y.makeSpan(["mord","accent"],[k],e);return i?(i.children[0]=F,i.height=Math.max(F.height,i.height),i.classes[0]="mord",i):F},"htmlBuilder$a"),u1=d((r,e)=>{var t=r.isStretchy?q0.mathMLnode(r.label):new A.MathNode("mo",[v0(r.label,r.mode)]),a=new A.MathNode("mover",[X(r.base,e),t]);return a.setAttribute("accent","true"),a},"mathmlBuilder$9"),Ja=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(r=>"\\"+r).join("|"));T({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:d((r,e)=>{var t=nt(e[0]),a=!Ja.test(r.funcName),i=!a||r.funcName==="\\widehat"||r.funcName==="\\widetilde"||r.funcName==="\\widecheck";return{type:"accent",mode:r.parser.mode,label:r.funcName,isStretchy:a,isShifty:i,base:t}},"handler"),htmlBuilder:Jt,mathmlBuilder:u1});T({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:d((r,e)=>{var t=e[0],a=r.parser.mode;return a==="math"&&(r.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+r.funcName+" works only in text mode"),a="text"),{type:"accent",mode:a,label:r.funcName,isStretchy:!1,isShifty:!0,base:t}},"handler"),htmlBuilder:Jt,mathmlBuilder:u1});T({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:d((r,e)=>{var{parser:t,funcName:a}=r,i=e[0];return{type:"accentUnder",mode:t.mode,label:a,base:i}},"handler"),htmlBuilder:d((r,e)=>{var t=V(r.base,e),a=q0.svgSpan(r,e),i=r.label==="\\utilde"?.12:0,l=y.makeVList({positionType:"top",positionData:t.height,children:[{type:"elem",elem:a,wrapperClasses:["svg-align"]},{type:"kern",size:i},{type:"elem",elem:t}]},e);return y.makeSpan(["mord","accentunder"],[l],e)},"htmlBuilder"),mathmlBuilder:d((r,e)=>{var t=q0.mathMLnode(r.label),a=new A.MathNode("munder",[X(r.base,e),t]);return a.setAttribute("accentunder","true"),a},"mathmlBuilder")});var Ze=d(r=>{var e=new A.MathNode("mpadded",r?[r]:[]);return e.setAttribute("width","+0.6em"),e.setAttribute("lspace","0.3em"),e},"paddedNode");T({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(r,e,t){var{parser:a,funcName:i}=r;return{type:"xArrow",mode:a.mode,label:i,body:e[0],below:t[0]}},htmlBuilder(r,e){var t=e.style,a=e.havingStyle(t.sup()),i=y.wrapFragment(V(r.body,a,e),e),l=r.label.slice(0,2)==="\\x"?"x":"cd";i.classes.push(l+"-arrow-pad");var u;r.below&&(a=e.havingStyle(t.sub()),u=y.wrapFragment(V(r.below,a,e),e),u.classes.push(l+"-arrow-pad"));var h=q0.svgSpan(r,e),c=-e.fontMetrics().axisHeight+.5*h.height,v=-e.fontMetrics().axisHeight-.5*h.height-.111;(i.depth>.25||r.label==="\\xleftequilibrium")&&(v-=i.depth);var b;if(u){var x=-e.fontMetrics().axisHeight+u.height+.5*h.height+.111;b=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:v},{type:"elem",elem:h,shift:c},{type:"elem",elem:u,shift:x}]},e)}else b=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:v},{type:"elem",elem:h,shift:c}]},e);return b.children[0].children[0].children[1].classes.push("svg-align"),y.makeSpan(["mrel","x-arrow"],[b],e)},mathmlBuilder(r,e){var t=q0.mathMLnode(r.label);t.setAttribute("minsize",r.label.charAt(0)==="x"?"1.75em":"3.0em");var a;if(r.body){var i=Ze(X(r.body,e));if(r.below){var l=Ze(X(r.below,e));a=new A.MathNode("munderover",[t,l,i])}else a=new A.MathNode("mover",[t,i])}else if(r.below){var u=Ze(X(r.below,e));a=new A.MathNode("munder",[t,u])}else a=Ze(),a=new A.MathNode("mover",[t,a]);return a}});var Qa=y.makeSpan;function Qt(r,e){var t=r0(r.body,e,!0);return Qa([r.mclass],t,e)}d(Qt,"htmlBuilder$9");function _t(r,e){var t,a=o0(r.body,e);return r.mclass==="minner"?t=new A.MathNode("mpadded",a):r.mclass==="mord"?r.isCharacterBox?(t=a[0],t.type="mi"):t=new A.MathNode("mi",a):(r.isCharacterBox?(t=a[0],t.type="mo"):t=new A.MathNode("mo",a),r.mclass==="mbin"?(t.attributes.lspace="0.22em",t.attributes.rspace="0.22em"):r.mclass==="mpunct"?(t.attributes.lspace="0em",t.attributes.rspace="0.17em"):r.mclass==="mopen"||r.mclass==="mclose"?(t.attributes.lspace="0em",t.attributes.rspace="0em"):r.mclass==="minner"&&(t.attributes.lspace="0.0556em",t.attributes.width="+0.1111em")),t}d(_t,"mathmlBuilder$8");T({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(r,e){var{parser:t,funcName:a}=r,i=e[0];return{type:"mclass",mode:t.mode,mclass:"m"+a.slice(5),body:_(i),isCharacterBox:N.isCharacterBox(i)}},htmlBuilder:Qt,mathmlBuilder:_t});var mt=d(r=>{var e=r.type==="ordgroup"&&r.body.length?r.body[0]:r;return e.type==="atom"&&(e.family==="bin"||e.family==="rel")?"m"+e.family:"mord"},"binrelClass");T({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(r,e){var{parser:t}=r;return{type:"mclass",mode:t.mode,mclass:mt(e[0]),body:_(e[1]),isCharacterBox:N.isCharacterBox(e[1])}}});T({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(r,e){var{parser:t,funcName:a}=r,i=e[1],l=e[0],u;a!=="\\stackrel"?u=mt(i):u="mrel";var h={type:"op",mode:i.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:a!=="\\stackrel",body:_(i)},c={type:"supsub",mode:l.mode,base:h,sup:a==="\\underset"?null:l,sub:a==="\\underset"?l:null};return{type:"mclass",mode:t.mode,mclass:u,body:[c],isCharacterBox:N.isCharacterBox(c)}},htmlBuilder:Qt,mathmlBuilder:_t});T({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(r,e){var{parser:t}=r;return{type:"pmb",mode:t.mode,mclass:mt(e[0]),body:_(e[0])}},htmlBuilder(r,e){var t=r0(r.body,e,!0),a=y.makeSpan([r.mclass],t,e);return a.style.textShadow="0.02em 0.01em 0.04px",a},mathmlBuilder(r,e){var t=o0(r.body,e),a=new A.MathNode("mstyle",t);return a.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),a}});var _a={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},wr=d(()=>({type:"styling",body:[],mode:"math",style:"display"}),"newCell"),kr=d(r=>r.type==="textord"&&r.text==="@","isStartOfArrow"),e4=d((r,e)=>(r.type==="mathord"||r.type==="atom")&&r.text===e,"isLabelEnd");function o1(r,e,t){var a=_a[r];switch(a){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return t.callFunction(a,[e[0]],[e[1]]);case"\\uparrow":case"\\downarrow":{var i=t.callFunction("\\\\cdleft",[e[0]],[]),l={type:"atom",text:a,mode:"math",family:"rel"},u=t.callFunction("\\Big",[l],[]),h=t.callFunction("\\\\cdright",[e[1]],[]),c={type:"ordgroup",mode:"math",body:[i,u,h]};return t.callFunction("\\\\cdparent",[c],[])}case"\\\\cdlongequal":return t.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":{var v={type:"textord",text:"\\Vert",mode:"math"};return t.callFunction("\\Big",[v],[])}default:return{type:"textord",text:" ",mode:"math"}}}d(o1,"cdArrow");function h1(r){var e=[];for(r.gullet.beginGroup(),r.gullet.macros.set("\\cr","\\\\\\relax"),r.gullet.beginGroup();;){e.push(r.parseExpression(!1,"\\\\")),r.gullet.endGroup(),r.gullet.beginGroup();var t=r.fetch().text;if(t==="&"||t==="\\\\")r.consume();else if(t==="\\end"){e[e.length-1].length===0&&e.pop();break}else throw new M("Expected \\\\ or \\cr or \\end",r.nextToken)}for(var a=[],i=[a],l=0;l<e.length;l++){for(var u=e[l],h=wr(),c=0;c<u.length;c++)if(!kr(u[c]))h.body.push(u[c]);else{a.push(h),c+=1;var v=ht(u[c]).text,b=new Array(2);if(b[0]={type:"ordgroup",mode:"math",body:[]},b[1]={type:"ordgroup",mode:"math",body:[]},!("=|.".indexOf(v)>-1))if("<>AV".indexOf(v)>-1)for(var x=0;x<2;x++){for(var k=!0,w=c+1;w<u.length;w++){if(e4(u[w],v)){k=!1,c=w;break}if(kr(u[w]))throw new M("Missing a "+v+" character to complete a CD arrow.",u[w]);b[x].body.push(u[w])}if(k)throw new M("Missing a "+v+" character to complete a CD arrow.",u[c])}else throw new M('Expected one of "<>AV=|." after @',u[c]);var B=o1(v,b,r),C={type:"styling",body:[B],mode:"math",style:"display"};a.push(C),h=wr()}l%2===0?a.push(h):a.shift(),a=[],i.push(a)}r.gullet.endGroup(),r.gullet.endGroup();var D=new Array(i[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:i,arraystretch:1,addJot:!0,rowGaps:[null],cols:D,colSeparationType:"CD",hLinesBeforeRow:new Array(i.length+1).fill([])}}d(h1,"parseCD");T({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(r,e){var{parser:t,funcName:a}=r;return{type:"cdlabel",mode:t.mode,side:a.slice(4),label:e[0]}},htmlBuilder(r,e){var t=e.havingStyle(e.style.sup()),a=y.wrapFragment(V(r.label,t,e),e);return a.classes.push("cd-label-"+r.side),a.style.bottom=z(.8-a.depth),a.height=0,a.depth=0,a},mathmlBuilder(r,e){var t=new A.MathNode("mrow",[X(r.label,e)]);return t=new A.MathNode("mpadded",[t]),t.setAttribute("width","0"),r.side==="left"&&t.setAttribute("lspace","-1width"),t.setAttribute("voffset","0.7em"),t=new A.MathNode("mstyle",[t]),t.setAttribute("displaystyle","false"),t.setAttribute("scriptlevel","1"),t}});T({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(r,e){var{parser:t}=r;return{type:"cdlabelparent",mode:t.mode,fragment:e[0]}},htmlBuilder(r,e){var t=y.wrapFragment(V(r.fragment,e),e);return t.classes.push("cd-vert-arrow"),t},mathmlBuilder(r,e){return new A.MathNode("mrow",[X(r.fragment,e)])}});T({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(r,e){for(var{parser:t}=r,a=H(e[0],"ordgroup"),i=a.body,l="",u=0;u<i.length;u++){var h=H(i[u],"textord");l+=h.text}var c=parseInt(l),v;if(isNaN(c))throw new M("\\@char has non-numeric argument "+l);if(c<0||c>=1114111)throw new M("\\@char with invalid code point "+l);return c<=65535?v=String.fromCharCode(c):(c-=65536,v=String.fromCharCode((c>>10)+55296,(c&1023)+56320)),{type:"textord",mode:t.mode,text:v}}});var m1=d((r,e)=>{var t=r0(r.body,e.withColor(r.color),!1);return y.makeFragment(t)},"htmlBuilder$8"),c1=d((r,e)=>{var t=o0(r.body,e.withColor(r.color)),a=new A.MathNode("mstyle",t);return a.setAttribute("mathcolor",r.color),a},"mathmlBuilder$7");T({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(r,e){var{parser:t}=r,a=H(e[0],"color-token").color,i=e[1];return{type:"color",mode:t.mode,color:a,body:_(i)}},htmlBuilder:m1,mathmlBuilder:c1});T({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(r,e){var{parser:t,breakOnTokenText:a}=r,i=H(e[0],"color-token").color;t.gullet.macros.set("\\current@color",i);var l=t.parseExpression(!0,a);return{type:"color",mode:t.mode,color:i,body:l}},htmlBuilder:m1,mathmlBuilder:c1});T({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(r,e,t){var{parser:a}=r,i=a.gullet.future().text==="["?a.parseSizeGroup(!0):null,l=!a.settings.displayMode||!a.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:a.mode,newLine:l,size:i&&H(i,"size").value}},htmlBuilder(r,e){var t=y.makeSpan(["mspace"],[],e);return r.newLine&&(t.classes.push("newline"),r.size&&(t.style.marginTop=z(Q(r.size,e)))),t},mathmlBuilder(r,e){var t=new A.MathNode("mspace");return r.newLine&&(t.setAttribute("linebreak","newline"),r.size&&t.setAttribute("height",z(Q(r.size,e)))),t}});var Ot={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},d1=d(r=>{var e=r.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(e))throw new M("Expected a control sequence",r);return e},"checkControlSequence"),t4=d(r=>{var e=r.gullet.popToken();return e.text==="="&&(e=r.gullet.popToken(),e.text===" "&&(e=r.gullet.popToken())),e},"getRHS"),p1=d((r,e,t,a)=>{var i=r.gullet.macros.get(t.text);i==null&&(t.noexpand=!0,i={tokens:[t],numArgs:0,unexpandable:!r.gullet.isExpandable(t.text)}),r.gullet.macros.set(e,i,a)},"letCommand");T({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(r){var{parser:e,funcName:t}=r;e.consumeSpaces();var a=e.fetch();if(Ot[a.text])return(t==="\\global"||t==="\\\\globallong")&&(a.text=Ot[a.text]),H(e.parseFunction(),"internal");throw new M("Invalid token after macro prefix",a)}});T({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r){var{parser:e,funcName:t}=r,a=e.gullet.popToken(),i=a.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(i))throw new M("Expected a control sequence",a);for(var l=0,u,h=[[]];e.gullet.future().text!=="{";)if(a=e.gullet.popToken(),a.text==="#"){if(e.gullet.future().text==="{"){u=e.gullet.future(),h[l].push("{");break}if(a=e.gullet.popToken(),!/^[1-9]$/.test(a.text))throw new M('Invalid argument number "'+a.text+'"');if(parseInt(a.text)!==l+1)throw new M('Argument number "'+a.text+'" out of order');l++,h.push([])}else{if(a.text==="EOF")throw new M("Expected a macro definition");h[l].push(a.text)}var{tokens:c}=e.gullet.consumeArg();return u&&c.unshift(u),(t==="\\edef"||t==="\\xdef")&&(c=e.gullet.expandTokens(c),c.reverse()),e.gullet.macros.set(i,{tokens:c,numArgs:l,delimiters:h},t===Ot[t]),{type:"internal",mode:e.mode}}});T({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r){var{parser:e,funcName:t}=r,a=d1(e.gullet.popToken());e.gullet.consumeSpaces();var i=t4(e);return p1(e,a,i,t==="\\\\globallet"),{type:"internal",mode:e.mode}}});T({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r){var{parser:e,funcName:t}=r,a=d1(e.gullet.popToken()),i=e.gullet.popToken(),l=e.gullet.popToken();return p1(e,a,l,t==="\\\\globalfuture"),e.gullet.pushToken(l),e.gullet.pushToken(i),{type:"internal",mode:e.mode}}});var Te=d(function(e,t,a){var i=W.math[e]&&W.math[e].replace,l=st(i||e,t,a);if(!l)throw new Error("Unsupported symbol "+e+" and font size "+t+".");return l},"getMetrics"),er=d(function(e,t,a,i){var l=a.havingBaseStyle(t),u=y.makeSpan(i.concat(l.sizingClasses(a)),[e],a),h=l.sizeMultiplier/a.sizeMultiplier;return u.height*=h,u.depth*=h,u.maxFontSize=l.sizeMultiplier,u},"styleWrap"),f1=d(function(e,t,a){var i=t.havingBaseStyle(a),l=(1-t.sizeMultiplier/i.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=z(l),e.height-=l,e.depth+=l},"centerSpan"),r4=d(function(e,t,a,i,l,u){var h=y.makeSymbol(e,"Main-Regular",l,i),c=er(h,t,i,u);return a&&f1(c,i,t),c},"makeSmallDelim"),a4=d(function(e,t,a,i){return y.makeSymbol(e,"Size"+t+"-Regular",a,i)},"mathrmSize"),v1=d(function(e,t,a,i,l,u){var h=a4(e,t,l,i),c=er(y.makeSpan(["delimsizing","size"+t],[h],i),R.TEXT,i,u);return a&&f1(c,i,R.TEXT),c},"makeLargeDelim"),kt=d(function(e,t,a){var i;t==="Size1-Regular"?i="delim-size1":i="delim-size4";var l=y.makeSpan(["delimsizinginner",i],[y.makeSpan([],[y.makeSymbol(e,t,a)])]);return{type:"elem",elem:l}},"makeGlyphSpan"),St=d(function(e,t,a){var i=k0["Size4-Regular"][e.charCodeAt(0)]?k0["Size4-Regular"][e.charCodeAt(0)][4]:k0["Size1-Regular"][e.charCodeAt(0)][4],l=new $0("inner",da(e,Math.round(1e3*t))),u=new F0([l],{width:z(i),height:z(t),style:"width:"+z(i),viewBox:"0 0 "+1e3*i+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),h=y.makeSvgSpan([],[u],a);return h.height=t,h.style.height=z(t),h.style.width=z(i),{type:"elem",elem:h}},"makeInner"),Ht=.008,Ke={type:"kern",size:-1*Ht},i4=["|","\\lvert","\\rvert","\\vert"],n4=["\\|","\\lVert","\\rVert","\\Vert"],g1=d(function(e,t,a,i,l,u){var h,c,v,b,x="",k=0;h=v=b=e,c=null;var w="Size1-Regular";e==="\\uparrow"?v=b="⏐":e==="\\Uparrow"?v=b="‖":e==="\\downarrow"?h=v="⏐":e==="\\Downarrow"?h=v="‖":e==="\\updownarrow"?(h="\\uparrow",v="⏐",b="\\downarrow"):e==="\\Updownarrow"?(h="\\Uparrow",v="‖",b="\\Downarrow"):N.contains(i4,e)?(v="∣",x="vert",k=333):N.contains(n4,e)?(v="∥",x="doublevert",k=556):e==="["||e==="\\lbrack"?(h="⎡",v="⎢",b="⎣",w="Size4-Regular",x="lbrack",k=667):e==="]"||e==="\\rbrack"?(h="⎤",v="⎥",b="⎦",w="Size4-Regular",x="rbrack",k=667):e==="\\lfloor"||e==="⌊"?(v=h="⎢",b="⎣",w="Size4-Regular",x="lfloor",k=667):e==="\\lceil"||e==="⌈"?(h="⎡",v=b="⎢",w="Size4-Regular",x="lceil",k=667):e==="\\rfloor"||e==="⌋"?(v=h="⎥",b="⎦",w="Size4-Regular",x="rfloor",k=667):e==="\\rceil"||e==="⌉"?(h="⎤",v=b="⎥",w="Size4-Regular",x="rceil",k=667):e==="("||e==="\\lparen"?(h="⎛",v="⎜",b="⎝",w="Size4-Regular",x="lparen",k=875):e===")"||e==="\\rparen"?(h="⎞",v="⎟",b="⎠",w="Size4-Regular",x="rparen",k=875):e==="\\{"||e==="\\lbrace"?(h="⎧",c="⎨",b="⎩",v="⎪",w="Size4-Regular"):e==="\\}"||e==="\\rbrace"?(h="⎫",c="⎬",b="⎭",v="⎪",w="Size4-Regular"):e==="\\lgroup"||e==="⟮"?(h="⎧",b="⎩",v="⎪",w="Size4-Regular"):e==="\\rgroup"||e==="⟯"?(h="⎫",b="⎭",v="⎪",w="Size4-Regular"):e==="\\lmoustache"||e==="⎰"?(h="⎧",b="⎭",v="⎪",w="Size4-Regular"):(e==="\\rmoustache"||e==="⎱")&&(h="⎫",b="⎩",v="⎪",w="Size4-Regular");var B=Te(h,w,l),C=B.height+B.depth,D=Te(v,w,l),F=D.height+D.depth,L=Te(b,w,l),O=L.height+L.depth,G=0,P=1;if(c!==null){var $=Te(c,w,l);G=$.height+$.depth,P=2}var U=C+O+G,Z=Math.max(0,Math.ceil((t-U)/(P*F))),Y=U+Z*P*F,z0=i.fontMetrics().axisHeight;a&&(z0*=i.sizeMultiplier);var i0=Y/2-z0,t0=[];if(x.length>0){var W0=Y-C-O,s0=Math.round(Y*1e3),g0=pa(x,Math.round(W0*1e3)),L0=new $0(x,g0),_0=(k/1e3).toFixed(3)+"em",ee=(s0/1e3).toFixed(3)+"em",ft=new F0([L0],{width:_0,height:ee,viewBox:"0 0 "+k+" "+s0}),O0=y.makeSvgSpan([],[ft],i);O0.height=s0/1e3,O0.style.width=_0,O0.style.height=ee,t0.push({type:"elem",elem:O0})}else{if(t0.push(kt(b,w,l)),t0.push(Ke),c===null){var H0=Y-C-O+2*Ht;t0.push(St(v,H0,i))}else{var c0=(Y-C-O-G)/2+2*Ht;t0.push(St(v,c0,i)),t0.push(Ke),t0.push(kt(c,w,l)),t0.push(Ke),t0.push(St(v,c0,i))}t0.push(Ke),t0.push(kt(h,w,l))}var Me=i.havingBaseStyle(R.TEXT),vt=y.makeVList({positionType:"bottom",positionData:i0,children:t0},Me);return er(y.makeSpan(["delimsizing","mult"],[vt],Me),R.TEXT,i,u)},"makeStackedDelim"),At=80,Mt=.08,Bt=d(function(e,t,a,i,l){var u=ca(e,i,a),h=new $0(e,u),c=new F0([h],{width:"400em",height:z(t),viewBox:"0 0 400000 "+a,preserveAspectRatio:"xMinYMin slice"});return y.makeSvgSpan(["hide-tail"],[c],l)},"sqrtSvg"),l4=d(function(e,t){var a=t.havingBaseSizing(),i=w1("\\surd",e*a.sizeMultiplier,x1,a),l=a.sizeMultiplier,u=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),h,c=0,v=0,b=0,x;return i.type==="small"?(b=1e3+1e3*u+At,e<1?l=1:e<1.4&&(l=.7),c=(1+u+Mt)/l,v=(1+u)/l,h=Bt("sqrtMain",c,b,u,t),h.style.minWidth="0.853em",x=.833/l):i.type==="large"?(b=(1e3+At)*Ee[i.size],v=(Ee[i.size]+u)/l,c=(Ee[i.size]+u+Mt)/l,h=Bt("sqrtSize"+i.size,c,b,u,t),h.style.minWidth="1.02em",x=1/l):(c=e+u+Mt,v=e+u,b=Math.floor(1e3*e+u)+At,h=Bt("sqrtTall",c,b,u,t),h.style.minWidth="0.742em",x=1.056),h.height=v,h.style.height=z(c),{span:h,advanceWidth:x,ruleWidth:(t.fontMetrics().sqrtRuleThickness+u)*l}},"makeSqrtImage"),b1=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],s4=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],y1=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],Ee=[0,1.2,1.8,2.4,3],u4=d(function(e,t,a,i,l){if(e==="<"||e==="\\lt"||e==="⟨"?e="\\langle":(e===">"||e==="\\gt"||e==="⟩")&&(e="\\rangle"),N.contains(b1,e)||N.contains(y1,e))return v1(e,t,!1,a,i,l);if(N.contains(s4,e))return g1(e,Ee[t],!1,a,i,l);throw new M("Illegal delimiter: '"+e+"'")},"makeSizedDelim"),o4=[{type:"small",style:R.SCRIPTSCRIPT},{type:"small",style:R.SCRIPT},{type:"small",style:R.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],h4=[{type:"small",style:R.SCRIPTSCRIPT},{type:"small",style:R.SCRIPT},{type:"small",style:R.TEXT},{type:"stack"}],x1=[{type:"small",style:R.SCRIPTSCRIPT},{type:"small",style:R.SCRIPT},{type:"small",style:R.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],m4=d(function(e){if(e.type==="small")return"Main-Regular";if(e.type==="large")return"Size"+e.size+"-Regular";if(e.type==="stack")return"Size4-Regular";throw new Error("Add support for delim type '"+e.type+"' here.")},"delimTypeToFont"),w1=d(function(e,t,a,i){for(var l=Math.min(2,3-i.style.size),u=l;u<a.length&&a[u].type!=="stack";u++){var h=Te(e,m4(a[u]),"math"),c=h.height+h.depth;if(a[u].type==="small"){var v=i.havingBaseStyle(a[u].style);c*=v.sizeMultiplier}if(c>t)return a[u]}return a[a.length-1]},"traverseSequence"),k1=d(function(e,t,a,i,l,u){e==="<"||e==="\\lt"||e==="⟨"?e="\\langle":(e===">"||e==="\\gt"||e==="⟩")&&(e="\\rangle");var h;N.contains(y1,e)?h=o4:N.contains(b1,e)?h=x1:h=h4;var c=w1(e,t,h,i);return c.type==="small"?r4(e,c.style,a,i,l,u):c.type==="large"?v1(e,c.size,a,i,l,u):g1(e,t,a,i,l,u)},"makeCustomSizedDelim"),c4=d(function(e,t,a,i,l,u){var h=i.fontMetrics().axisHeight*i.sizeMultiplier,c=901,v=5/i.fontMetrics().ptPerEm,b=Math.max(t-h,a+h),x=Math.max(b/500*c,2*b-v);return k1(e,x,!0,i,l,u)},"makeLeftRightDelim"),D0={sqrtImage:l4,sizedDelim:u4,sizeToMaxHeight:Ee,customSizedDelim:k1,leftRightDelim:c4},Sr={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},d4=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function Ie(r,e){var t=Re(r);if(t&&N.contains(d4,t.text))return t;throw t?new M("Invalid delimiter '"+t.text+"' after '"+e.funcName+"'",r):new M("Invalid delimiter type '"+r.type+"'",r)}d(Ie,"checkDelimiter");T({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:d((r,e)=>{var t=Ie(e[0],r);return{type:"delimsizing",mode:r.parser.mode,size:Sr[r.funcName].size,mclass:Sr[r.funcName].mclass,delim:t.text}},"handler"),htmlBuilder:d((r,e)=>r.delim==="."?y.makeSpan([r.mclass]):D0.sizedDelim(r.delim,r.size,e,r.mode,[r.mclass]),"htmlBuilder"),mathmlBuilder:d(r=>{var e=[];r.delim!=="."&&e.push(v0(r.delim,r.mode));var t=new A.MathNode("mo",e);r.mclass==="mopen"||r.mclass==="mclose"?t.setAttribute("fence","true"):t.setAttribute("fence","false"),t.setAttribute("stretchy","true");var a=z(D0.sizeToMaxHeight[r.size]);return t.setAttribute("minsize",a),t.setAttribute("maxsize",a),t},"mathmlBuilder")});function Pt(r){if(!r.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}d(Pt,"assertParsed");T({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:d((r,e)=>{var t=r.parser.gullet.macros.get("\\current@color");if(t&&typeof t!="string")throw new M("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:r.parser.mode,delim:Ie(e[0],r).text,color:t}},"handler")});T({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:d((r,e)=>{var t=Ie(e[0],r),a=r.parser;++a.leftrightDepth;var i=a.parseExpression(!1);--a.leftrightDepth,a.expect("\\right",!1);var l=H(a.parseFunction(),"leftright-right");return{type:"leftright",mode:a.mode,body:i,left:t.text,right:l.delim,rightColor:l.color}},"handler"),htmlBuilder:d((r,e)=>{Pt(r);for(var t=r0(r.body,e,!0,["mopen","mclose"]),a=0,i=0,l=!1,u=0;u<t.length;u++)t[u].isMiddle?l=!0:(a=Math.max(t[u].height,a),i=Math.max(t[u].depth,i));a*=e.sizeMultiplier,i*=e.sizeMultiplier;var h;if(r.left==="."?h=Fe(e,["mopen"]):h=D0.leftRightDelim(r.left,a,i,e,r.mode,["mopen"]),t.unshift(h),l)for(var c=1;c<t.length;c++){var v=t[c],b=v.isMiddle;b&&(t[c]=D0.leftRightDelim(b.delim,a,i,b.options,r.mode,[]))}var x;if(r.right===".")x=Fe(e,["mclose"]);else{var k=r.rightColor?e.withColor(r.rightColor):e;x=D0.leftRightDelim(r.right,a,i,k,r.mode,["mclose"])}return t.push(x),y.makeSpan(["minner"],t,e)},"htmlBuilder"),mathmlBuilder:d((r,e)=>{Pt(r);var t=o0(r.body,e);if(r.left!=="."){var a=new A.MathNode("mo",[v0(r.left,r.mode)]);a.setAttribute("fence","true"),t.unshift(a)}if(r.right!=="."){var i=new A.MathNode("mo",[v0(r.right,r.mode)]);i.setAttribute("fence","true"),r.rightColor&&i.setAttribute("mathcolor",r.rightColor),t.push(i)}return Zt(t)},"mathmlBuilder")});T({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:d((r,e)=>{var t=Ie(e[0],r);if(!r.parser.leftrightDepth)throw new M("\\middle without preceding \\left",t);return{type:"middle",mode:r.parser.mode,delim:t.text}},"handler"),htmlBuilder:d((r,e)=>{var t;if(r.delim===".")t=Fe(e,[]);else{t=D0.sizedDelim(r.delim,1,e,r.mode,[]);var a={delim:r.delim,options:e};t.isMiddle=a}return t},"htmlBuilder"),mathmlBuilder:d((r,e)=>{var t=r.delim==="\\vert"||r.delim==="|"?v0("|","text"):v0(r.delim,r.mode),a=new A.MathNode("mo",[t]);return a.setAttribute("fence","true"),a.setAttribute("lspace","0.05em"),a.setAttribute("rspace","0.05em"),a},"mathmlBuilder")});var tr=d((r,e)=>{var t=y.wrapFragment(V(r.body,e),e),a=r.label.slice(1),i=e.sizeMultiplier,l,u=0,h=N.isCharacterBox(r.body);if(a==="sout")l=y.makeSpan(["stretchy","sout"]),l.height=e.fontMetrics().defaultRuleThickness/i,u=-.5*e.fontMetrics().xHeight;else if(a==="phase"){var c=Q({number:.6,unit:"pt"},e),v=Q({number:.35,unit:"ex"},e),b=e.havingBaseSizing();i=i/b.sizeMultiplier;var x=t.height+t.depth+c+v;t.style.paddingLeft=z(x/2+c);var k=Math.floor(1e3*x*i),w=ha(k),B=new F0([new $0("phase",w)],{width:"400em",height:z(k/1e3),viewBox:"0 0 400000 "+k,preserveAspectRatio:"xMinYMin slice"});l=y.makeSvgSpan(["hide-tail"],[B],e),l.style.height=z(x),u=t.depth+c+v}else{/cancel/.test(a)?h||t.classes.push("cancel-pad"):a==="angl"?t.classes.push("anglpad"):t.classes.push("boxpad");var C=0,D=0,F=0;/box/.test(a)?(F=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness),C=e.fontMetrics().fboxsep+(a==="colorbox"?0:F),D=C):a==="angl"?(F=Math.max(e.fontMetrics().defaultRuleThickness,e.minRuleThickness),C=4*F,D=Math.max(0,.25-t.depth)):(C=h?.2:0,D=C),l=q0.encloseSpan(t,a,C,D,e),/fbox|boxed|fcolorbox/.test(a)?(l.style.borderStyle="solid",l.style.borderWidth=z(F)):a==="angl"&&F!==.049&&(l.style.borderTopWidth=z(F),l.style.borderRightWidth=z(F)),u=t.depth+D,r.backgroundColor&&(l.style.backgroundColor=r.backgroundColor,r.borderColor&&(l.style.borderColor=r.borderColor))}var L;if(r.backgroundColor)L=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:l,shift:u},{type:"elem",elem:t,shift:0}]},e);else{var O=/cancel|phase/.test(a)?["svg-align"]:[];L=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:t,shift:0},{type:"elem",elem:l,shift:u,wrapperClasses:O}]},e)}return/cancel/.test(a)&&(L.height=t.height,L.depth=t.depth),/cancel/.test(a)&&!h?y.makeSpan(["mord","cancel-lap"],[L],e):y.makeSpan(["mord"],[L],e)},"htmlBuilder$7"),rr=d((r,e)=>{var t=0,a=new A.MathNode(r.label.indexOf("colorbox")>-1?"mpadded":"menclose",[X(r.body,e)]);switch(r.label){case"\\cancel":a.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":a.setAttribute("notation","downdiagonalstrike");break;case"\\phase":a.setAttribute("notation","phasorangle");break;case"\\sout":a.setAttribute("notation","horizontalstrike");break;case"\\fbox":a.setAttribute("notation","box");break;case"\\angl":a.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(t=e.fontMetrics().fboxsep*e.fontMetrics().ptPerEm,a.setAttribute("width","+"+2*t+"pt"),a.setAttribute("height","+"+2*t+"pt"),a.setAttribute("lspace",t+"pt"),a.setAttribute("voffset",t+"pt"),r.label==="\\fcolorbox"){var i=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness);a.setAttribute("style","border: "+i+"em solid "+String(r.borderColor))}break;case"\\xcancel":a.setAttribute("notation","updiagonalstrike downdiagonalstrike");break}return r.backgroundColor&&a.setAttribute("mathbackground",r.backgroundColor),a},"mathmlBuilder$6");T({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(r,e,t){var{parser:a,funcName:i}=r,l=H(e[0],"color-token").color,u=e[1];return{type:"enclose",mode:a.mode,label:i,backgroundColor:l,body:u}},htmlBuilder:tr,mathmlBuilder:rr});T({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(r,e,t){var{parser:a,funcName:i}=r,l=H(e[0],"color-token").color,u=H(e[1],"color-token").color,h=e[2];return{type:"enclose",mode:a.mode,label:i,backgroundColor:u,borderColor:l,body:h}},htmlBuilder:tr,mathmlBuilder:rr});T({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(r,e){var{parser:t}=r;return{type:"enclose",mode:t.mode,label:"\\fbox",body:e[0]}}});T({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(r,e){var{parser:t,funcName:a}=r,i=e[0];return{type:"enclose",mode:t.mode,label:a,body:i}},htmlBuilder:tr,mathmlBuilder:rr});T({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(r,e){var{parser:t}=r;return{type:"enclose",mode:t.mode,label:"\\angl",body:e[0]}}});var S1={};function x0(r){for(var{type:e,names:t,props:a,handler:i,htmlBuilder:l,mathmlBuilder:u}=r,h={type:e,numArgs:a.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:i},c=0;c<t.length;++c)S1[t[c]]=h;l&&(at[e]=l),u&&(it[e]=u)}d(x0,"defineEnvironment");var A1={};function m(r,e){A1[r]=e}d(m,"defineMacro");function Gt(r){var e=[];r.consumeSpaces();var t=r.fetch().text;for(t==="\\relax"&&(r.consume(),r.consumeSpaces(),t=r.fetch().text);t==="\\hline"||t==="\\hdashline";)r.consume(),e.push(t==="\\hdashline"),r.consumeSpaces(),t=r.fetch().text;return e}d(Gt,"getHLines");var ct=d(r=>{var e=r.parser.settings;if(!e.displayMode)throw new M("{"+r.envName+"} can be used only in display mode.")},"validateAmsEnvironmentContext");function dt(r){if(r.indexOf("ed")===-1)return r.indexOf("*")===-1}d(dt,"getAutoTag");function I0(r,e,t){var{hskipBeforeAndAfter:a,addJot:i,cols:l,arraystretch:u,colSeparationType:h,autoTag:c,singleRow:v,emptySingleRow:b,maxNumCols:x,leqno:k}=e;if(r.gullet.beginGroup(),v||r.gullet.macros.set("\\cr","\\\\\\relax"),!u){var w=r.gullet.expandMacroAsText("\\arraystretch");if(w==null)u=1;else if(u=parseFloat(w),!u||u<0)throw new M("Invalid \\arraystretch: "+w)}r.gullet.beginGroup();var B=[],C=[B],D=[],F=[],L=c!=null?[]:void 0;function O(){c&&r.gullet.macros.set("\\@eqnsw","1",!0)}d(O,"beginRow");function G(){L&&(r.gullet.macros.get("\\df@tag")?(L.push(r.subparse([new A0("\\df@tag")])),r.gullet.macros.set("\\df@tag",void 0,!0)):L.push(!!c&&r.gullet.macros.get("\\@eqnsw")==="1"))}for(d(G,"endRow"),O(),F.push(Gt(r));;){var P=r.parseExpression(!1,v?"\\end":"\\\\");r.gullet.endGroup(),r.gullet.beginGroup(),P={type:"ordgroup",mode:r.mode,body:P},t&&(P={type:"styling",mode:r.mode,style:t,body:[P]}),B.push(P);var $=r.fetch().text;if($==="&"){if(x&&B.length===x){if(v||h)throw new M("Too many tab characters: &",r.nextToken);r.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}r.consume()}else if($==="\\end"){G(),B.length===1&&P.type==="styling"&&P.body[0].body.length===0&&(C.length>1||!b)&&C.pop(),F.length<C.length+1&&F.push([]);break}else if($==="\\\\"){r.consume();var U=void 0;r.gullet.future().text!==" "&&(U=r.parseSizeGroup(!0)),D.push(U?U.value:null),G(),F.push(Gt(r)),B=[],C.push(B),O()}else throw new M("Expected & or \\\\ or \\cr or \\end",r.nextToken)}return r.gullet.endGroup(),r.gullet.endGroup(),{type:"array",mode:r.mode,addJot:i,arraystretch:u,body:C,cols:l,rowGaps:D,hskipBeforeAndAfter:a,hLinesBeforeRow:F,colSeparationType:h,tags:L,leqno:k}}d(I0,"parseArray");function pt(r){return r.slice(0,1)==="d"?"display":"text"}d(pt,"dCellStyle");var M0=d(function(e,t){var a,i,l=e.body.length,u=e.hLinesBeforeRow,h=0,c=new Array(l),v=[],b=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),x=1/t.fontMetrics().ptPerEm,k=5*x;if(e.colSeparationType&&e.colSeparationType==="small"){var w=t.havingStyle(R.SCRIPT).sizeMultiplier;k=.2778*(w/t.sizeMultiplier)}var B=e.colSeparationType==="CD"?Q({number:3,unit:"ex"},t):12*x,C=3*x,D=e.arraystretch*B,F=.7*D,L=.3*D,O=0;function G(Pe){for(var Ge=0;Ge<Pe.length;++Ge)Ge>0&&(O+=.25),v.push({pos:O,isDashed:Pe[Ge]})}for(d(G,"setHLinePos"),G(u[0]),a=0;a<e.body.length;++a){var P=e.body[a],$=F,U=L;h<P.length&&(h=P.length);var Z=new Array(P.length);for(i=0;i<P.length;++i){var Y=V(P[i],t);U<Y.depth&&(U=Y.depth),$<Y.height&&($=Y.height),Z[i]=Y}var z0=e.rowGaps[a],i0=0;z0&&(i0=Q(z0,t),i0>0&&(i0+=L,U<i0&&(U=i0),i0=0)),e.addJot&&(U+=C),Z.height=$,Z.depth=U,O+=$,Z.pos=O,O+=U+i0,c[a]=Z,G(u[a+1])}var t0=O/2+t.fontMetrics().axisHeight,W0=e.cols||[],s0=[],g0,L0,_0=[];if(e.tags&&e.tags.some(Pe=>Pe))for(a=0;a<l;++a){var ee=c[a],ft=ee.pos-t0,O0=e.tags[a],H0=void 0;O0===!0?H0=y.makeSpan(["eqn-num"],[],t):O0===!1?H0=y.makeSpan([],[],t):H0=y.makeSpan([],r0(O0,t,!0),t),H0.depth=ee.depth,H0.height=ee.height,_0.push({type:"elem",elem:H0,shift:ft})}for(i=0,L0=0;i<h||L0<W0.length;++i,++L0){for(var c0=W0[L0]||{},Me=!0;c0.type==="separator";){if(Me||(g0=y.makeSpan(["arraycolsep"],[]),g0.style.width=z(t.fontMetrics().doubleRuleSep),s0.push(g0)),c0.separator==="|"||c0.separator===":"){var vt=c0.separator==="|"?"solid":"dashed",te=y.makeSpan(["vertical-separator"],[],t);te.style.height=z(O),te.style.borderRightWidth=z(b),te.style.borderRightStyle=vt,te.style.margin="0 "+z(-b/2);var hr=O-t0;hr&&(te.style.verticalAlign=z(-hr)),s0.push(te)}else throw new M("Invalid separator type: "+c0.separator);L0++,c0=W0[L0]||{},Me=!1}if(!(i>=h)){var re=void 0;(i>0||e.hskipBeforeAndAfter)&&(re=N.deflt(c0.pregap,k),re!==0&&(g0=y.makeSpan(["arraycolsep"],[]),g0.style.width=z(re),s0.push(g0)));var ae=[];for(a=0;a<l;++a){var Oe=c[a],He=Oe[i];if(He){var G1=Oe.pos-t0;He.depth=Oe.depth,He.height=Oe.height,ae.push({type:"elem",elem:He,shift:G1})}}ae=y.makeVList({positionType:"individualShift",children:ae},t),ae=y.makeSpan(["col-align-"+(c0.align||"c")],[ae]),s0.push(ae),(i<h-1||e.hskipBeforeAndAfter)&&(re=N.deflt(c0.postgap,k),re!==0&&(g0=y.makeSpan(["arraycolsep"],[]),g0.style.width=z(re),s0.push(g0)))}}if(c=y.makeSpan(["mtable"],s0),v.length>0){for(var V1=y.makeLineSpan("hline",t,b),U1=y.makeLineSpan("hdashline",t,b),gt=[{type:"elem",elem:c,shift:0}];v.length>0;){var mr=v.pop(),cr=mr.pos-t0;mr.isDashed?gt.push({type:"elem",elem:U1,shift:cr}):gt.push({type:"elem",elem:V1,shift:cr})}c=y.makeVList({positionType:"individualShift",children:gt},t)}if(_0.length===0)return y.makeSpan(["mord"],[c],t);var bt=y.makeVList({positionType:"individualShift",children:_0},t);return bt=y.makeSpan(["tag"],[bt],t),y.makeFragment([c,bt])},"htmlBuilder"),p4={c:"center ",l:"left ",r:"right "},B0=d(function(e,t){for(var a=[],i=new A.MathNode("mtd",[],["mtr-glue"]),l=new A.MathNode("mtd",[],["mml-eqn-num"]),u=0;u<e.body.length;u++){for(var h=e.body[u],c=[],v=0;v<h.length;v++)c.push(new A.MathNode("mtd",[X(h[v],t)]));e.tags&&e.tags[u]&&(c.unshift(i),c.push(i),e.leqno?c.unshift(l):c.push(l)),a.push(new A.MathNode("mtr",c))}var b=new A.MathNode("mtable",a),x=e.arraystretch===.5?.1:.16+e.arraystretch-1+(e.addJot?.09:0);b.setAttribute("rowspacing",z(x));var k="",w="";if(e.cols&&e.cols.length>0){var B=e.cols,C="",D=!1,F=0,L=B.length;B[0].type==="separator"&&(k+="top ",F=1),B[B.length-1].type==="separator"&&(k+="bottom ",L-=1);for(var O=F;O<L;O++)B[O].type==="align"?(w+=p4[B[O].align],D&&(C+="none "),D=!0):B[O].type==="separator"&&D&&(C+=B[O].separator==="|"?"solid ":"dashed ",D=!1);b.setAttribute("columnalign",w.trim()),/[sd]/.test(C)&&b.setAttribute("columnlines",C.trim())}if(e.colSeparationType==="align"){for(var G=e.cols||[],P="",$=1;$<G.length;$++)P+=$%2?"0em ":"1em ";b.setAttribute("columnspacing",P.trim())}else e.colSeparationType==="alignat"||e.colSeparationType==="gather"?b.setAttribute("columnspacing","0em"):e.colSeparationType==="small"?b.setAttribute("columnspacing","0.2778em"):e.colSeparationType==="CD"?b.setAttribute("columnspacing","0.5em"):b.setAttribute("columnspacing","1em");var U="",Z=e.hLinesBeforeRow;k+=Z[0].length>0?"left ":"",k+=Z[Z.length-1].length>0?"right ":"";for(var Y=1;Y<Z.length-1;Y++)U+=Z[Y].length===0?"none ":Z[Y][0]?"dashed ":"solid ";return/[sd]/.test(U)&&b.setAttribute("rowlines",U.trim()),k!==""&&(b=new A.MathNode("menclose",[b]),b.setAttribute("notation",k.trim())),e.arraystretch&&e.arraystretch<1&&(b=new A.MathNode("mstyle",[b]),b.setAttribute("scriptlevel","1")),b},"mathmlBuilder"),M1=d(function(e,t){e.envName.indexOf("ed")===-1&&ct(e);var a=[],i=e.envName.indexOf("at")>-1?"alignat":"align",l=e.envName==="split",u=I0(e.parser,{cols:a,addJot:!0,autoTag:l?void 0:dt(e.envName),emptySingleRow:!0,colSeparationType:i,maxNumCols:l?2:void 0,leqno:e.parser.settings.leqno},"display"),h,c=0,v={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&t[0].type==="ordgroup"){for(var b="",x=0;x<t[0].body.length;x++){var k=H(t[0].body[x],"textord");b+=k.text}h=Number(b),c=h*2}var w=!c;u.body.forEach(function(F){for(var L=1;L<F.length;L+=2){var O=H(F[L],"styling"),G=H(O.body[0],"ordgroup");G.body.unshift(v)}if(w)c<F.length&&(c=F.length);else{var P=F.length/2;if(h<P)throw new M("Too many math in a row: "+("expected "+h+", but got "+P),F[0])}});for(var B=0;B<c;++B){var C="r",D=0;B%2===1?C="l":B>0&&w&&(D=1),a[B]={type:"align",align:C,pregap:D,postgap:0}}return u.colSeparationType=w?"align":"alignat",u},"alignedHandler");x0({type:"array",names:["array","darray"],props:{numArgs:1},handler(r,e){var t=Re(e[0]),a=t?[e[0]]:H(e[0],"ordgroup").body,i=a.map(function(u){var h=ht(u),c=h.text;if("lcr".indexOf(c)!==-1)return{type:"align",align:c};if(c==="|")return{type:"separator",separator:"|"};if(c===":")return{type:"separator",separator:":"};throw new M("Unknown column alignment: "+c,u)}),l={cols:i,hskipBeforeAndAfter:!0,maxNumCols:i.length};return I0(r.parser,l,pt(r.envName))},htmlBuilder:M0,mathmlBuilder:B0});x0({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(r){var e={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[r.envName.replace("*","")],t="c",a={hskipBeforeAndAfter:!1,cols:[{type:"align",align:t}]};if(r.envName.charAt(r.envName.length-1)==="*"){var i=r.parser;if(i.consumeSpaces(),i.fetch().text==="["){if(i.consume(),i.consumeSpaces(),t=i.fetch().text,"lcr".indexOf(t)===-1)throw new M("Expected l or c or r",i.nextToken);i.consume(),i.consumeSpaces(),i.expect("]"),i.consume(),a.cols=[{type:"align",align:t}]}}var l=I0(r.parser,a,pt(r.envName)),u=Math.max(0,...l.body.map(h=>h.length));return l.cols=new Array(u).fill({type:"align",align:t}),e?{type:"leftright",mode:r.mode,body:[l],left:e[0],right:e[1],rightColor:void 0}:l},htmlBuilder:M0,mathmlBuilder:B0});x0({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(r){var e={arraystretch:.5},t=I0(r.parser,e,"script");return t.colSeparationType="small",t},htmlBuilder:M0,mathmlBuilder:B0});x0({type:"array",names:["subarray"],props:{numArgs:1},handler(r,e){var t=Re(e[0]),a=t?[e[0]]:H(e[0],"ordgroup").body,i=a.map(function(u){var h=ht(u),c=h.text;if("lc".indexOf(c)!==-1)return{type:"align",align:c};throw new M("Unknown column alignment: "+c,u)});if(i.length>1)throw new M("{subarray} can contain only one column");var l={cols:i,hskipBeforeAndAfter:!1,arraystretch:.5};if(l=I0(r.parser,l,"script"),l.body.length>0&&l.body[0].length>1)throw new M("{subarray} can contain only one column");return l},htmlBuilder:M0,mathmlBuilder:B0});x0({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(r){var e={arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},t=I0(r.parser,e,pt(r.envName));return{type:"leftright",mode:r.mode,body:[t],left:r.envName.indexOf("r")>-1?".":"\\{",right:r.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:M0,mathmlBuilder:B0});x0({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:M1,htmlBuilder:M0,mathmlBuilder:B0});x0({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(r){N.contains(["gather","gather*"],r.envName)&&ct(r);var e={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:dt(r.envName),emptySingleRow:!0,leqno:r.parser.settings.leqno};return I0(r.parser,e,"display")},htmlBuilder:M0,mathmlBuilder:B0});x0({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:M1,htmlBuilder:M0,mathmlBuilder:B0});x0({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(r){ct(r);var e={autoTag:dt(r.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:r.parser.settings.leqno};return I0(r.parser,e,"display")},htmlBuilder:M0,mathmlBuilder:B0});x0({type:"array",names:["CD"],props:{numArgs:0},handler(r){return ct(r),h1(r.parser)},htmlBuilder:M0,mathmlBuilder:B0});m("\\nonumber","\\gdef\\@eqnsw{0}");m("\\notag","\\nonumber");T({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(r,e){throw new M(r.funcName+" valid only within array environment")}});var Ar=S1;T({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(r,e){var{parser:t,funcName:a}=r,i=e[0];if(i.type!=="ordgroup")throw new M("Invalid environment name",i);for(var l="",u=0;u<i.body.length;++u)l+=H(i.body[u],"textord").text;if(a==="\\begin"){if(!Ar.hasOwnProperty(l))throw new M("No such environment: "+l,i);var h=Ar[l],{args:c,optArgs:v}=t.parseArguments("\\begin{"+l+"}",h),b={mode:t.mode,envName:l,parser:t},x=h.handler(b,c,v);t.expect("\\end",!1);var k=t.nextToken,w=H(t.parseFunction(),"environment");if(w.name!==l)throw new M("Mismatch: \\begin{"+l+"} matched by \\end{"+w.name+"}",k);return x}return{type:"environment",mode:t.mode,name:l,nameGroup:i}}});var B1=d((r,e)=>{var t=r.font,a=e.withFont(t);return V(r.body,a)},"htmlBuilder$5"),z1=d((r,e)=>{var t=r.font,a=e.withFont(t);return X(r.body,a)},"mathmlBuilder$4"),Mr={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};T({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathsfit","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:d((r,e)=>{var{parser:t,funcName:a}=r,i=nt(e[0]),l=a;return l in Mr&&(l=Mr[l]),{type:"font",mode:t.mode,font:l.slice(1),body:i}},"handler"),htmlBuilder:B1,mathmlBuilder:z1});T({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:d((r,e)=>{var{parser:t}=r,a=e[0],i=N.isCharacterBox(a);return{type:"mclass",mode:t.mode,mclass:mt(a),body:[{type:"font",mode:t.mode,font:"boldsymbol",body:a}],isCharacterBox:i}},"handler")});T({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:d((r,e)=>{var{parser:t,funcName:a,breakOnTokenText:i}=r,{mode:l}=t,u=t.parseExpression(!0,i),h="math"+a.slice(1);return{type:"font",mode:l,font:h,body:{type:"ordgroup",mode:t.mode,body:u}}},"handler"),htmlBuilder:B1,mathmlBuilder:z1});var C1=d((r,e)=>{var t=e;return r==="display"?t=t.id>=R.SCRIPT.id?t.text():R.DISPLAY:r==="text"&&t.size===R.DISPLAY.size?t=R.TEXT:r==="script"?t=R.SCRIPT:r==="scriptscript"&&(t=R.SCRIPTSCRIPT),t},"adjustStyle"),ar=d((r,e)=>{var t=C1(r.size,e.style),a=t.fracNum(),i=t.fracDen(),l;l=e.havingStyle(a);var u=V(r.numer,l,e);if(r.continued){var h=8.5/e.fontMetrics().ptPerEm,c=3.5/e.fontMetrics().ptPerEm;u.height=u.height<h?h:u.height,u.depth=u.depth<c?c:u.depth}l=e.havingStyle(i);var v=V(r.denom,l,e),b,x,k;r.hasBarLine?(r.barSize?(x=Q(r.barSize,e),b=y.makeLineSpan("frac-line",e,x)):b=y.makeLineSpan("frac-line",e),x=b.height,k=b.height):(b=null,x=0,k=e.fontMetrics().defaultRuleThickness);var w,B,C;t.size===R.DISPLAY.size||r.size==="display"?(w=e.fontMetrics().num1,x>0?B=3*k:B=7*k,C=e.fontMetrics().denom1):(x>0?(w=e.fontMetrics().num2,B=k):(w=e.fontMetrics().num3,B=3*k),C=e.fontMetrics().denom2);var D;if(b){var L=e.fontMetrics().axisHeight;w-u.depth-(L+.5*x)<B&&(w+=B-(w-u.depth-(L+.5*x))),L-.5*x-(v.height-C)<B&&(C+=B-(L-.5*x-(v.height-C)));var O=-(L-.5*x);D=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:v,shift:C},{type:"elem",elem:b,shift:O},{type:"elem",elem:u,shift:-w}]},e)}else{var F=w-u.depth-(v.height-C);F<B&&(w+=.5*(B-F),C+=.5*(B-F)),D=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:v,shift:C},{type:"elem",elem:u,shift:-w}]},e)}l=e.havingStyle(t),D.height*=l.sizeMultiplier/e.sizeMultiplier,D.depth*=l.sizeMultiplier/e.sizeMultiplier;var G;t.size===R.DISPLAY.size?G=e.fontMetrics().delim1:t.size===R.SCRIPTSCRIPT.size?G=e.havingStyle(R.SCRIPT).fontMetrics().delim2:G=e.fontMetrics().delim2;var P,$;return r.leftDelim==null?P=Fe(e,["mopen"]):P=D0.customSizedDelim(r.leftDelim,G,!0,e.havingStyle(t),r.mode,["mopen"]),r.continued?$=y.makeSpan([]):r.rightDelim==null?$=Fe(e,["mclose"]):$=D0.customSizedDelim(r.rightDelim,G,!0,e.havingStyle(t),r.mode,["mclose"]),y.makeSpan(["mord"].concat(l.sizingClasses(e)),[P,y.makeSpan(["mfrac"],[D]),$],e)},"htmlBuilder$4"),ir=d((r,e)=>{var t=new A.MathNode("mfrac",[X(r.numer,e),X(r.denom,e)]);if(!r.hasBarLine)t.setAttribute("linethickness","0px");else if(r.barSize){var a=Q(r.barSize,e);t.setAttribute("linethickness",z(a))}var i=C1(r.size,e.style);if(i.size!==e.style.size){t=new A.MathNode("mstyle",[t]);var l=i.size===R.DISPLAY.size?"true":"false";t.setAttribute("displaystyle",l),t.setAttribute("scriptlevel","0")}if(r.leftDelim!=null||r.rightDelim!=null){var u=[];if(r.leftDelim!=null){var h=new A.MathNode("mo",[new A.TextNode(r.leftDelim.replace("\\",""))]);h.setAttribute("fence","true"),u.push(h)}if(u.push(t),r.rightDelim!=null){var c=new A.MathNode("mo",[new A.TextNode(r.rightDelim.replace("\\",""))]);c.setAttribute("fence","true"),u.push(c)}return Zt(u)}return t},"mathmlBuilder$3");T({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:d((r,e)=>{var{parser:t,funcName:a}=r,i=e[0],l=e[1],u,h=null,c=null,v="auto";switch(a){case"\\dfrac":case"\\frac":case"\\tfrac":u=!0;break;case"\\\\atopfrac":u=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":u=!1,h="(",c=")";break;case"\\\\bracefrac":u=!1,h="\\{",c="\\}";break;case"\\\\brackfrac":u=!1,h="[",c="]";break;default:throw new Error("Unrecognized genfrac command")}switch(a){case"\\dfrac":case"\\dbinom":v="display";break;case"\\tfrac":case"\\tbinom":v="text";break}return{type:"genfrac",mode:t.mode,continued:!1,numer:i,denom:l,hasBarLine:u,leftDelim:h,rightDelim:c,size:v,barSize:null}},"handler"),htmlBuilder:ar,mathmlBuilder:ir});T({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:d((r,e)=>{var{parser:t,funcName:a}=r,i=e[0],l=e[1];return{type:"genfrac",mode:t.mode,continued:!0,numer:i,denom:l,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}},"handler")});T({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(r){var{parser:e,funcName:t,token:a}=r,i;switch(t){case"\\over":i="\\frac";break;case"\\choose":i="\\binom";break;case"\\atop":i="\\\\atopfrac";break;case"\\brace":i="\\\\bracefrac";break;case"\\brack":i="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:e.mode,replaceWith:i,token:a}}});var Br=["display","text","script","scriptscript"],zr=d(function(e){var t=null;return e.length>0&&(t=e,t=t==="."?null:t),t},"delimFromValue");T({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(r,e){var{parser:t}=r,a=e[4],i=e[5],l=nt(e[0]),u=l.type==="atom"&&l.family==="open"?zr(l.text):null,h=nt(e[1]),c=h.type==="atom"&&h.family==="close"?zr(h.text):null,v=H(e[2],"size"),b,x=null;v.isBlank?b=!0:(x=v.value,b=x.number>0);var k="auto",w=e[3];if(w.type==="ordgroup"){if(w.body.length>0){var B=H(w.body[0],"textord");k=Br[Number(B.text)]}}else w=H(w,"textord"),k=Br[Number(w.text)];return{type:"genfrac",mode:t.mode,numer:a,denom:i,continued:!1,hasBarLine:b,barSize:x,leftDelim:u,rightDelim:c,size:k}},htmlBuilder:ar,mathmlBuilder:ir});T({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(r,e){var{parser:t,funcName:a,token:i}=r;return{type:"infix",mode:t.mode,replaceWith:"\\\\abovefrac",size:H(e[0],"size").value,token:i}}});T({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:d((r,e)=>{var{parser:t,funcName:a}=r,i=e[0],l=J1(H(e[1],"infix").size),u=e[2],h=l.number>0;return{type:"genfrac",mode:t.mode,numer:i,denom:u,continued:!1,hasBarLine:h,barSize:l,leftDelim:null,rightDelim:null,size:"auto"}},"handler"),htmlBuilder:ar,mathmlBuilder:ir});var T1=d((r,e)=>{var t=e.style,a,i;r.type==="supsub"?(a=r.sup?V(r.sup,e.havingStyle(t.sup()),e):V(r.sub,e.havingStyle(t.sub()),e),i=H(r.base,"horizBrace")):i=H(r,"horizBrace");var l=V(i.base,e.havingBaseStyle(R.DISPLAY)),u=q0.svgSpan(i,e),h;if(i.isOver?(h=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"kern",size:.1},{type:"elem",elem:u}]},e),h.children[0].children[0].children[1].classes.push("svg-align")):(h=y.makeVList({positionType:"bottom",positionData:l.depth+.1+u.height,children:[{type:"elem",elem:u},{type:"kern",size:.1},{type:"elem",elem:l}]},e),h.children[0].children[0].children[0].classes.push("svg-align")),a){var c=y.makeSpan(["mord",i.isOver?"mover":"munder"],[h],e);i.isOver?h=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:c},{type:"kern",size:.2},{type:"elem",elem:a}]},e):h=y.makeVList({positionType:"bottom",positionData:c.depth+.2+a.height+a.depth,children:[{type:"elem",elem:a},{type:"kern",size:.2},{type:"elem",elem:c}]},e)}return y.makeSpan(["mord",i.isOver?"mover":"munder"],[h],e)},"htmlBuilder$3"),f4=d((r,e)=>{var t=q0.mathMLnode(r.label);return new A.MathNode(r.isOver?"mover":"munder",[X(r.base,e),t])},"mathmlBuilder$2");T({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(r,e){var{parser:t,funcName:a}=r;return{type:"horizBrace",mode:t.mode,label:a,isOver:/^\\over/.test(a),base:e[0]}},htmlBuilder:T1,mathmlBuilder:f4});T({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:d((r,e)=>{var{parser:t}=r,a=e[1],i=H(e[0],"url").url;return t.settings.isTrusted({command:"\\href",url:i})?{type:"href",mode:t.mode,href:i,body:_(a)}:t.formatUnsupportedCmd("\\href")},"handler"),htmlBuilder:d((r,e)=>{var t=r0(r.body,e,!1);return y.makeAnchor(r.href,[],t,e)},"htmlBuilder"),mathmlBuilder:d((r,e)=>{var t=Y0(r.body,e);return t instanceof h0||(t=new h0("mrow",[t])),t.setAttribute("href",r.href),t},"mathmlBuilder")});T({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:d((r,e)=>{var{parser:t}=r,a=H(e[0],"url").url;if(!t.settings.isTrusted({command:"\\url",url:a}))return t.formatUnsupportedCmd("\\url");for(var i=[],l=0;l<a.length;l++){var u=a[l];u==="~"&&(u="\\textasciitilde"),i.push({type:"textord",mode:"text",text:u})}var h={type:"text",mode:t.mode,font:"\\texttt",body:i};return{type:"href",mode:t.mode,href:a,body:_(h)}},"handler")});T({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(r,e){var{parser:t}=r;return{type:"hbox",mode:t.mode,body:_(e[0])}},htmlBuilder(r,e){var t=r0(r.body,e,!1);return y.makeFragment(t)},mathmlBuilder(r,e){return new A.MathNode("mrow",o0(r.body,e))}});T({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:d((r,e)=>{var{parser:t,funcName:a,token:i}=r,l=H(e[0],"raw").string,u=e[1];t.settings.strict&&t.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");var h,c={};switch(a){case"\\htmlClass":c.class=l,h={command:"\\htmlClass",class:l};break;case"\\htmlId":c.id=l,h={command:"\\htmlId",id:l};break;case"\\htmlStyle":c.style=l,h={command:"\\htmlStyle",style:l};break;case"\\htmlData":{for(var v=l.split(","),b=0;b<v.length;b++){var x=v[b].split("=");if(x.length!==2)throw new M("Error parsing key-value for \\htmlData");c["data-"+x[0].trim()]=x[1].trim()}h={command:"\\htmlData",attributes:c};break}default:throw new Error("Unrecognized html command")}return t.settings.isTrusted(h)?{type:"html",mode:t.mode,attributes:c,body:_(u)}:t.formatUnsupportedCmd(a)},"handler"),htmlBuilder:d((r,e)=>{var t=r0(r.body,e,!1),a=["enclosing"];r.attributes.class&&a.push(...r.attributes.class.trim().split(/\s+/));var i=y.makeSpan(a,t,e);for(var l in r.attributes)l!=="class"&&r.attributes.hasOwnProperty(l)&&i.setAttribute(l,r.attributes[l]);return i},"htmlBuilder"),mathmlBuilder:d((r,e)=>Y0(r.body,e),"mathmlBuilder")});T({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:d((r,e)=>{var{parser:t}=r;return{type:"htmlmathml",mode:t.mode,html:_(e[0]),mathml:_(e[1])}},"handler"),htmlBuilder:d((r,e)=>{var t=r0(r.html,e,!1);return y.makeFragment(t)},"htmlBuilder"),mathmlBuilder:d((r,e)=>Y0(r.mathml,e),"mathmlBuilder")});var zt=d(function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};var t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new M("Invalid size: '"+e+"' in \\includegraphics");var a={number:+(t[1]+t[2]),unit:t[3]};if(!jr(a))throw new M("Invalid unit: '"+a.unit+"' in \\includegraphics.");return a},"sizeData");T({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:d((r,e,t)=>{var{parser:a}=r,i={number:0,unit:"em"},l={number:.9,unit:"em"},u={number:0,unit:"em"},h="";if(t[0])for(var c=H(t[0],"raw").string,v=c.split(","),b=0;b<v.length;b++){var x=v[b].split("=");if(x.length===2){var k=x[1].trim();switch(x[0].trim()){case"alt":h=k;break;case"width":i=zt(k);break;case"height":l=zt(k);break;case"totalheight":u=zt(k);break;default:throw new M("Invalid key: '"+x[0]+"' in \\includegraphics.")}}}var w=H(e[0],"url").url;return h===""&&(h=w,h=h.replace(/^.*[\\/]/,""),h=h.substring(0,h.lastIndexOf("."))),a.settings.isTrusted({command:"\\includegraphics",url:w})?{type:"includegraphics",mode:a.mode,alt:h,width:i,height:l,totalheight:u,src:w}:a.formatUnsupportedCmd("\\includegraphics")},"handler"),htmlBuilder:d((r,e)=>{var t=Q(r.height,e),a=0;r.totalheight.number>0&&(a=Q(r.totalheight,e)-t);var i=0;r.width.number>0&&(i=Q(r.width,e));var l={height:z(t+a)};i>0&&(l.width=z(i)),a>0&&(l.verticalAlign=z(-a));var u=new ba(r.src,r.alt,l);return u.height=t,u.depth=a,u},"htmlBuilder"),mathmlBuilder:d((r,e)=>{var t=new A.MathNode("mglyph",[]);t.setAttribute("alt",r.alt);var a=Q(r.height,e),i=0;if(r.totalheight.number>0&&(i=Q(r.totalheight,e)-a,t.setAttribute("valign",z(-i))),t.setAttribute("height",z(a+i)),r.width.number>0){var l=Q(r.width,e);t.setAttribute("width",z(l))}return t.setAttribute("src",r.src),t},"mathmlBuilder")});T({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(r,e){var{parser:t,funcName:a}=r,i=H(e[0],"size");if(t.settings.strict){var l=a[1]==="m",u=i.value.unit==="mu";l?(u||t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" supports only mu units, "+("not "+i.value.unit+" units")),t.mode!=="math"&&t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" works only in math mode")):u&&t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" doesn't support mu units")}return{type:"kern",mode:t.mode,dimension:i.value}},htmlBuilder(r,e){return y.makeGlue(r.dimension,e)},mathmlBuilder(r,e){var t=Q(r.dimension,e);return new A.SpaceNode(t)}});T({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:d((r,e)=>{var{parser:t,funcName:a}=r,i=e[0];return{type:"lap",mode:t.mode,alignment:a.slice(5),body:i}},"handler"),htmlBuilder:d((r,e)=>{var t;r.alignment==="clap"?(t=y.makeSpan([],[V(r.body,e)]),t=y.makeSpan(["inner"],[t],e)):t=y.makeSpan(["inner"],[V(r.body,e)]);var a=y.makeSpan(["fix"],[]),i=y.makeSpan([r.alignment],[t,a],e),l=y.makeSpan(["strut"]);return l.style.height=z(i.height+i.depth),i.depth&&(l.style.verticalAlign=z(-i.depth)),i.children.unshift(l),i=y.makeSpan(["thinbox"],[i],e),y.makeSpan(["mord","vbox"],[i],e)},"htmlBuilder"),mathmlBuilder:d((r,e)=>{var t=new A.MathNode("mpadded",[X(r.body,e)]);if(r.alignment!=="rlap"){var a=r.alignment==="llap"?"-1":"-0.5";t.setAttribute("lspace",a+"width")}return t.setAttribute("width","0px"),t},"mathmlBuilder")});T({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(r,e){var{funcName:t,parser:a}=r,i=a.mode;a.switchMode("math");var l=t==="\\("?"\\)":"$",u=a.parseExpression(!1,l);return a.expect(l),a.switchMode(i),{type:"styling",mode:a.mode,style:"text",body:u}}});T({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(r,e){throw new M("Mismatched "+r.funcName)}});var Cr=d((r,e)=>{switch(e.style.size){case R.DISPLAY.size:return r.display;case R.TEXT.size:return r.text;case R.SCRIPT.size:return r.script;case R.SCRIPTSCRIPT.size:return r.scriptscript;default:return r.text}},"chooseMathStyle");T({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:d((r,e)=>{var{parser:t}=r;return{type:"mathchoice",mode:t.mode,display:_(e[0]),text:_(e[1]),script:_(e[2]),scriptscript:_(e[3])}},"handler"),htmlBuilder:d((r,e)=>{var t=Cr(r,e),a=r0(t,e,!1);return y.makeFragment(a)},"htmlBuilder"),mathmlBuilder:d((r,e)=>{var t=Cr(r,e);return Y0(t,e)},"mathmlBuilder")});var E1=d((r,e,t,a,i,l,u)=>{r=y.makeSpan([],[r]);var h=t&&N.isCharacterBox(t),c,v;if(e){var b=V(e,a.havingStyle(i.sup()),a);v={elem:b,kern:Math.max(a.fontMetrics().bigOpSpacing1,a.fontMetrics().bigOpSpacing3-b.depth)}}if(t){var x=V(t,a.havingStyle(i.sub()),a);c={elem:x,kern:Math.max(a.fontMetrics().bigOpSpacing2,a.fontMetrics().bigOpSpacing4-x.height)}}var k;if(v&&c){var w=a.fontMetrics().bigOpSpacing5+c.elem.height+c.elem.depth+c.kern+r.depth+u;k=y.makeVList({positionType:"bottom",positionData:w,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:c.elem,marginLeft:z(-l)},{type:"kern",size:c.kern},{type:"elem",elem:r},{type:"kern",size:v.kern},{type:"elem",elem:v.elem,marginLeft:z(l)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else if(c){var B=r.height-u;k=y.makeVList({positionType:"top",positionData:B,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:c.elem,marginLeft:z(-l)},{type:"kern",size:c.kern},{type:"elem",elem:r}]},a)}else if(v){var C=r.depth+u;k=y.makeVList({positionType:"bottom",positionData:C,children:[{type:"elem",elem:r},{type:"kern",size:v.kern},{type:"elem",elem:v.elem,marginLeft:z(l)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else return r;var D=[k];if(c&&l!==0&&!h){var F=y.makeSpan(["mspace"],[],a);F.style.marginRight=z(l),D.unshift(F)}return y.makeSpan(["mop","op-limits"],D,a)},"assembleSupSub"),D1=["\\smallint"],Ae=d((r,e)=>{var t,a,i=!1,l;r.type==="supsub"?(t=r.sup,a=r.sub,l=H(r.base,"op"),i=!0):l=H(r,"op");var u=e.style,h=!1;u.size===R.DISPLAY.size&&l.symbol&&!N.contains(D1,l.name)&&(h=!0);var c;if(l.symbol){var v=h?"Size2-Regular":"Size1-Regular",b="";if((l.name==="\\oiint"||l.name==="\\oiiint")&&(b=l.name.slice(1),l.name=b==="oiint"?"\\iint":"\\iiint"),c=y.makeSymbol(l.name,v,"math",e,["mop","op-symbol",h?"large-op":"small-op"]),b.length>0){var x=c.italic,k=y.staticSvg(b+"Size"+(h?"2":"1"),e);c=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:c,shift:0},{type:"elem",elem:k,shift:h?.08:0}]},e),l.name="\\"+b,c.classes.unshift("mop"),c.italic=x}}else if(l.body){var w=r0(l.body,e,!0);w.length===1&&w[0]instanceof f0?(c=w[0],c.classes[0]="mop"):c=y.makeSpan(["mop"],w,e)}else{for(var B=[],C=1;C<l.name.length;C++)B.push(y.mathsym(l.name[C],l.mode,e));c=y.makeSpan(["mop"],B,e)}var D=0,F=0;return(c instanceof f0||l.name==="\\oiint"||l.name==="\\oiiint")&&!l.suppressBaseShift&&(D=(c.height-c.depth)/2-e.fontMetrics().axisHeight,F=c.italic),i?E1(c,t,a,e,u,F,D):(D&&(c.style.position="relative",c.style.top=z(D)),c)},"htmlBuilder$2"),Le=d((r,e)=>{var t;if(r.symbol)t=new h0("mo",[v0(r.name,r.mode)]),N.contains(D1,r.name)&&t.setAttribute("largeop","false");else if(r.body)t=new h0("mo",o0(r.body,e));else{t=new h0("mi",[new S0(r.name.slice(1))]);var a=new h0("mo",[v0("⁡","text")]);r.parentIsSupSub?t=new h0("mrow",[t,a]):t=jt([t,a])}return t},"mathmlBuilder$1"),v4={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};T({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:d((r,e)=>{var{parser:t,funcName:a}=r,i=a;return i.length===1&&(i=v4[i]),{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:i}},"handler"),htmlBuilder:Ae,mathmlBuilder:Le});T({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:d((r,e)=>{var{parser:t}=r,a=e[0];return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:_(a)}},"handler"),htmlBuilder:Ae,mathmlBuilder:Le});var g4={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};T({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(r){var{parser:e,funcName:t}=r;return{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:t}},htmlBuilder:Ae,mathmlBuilder:Le});T({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(r){var{parser:e,funcName:t}=r;return{type:"op",mode:e.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:t}},htmlBuilder:Ae,mathmlBuilder:Le});T({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(r){var{parser:e,funcName:t}=r,a=t;return a.length===1&&(a=g4[a]),{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:a}},htmlBuilder:Ae,mathmlBuilder:Le});var F1=d((r,e)=>{var t,a,i=!1,l;r.type==="supsub"?(t=r.sup,a=r.sub,l=H(r.base,"operatorname"),i=!0):l=H(r,"operatorname");var u;if(l.body.length>0){for(var h=l.body.map(x=>{var k=x.text;return typeof k=="string"?{type:"textord",mode:x.mode,text:k}:x}),c=r0(h,e.withFont("mathrm"),!0),v=0;v<c.length;v++){var b=c[v];b instanceof f0&&(b.text=b.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}u=y.makeSpan(["mop"],c,e)}else u=y.makeSpan(["mop"],[],e);return i?E1(u,t,a,e,e.style,0,0):u},"htmlBuilder$1"),b4=d((r,e)=>{for(var t=o0(r.body,e.withFont("mathrm")),a=!0,i=0;i<t.length;i++){var l=t[i];if(!(l instanceof A.SpaceNode))if(l instanceof A.MathNode)switch(l.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{var u=l.children[0];l.children.length===1&&u instanceof A.TextNode?u.text=u.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):a=!1;break}default:a=!1}else a=!1}if(a){var h=t.map(b=>b.toText()).join("");t=[new A.TextNode(h)]}var c=new A.MathNode("mi",t);c.setAttribute("mathvariant","normal");var v=new A.MathNode("mo",[v0("⁡","text")]);return r.parentIsSupSub?new A.MathNode("mrow",[c,v]):A.newDocumentFragment([c,v])},"mathmlBuilder");T({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:d((r,e)=>{var{parser:t,funcName:a}=r,i=e[0];return{type:"operatorname",mode:t.mode,body:_(i),alwaysHandleSupSub:a==="\\operatornamewithlimits",limits:!1,parentIsSupSub:!1}},"handler"),htmlBuilder:F1,mathmlBuilder:b4});m("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@");X0({type:"ordgroup",htmlBuilder(r,e){return r.semisimple?y.makeFragment(r0(r.body,e,!1)):y.makeSpan(["mord"],r0(r.body,e,!0),e)},mathmlBuilder(r,e){return Y0(r.body,e,!0)}});T({type:"overline",names:["\\overline"],props:{numArgs:1},handler(r,e){var{parser:t}=r,a=e[0];return{type:"overline",mode:t.mode,body:a}},htmlBuilder(r,e){var t=V(r.body,e.havingCrampedStyle()),a=y.makeLineSpan("overline-line",e),i=e.fontMetrics().defaultRuleThickness,l=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t},{type:"kern",size:3*i},{type:"elem",elem:a},{type:"kern",size:i}]},e);return y.makeSpan(["mord","overline"],[l],e)},mathmlBuilder(r,e){var t=new A.MathNode("mo",[new A.TextNode("‾")]);t.setAttribute("stretchy","true");var a=new A.MathNode("mover",[X(r.body,e),t]);return a.setAttribute("accent","true"),a}});T({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:d((r,e)=>{var{parser:t}=r,a=e[0];return{type:"phantom",mode:t.mode,body:_(a)}},"handler"),htmlBuilder:d((r,e)=>{var t=r0(r.body,e.withPhantom(),!1);return y.makeFragment(t)},"htmlBuilder"),mathmlBuilder:d((r,e)=>{var t=o0(r.body,e);return new A.MathNode("mphantom",t)},"mathmlBuilder")});T({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:d((r,e)=>{var{parser:t}=r,a=e[0];return{type:"hphantom",mode:t.mode,body:a}},"handler"),htmlBuilder:d((r,e)=>{var t=y.makeSpan([],[V(r.body,e.withPhantom())]);if(t.height=0,t.depth=0,t.children)for(var a=0;a<t.children.length;a++)t.children[a].height=0,t.children[a].depth=0;return t=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t}]},e),y.makeSpan(["mord"],[t],e)},"htmlBuilder"),mathmlBuilder:d((r,e)=>{var t=o0(_(r.body),e),a=new A.MathNode("mphantom",t),i=new A.MathNode("mpadded",[a]);return i.setAttribute("height","0px"),i.setAttribute("depth","0px"),i},"mathmlBuilder")});T({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:d((r,e)=>{var{parser:t}=r,a=e[0];return{type:"vphantom",mode:t.mode,body:a}},"handler"),htmlBuilder:d((r,e)=>{var t=y.makeSpan(["inner"],[V(r.body,e.withPhantom())]),a=y.makeSpan(["fix"],[]);return y.makeSpan(["mord","rlap"],[t,a],e)},"htmlBuilder"),mathmlBuilder:d((r,e)=>{var t=o0(_(r.body),e),a=new A.MathNode("mphantom",t),i=new A.MathNode("mpadded",[a]);return i.setAttribute("width","0px"),i},"mathmlBuilder")});T({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(r,e){var{parser:t}=r,a=H(e[0],"size").value,i=e[1];return{type:"raisebox",mode:t.mode,dy:a,body:i}},htmlBuilder(r,e){var t=V(r.body,e),a=Q(r.dy,e);return y.makeVList({positionType:"shift",positionData:-a,children:[{type:"elem",elem:t}]},e)},mathmlBuilder(r,e){var t=new A.MathNode("mpadded",[X(r.body,e)]),a=r.dy.number+r.dy.unit;return t.setAttribute("voffset",a),t}});T({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0,allowedInArgument:!0},handler(r){var{parser:e}=r;return{type:"internal",mode:e.mode}}});T({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["size","size","size"]},handler(r,e,t){var{parser:a}=r,i=t[0],l=H(e[0],"size"),u=H(e[1],"size");return{type:"rule",mode:a.mode,shift:i&&H(i,"size").value,width:l.value,height:u.value}},htmlBuilder(r,e){var t=y.makeSpan(["mord","rule"],[],e),a=Q(r.width,e),i=Q(r.height,e),l=r.shift?Q(r.shift,e):0;return t.style.borderRightWidth=z(a),t.style.borderTopWidth=z(i),t.style.bottom=z(l),t.width=a,t.height=i+l,t.depth=-l,t.maxFontSize=i*1.125*e.sizeMultiplier,t},mathmlBuilder(r,e){var t=Q(r.width,e),a=Q(r.height,e),i=r.shift?Q(r.shift,e):0,l=e.color&&e.getColor()||"black",u=new A.MathNode("mspace");u.setAttribute("mathbackground",l),u.setAttribute("width",z(t)),u.setAttribute("height",z(a));var h=new A.MathNode("mpadded",[u]);return i>=0?h.setAttribute("height",z(i)):(h.setAttribute("height",z(i)),h.setAttribute("depth",z(-i))),h.setAttribute("voffset",z(i)),h}});function nr(r,e,t){for(var a=r0(r,e,!1),i=e.sizeMultiplier/t.sizeMultiplier,l=0;l<a.length;l++){var u=a[l].classes.indexOf("sizing");u<0?Array.prototype.push.apply(a[l].classes,e.sizingClasses(t)):a[l].classes[u+1]==="reset-size"+e.size&&(a[l].classes[u+1]="reset-size"+t.size),a[l].height*=i,a[l].depth*=i}return y.makeFragment(a)}d(nr,"sizingGroup");var Tr=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"],y4=d((r,e)=>{var t=e.havingSize(r.size);return nr(r.body,t,e)},"htmlBuilder");T({type:"sizing",names:Tr,props:{numArgs:0,allowedInText:!0},handler:d((r,e)=>{var{breakOnTokenText:t,funcName:a,parser:i}=r,l=i.parseExpression(!1,t);return{type:"sizing",mode:i.mode,size:Tr.indexOf(a)+1,body:l}},"handler"),htmlBuilder:y4,mathmlBuilder:d((r,e)=>{var t=e.havingSize(r.size),a=o0(r.body,t),i=new A.MathNode("mstyle",a);return i.setAttribute("mathsize",z(t.sizeMultiplier)),i},"mathmlBuilder")});T({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:d((r,e,t)=>{var{parser:a}=r,i=!1,l=!1,u=t[0]&&H(t[0],"ordgroup");if(u)for(var h="",c=0;c<u.body.length;++c){var v=u.body[c];if(h=v.text,h==="t")i=!0;else if(h==="b")l=!0;else{i=!1,l=!1;break}}else i=!0,l=!0;var b=e[0];return{type:"smash",mode:a.mode,body:b,smashHeight:i,smashDepth:l}},"handler"),htmlBuilder:d((r,e)=>{var t=y.makeSpan([],[V(r.body,e)]);if(!r.smashHeight&&!r.smashDepth)return t;if(r.smashHeight&&(t.height=0,t.children))for(var a=0;a<t.children.length;a++)t.children[a].height=0;if(r.smashDepth&&(t.depth=0,t.children))for(var i=0;i<t.children.length;i++)t.children[i].depth=0;var l=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t}]},e);return y.makeSpan(["mord"],[l],e)},"htmlBuilder"),mathmlBuilder:d((r,e)=>{var t=new A.MathNode("mpadded",[X(r.body,e)]);return r.smashHeight&&t.setAttribute("height","0px"),r.smashDepth&&t.setAttribute("depth","0px"),t},"mathmlBuilder")});T({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(r,e,t){var{parser:a}=r,i=t[0],l=e[0];return{type:"sqrt",mode:a.mode,body:l,index:i}},htmlBuilder(r,e){var t=V(r.body,e.havingCrampedStyle());t.height===0&&(t.height=e.fontMetrics().xHeight),t=y.wrapFragment(t,e);var a=e.fontMetrics(),i=a.defaultRuleThickness,l=i;e.style.id<R.TEXT.id&&(l=e.fontMetrics().xHeight);var u=i+l/4,h=t.height+t.depth+u+i,{span:c,ruleWidth:v,advanceWidth:b}=D0.sqrtImage(h,e),x=c.height-v;x>t.height+t.depth+u&&(u=(u+x-t.height-t.depth)/2);var k=c.height-t.height-u-v;t.style.paddingLeft=z(b);var w=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t,wrapperClasses:["svg-align"]},{type:"kern",size:-(t.height+k)},{type:"elem",elem:c},{type:"kern",size:v}]},e);if(r.index){var B=e.havingStyle(R.SCRIPTSCRIPT),C=V(r.index,B,e),D=.6*(w.height-w.depth),F=y.makeVList({positionType:"shift",positionData:-D,children:[{type:"elem",elem:C}]},e),L=y.makeSpan(["root"],[F]);return y.makeSpan(["mord","sqrt"],[L,w],e)}else return y.makeSpan(["mord","sqrt"],[w],e)},mathmlBuilder(r,e){var{body:t,index:a}=r;return a?new A.MathNode("mroot",[X(t,e),X(a,e)]):new A.MathNode("msqrt",[X(t,e)])}});var Er={display:R.DISPLAY,text:R.TEXT,script:R.SCRIPT,scriptscript:R.SCRIPTSCRIPT};T({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r,e){var{breakOnTokenText:t,funcName:a,parser:i}=r,l=i.parseExpression(!0,t),u=a.slice(1,a.length-5);return{type:"styling",mode:i.mode,style:u,body:l}},htmlBuilder(r,e){var t=Er[r.style],a=e.havingStyle(t).withFont("");return nr(r.body,a,e)},mathmlBuilder(r,e){var t=Er[r.style],a=e.havingStyle(t),i=o0(r.body,a),l=new A.MathNode("mstyle",i),u={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]},h=u[r.style];return l.setAttribute("scriptlevel",h[0]),l.setAttribute("displaystyle",h[1]),l}});var x4=d(function(e,t){var a=e.base;if(a)if(a.type==="op"){var i=a.limits&&(t.style.size===R.DISPLAY.size||a.alwaysHandleSupSub);return i?Ae:null}else if(a.type==="operatorname"){var l=a.alwaysHandleSupSub&&(t.style.size===R.DISPLAY.size||a.limits);return l?F1:null}else{if(a.type==="accent")return N.isCharacterBox(a.base)?Jt:null;if(a.type==="horizBrace"){var u=!e.sub;return u===a.isOver?T1:null}else return null}else return null},"htmlBuilderDelegate");X0({type:"supsub",htmlBuilder(r,e){var t=x4(r,e);if(t)return t(r,e);var{base:a,sup:i,sub:l}=r,u=V(a,e),h,c,v=e.fontMetrics(),b=0,x=0,k=a&&N.isCharacterBox(a);if(i){var w=e.havingStyle(e.style.sup());h=V(i,w,e),k||(b=u.height-w.fontMetrics().supDrop*w.sizeMultiplier/e.sizeMultiplier)}if(l){var B=e.havingStyle(e.style.sub());c=V(l,B,e),k||(x=u.depth+B.fontMetrics().subDrop*B.sizeMultiplier/e.sizeMultiplier)}var C;e.style===R.DISPLAY?C=v.sup1:e.style.cramped?C=v.sup3:C=v.sup2;var D=e.sizeMultiplier,F=z(.5/v.ptPerEm/D),L=null;if(c){var O=r.base&&r.base.type==="op"&&r.base.name&&(r.base.name==="\\oiint"||r.base.name==="\\oiiint");(u instanceof f0||O)&&(L=z(-u.italic))}var G;if(h&&c){b=Math.max(b,C,h.depth+.25*v.xHeight),x=Math.max(x,v.sub2);var P=v.defaultRuleThickness,$=4*P;if(b-h.depth-(c.height-x)<$){x=$-(b-h.depth)+c.height;var U=.8*v.xHeight-(b-h.depth);U>0&&(b+=U,x-=U)}var Z=[{type:"elem",elem:c,shift:x,marginRight:F,marginLeft:L},{type:"elem",elem:h,shift:-b,marginRight:F}];G=y.makeVList({positionType:"individualShift",children:Z},e)}else if(c){x=Math.max(x,v.sub1,c.height-.8*v.xHeight);var Y=[{type:"elem",elem:c,marginLeft:L,marginRight:F}];G=y.makeVList({positionType:"shift",positionData:x,children:Y},e)}else if(h)b=Math.max(b,C,h.depth+.25*v.xHeight),G=y.makeVList({positionType:"shift",positionData:-b,children:[{type:"elem",elem:h,marginRight:F}]},e);else throw new Error("supsub must have either sup or sub.");var z0=It(u,"right")||"mord";return y.makeSpan([z0],[u,y.makeSpan(["msupsub"],[G])],e)},mathmlBuilder(r,e){var t=!1,a,i;r.base&&r.base.type==="horizBrace"&&(i=!!r.sup,i===r.base.isOver&&(t=!0,a=r.base.isOver)),r.base&&(r.base.type==="op"||r.base.type==="operatorname")&&(r.base.parentIsSupSub=!0);var l=[X(r.base,e)];r.sub&&l.push(X(r.sub,e)),r.sup&&l.push(X(r.sup,e));var u;if(t)u=a?"mover":"munder";else if(r.sub)if(r.sup){var v=r.base;v&&v.type==="op"&&v.limits&&e.style===R.DISPLAY||v&&v.type==="operatorname"&&v.alwaysHandleSupSub&&(e.style===R.DISPLAY||v.limits)?u="munderover":u="msubsup"}else{var c=r.base;c&&c.type==="op"&&c.limits&&(e.style===R.DISPLAY||c.alwaysHandleSupSub)||c&&c.type==="operatorname"&&c.alwaysHandleSupSub&&(c.limits||e.style===R.DISPLAY)?u="munder":u="msub"}else{var h=r.base;h&&h.type==="op"&&h.limits&&(e.style===R.DISPLAY||h.alwaysHandleSupSub)||h&&h.type==="operatorname"&&h.alwaysHandleSupSub&&(h.limits||e.style===R.DISPLAY)?u="mover":u="msup"}return new A.MathNode(u,l)}});X0({type:"atom",htmlBuilder(r,e){return y.mathsym(r.text,r.mode,e,["m"+r.family])},mathmlBuilder(r,e){var t=new A.MathNode("mo",[v0(r.text,r.mode)]);if(r.family==="bin"){var a=Kt(r,e);a==="bold-italic"&&t.setAttribute("mathvariant",a)}else r.family==="punct"?t.setAttribute("separator","true"):(r.family==="open"||r.family==="close")&&t.setAttribute("stretchy","false");return t}});var N1={mi:"italic",mn:"normal",mtext:"normal"};X0({type:"mathord",htmlBuilder(r,e){return y.makeOrd(r,e,"mathord")},mathmlBuilder(r,e){var t=new A.MathNode("mi",[v0(r.text,r.mode,e)]),a=Kt(r,e)||"italic";return a!==N1[t.type]&&t.setAttribute("mathvariant",a),t}});X0({type:"textord",htmlBuilder(r,e){return y.makeOrd(r,e,"textord")},mathmlBuilder(r,e){var t=v0(r.text,r.mode,e),a=Kt(r,e)||"normal",i;return r.mode==="text"?i=new A.MathNode("mtext",[t]):/[0-9]/.test(r.text)?i=new A.MathNode("mn",[t]):r.text==="\\prime"?i=new A.MathNode("mo",[t]):i=new A.MathNode("mi",[t]),a!==N1[i.type]&&i.setAttribute("mathvariant",a),i}});var Ct={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},Tt={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};X0({type:"spacing",htmlBuilder(r,e){if(Tt.hasOwnProperty(r.text)){var t=Tt[r.text].className||"";if(r.mode==="text"){var a=y.makeOrd(r,e,"textord");return a.classes.push(t),a}else return y.makeSpan(["mspace",t],[y.mathsym(r.text,r.mode,e)],e)}else{if(Ct.hasOwnProperty(r.text))return y.makeSpan(["mspace",Ct[r.text]],[],e);throw new M('Unknown type of space "'+r.text+'"')}},mathmlBuilder(r,e){var t;if(Tt.hasOwnProperty(r.text))t=new A.MathNode("mtext",[new A.TextNode(" ")]);else{if(Ct.hasOwnProperty(r.text))return new A.MathNode("mspace");throw new M('Unknown type of space "'+r.text+'"')}return t}});var Dr=d(()=>{var r=new A.MathNode("mtd",[]);return r.setAttribute("width","50%"),r},"pad");X0({type:"tag",mathmlBuilder(r,e){var t=new A.MathNode("mtable",[new A.MathNode("mtr",[Dr(),new A.MathNode("mtd",[Y0(r.body,e)]),Dr(),new A.MathNode("mtd",[Y0(r.tag,e)])])]);return t.setAttribute("width","100%"),t}});var Fr={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},Nr={"\\textbf":"textbf","\\textmd":"textmd"},w4={"\\textit":"textit","\\textup":"textup"},qr=d((r,e)=>{var t=r.font;if(t){if(Fr[t])return e.withTextFontFamily(Fr[t]);if(Nr[t])return e.withTextFontWeight(Nr[t]);if(t==="\\emph")return e.fontShape==="textit"?e.withTextFontShape("textup"):e.withTextFontShape("textit")}else return e;return e.withTextFontShape(w4[t])},"optionsWithFont");T({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup","\\emph"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(r,e){var{parser:t,funcName:a}=r,i=e[0];return{type:"text",mode:t.mode,body:_(i),font:a}},htmlBuilder(r,e){var t=qr(r,e),a=r0(r.body,t,!0);return y.makeSpan(["mord","text"],a,t)},mathmlBuilder(r,e){var t=qr(r,e);return Y0(r.body,t)}});T({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(r,e){var{parser:t}=r;return{type:"underline",mode:t.mode,body:e[0]}},htmlBuilder(r,e){var t=V(r.body,e),a=y.makeLineSpan("underline-line",e),i=e.fontMetrics().defaultRuleThickness,l=y.makeVList({positionType:"top",positionData:t.height,children:[{type:"kern",size:i},{type:"elem",elem:a},{type:"kern",size:3*i},{type:"elem",elem:t}]},e);return y.makeSpan(["mord","underline"],[l],e)},mathmlBuilder(r,e){var t=new A.MathNode("mo",[new A.TextNode("‾")]);t.setAttribute("stretchy","true");var a=new A.MathNode("munder",[X(r.body,e),t]);return a.setAttribute("accentunder","true"),a}});T({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(r,e){var{parser:t}=r;return{type:"vcenter",mode:t.mode,body:e[0]}},htmlBuilder(r,e){var t=V(r.body,e),a=e.fontMetrics().axisHeight,i=.5*(t.height-a-(t.depth+a));return y.makeVList({positionType:"shift",positionData:i,children:[{type:"elem",elem:t}]},e)},mathmlBuilder(r,e){return new A.MathNode("mpadded",[X(r.body,e)],["vcenter"])}});T({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(r,e,t){throw new M("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(r,e){for(var t=Rr(r),a=[],i=e.havingStyle(e.style.text()),l=0;l<t.length;l++){var u=t[l];u==="~"&&(u="\\textasciitilde"),a.push(y.makeSymbol(u,"Typewriter-Regular",r.mode,i,["mord","texttt"]))}return y.makeSpan(["mord","text"].concat(i.sizingClasses(e)),y.tryCombineChars(a),i)},mathmlBuilder(r,e){var t=new A.TextNode(Rr(r)),a=new A.MathNode("mtext",[t]);return a.setAttribute("mathvariant","monospace"),a}});var Rr=d(r=>r.body.replace(/ /g,r.star?"␣":" "),"makeVerb"),V0=i1,q1=`[ \r
	]`,k4="\\\\[a-zA-Z@]+",S4="\\\\[^\uD800-\uDFFF]",A4="("+k4+")"+q1+"*",M4=`\\\\(
|[ \r	]+
?)[ \r	]*`,Vt="[̀-ͯ]",B4=new RegExp(Vt+"+$"),z4="("+q1+"+)|"+(M4+"|")+"([!-\\[\\]-‧‪-퟿豈-￿]"+(Vt+"*")+"|[\uD800-\uDBFF][\uDC00-\uDFFF]"+(Vt+"*")+"|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5"+("|"+A4)+("|"+S4+")"),ye,Ir=(ye=class{constructor(e,t){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=t,this.tokenRegex=new RegExp(z4,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,t){this.catcodes[e]=t}lex(){var e=this.input,t=this.tokenRegex.lastIndex;if(t===e.length)return new A0("EOF",new b0(this,t,t));var a=this.tokenRegex.exec(e);if(a===null||a.index!==t)throw new M("Unexpected character: '"+e[t]+"'",new A0(e[t],new b0(this,t,t+1)));var i=a[6]||a[3]||(a[2]?"\\ ":" ");if(this.catcodes[i]===14){var l=e.indexOf(`
`,this.tokenRegex.lastIndex);return l===-1?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=l+1,this.lex()}return new A0(i,new b0(this,t,this.tokenRegex.lastIndex))}},d(ye,"Lexer"),ye),xe,C4=(xe=class{constructor(e,t){e===void 0&&(e={}),t===void 0&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(this.undefStack.length===0)throw new M("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var e=this.undefStack.pop();for(var t in e)e.hasOwnProperty(t)&&(e[t]==null?delete this.current[t]:this.current[t]=e[t])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,t,a){if(a===void 0&&(a=!1),a){for(var i=0;i<this.undefStack.length;i++)delete this.undefStack[i][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=t)}else{var l=this.undefStack[this.undefStack.length-1];l&&!l.hasOwnProperty(e)&&(l[e]=this.current[e])}t==null?delete this.current[e]:this.current[e]=t}},d(xe,"Namespace"),xe),T4=A1;m("\\noexpand",function(r){var e=r.popToken();return r.isExpandable(e.text)&&(e.noexpand=!0,e.treatAsRelax=!0),{tokens:[e],numArgs:0}});m("\\expandafter",function(r){var e=r.popToken();return r.expandOnce(!0),{tokens:[e],numArgs:0}});m("\\@firstoftwo",function(r){var e=r.consumeArgs(2);return{tokens:e[0],numArgs:0}});m("\\@secondoftwo",function(r){var e=r.consumeArgs(2);return{tokens:e[1],numArgs:0}});m("\\@ifnextchar",function(r){var e=r.consumeArgs(3);r.consumeSpaces();var t=r.future();return e[0].length===1&&e[0][0].text===t.text?{tokens:e[1],numArgs:0}:{tokens:e[2],numArgs:0}});m("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}");m("\\TextOrMath",function(r){var e=r.consumeArgs(2);return r.mode==="text"?{tokens:e[0],numArgs:0}:{tokens:e[1],numArgs:0}});var Lr={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};m("\\char",function(r){var e=r.popToken(),t,a="";if(e.text==="'")t=8,e=r.popToken();else if(e.text==='"')t=16,e=r.popToken();else if(e.text==="`")if(e=r.popToken(),e.text[0]==="\\")a=e.text.charCodeAt(1);else{if(e.text==="EOF")throw new M("\\char` missing argument");a=e.text.charCodeAt(0)}else t=10;if(t){if(a=Lr[e.text],a==null||a>=t)throw new M("Invalid base-"+t+" digit "+e.text);for(var i;(i=Lr[r.future().text])!=null&&i<t;)a*=t,a+=i,r.popToken()}return"\\@char{"+a+"}"});var lr=d((r,e,t,a)=>{var i=r.consumeArg().tokens;if(i.length!==1)throw new M("\\newcommand's first argument must be a macro name");var l=i[0].text,u=r.isDefined(l);if(u&&!e)throw new M("\\newcommand{"+l+"} attempting to redefine "+(l+"; use \\renewcommand"));if(!u&&!t)throw new M("\\renewcommand{"+l+"} when command "+l+" does not yet exist; use \\newcommand");var h=0;if(i=r.consumeArg().tokens,i.length===1&&i[0].text==="["){for(var c="",v=r.expandNextToken();v.text!=="]"&&v.text!=="EOF";)c+=v.text,v=r.expandNextToken();if(!c.match(/^\s*[0-9]+\s*$/))throw new M("Invalid number of arguments: "+c);h=parseInt(c),i=r.consumeArg().tokens}return u&&a||r.macros.set(l,{tokens:i,numArgs:h}),""},"newcommand");m("\\newcommand",r=>lr(r,!1,!0,!1));m("\\renewcommand",r=>lr(r,!0,!1,!1));m("\\providecommand",r=>lr(r,!0,!0,!0));m("\\message",r=>{var e=r.consumeArgs(1)[0];return console.log(e.reverse().map(t=>t.text).join("")),""});m("\\errmessage",r=>{var e=r.consumeArgs(1)[0];return console.error(e.reverse().map(t=>t.text).join("")),""});m("\\show",r=>{var e=r.popToken(),t=e.text;return console.log(e,r.macros.get(t),V0[t],W.math[t],W.text[t]),""});m("\\bgroup","{");m("\\egroup","}");m("~","\\nobreakspace");m("\\lq","`");m("\\rq","'");m("\\aa","\\r a");m("\\AA","\\r A");m("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`©}");m("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}");m("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`®}");m("ℬ","\\mathscr{B}");m("ℰ","\\mathscr{E}");m("ℱ","\\mathscr{F}");m("ℋ","\\mathscr{H}");m("ℐ","\\mathscr{I}");m("ℒ","\\mathscr{L}");m("ℳ","\\mathscr{M}");m("ℛ","\\mathscr{R}");m("ℭ","\\mathfrak{C}");m("ℌ","\\mathfrak{H}");m("ℨ","\\mathfrak{Z}");m("\\Bbbk","\\Bbb{k}");m("·","\\cdotp");m("\\llap","\\mathllap{\\textrm{#1}}");m("\\rlap","\\mathrlap{\\textrm{#1}}");m("\\clap","\\mathclap{\\textrm{#1}}");m("\\mathstrut","\\vphantom{(}");m("\\underbar","\\underline{\\text{#1}}");m("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}');m("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}");m("\\ne","\\neq");m("≠","\\neq");m("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}");m("∉","\\notin");m("≘","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}");m("≙","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}");m("≚","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}");m("≛","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}");m("≝","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}");m("≞","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}");m("≟","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}");m("⟂","\\perp");m("‼","\\mathclose{!\\mkern-0.8mu!}");m("∌","\\notni");m("⌜","\\ulcorner");m("⌝","\\urcorner");m("⌞","\\llcorner");m("⌟","\\lrcorner");m("©","\\copyright");m("®","\\textregistered");m("️","\\textregistered");m("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}');m("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}');m("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}');m("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}');m("\\vdots","{\\varvdots\\rule{0pt}{15pt}}");m("⋮","\\vdots");m("\\varGamma","\\mathit{\\Gamma}");m("\\varDelta","\\mathit{\\Delta}");m("\\varTheta","\\mathit{\\Theta}");m("\\varLambda","\\mathit{\\Lambda}");m("\\varXi","\\mathit{\\Xi}");m("\\varPi","\\mathit{\\Pi}");m("\\varSigma","\\mathit{\\Sigma}");m("\\varUpsilon","\\mathit{\\Upsilon}");m("\\varPhi","\\mathit{\\Phi}");m("\\varPsi","\\mathit{\\Psi}");m("\\varOmega","\\mathit{\\Omega}");m("\\substack","\\begin{subarray}{c}#1\\end{subarray}");m("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax");m("\\boxed","\\fbox{$\\displaystyle{#1}$}");m("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;");m("\\implies","\\DOTSB\\;\\Longrightarrow\\;");m("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;");m("\\dddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ...}}{#1}}");m("\\ddddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ....}}{#1}}");var Or={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};m("\\dots",function(r){var e="\\dotso",t=r.expandAfterFuture().text;return t in Or?e=Or[t]:(t.slice(0,4)==="\\not"||t in W.math&&N.contains(["bin","rel"],W.math[t].group))&&(e="\\dotsb"),e});var sr={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};m("\\dotso",function(r){var e=r.future().text;return e in sr?"\\ldots\\,":"\\ldots"});m("\\dotsc",function(r){var e=r.future().text;return e in sr&&e!==","?"\\ldots\\,":"\\ldots"});m("\\cdots",function(r){var e=r.future().text;return e in sr?"\\@cdots\\,":"\\@cdots"});m("\\dotsb","\\cdots");m("\\dotsm","\\cdots");m("\\dotsi","\\!\\cdots");m("\\dotsx","\\ldots\\,");m("\\DOTSI","\\relax");m("\\DOTSB","\\relax");m("\\DOTSX","\\relax");m("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax");m("\\,","\\tmspace+{3mu}{.1667em}");m("\\thinspace","\\,");m("\\>","\\mskip{4mu}");m("\\:","\\tmspace+{4mu}{.2222em}");m("\\medspace","\\:");m("\\;","\\tmspace+{5mu}{.2777em}");m("\\thickspace","\\;");m("\\!","\\tmspace-{3mu}{.1667em}");m("\\negthinspace","\\!");m("\\negmedspace","\\tmspace-{4mu}{.2222em}");m("\\negthickspace","\\tmspace-{5mu}{.277em}");m("\\enspace","\\kern.5em ");m("\\enskip","\\hskip.5em\\relax");m("\\quad","\\hskip1em\\relax");m("\\qquad","\\hskip2em\\relax");m("\\tag","\\@ifstar\\tag@literal\\tag@paren");m("\\tag@paren","\\tag@literal{({#1})}");m("\\tag@literal",r=>{if(r.macros.get("\\df@tag"))throw new M("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"});m("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}");m("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)");m("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}");m("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1");m("\\newline","\\\\\\relax");m("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");var R1=z(k0["Main-Regular"][84][1]-.7*k0["Main-Regular"][65][1]);m("\\LaTeX","\\textrm{\\html@mathml{"+("L\\kern-.36em\\raisebox{"+R1+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{LaTeX}}");m("\\KaTeX","\\textrm{\\html@mathml{"+("K\\kern-.17em\\raisebox{"+R1+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{KaTeX}}");m("\\hspace","\\@ifstar\\@hspacer\\@hspace");m("\\@hspace","\\hskip #1\\relax");m("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax");m("\\ordinarycolon",":");m("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}");m("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}');m("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}');m("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}');m("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}');m("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}');m("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}');m("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}');m("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}');m("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}');m("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}');m("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}');m("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}');m("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}');m("∷","\\dblcolon");m("∹","\\eqcolon");m("≔","\\coloneqq");m("≕","\\eqqcolon");m("⩴","\\Coloneqq");m("\\ratio","\\vcentcolon");m("\\coloncolon","\\dblcolon");m("\\colonequals","\\coloneqq");m("\\coloncolonequals","\\Coloneqq");m("\\equalscolon","\\eqqcolon");m("\\equalscoloncolon","\\Eqqcolon");m("\\colonminus","\\coloneq");m("\\coloncolonminus","\\Coloneq");m("\\minuscolon","\\eqcolon");m("\\minuscoloncolon","\\Eqcolon");m("\\coloncolonapprox","\\Colonapprox");m("\\coloncolonsim","\\Colonsim");m("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}");m("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}");m("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}");m("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}");m("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}");m("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}");m("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}");m("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}");m("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}");m("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}");m("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}");m("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}");m("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}");m("\\gvertneqq","\\html@mathml{\\@gvertneqq}{≩}");m("\\lvertneqq","\\html@mathml{\\@lvertneqq}{≨}");m("\\ngeqq","\\html@mathml{\\@ngeqq}{≱}");m("\\ngeqslant","\\html@mathml{\\@ngeqslant}{≱}");m("\\nleqq","\\html@mathml{\\@nleqq}{≰}");m("\\nleqslant","\\html@mathml{\\@nleqslant}{≰}");m("\\nshortmid","\\html@mathml{\\@nshortmid}{∤}");m("\\nshortparallel","\\html@mathml{\\@nshortparallel}{∦}");m("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{⊈}");m("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{⊉}");m("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{⊊}");m("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{⫋}");m("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{⊋}");m("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{⫌}");m("\\imath","\\html@mathml{\\@imath}{ı}");m("\\jmath","\\html@mathml{\\@jmath}{ȷ}");m("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}");m("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}");m("⟦","\\llbracket");m("⟧","\\rrbracket");m("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}");m("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}");m("⦃","\\lBrace");m("⦄","\\rBrace");m("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}");m("⦵","\\minuso");m("\\darr","\\downarrow");m("\\dArr","\\Downarrow");m("\\Darr","\\Downarrow");m("\\lang","\\langle");m("\\rang","\\rangle");m("\\uarr","\\uparrow");m("\\uArr","\\Uparrow");m("\\Uarr","\\Uparrow");m("\\N","\\mathbb{N}");m("\\R","\\mathbb{R}");m("\\Z","\\mathbb{Z}");m("\\alef","\\aleph");m("\\alefsym","\\aleph");m("\\Alpha","\\mathrm{A}");m("\\Beta","\\mathrm{B}");m("\\bull","\\bullet");m("\\Chi","\\mathrm{X}");m("\\clubs","\\clubsuit");m("\\cnums","\\mathbb{C}");m("\\Complex","\\mathbb{C}");m("\\Dagger","\\ddagger");m("\\diamonds","\\diamondsuit");m("\\empty","\\emptyset");m("\\Epsilon","\\mathrm{E}");m("\\Eta","\\mathrm{H}");m("\\exist","\\exists");m("\\harr","\\leftrightarrow");m("\\hArr","\\Leftrightarrow");m("\\Harr","\\Leftrightarrow");m("\\hearts","\\heartsuit");m("\\image","\\Im");m("\\infin","\\infty");m("\\Iota","\\mathrm{I}");m("\\isin","\\in");m("\\Kappa","\\mathrm{K}");m("\\larr","\\leftarrow");m("\\lArr","\\Leftarrow");m("\\Larr","\\Leftarrow");m("\\lrarr","\\leftrightarrow");m("\\lrArr","\\Leftrightarrow");m("\\Lrarr","\\Leftrightarrow");m("\\Mu","\\mathrm{M}");m("\\natnums","\\mathbb{N}");m("\\Nu","\\mathrm{N}");m("\\Omicron","\\mathrm{O}");m("\\plusmn","\\pm");m("\\rarr","\\rightarrow");m("\\rArr","\\Rightarrow");m("\\Rarr","\\Rightarrow");m("\\real","\\Re");m("\\reals","\\mathbb{R}");m("\\Reals","\\mathbb{R}");m("\\Rho","\\mathrm{P}");m("\\sdot","\\cdot");m("\\sect","\\S");m("\\spades","\\spadesuit");m("\\sub","\\subset");m("\\sube","\\subseteq");m("\\supe","\\supseteq");m("\\Tau","\\mathrm{T}");m("\\thetasym","\\vartheta");m("\\weierp","\\wp");m("\\Zeta","\\mathrm{Z}");m("\\argmin","\\DOTSB\\operatorname*{arg\\,min}");m("\\argmax","\\DOTSB\\operatorname*{arg\\,max}");m("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits");m("\\bra","\\mathinner{\\langle{#1}|}");m("\\ket","\\mathinner{|{#1}\\rangle}");m("\\braket","\\mathinner{\\langle{#1}\\rangle}");m("\\Bra","\\left\\langle#1\\right|");m("\\Ket","\\left|#1\\right\\rangle");var I1=d(r=>e=>{var t=e.consumeArg().tokens,a=e.consumeArg().tokens,i=e.consumeArg().tokens,l=e.consumeArg().tokens,u=e.macros.get("|"),h=e.macros.get("\\|");e.macros.beginGroup();var c=d(x=>k=>{r&&(k.macros.set("|",u),i.length&&k.macros.set("\\|",h));var w=x;if(!x&&i.length){var B=k.future();B.text==="|"&&(k.popToken(),w=!0)}return{tokens:w?i:a,numArgs:0}},"midMacro");e.macros.set("|",c(!1)),i.length&&e.macros.set("\\|",c(!0));var v=e.consumeArg().tokens,b=e.expandTokens([...l,...v,...t]);return e.macros.endGroup(),{tokens:b.reverse(),numArgs:0}},"braketHelper");m("\\bra@ket",I1(!1));m("\\bra@set",I1(!0));m("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}");m("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}");m("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}");m("\\angln","{\\angl n}");m("\\blue","\\textcolor{##6495ed}{#1}");m("\\orange","\\textcolor{##ffa500}{#1}");m("\\pink","\\textcolor{##ff00af}{#1}");m("\\red","\\textcolor{##df0030}{#1}");m("\\green","\\textcolor{##28ae7b}{#1}");m("\\gray","\\textcolor{gray}{#1}");m("\\purple","\\textcolor{##9d38bd}{#1}");m("\\blueA","\\textcolor{##ccfaff}{#1}");m("\\blueB","\\textcolor{##80f6ff}{#1}");m("\\blueC","\\textcolor{##63d9ea}{#1}");m("\\blueD","\\textcolor{##11accd}{#1}");m("\\blueE","\\textcolor{##0c7f99}{#1}");m("\\tealA","\\textcolor{##94fff5}{#1}");m("\\tealB","\\textcolor{##26edd5}{#1}");m("\\tealC","\\textcolor{##01d1c1}{#1}");m("\\tealD","\\textcolor{##01a995}{#1}");m("\\tealE","\\textcolor{##208170}{#1}");m("\\greenA","\\textcolor{##b6ffb0}{#1}");m("\\greenB","\\textcolor{##8af281}{#1}");m("\\greenC","\\textcolor{##74cf70}{#1}");m("\\greenD","\\textcolor{##1fab54}{#1}");m("\\greenE","\\textcolor{##0d923f}{#1}");m("\\goldA","\\textcolor{##ffd0a9}{#1}");m("\\goldB","\\textcolor{##ffbb71}{#1}");m("\\goldC","\\textcolor{##ff9c39}{#1}");m("\\goldD","\\textcolor{##e07d10}{#1}");m("\\goldE","\\textcolor{##a75a05}{#1}");m("\\redA","\\textcolor{##fca9a9}{#1}");m("\\redB","\\textcolor{##ff8482}{#1}");m("\\redC","\\textcolor{##f9685d}{#1}");m("\\redD","\\textcolor{##e84d39}{#1}");m("\\redE","\\textcolor{##bc2612}{#1}");m("\\maroonA","\\textcolor{##ffbde0}{#1}");m("\\maroonB","\\textcolor{##ff92c6}{#1}");m("\\maroonC","\\textcolor{##ed5fa6}{#1}");m("\\maroonD","\\textcolor{##ca337c}{#1}");m("\\maroonE","\\textcolor{##9e034e}{#1}");m("\\purpleA","\\textcolor{##ddd7ff}{#1}");m("\\purpleB","\\textcolor{##c6b9fc}{#1}");m("\\purpleC","\\textcolor{##aa87ff}{#1}");m("\\purpleD","\\textcolor{##7854ab}{#1}");m("\\purpleE","\\textcolor{##543b78}{#1}");m("\\mintA","\\textcolor{##f5f9e8}{#1}");m("\\mintB","\\textcolor{##edf2df}{#1}");m("\\mintC","\\textcolor{##e0e5cc}{#1}");m("\\grayA","\\textcolor{##f6f7f7}{#1}");m("\\grayB","\\textcolor{##f0f1f2}{#1}");m("\\grayC","\\textcolor{##e3e5e6}{#1}");m("\\grayD","\\textcolor{##d6d8da}{#1}");m("\\grayE","\\textcolor{##babec2}{#1}");m("\\grayF","\\textcolor{##888d93}{#1}");m("\\grayG","\\textcolor{##626569}{#1}");m("\\grayH","\\textcolor{##3b3e40}{#1}");m("\\grayI","\\textcolor{##21242c}{#1}");m("\\kaBlue","\\textcolor{##314453}{#1}");m("\\kaGreen","\\textcolor{##71B307}{#1}");var L1={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0},we,E4=(we=class{constructor(e,t,a){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new C4(T4,t.macros),this.mode=a,this.stack=[]}feed(e){this.lexer=new Ir(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return this.stack.length===0&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){var t,a,i;if(e){if(this.consumeSpaces(),this.future().text!=="[")return null;t=this.popToken(),{tokens:i,end:a}=this.consumeArg(["]"])}else({tokens:i,start:t,end:a}=this.consumeArg());return this.pushToken(new A0("EOF",a.loc)),this.pushTokens(i),t.range(a,"")}consumeSpaces(){for(;;){var e=this.future();if(e.text===" ")this.stack.pop();else break}}consumeArg(e){var t=[],a=e&&e.length>0;a||this.consumeSpaces();var i=this.future(),l,u=0,h=0;do{if(l=this.popToken(),t.push(l),l.text==="{")++u;else if(l.text==="}"){if(--u,u===-1)throw new M("Extra }",l)}else if(l.text==="EOF")throw new M("Unexpected end of input in a macro argument, expected '"+(e&&a?e[h]:"}")+"'",l);if(e&&a)if((u===0||u===1&&e[h]==="{")&&l.text===e[h]){if(++h,h===e.length){t.splice(-h,h);break}}else h=0}while(u!==0||a);return i.text==="{"&&t[t.length-1].text==="}"&&(t.pop(),t.shift()),t.reverse(),{tokens:t,start:i,end:l}}consumeArgs(e,t){if(t){if(t.length!==e+1)throw new M("The length of delimiters doesn't match the number of args!");for(var a=t[0],i=0;i<a.length;i++){var l=this.popToken();if(a[i]!==l.text)throw new M("Use of the macro doesn't match its definition",l)}}for(var u=[],h=0;h<e;h++)u.push(this.consumeArg(t&&t[h+1]).tokens);return u}countExpansion(e){if(this.expansionCount+=e,this.expansionCount>this.settings.maxExpand)throw new M("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(e){var t=this.popToken(),a=t.text,i=t.noexpand?null:this._getExpansion(a);if(i==null||e&&i.unexpandable){if(e&&i==null&&a[0]==="\\"&&!this.isDefined(a))throw new M("Undefined control sequence: "+a);return this.pushToken(t),!1}this.countExpansion(1);var l=i.tokens,u=this.consumeArgs(i.numArgs,i.delimiters);if(i.numArgs){l=l.slice();for(var h=l.length-1;h>=0;--h){var c=l[h];if(c.text==="#"){if(h===0)throw new M("Incomplete placeholder at end of macro body",c);if(c=l[--h],c.text==="#")l.splice(h+1,1);else if(/^[1-9]$/.test(c.text))l.splice(h,2,...u[+c.text-1]);else throw new M("Not a valid argument number",c)}}}return this.pushTokens(l),l.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(this.expandOnce()===!1){var e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw new Error}expandMacro(e){return this.macros.has(e)?this.expandTokens([new A0(e)]):void 0}expandTokens(e){var t=[],a=this.stack.length;for(this.pushTokens(e);this.stack.length>a;)if(this.expandOnce(!0)===!1){var i=this.stack.pop();i.treatAsRelax&&(i.noexpand=!1,i.treatAsRelax=!1),t.push(i)}return this.countExpansion(t.length),t}expandMacroAsText(e){var t=this.expandMacro(e);return t&&t.map(a=>a.text).join("")}_getExpansion(e){var t=this.macros.get(e);if(t==null)return t;if(e.length===1){var a=this.lexer.catcodes[e];if(a!=null&&a!==13)return}var i=typeof t=="function"?t(this):t;if(typeof i=="string"){var l=0;if(i.indexOf("#")!==-1)for(var u=i.replace(/##/g,"");u.indexOf("#"+(l+1))!==-1;)++l;for(var h=new Ir(i,this.settings),c=[],v=h.lex();v.text!=="EOF";)c.push(v),v=h.lex();c.reverse();var b={tokens:c,numArgs:l};return b}return i}isDefined(e){return this.macros.has(e)||V0.hasOwnProperty(e)||W.math.hasOwnProperty(e)||W.text.hasOwnProperty(e)||L1.hasOwnProperty(e)}isExpandable(e){var t=this.macros.get(e);return t!=null?typeof t=="string"||typeof t=="function"||!t.unexpandable:V0.hasOwnProperty(e)&&!V0[e].primitive}},d(we,"MacroExpander"),we),Hr=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,Je=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9","ₐ":"a","ₑ":"e","ₕ":"h","ᵢ":"i","ⱼ":"j","ₖ":"k","ₗ":"l","ₘ":"m","ₙ":"n","ₒ":"o","ₚ":"p","ᵣ":"r","ₛ":"s","ₜ":"t","ᵤ":"u","ᵥ":"v","ₓ":"x","ᵦ":"β","ᵧ":"γ","ᵨ":"ρ","ᵩ":"ϕ","ᵪ":"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","¹":"1","²":"2","³":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9","ᴬ":"A","ᴮ":"B","ᴰ":"D","ᴱ":"E","ᴳ":"G","ᴴ":"H","ᴵ":"I","ᴶ":"J","ᴷ":"K","ᴸ":"L","ᴹ":"M","ᴺ":"N","ᴼ":"O","ᴾ":"P","ᴿ":"R","ᵀ":"T","ᵁ":"U","ⱽ":"V","ᵂ":"W","ᵃ":"a","ᵇ":"b","ᶜ":"c","ᵈ":"d","ᵉ":"e","ᶠ":"f","ᵍ":"g",ʰ:"h","ⁱ":"i",ʲ:"j","ᵏ":"k",ˡ:"l","ᵐ":"m",ⁿ:"n","ᵒ":"o","ᵖ":"p",ʳ:"r",ˢ:"s","ᵗ":"t","ᵘ":"u","ᵛ":"v",ʷ:"w",ˣ:"x",ʸ:"y","ᶻ":"z","ᵝ":"β","ᵞ":"γ","ᵟ":"δ","ᵠ":"ϕ","ᵡ":"χ","ᶿ":"θ"}),Et={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},Pr={á:"á",à:"à",ä:"ä",ǟ:"ǟ",ã:"ã",ā:"ā",ă:"ă",ắ:"ắ",ằ:"ằ",ẵ:"ẵ",ǎ:"ǎ",â:"â",ấ:"ấ",ầ:"ầ",ẫ:"ẫ",ȧ:"ȧ",ǡ:"ǡ",å:"å",ǻ:"ǻ",ḃ:"ḃ",ć:"ć",ḉ:"ḉ",č:"č",ĉ:"ĉ",ċ:"ċ",ç:"ç",ď:"ď",ḋ:"ḋ",ḑ:"ḑ",é:"é",è:"è",ë:"ë",ẽ:"ẽ",ē:"ē",ḗ:"ḗ",ḕ:"ḕ",ĕ:"ĕ",ḝ:"ḝ",ě:"ě",ê:"ê",ế:"ế",ề:"ề",ễ:"ễ",ė:"ė",ȩ:"ȩ",ḟ:"ḟ",ǵ:"ǵ",ḡ:"ḡ",ğ:"ğ",ǧ:"ǧ",ĝ:"ĝ",ġ:"ġ",ģ:"ģ",ḧ:"ḧ",ȟ:"ȟ",ĥ:"ĥ",ḣ:"ḣ",ḩ:"ḩ",í:"í",ì:"ì",ï:"ï",ḯ:"ḯ",ĩ:"ĩ",ī:"ī",ĭ:"ĭ",ǐ:"ǐ",î:"î",ǰ:"ǰ",ĵ:"ĵ",ḱ:"ḱ",ǩ:"ǩ",ķ:"ķ",ĺ:"ĺ",ľ:"ľ",ļ:"ļ",ḿ:"ḿ",ṁ:"ṁ",ń:"ń",ǹ:"ǹ",ñ:"ñ",ň:"ň",ṅ:"ṅ",ņ:"ņ",ó:"ó",ò:"ò",ö:"ö",ȫ:"ȫ",õ:"õ",ṍ:"ṍ",ṏ:"ṏ",ȭ:"ȭ",ō:"ō",ṓ:"ṓ",ṑ:"ṑ",ŏ:"ŏ",ǒ:"ǒ",ô:"ô",ố:"ố",ồ:"ồ",ỗ:"ỗ",ȯ:"ȯ",ȱ:"ȱ",ő:"ő",ṕ:"ṕ",ṗ:"ṗ",ŕ:"ŕ",ř:"ř",ṙ:"ṙ",ŗ:"ŗ",ś:"ś",ṥ:"ṥ",š:"š",ṧ:"ṧ",ŝ:"ŝ",ṡ:"ṡ",ş:"ş",ẗ:"ẗ",ť:"ť",ṫ:"ṫ",ţ:"ţ",ú:"ú",ù:"ù",ü:"ü",ǘ:"ǘ",ǜ:"ǜ",ǖ:"ǖ",ǚ:"ǚ",ũ:"ũ",ṹ:"ṹ",ū:"ū",ṻ:"ṻ",ŭ:"ŭ",ǔ:"ǔ",û:"û",ů:"ů",ű:"ű",ṽ:"ṽ",ẃ:"ẃ",ẁ:"ẁ",ẅ:"ẅ",ŵ:"ŵ",ẇ:"ẇ",ẘ:"ẘ",ẍ:"ẍ",ẋ:"ẋ",ý:"ý",ỳ:"ỳ",ÿ:"ÿ",ỹ:"ỹ",ȳ:"ȳ",ŷ:"ŷ",ẏ:"ẏ",ẙ:"ẙ",ź:"ź",ž:"ž",ẑ:"ẑ",ż:"ż",Á:"Á",À:"À",Ä:"Ä",Ǟ:"Ǟ",Ã:"Ã",Ā:"Ā",Ă:"Ă",Ắ:"Ắ",Ằ:"Ằ",Ẵ:"Ẵ",Ǎ:"Ǎ",Â:"Â",Ấ:"Ấ",Ầ:"Ầ",Ẫ:"Ẫ",Ȧ:"Ȧ",Ǡ:"Ǡ",Å:"Å",Ǻ:"Ǻ",Ḃ:"Ḃ",Ć:"Ć",Ḉ:"Ḉ",Č:"Č",Ĉ:"Ĉ",Ċ:"Ċ",Ç:"Ç",Ď:"Ď",Ḋ:"Ḋ",Ḑ:"Ḑ",É:"É",È:"È",Ë:"Ë",Ẽ:"Ẽ",Ē:"Ē",Ḗ:"Ḗ",Ḕ:"Ḕ",Ĕ:"Ĕ",Ḝ:"Ḝ",Ě:"Ě",Ê:"Ê",Ế:"Ế",Ề:"Ề",Ễ:"Ễ",Ė:"Ė",Ȩ:"Ȩ",Ḟ:"Ḟ",Ǵ:"Ǵ",Ḡ:"Ḡ",Ğ:"Ğ",Ǧ:"Ǧ",Ĝ:"Ĝ",Ġ:"Ġ",Ģ:"Ģ",Ḧ:"Ḧ",Ȟ:"Ȟ",Ĥ:"Ĥ",Ḣ:"Ḣ",Ḩ:"Ḩ",Í:"Í",Ì:"Ì",Ï:"Ï",Ḯ:"Ḯ",Ĩ:"Ĩ",Ī:"Ī",Ĭ:"Ĭ",Ǐ:"Ǐ",Î:"Î",İ:"İ",Ĵ:"Ĵ",Ḱ:"Ḱ",Ǩ:"Ǩ",Ķ:"Ķ",Ĺ:"Ĺ",Ľ:"Ľ",Ļ:"Ļ",Ḿ:"Ḿ",Ṁ:"Ṁ",Ń:"Ń",Ǹ:"Ǹ",Ñ:"Ñ",Ň:"Ň",Ṅ:"Ṅ",Ņ:"Ņ",Ó:"Ó",Ò:"Ò",Ö:"Ö",Ȫ:"Ȫ",Õ:"Õ",Ṍ:"Ṍ",Ṏ:"Ṏ",Ȭ:"Ȭ",Ō:"Ō",Ṓ:"Ṓ",Ṑ:"Ṑ",Ŏ:"Ŏ",Ǒ:"Ǒ",Ô:"Ô",Ố:"Ố",Ồ:"Ồ",Ỗ:"Ỗ",Ȯ:"Ȯ",Ȱ:"Ȱ",Ő:"Ő",Ṕ:"Ṕ",Ṗ:"Ṗ",Ŕ:"Ŕ",Ř:"Ř",Ṙ:"Ṙ",Ŗ:"Ŗ",Ś:"Ś",Ṥ:"Ṥ",Š:"Š",Ṧ:"Ṧ",Ŝ:"Ŝ",Ṡ:"Ṡ",Ş:"Ş",Ť:"Ť",Ṫ:"Ṫ",Ţ:"Ţ",Ú:"Ú",Ù:"Ù",Ü:"Ü",Ǘ:"Ǘ",Ǜ:"Ǜ",Ǖ:"Ǖ",Ǚ:"Ǚ",Ũ:"Ũ",Ṹ:"Ṹ",Ū:"Ū",Ṻ:"Ṻ",Ŭ:"Ŭ",Ǔ:"Ǔ",Û:"Û",Ů:"Ů",Ű:"Ű",Ṽ:"Ṽ",Ẃ:"Ẃ",Ẁ:"Ẁ",Ẅ:"Ẅ",Ŵ:"Ŵ",Ẇ:"Ẇ",Ẍ:"Ẍ",Ẋ:"Ẋ",Ý:"Ý",Ỳ:"Ỳ",Ÿ:"Ÿ",Ỹ:"Ỹ",Ȳ:"Ȳ",Ŷ:"Ŷ",Ẏ:"Ẏ",Ź:"Ź",Ž:"Ž",Ẑ:"Ẑ",Ż:"Ż",ά:"ά",ὰ:"ὰ",ᾱ:"ᾱ",ᾰ:"ᾰ",έ:"έ",ὲ:"ὲ",ή:"ή",ὴ:"ὴ",ί:"ί",ὶ:"ὶ",ϊ:"ϊ",ΐ:"ΐ",ῒ:"ῒ",ῑ:"ῑ",ῐ:"ῐ",ό:"ό",ὸ:"ὸ",ύ:"ύ",ὺ:"ὺ",ϋ:"ϋ",ΰ:"ΰ",ῢ:"ῢ",ῡ:"ῡ",ῠ:"ῠ",ώ:"ώ",ὼ:"ὼ",Ύ:"Ύ",Ὺ:"Ὺ",Ϋ:"Ϋ",Ῡ:"Ῡ",Ῠ:"Ῠ",Ώ:"Ώ",Ὼ:"Ὼ"},Q0,O1=(Q0=class{constructor(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new E4(e,t,this.mode),this.settings=t,this.leftrightDepth=0}expect(e,t){if(t===void 0&&(t=!0),this.fetch().text!==e)throw new M("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());t&&this.consume()}consume(){this.nextToken=null}fetch(){return this.nextToken==null&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{var e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){var t=this.nextToken;this.consume(),this.gullet.pushToken(new A0("}")),this.gullet.pushTokens(e);var a=this.parseExpression(!1);return this.expect("}"),this.nextToken=t,a}parseExpression(e,t){for(var a=[];;){this.mode==="math"&&this.consumeSpaces();var i=this.fetch();if(Q0.endOfExpression.indexOf(i.text)!==-1||t&&i.text===t||e&&V0[i.text]&&V0[i.text].infix)break;var l=this.parseAtom(t);if(l){if(l.type==="internal")continue}else break;a.push(l)}return this.mode==="text"&&this.formLigatures(a),this.handleInfixNodes(a)}handleInfixNodes(e){for(var t=-1,a,i=0;i<e.length;i++)if(e[i].type==="infix"){if(t!==-1)throw new M("only one infix operator per group",e[i].token);t=i,a=e[i].replaceWith}if(t!==-1&&a){var l,u,h=e.slice(0,t),c=e.slice(t+1);h.length===1&&h[0].type==="ordgroup"?l=h[0]:l={type:"ordgroup",mode:this.mode,body:h},c.length===1&&c[0].type==="ordgroup"?u=c[0]:u={type:"ordgroup",mode:this.mode,body:c};var v;return a==="\\\\abovefrac"?v=this.callFunction(a,[l,e[t],u],[]):v=this.callFunction(a,[l,u],[]),[v]}else return e}handleSupSubscript(e){var t=this.fetch(),a=t.text;this.consume(),this.consumeSpaces();var i;do{var l;i=this.parseGroup(e)}while(((l=i)==null?void 0:l.type)==="internal");if(!i)throw new M("Expected group after '"+a+"'",t);return i}formatUnsupportedCmd(e){for(var t=[],a=0;a<e.length;a++)t.push({type:"textord",mode:"text",text:e[a]});var i={type:"text",mode:this.mode,body:t},l={type:"color",mode:this.mode,color:this.settings.errorColor,body:[i]};return l}parseAtom(e){var t=this.parseGroup("atom",e);if(t?.type==="internal"||this.mode==="text")return t;for(var a,i;;){this.consumeSpaces();var l=this.fetch();if(l.text==="\\limits"||l.text==="\\nolimits"){if(t&&t.type==="op"){var u=l.text==="\\limits";t.limits=u,t.alwaysHandleSupSub=!0}else if(t&&t.type==="operatorname")t.alwaysHandleSupSub&&(t.limits=l.text==="\\limits");else throw new M("Limit controls must follow a math operator",l);this.consume()}else if(l.text==="^"){if(a)throw new M("Double superscript",l);a=this.handleSupSubscript("superscript")}else if(l.text==="_"){if(i)throw new M("Double subscript",l);i=this.handleSupSubscript("subscript")}else if(l.text==="'"){if(a)throw new M("Double superscript",l);var h={type:"textord",mode:this.mode,text:"\\prime"},c=[h];for(this.consume();this.fetch().text==="'";)c.push(h),this.consume();this.fetch().text==="^"&&c.push(this.handleSupSubscript("superscript")),a={type:"ordgroup",mode:this.mode,body:c}}else if(Je[l.text]){var v=Hr.test(l.text),b=[];for(b.push(new A0(Je[l.text])),this.consume();;){var x=this.fetch().text;if(!Je[x]||Hr.test(x)!==v)break;b.unshift(new A0(Je[x])),this.consume()}var k=this.subparse(b);v?i={type:"ordgroup",mode:"math",body:k}:a={type:"ordgroup",mode:"math",body:k}}else break}return a||i?{type:"supsub",mode:this.mode,base:t,sup:a,sub:i}:t}parseFunction(e,t){var a=this.fetch(),i=a.text,l=V0[i];if(!l)return null;if(this.consume(),t&&t!=="atom"&&!l.allowedInArgument)throw new M("Got function '"+i+"' with no arguments"+(t?" as "+t:""),a);if(this.mode==="text"&&!l.allowedInText)throw new M("Can't use function '"+i+"' in text mode",a);if(this.mode==="math"&&l.allowedInMath===!1)throw new M("Can't use function '"+i+"' in math mode",a);var{args:u,optArgs:h}=this.parseArguments(i,l);return this.callFunction(i,u,h,a,e)}callFunction(e,t,a,i,l){var u={funcName:e,parser:this,token:i,breakOnTokenText:l},h=V0[e];if(h&&h.handler)return h.handler(u,t,a);throw new M("No function handler for "+e)}parseArguments(e,t){var a=t.numArgs+t.numOptionalArgs;if(a===0)return{args:[],optArgs:[]};for(var i=[],l=[],u=0;u<a;u++){var h=t.argTypes&&t.argTypes[u],c=u<t.numOptionalArgs;(t.primitive&&h==null||t.type==="sqrt"&&u===1&&l[0]==null)&&(h="primitive");var v=this.parseGroupOfType("argument to '"+e+"'",h,c);if(c)l.push(v);else if(v!=null)i.push(v);else throw new M("Null argument, please report this as a bug")}return{args:i,optArgs:l}}parseGroupOfType(e,t,a){switch(t){case"color":return this.parseColorGroup(a);case"size":return this.parseSizeGroup(a);case"url":return this.parseUrlGroup(a);case"math":case"text":return this.parseArgumentGroup(a,t);case"hbox":{var i=this.parseArgumentGroup(a,"text");return i!=null?{type:"styling",mode:i.mode,body:[i],style:"text"}:null}case"raw":{var l=this.parseStringGroup("raw",a);return l!=null?{type:"raw",mode:"text",string:l.text}:null}case"primitive":{if(a)throw new M("A primitive argument cannot be optional");var u=this.parseGroup(e);if(u==null)throw new M("Expected group as "+e,this.fetch());return u}case"original":case null:case void 0:return this.parseArgumentGroup(a);default:throw new M("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;this.fetch().text===" ";)this.consume()}parseStringGroup(e,t){var a=this.gullet.scanArgument(t);if(a==null)return null;for(var i="",l;(l=this.fetch()).text!=="EOF";)i+=l.text,this.consume();return this.consume(),a.text=i,a}parseRegexGroup(e,t){for(var a=this.fetch(),i=a,l="",u;(u=this.fetch()).text!=="EOF"&&e.test(l+u.text);)i=u,l+=i.text,this.consume();if(l==="")throw new M("Invalid "+t+": '"+a.text+"'",a);return a.range(i,l)}parseColorGroup(e){var t=this.parseStringGroup("color",e);if(t==null)return null;var a=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!a)throw new M("Invalid color: '"+t.text+"'",t);var i=a[0];return/^[0-9a-f]{6}$/i.test(i)&&(i="#"+i),{type:"color-token",mode:this.mode,color:i}}parseSizeGroup(e){var t,a=!1;if(this.gullet.consumeSpaces(),!e&&this.gullet.future().text!=="{"?t=this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size"):t=this.parseStringGroup("size",e),!t)return null;!e&&t.text.length===0&&(t.text="0pt",a=!0);var i=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!i)throw new M("Invalid size: '"+t.text+"'",t);var l={number:+(i[1]+i[2]),unit:i[3]};if(!jr(l))throw new M("Invalid unit: '"+l.unit+"'",t);return{type:"size",mode:this.mode,value:l,isBlank:a}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);var t=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),t==null)return null;var a=t.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:a}}parseArgumentGroup(e,t){var a=this.gullet.scanArgument(e);if(a==null)return null;var i=this.mode;t&&this.switchMode(t),this.gullet.beginGroup();var l=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();var u={type:"ordgroup",mode:this.mode,loc:a.loc,body:l};return t&&this.switchMode(i),u}parseGroup(e,t){var a=this.fetch(),i=a.text,l;if(i==="{"||i==="\\begingroup"){this.consume();var u=i==="{"?"}":"\\endgroup";this.gullet.beginGroup();var h=this.parseExpression(!1,u),c=this.fetch();this.expect(u),this.gullet.endGroup(),l={type:"ordgroup",mode:this.mode,loc:b0.range(a,c),body:h,semisimple:i==="\\begingroup"||void 0}}else if(l=this.parseFunction(t,e)||this.parseSymbol(),l==null&&i[0]==="\\"&&!L1.hasOwnProperty(i)){if(this.settings.throwOnError)throw new M("Undefined control sequence: "+i,a);l=this.formatUnsupportedCmd(i),this.consume()}return l}formLigatures(e){for(var t=e.length-1,a=0;a<t;++a){var i=e[a],l=i.text;l==="-"&&e[a+1].text==="-"&&(a+1<t&&e[a+2].text==="-"?(e.splice(a,3,{type:"textord",mode:"text",loc:b0.range(i,e[a+2]),text:"---"}),t-=2):(e.splice(a,2,{type:"textord",mode:"text",loc:b0.range(i,e[a+1]),text:"--"}),t-=1)),(l==="'"||l==="`")&&e[a+1].text===l&&(e.splice(a,2,{type:"textord",mode:"text",loc:b0.range(i,e[a+1]),text:l+l}),t-=1)}}parseSymbol(){var e=this.fetch(),t=e.text;if(/^\\verb[^a-zA-Z]/.test(t)){this.consume();var a=t.slice(5),i=a.charAt(0)==="*";if(i&&(a=a.slice(1)),a.length<2||a.charAt(0)!==a.slice(-1))throw new M(`\\verb assertion failed --
                    please report what input caused this bug`);return a=a.slice(1,-1),{type:"verb",mode:"text",body:a,star:i}}Pr.hasOwnProperty(t[0])&&!W[this.mode][t[0]]&&(this.settings.strict&&this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+t[0]+'" used in math mode',e),t=Pr[t[0]]+t.slice(1));var l=B4.exec(t);l&&(t=t.substring(0,l.index),t==="i"?t="ı":t==="j"&&(t="ȷ"));var u;if(W[this.mode][t]){this.settings.strict&&this.mode==="math"&&Rt.indexOf(t)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+t[0]+'" used in math mode',e);var h=W[this.mode][t].group,c=b0.range(e),v;if(xa.hasOwnProperty(h)){var b=h;v={type:"atom",mode:this.mode,family:b,loc:c,text:t}}else v={type:h,mode:this.mode,loc:c,text:t};u=v}else if(t.charCodeAt(0)>=128)this.settings.strict&&(Yt(t.charCodeAt(0))?this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+t[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+t[0]+'"'+(" ("+t.charCodeAt(0)+")"),e)),u={type:"textord",mode:"text",loc:b0.range(e),text:t};else return null;if(this.consume(),l)for(var x=0;x<l[0].length;x++){var k=l[0][x];if(!Et[k])throw new M("Unknown accent ' "+k+"'",e);var w=Et[k][this.mode]||Et[k].text;if(!w)throw new M("Accent "+k+" unsupported in "+this.mode+" mode",e);u={type:"accent",mode:this.mode,loc:b0.range(e),label:w,isStretchy:!1,isShifty:!0,base:u}}return u}},d(Q0,"Parser"),Q0);O1.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var ur=d(function(e,t){if(!(typeof e=="string"||e instanceof String))throw new TypeError("KaTeX can only parse string typed expression");var a=new O1(e,t);delete a.gullet.macros.current["\\df@tag"];var i=a.parse();if(delete a.gullet.macros.current["\\current@color"],delete a.gullet.macros.current["\\color"],a.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new M("\\tag works only in display equations");i=[{type:"tag",mode:"text",body:i,tag:a.subparse([new A0("\\df@tag")])}]}return i},"parseTree"),H1=d(function(e,t,a){t.textContent="";var i=or(e,a).toNode();t.appendChild(i)},"render");typeof document<"u"&&document.compatMode!=="CSS1Compat"&&(typeof console<"u"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),H1=d(function(){throw new M("KaTeX doesn't work in quirks mode.")},"render"));var D4=d(function(e,t){var a=or(e,t).toMarkup();return a},"renderToString"),F4=d(function(e,t){var a=new Ut(t);return ur(e,a)},"generateParseTree"),P1=d(function(e,t,a){if(a.throwOnError||!(e instanceof M))throw e;var i=y.makeSpan(["katex-error"],[new f0(t)]);return i.setAttribute("title",e.toString()),i.setAttribute("style","color:"+a.errorColor),i},"renderError"),or=d(function(e,t){var a=new Ut(t);try{var i=ur(e,a);return Ua(i,e,a)}catch(l){return P1(l,e,a)}},"renderToDomTree"),N4=d(function(e,t){var a=new Ut(t);try{var i=ur(e,a);return $a(i,e,a)}catch(l){return P1(l,e,a)}},"renderToHTMLTree"),q4="0.16.22",R4={Span:qe,Anchor:Xt,SymbolNode:f0,SvgNode:F0,PathNode:$0,LineNode:Nt},L4={version:q4,render:H1,renderToString:D4,ParseError:M,SETTINGS_SCHEMA:Qe,__parse:F4,__renderToDomTree:or,__renderToHTMLTree:N4,__setFontMetrics:Yr,__defineSymbol:n,__defineFunction:T,__defineMacro:m,__domTree:R4};export{M as ParseError,Qe as SETTINGS_SCHEMA,T as __defineFunction,m as __defineMacro,n as __defineSymbol,R4 as __domTree,F4 as __parse,or as __renderToDomTree,N4 as __renderToHTMLTree,Yr as __setFontMetrics,L4 as default,H1 as render,D4 as renderToString,q4 as version};
