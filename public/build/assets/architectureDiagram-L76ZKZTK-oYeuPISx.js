import{c as xe}from"./chunk-COUQ6RZ3-CV_BEwpd.js";import{p as ze}from"./chunk-K2ZEYYM2-CnH50PEr.js";import{p as Be}from"./treemap-KMMF4GRG-5JCI3IDA-Dr_hM2q7.js";import{_ as C,a6 as $e,ap as ke,l as Ie,b as Ze,a as qe,q as Qe,t as Je,g as Ke,s as _e,z as je,F as tr,G as er,H as rr,c as ve,au as pe,aF as ue,i as ir,d as ar,j as nr,a5 as ye,aG as or,aH as sr}from"./app-BQZQgfaL.js";import"./chunk-TGZYFRKZ-DbNMVLyI.js";var Ae=ye({"../../node_modules/.pnpm/layout-base@2.0.1/node_modules/layout-base/layout-base.js"(x,I){C(function(b,T){typeof x=="object"&&typeof I=="object"?I.exports=T():typeof define=="function"&&define.amd?define([],T):typeof x=="object"?x.layoutBase=T():b.layoutBase=T()},"webpackUniversalModuleDefinition")(x,function(){return(function(M){var b={};function T(g){if(b[g])return b[g].exports;var l=b[g]={i:g,l:!1,exports:{}};return M[g].call(l.exports,l,l.exports,T),l.l=!0,l.exports}return C(T,"__webpack_require__"),T.m=M,T.c=b,T.i=function(g){return g},T.d=function(g,l,a){T.o(g,l)||Object.defineProperty(g,l,{configurable:!1,enumerable:!0,get:a})},T.n=function(g){var l=g&&g.__esModule?C(function(){return g.default},"getDefault"):C(function(){return g},"getModuleExports");return T.d(l,"a",l),l},T.o=function(g,l){return Object.prototype.hasOwnProperty.call(g,l)},T.p="",T(T.s=28)})([function(M,b,T){function g(){}C(g,"LayoutConstants"),g.QUALITY=1,g.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,g.DEFAULT_INCREMENTAL=!1,g.DEFAULT_ANIMATION_ON_LAYOUT=!0,g.DEFAULT_ANIMATION_DURING_LAYOUT=!1,g.DEFAULT_ANIMATION_PERIOD=50,g.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,g.DEFAULT_GRAPH_MARGIN=15,g.NODE_DIMENSIONS_INCLUDE_LABELS=!1,g.SIMPLE_NODE_SIZE=40,g.SIMPLE_NODE_HALF_SIZE=g.SIMPLE_NODE_SIZE/2,g.EMPTY_COMPOUND_NODE_SIZE=40,g.MIN_EDGE_LENGTH=1,g.WORLD_BOUNDARY=1e6,g.INITIAL_WORLD_BOUNDARY=g.WORLD_BOUNDARY/1e3,g.WORLD_CENTER_X=1200,g.WORLD_CENTER_Y=900,M.exports=g},function(M,b,T){var g=T(2),l=T(8),a=T(9);function e(h,i,u){g.call(this,u),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=u,this.bendpoints=[],this.source=h,this.target=i}C(e,"LEdge"),e.prototype=Object.create(g.prototype);for(var r in g)e[r]=g[r];e.prototype.getSource=function(){return this.source},e.prototype.getTarget=function(){return this.target},e.prototype.isInterGraph=function(){return this.isInterGraph},e.prototype.getLength=function(){return this.length},e.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},e.prototype.getBendpoints=function(){return this.bendpoints},e.prototype.getLca=function(){return this.lca},e.prototype.getSourceInLca=function(){return this.sourceInLca},e.prototype.getTargetInLca=function(){return this.targetInLca},e.prototype.getOtherEnd=function(h){if(this.source===h)return this.target;if(this.target===h)return this.source;throw"Node is not incident with this edge"},e.prototype.getOtherEndInGraph=function(h,i){for(var u=this.getOtherEnd(h),t=i.getGraphManager().getRoot();;){if(u.getOwner()==i)return u;if(u.getOwner()==t)break;u=u.getOwner().getParent()}return null},e.prototype.updateLength=function(){var h=new Array(4);this.isOverlapingSourceAndTarget=l.getIntersection(this.target.getRect(),this.source.getRect(),h),this.isOverlapingSourceAndTarget||(this.lengthX=h[0]-h[2],this.lengthY=h[1]-h[3],Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},e.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},M.exports=e},function(M,b,T){function g(l){this.vGraphObject=l}C(g,"LGraphObject"),M.exports=g},function(M,b,T){var g=T(2),l=T(10),a=T(13),e=T(0),r=T(16),h=T(5);function i(t,o,s,c){s==null&&c==null&&(c=o),g.call(this,c),t.graphManager!=null&&(t=t.graphManager),this.estimatedSize=l.MIN_VALUE,this.inclusionTreeDepth=l.MAX_VALUE,this.vGraphObject=c,this.edges=[],this.graphManager=t,s!=null&&o!=null?this.rect=new a(o.x,o.y,s.width,s.height):this.rect=new a}C(i,"LNode"),i.prototype=Object.create(g.prototype);for(var u in g)i[u]=g[u];i.prototype.getEdges=function(){return this.edges},i.prototype.getChild=function(){return this.child},i.prototype.getOwner=function(){return this.owner},i.prototype.getWidth=function(){return this.rect.width},i.prototype.setWidth=function(t){this.rect.width=t},i.prototype.getHeight=function(){return this.rect.height},i.prototype.setHeight=function(t){this.rect.height=t},i.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},i.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},i.prototype.getCenter=function(){return new h(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},i.prototype.getLocation=function(){return new h(this.rect.x,this.rect.y)},i.prototype.getRect=function(){return this.rect},i.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},i.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},i.prototype.setRect=function(t,o){this.rect.x=t.x,this.rect.y=t.y,this.rect.width=o.width,this.rect.height=o.height},i.prototype.setCenter=function(t,o){this.rect.x=t-this.rect.width/2,this.rect.y=o-this.rect.height/2},i.prototype.setLocation=function(t,o){this.rect.x=t,this.rect.y=o},i.prototype.moveBy=function(t,o){this.rect.x+=t,this.rect.y+=o},i.prototype.getEdgeListToNode=function(t){var o=[],s=this;return s.edges.forEach(function(c){if(c.target==t){if(c.source!=s)throw"Incorrect edge source!";o.push(c)}}),o},i.prototype.getEdgesBetween=function(t){var o=[],s=this;return s.edges.forEach(function(c){if(!(c.source==s||c.target==s))throw"Incorrect edge source and/or target";(c.target==t||c.source==t)&&o.push(c)}),o},i.prototype.getNeighborsList=function(){var t=new Set,o=this;return o.edges.forEach(function(s){if(s.source==o)t.add(s.target);else{if(s.target!=o)throw"Incorrect incidency!";t.add(s.source)}}),t},i.prototype.withChildren=function(){var t=new Set,o,s;if(t.add(this),this.child!=null)for(var c=this.child.getNodes(),f=0;f<c.length;f++)o=c[f],s=o.withChildren(),s.forEach(function(N){t.add(N)});return t},i.prototype.getNoOfChildren=function(){var t=0,o;if(this.child==null)t=1;else for(var s=this.child.getNodes(),c=0;c<s.length;c++)o=s[c],t+=o.getNoOfChildren();return t==0&&(t=1),t},i.prototype.getEstimatedSize=function(){if(this.estimatedSize==l.MIN_VALUE)throw"assert failed";return this.estimatedSize},i.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},i.prototype.scatter=function(){var t,o,s=-e.INITIAL_WORLD_BOUNDARY,c=e.INITIAL_WORLD_BOUNDARY;t=e.WORLD_CENTER_X+r.nextDouble()*(c-s)+s;var f=-e.INITIAL_WORLD_BOUNDARY,N=e.INITIAL_WORLD_BOUNDARY;o=e.WORLD_CENTER_Y+r.nextDouble()*(N-f)+f,this.rect.x=t,this.rect.y=o},i.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var t=this.getChild();if(t.updateBounds(!0),this.rect.x=t.getLeft(),this.rect.y=t.getTop(),this.setWidth(t.getRight()-t.getLeft()),this.setHeight(t.getBottom()-t.getTop()),e.NODE_DIMENSIONS_INCLUDE_LABELS){var o=t.getRight()-t.getLeft(),s=t.getBottom()-t.getTop();this.labelWidth&&(this.labelPosHorizontal=="left"?(this.rect.x-=this.labelWidth,this.setWidth(o+this.labelWidth)):this.labelPosHorizontal=="center"&&this.labelWidth>o?(this.rect.x-=(this.labelWidth-o)/2,this.setWidth(this.labelWidth)):this.labelPosHorizontal=="right"&&this.setWidth(o+this.labelWidth)),this.labelHeight&&(this.labelPosVertical=="top"?(this.rect.y-=this.labelHeight,this.setHeight(s+this.labelHeight)):this.labelPosVertical=="center"&&this.labelHeight>s?(this.rect.y-=(this.labelHeight-s)/2,this.setHeight(this.labelHeight)):this.labelPosVertical=="bottom"&&this.setHeight(s+this.labelHeight))}}},i.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==l.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},i.prototype.transform=function(t){var o=this.rect.x;o>e.WORLD_BOUNDARY?o=e.WORLD_BOUNDARY:o<-e.WORLD_BOUNDARY&&(o=-e.WORLD_BOUNDARY);var s=this.rect.y;s>e.WORLD_BOUNDARY?s=e.WORLD_BOUNDARY:s<-e.WORLD_BOUNDARY&&(s=-e.WORLD_BOUNDARY);var c=new h(o,s),f=t.inverseTransformPoint(c);this.setLocation(f.x,f.y)},i.prototype.getLeft=function(){return this.rect.x},i.prototype.getRight=function(){return this.rect.x+this.rect.width},i.prototype.getTop=function(){return this.rect.y},i.prototype.getBottom=function(){return this.rect.y+this.rect.height},i.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},M.exports=i},function(M,b,T){var g=T(0);function l(){}C(l,"FDLayoutConstants");for(var a in g)l[a]=g[a];l.MAX_ITERATIONS=2500,l.DEFAULT_EDGE_LENGTH=50,l.DEFAULT_SPRING_STRENGTH=.45,l.DEFAULT_REPULSION_STRENGTH=4500,l.DEFAULT_GRAVITY_STRENGTH=.4,l.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,l.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,l.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,l.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,l.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,l.COOLING_ADAPTATION_FACTOR=.33,l.ADAPTATION_LOWER_NODE_LIMIT=1e3,l.ADAPTATION_UPPER_NODE_LIMIT=5e3,l.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,l.MAX_NODE_DISPLACEMENT=l.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,l.MIN_REPULSION_DIST=l.DEFAULT_EDGE_LENGTH/10,l.CONVERGENCE_CHECK_PERIOD=100,l.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,l.MIN_EDGE_LENGTH=1,l.GRID_CALCULATION_CHECK_PERIOD=10,M.exports=l},function(M,b,T){function g(l,a){l==null&&a==null?(this.x=0,this.y=0):(this.x=l,this.y=a)}C(g,"PointD"),g.prototype.getX=function(){return this.x},g.prototype.getY=function(){return this.y},g.prototype.setX=function(l){this.x=l},g.prototype.setY=function(l){this.y=l},g.prototype.getDifference=function(l){return new DimensionD(this.x-l.x,this.y-l.y)},g.prototype.getCopy=function(){return new g(this.x,this.y)},g.prototype.translate=function(l){return this.x+=l.width,this.y+=l.height,this},M.exports=g},function(M,b,T){var g=T(2),l=T(10),a=T(0),e=T(7),r=T(3),h=T(1),i=T(13),u=T(12),t=T(11);function o(c,f,N){g.call(this,N),this.estimatedSize=l.MIN_VALUE,this.margin=a.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=c,f!=null&&f instanceof e?this.graphManager=f:f!=null&&f instanceof Layout&&(this.graphManager=f.graphManager)}C(o,"LGraph"),o.prototype=Object.create(g.prototype);for(var s in g)o[s]=g[s];o.prototype.getNodes=function(){return this.nodes},o.prototype.getEdges=function(){return this.edges},o.prototype.getGraphManager=function(){return this.graphManager},o.prototype.getParent=function(){return this.parent},o.prototype.getLeft=function(){return this.left},o.prototype.getRight=function(){return this.right},o.prototype.getTop=function(){return this.top},o.prototype.getBottom=function(){return this.bottom},o.prototype.isConnected=function(){return this.isConnected},o.prototype.add=function(c,f,N){if(f==null&&N==null){var d=c;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(d)>-1)throw"Node already in graph!";return d.owner=this,this.getNodes().push(d),d}else{var v=c;if(!(this.getNodes().indexOf(f)>-1&&this.getNodes().indexOf(N)>-1))throw"Source or target not in graph!";if(!(f.owner==N.owner&&f.owner==this))throw"Both owners must be this graph!";return f.owner!=N.owner?null:(v.source=f,v.target=N,v.isInterGraph=!1,this.getEdges().push(v),f.edges.push(v),N!=f&&N.edges.push(v),v)}},o.prototype.remove=function(c){var f=c;if(c instanceof r){if(f==null)throw"Node is null!";if(!(f.owner!=null&&f.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var N=f.edges.slice(),d,v=N.length,L=0;L<v;L++)d=N[L],d.isInterGraph?this.graphManager.remove(d):d.source.owner.remove(d);var G=this.nodes.indexOf(f);if(G==-1)throw"Node not in owner node list!";this.nodes.splice(G,1)}else if(c instanceof h){var d=c;if(d==null)throw"Edge is null!";if(!(d.source!=null&&d.target!=null))throw"Source and/or target is null!";if(!(d.source.owner!=null&&d.target.owner!=null&&d.source.owner==this&&d.target.owner==this))throw"Source and/or target owner is invalid!";var A=d.source.edges.indexOf(d),U=d.target.edges.indexOf(d);if(!(A>-1&&U>-1))throw"Source and/or target doesn't know this edge!";d.source.edges.splice(A,1),d.target!=d.source&&d.target.edges.splice(U,1);var G=d.source.owner.getEdges().indexOf(d);if(G==-1)throw"Not in owner's edge list!";d.source.owner.getEdges().splice(G,1)}},o.prototype.updateLeftTop=function(){for(var c=l.MAX_VALUE,f=l.MAX_VALUE,N,d,v,L=this.getNodes(),G=L.length,A=0;A<G;A++){var U=L[A];N=U.getTop(),d=U.getLeft(),c>N&&(c=N),f>d&&(f=d)}return c==l.MAX_VALUE?null:(L[0].getParent().paddingLeft!=null?v=L[0].getParent().paddingLeft:v=this.margin,this.left=f-v,this.top=c-v,new u(this.left,this.top))},o.prototype.updateBounds=function(c){for(var f=l.MAX_VALUE,N=-l.MAX_VALUE,d=l.MAX_VALUE,v=-l.MAX_VALUE,L,G,A,U,q,X=this.nodes,_=X.length,D=0;D<_;D++){var at=X[D];c&&at.child!=null&&at.updateBounds(),L=at.getLeft(),G=at.getRight(),A=at.getTop(),U=at.getBottom(),f>L&&(f=L),N<G&&(N=G),d>A&&(d=A),v<U&&(v=U)}var n=new i(f,d,N-f,v-d);f==l.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),X[0].getParent().paddingLeft!=null?q=X[0].getParent().paddingLeft:q=this.margin,this.left=n.x-q,this.right=n.x+n.width+q,this.top=n.y-q,this.bottom=n.y+n.height+q},o.calculateBounds=function(c){for(var f=l.MAX_VALUE,N=-l.MAX_VALUE,d=l.MAX_VALUE,v=-l.MAX_VALUE,L,G,A,U,q=c.length,X=0;X<q;X++){var _=c[X];L=_.getLeft(),G=_.getRight(),A=_.getTop(),U=_.getBottom(),f>L&&(f=L),N<G&&(N=G),d>A&&(d=A),v<U&&(v=U)}var D=new i(f,d,N-f,v-d);return D},o.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},o.prototype.getEstimatedSize=function(){if(this.estimatedSize==l.MIN_VALUE)throw"assert failed";return this.estimatedSize},o.prototype.calcEstimatedSize=function(){for(var c=0,f=this.nodes,N=f.length,d=0;d<N;d++){var v=f[d];c+=v.calcEstimatedSize()}return c==0?this.estimatedSize=a.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=c/Math.sqrt(this.nodes.length),this.estimatedSize},o.prototype.updateConnected=function(){var c=this;if(this.nodes.length==0){this.isConnected=!0;return}var f=new t,N=new Set,d=this.nodes[0],v,L,G=d.withChildren();for(G.forEach(function(D){f.push(D),N.add(D)});f.length!==0;){d=f.shift(),v=d.getEdges();for(var A=v.length,U=0;U<A;U++){var q=v[U];if(L=q.getOtherEndInGraph(d,this),L!=null&&!N.has(L)){var X=L.withChildren();X.forEach(function(D){f.push(D),N.add(D)})}}}if(this.isConnected=!1,N.size>=this.nodes.length){var _=0;N.forEach(function(D){D.owner==c&&_++}),_==this.nodes.length&&(this.isConnected=!0)}},M.exports=o},function(M,b,T){var g,l=T(1);function a(e){g=T(6),this.layout=e,this.graphs=[],this.edges=[]}C(a,"LGraphManager"),a.prototype.addRoot=function(){var e=this.layout.newGraph(),r=this.layout.newNode(null),h=this.add(e,r);return this.setRootGraph(h),this.rootGraph},a.prototype.add=function(e,r,h,i,u){if(h==null&&i==null&&u==null){if(e==null)throw"Graph is null!";if(r==null)throw"Parent node is null!";if(this.graphs.indexOf(e)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(e),e.parent!=null)throw"Already has a parent!";if(r.child!=null)throw"Already has a child!";return e.parent=r,r.child=e,e}else{u=h,i=r,h=e;var t=i.getOwner(),o=u.getOwner();if(!(t!=null&&t.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(o!=null&&o.getGraphManager()==this))throw"Target not in this graph mgr!";if(t==o)return h.isInterGraph=!1,t.add(h,i,u);if(h.isInterGraph=!0,h.source=i,h.target=u,this.edges.indexOf(h)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(h),!(h.source!=null&&h.target!=null))throw"Edge source and/or target is null!";if(!(h.source.edges.indexOf(h)==-1&&h.target.edges.indexOf(h)==-1))throw"Edge already in source and/or target incidency list!";return h.source.edges.push(h),h.target.edges.push(h),h}},a.prototype.remove=function(e){if(e instanceof g){var r=e;if(r.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(r==this.rootGraph||r.parent!=null&&r.parent.graphManager==this))throw"Invalid parent node!";var h=[];h=h.concat(r.getEdges());for(var i,u=h.length,t=0;t<u;t++)i=h[t],r.remove(i);var o=[];o=o.concat(r.getNodes());var s;u=o.length;for(var t=0;t<u;t++)s=o[t],r.remove(s);r==this.rootGraph&&this.setRootGraph(null);var c=this.graphs.indexOf(r);this.graphs.splice(c,1),r.parent=null}else if(e instanceof l){if(i=e,i==null)throw"Edge is null!";if(!i.isInterGraph)throw"Not an inter-graph edge!";if(!(i.source!=null&&i.target!=null))throw"Source and/or target is null!";if(!(i.source.edges.indexOf(i)!=-1&&i.target.edges.indexOf(i)!=-1))throw"Source and/or target doesn't know this edge!";var c=i.source.edges.indexOf(i);if(i.source.edges.splice(c,1),c=i.target.edges.indexOf(i),i.target.edges.splice(c,1),!(i.source.owner!=null&&i.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(i.source.owner.getGraphManager().edges.indexOf(i)==-1)throw"Not in owner graph manager's edge list!";var c=i.source.owner.getGraphManager().edges.indexOf(i);i.source.owner.getGraphManager().edges.splice(c,1)}},a.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},a.prototype.getGraphs=function(){return this.graphs},a.prototype.getAllNodes=function(){if(this.allNodes==null){for(var e=[],r=this.getGraphs(),h=r.length,i=0;i<h;i++)e=e.concat(r[i].getNodes());this.allNodes=e}return this.allNodes},a.prototype.resetAllNodes=function(){this.allNodes=null},a.prototype.resetAllEdges=function(){this.allEdges=null},a.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},a.prototype.getAllEdges=function(){if(this.allEdges==null){var e=[],r=this.getGraphs();r.length;for(var h=0;h<r.length;h++)e=e.concat(r[h].getEdges());e=e.concat(this.edges),this.allEdges=e}return this.allEdges},a.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},a.prototype.setAllNodesToApplyGravitation=function(e){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=e},a.prototype.getRoot=function(){return this.rootGraph},a.prototype.setRootGraph=function(e){if(e.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=e,e.parent==null&&(e.parent=this.layout.newNode("Root node"))},a.prototype.getLayout=function(){return this.layout},a.prototype.isOneAncestorOfOther=function(e,r){if(!(e!=null&&r!=null))throw"assert failed";if(e==r)return!0;var h=e.getOwner(),i;do{if(i=h.getParent(),i==null)break;if(i==r)return!0;if(h=i.getOwner(),h==null)break}while(!0);h=r.getOwner();do{if(i=h.getParent(),i==null)break;if(i==e)return!0;if(h=i.getOwner(),h==null)break}while(!0);return!1},a.prototype.calcLowestCommonAncestors=function(){for(var e,r,h,i,u,t=this.getAllEdges(),o=t.length,s=0;s<o;s++){if(e=t[s],r=e.source,h=e.target,e.lca=null,e.sourceInLca=r,e.targetInLca=h,r==h){e.lca=r.getOwner();continue}for(i=r.getOwner();e.lca==null;){for(e.targetInLca=h,u=h.getOwner();e.lca==null;){if(u==i){e.lca=u;break}if(u==this.rootGraph)break;if(e.lca!=null)throw"assert failed";e.targetInLca=u.getParent(),u=e.targetInLca.getOwner()}if(i==this.rootGraph)break;e.lca==null&&(e.sourceInLca=i.getParent(),i=e.sourceInLca.getOwner())}if(e.lca==null)throw"assert failed"}},a.prototype.calcLowestCommonAncestor=function(e,r){if(e==r)return e.getOwner();var h=e.getOwner();do{if(h==null)break;var i=r.getOwner();do{if(i==null)break;if(i==h)return i;i=i.getParent().getOwner()}while(!0);h=h.getParent().getOwner()}while(!0);return h},a.prototype.calcInclusionTreeDepths=function(e,r){e==null&&r==null&&(e=this.rootGraph,r=1);for(var h,i=e.getNodes(),u=i.length,t=0;t<u;t++)h=i[t],h.inclusionTreeDepth=r,h.child!=null&&this.calcInclusionTreeDepths(h.child,r+1)},a.prototype.includesInvalidEdge=function(){for(var e,r=[],h=this.edges.length,i=0;i<h;i++)e=this.edges[i],this.isOneAncestorOfOther(e.source,e.target)&&r.push(e);for(var i=0;i<r.length;i++)this.remove(r[i]);return!1},M.exports=a},function(M,b,T){var g=T(12);function l(){}C(l,"IGeometry"),l.calcSeparationAmount=function(a,e,r,h){if(!a.intersects(e))throw"assert failed";var i=new Array(2);this.decideDirectionsForOverlappingNodes(a,e,i),r[0]=Math.min(a.getRight(),e.getRight())-Math.max(a.x,e.x),r[1]=Math.min(a.getBottom(),e.getBottom())-Math.max(a.y,e.y),a.getX()<=e.getX()&&a.getRight()>=e.getRight()?r[0]+=Math.min(e.getX()-a.getX(),a.getRight()-e.getRight()):e.getX()<=a.getX()&&e.getRight()>=a.getRight()&&(r[0]+=Math.min(a.getX()-e.getX(),e.getRight()-a.getRight())),a.getY()<=e.getY()&&a.getBottom()>=e.getBottom()?r[1]+=Math.min(e.getY()-a.getY(),a.getBottom()-e.getBottom()):e.getY()<=a.getY()&&e.getBottom()>=a.getBottom()&&(r[1]+=Math.min(a.getY()-e.getY(),e.getBottom()-a.getBottom()));var u=Math.abs((e.getCenterY()-a.getCenterY())/(e.getCenterX()-a.getCenterX()));e.getCenterY()===a.getCenterY()&&e.getCenterX()===a.getCenterX()&&(u=1);var t=u*r[0],o=r[1]/u;r[0]<o?o=r[0]:t=r[1],r[0]=-1*i[0]*(o/2+h),r[1]=-1*i[1]*(t/2+h)},l.decideDirectionsForOverlappingNodes=function(a,e,r){a.getCenterX()<e.getCenterX()?r[0]=-1:r[0]=1,a.getCenterY()<e.getCenterY()?r[1]=-1:r[1]=1},l.getIntersection2=function(a,e,r){var h=a.getCenterX(),i=a.getCenterY(),u=e.getCenterX(),t=e.getCenterY();if(a.intersects(e))return r[0]=h,r[1]=i,r[2]=u,r[3]=t,!0;var o=a.getX(),s=a.getY(),c=a.getRight(),f=a.getX(),N=a.getBottom(),d=a.getRight(),v=a.getWidthHalf(),L=a.getHeightHalf(),G=e.getX(),A=e.getY(),U=e.getRight(),q=e.getX(),X=e.getBottom(),_=e.getRight(),D=e.getWidthHalf(),at=e.getHeightHalf(),n=!1,E=!1;if(h===u){if(i>t)return r[0]=h,r[1]=s,r[2]=u,r[3]=X,!1;if(i<t)return r[0]=h,r[1]=N,r[2]=u,r[3]=A,!1}else if(i===t){if(h>u)return r[0]=o,r[1]=i,r[2]=U,r[3]=t,!1;if(h<u)return r[0]=c,r[1]=i,r[2]=G,r[3]=t,!1}else{var p=a.height/a.width,m=e.height/e.width,y=(t-i)/(u-h),S=void 0,w=void 0,F=void 0,V=void 0,R=void 0,Q=void 0;if(-p===y?h>u?(r[0]=f,r[1]=N,n=!0):(r[0]=c,r[1]=s,n=!0):p===y&&(h>u?(r[0]=o,r[1]=s,n=!0):(r[0]=d,r[1]=N,n=!0)),-m===y?u>h?(r[2]=q,r[3]=X,E=!0):(r[2]=U,r[3]=A,E=!0):m===y&&(u>h?(r[2]=G,r[3]=A,E=!0):(r[2]=_,r[3]=X,E=!0)),n&&E)return!1;if(h>u?i>t?(S=this.getCardinalDirection(p,y,4),w=this.getCardinalDirection(m,y,2)):(S=this.getCardinalDirection(-p,y,3),w=this.getCardinalDirection(-m,y,1)):i>t?(S=this.getCardinalDirection(-p,y,1),w=this.getCardinalDirection(-m,y,3)):(S=this.getCardinalDirection(p,y,2),w=this.getCardinalDirection(m,y,4)),!n)switch(S){case 1:V=s,F=h+-L/y,r[0]=F,r[1]=V;break;case 2:F=d,V=i+v*y,r[0]=F,r[1]=V;break;case 3:V=N,F=h+L/y,r[0]=F,r[1]=V;break;case 4:F=f,V=i+-v*y,r[0]=F,r[1]=V;break}if(!E)switch(w){case 1:Q=A,R=u+-at/y,r[2]=R,r[3]=Q;break;case 2:R=_,Q=t+D*y,r[2]=R,r[3]=Q;break;case 3:Q=X,R=u+at/y,r[2]=R,r[3]=Q;break;case 4:R=q,Q=t+-D*y,r[2]=R,r[3]=Q;break}}return!1},l.getCardinalDirection=function(a,e,r){return a>e?r:1+r%4},l.getIntersection=function(a,e,r,h){if(h==null)return this.getIntersection2(a,e,r);var i=a.x,u=a.y,t=e.x,o=e.y,s=r.x,c=r.y,f=h.x,N=h.y,d=void 0,v=void 0,L=void 0,G=void 0,A=void 0,U=void 0,q=void 0,X=void 0,_=void 0;return L=o-u,A=i-t,q=t*u-i*o,G=N-c,U=s-f,X=f*c-s*N,_=L*U-G*A,_===0?null:(d=(A*X-U*q)/_,v=(G*q-L*X)/_,new g(d,v))},l.angleOfVector=function(a,e,r,h){var i=void 0;return a!==r?(i=Math.atan((h-e)/(r-a)),r<a?i+=Math.PI:h<e&&(i+=this.TWO_PI)):h<e?i=this.ONE_AND_HALF_PI:i=this.HALF_PI,i},l.doIntersect=function(a,e,r,h){var i=a.x,u=a.y,t=e.x,o=e.y,s=r.x,c=r.y,f=h.x,N=h.y,d=(t-i)*(N-c)-(f-s)*(o-u);if(d===0)return!1;var v=((N-c)*(f-i)+(s-f)*(N-u))/d,L=((u-o)*(f-i)+(t-i)*(N-u))/d;return 0<v&&v<1&&0<L&&L<1},l.findCircleLineIntersections=function(a,e,r,h,i,u,t){var o=(r-a)*(r-a)+(h-e)*(h-e),s=2*((a-i)*(r-a)+(e-u)*(h-e)),c=(a-i)*(a-i)+(e-u)*(e-u)-t*t,f=s*s-4*o*c;if(f>=0){var N=(-s+Math.sqrt(s*s-4*o*c))/(2*o),d=(-s-Math.sqrt(s*s-4*o*c))/(2*o),v=null;return N>=0&&N<=1?[N]:d>=0&&d<=1?[d]:v}else return null},l.HALF_PI=.5*Math.PI,l.ONE_AND_HALF_PI=1.5*Math.PI,l.TWO_PI=2*Math.PI,l.THREE_PI=3*Math.PI,M.exports=l},function(M,b,T){function g(){}C(g,"IMath"),g.sign=function(l){return l>0?1:l<0?-1:0},g.floor=function(l){return l<0?Math.ceil(l):Math.floor(l)},g.ceil=function(l){return l<0?Math.floor(l):Math.ceil(l)},M.exports=g},function(M,b,T){function g(){}C(g,"Integer"),g.MAX_VALUE=2147483647,g.MIN_VALUE=-2147483648,M.exports=g},function(M,b,T){var g=(function(){function i(u,t){for(var o=0;o<t.length;o++){var s=t[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(u,s.key,s)}}return C(i,"defineProperties"),function(u,t,o){return t&&i(u.prototype,t),o&&i(u,o),u}})();function l(i,u){if(!(i instanceof u))throw new TypeError("Cannot call a class as a function")}C(l,"_classCallCheck");var a=C(function(u){return{value:u,next:null,prev:null}},"nodeFrom"),e=C(function(u,t,o,s){return u!==null?u.next=t:s.head=t,o!==null?o.prev=t:s.tail=t,t.prev=u,t.next=o,s.length++,t},"add"),r=C(function(u,t){var o=u.prev,s=u.next;return o!==null?o.next=s:t.head=s,s!==null?s.prev=o:t.tail=o,u.prev=u.next=null,t.length--,u},"_remove"),h=(function(){function i(u){var t=this;l(this,i),this.length=0,this.head=null,this.tail=null,u?.forEach(function(o){return t.push(o)})}return C(i,"LinkedList"),g(i,[{key:"size",value:C(function(){return this.length},"size")},{key:"insertBefore",value:C(function(t,o){return e(o.prev,a(t),o,this)},"insertBefore")},{key:"insertAfter",value:C(function(t,o){return e(o,a(t),o.next,this)},"insertAfter")},{key:"insertNodeBefore",value:C(function(t,o){return e(o.prev,t,o,this)},"insertNodeBefore")},{key:"insertNodeAfter",value:C(function(t,o){return e(o,t,o.next,this)},"insertNodeAfter")},{key:"push",value:C(function(t){return e(this.tail,a(t),null,this)},"push")},{key:"unshift",value:C(function(t){return e(null,a(t),this.head,this)},"unshift")},{key:"remove",value:C(function(t){return r(t,this)},"remove")},{key:"pop",value:C(function(){return r(this.tail,this).value},"pop")},{key:"popNode",value:C(function(){return r(this.tail,this)},"popNode")},{key:"shift",value:C(function(){return r(this.head,this).value},"shift")},{key:"shiftNode",value:C(function(){return r(this.head,this)},"shiftNode")},{key:"get_object_at",value:C(function(t){if(t<=this.length()){for(var o=1,s=this.head;o<t;)s=s.next,o++;return s.value}},"get_object_at")},{key:"set_object_at",value:C(function(t,o){if(t<=this.length()){for(var s=1,c=this.head;s<t;)c=c.next,s++;c.value=o}},"set_object_at")}]),i})();M.exports=h},function(M,b,T){function g(l,a,e){this.x=null,this.y=null,l==null&&a==null&&e==null?(this.x=0,this.y=0):typeof l=="number"&&typeof a=="number"&&e==null?(this.x=l,this.y=a):l.constructor.name=="Point"&&a==null&&e==null&&(e=l,this.x=e.x,this.y=e.y)}C(g,"Point"),g.prototype.getX=function(){return this.x},g.prototype.getY=function(){return this.y},g.prototype.getLocation=function(){return new g(this.x,this.y)},g.prototype.setLocation=function(l,a,e){l.constructor.name=="Point"&&a==null&&e==null?(e=l,this.setLocation(e.x,e.y)):typeof l=="number"&&typeof a=="number"&&e==null&&(parseInt(l)==l&&parseInt(a)==a?this.move(l,a):(this.x=Math.floor(l+.5),this.y=Math.floor(a+.5)))},g.prototype.move=function(l,a){this.x=l,this.y=a},g.prototype.translate=function(l,a){this.x+=l,this.y+=a},g.prototype.equals=function(l){if(l.constructor.name=="Point"){var a=l;return this.x==a.x&&this.y==a.y}return this==l},g.prototype.toString=function(){return new g().constructor.name+"[x="+this.x+",y="+this.y+"]"},M.exports=g},function(M,b,T){function g(l,a,e,r){this.x=0,this.y=0,this.width=0,this.height=0,l!=null&&a!=null&&e!=null&&r!=null&&(this.x=l,this.y=a,this.width=e,this.height=r)}C(g,"RectangleD"),g.prototype.getX=function(){return this.x},g.prototype.setX=function(l){this.x=l},g.prototype.getY=function(){return this.y},g.prototype.setY=function(l){this.y=l},g.prototype.getWidth=function(){return this.width},g.prototype.setWidth=function(l){this.width=l},g.prototype.getHeight=function(){return this.height},g.prototype.setHeight=function(l){this.height=l},g.prototype.getRight=function(){return this.x+this.width},g.prototype.getBottom=function(){return this.y+this.height},g.prototype.intersects=function(l){return!(this.getRight()<l.x||this.getBottom()<l.y||l.getRight()<this.x||l.getBottom()<this.y)},g.prototype.getCenterX=function(){return this.x+this.width/2},g.prototype.getMinX=function(){return this.getX()},g.prototype.getMaxX=function(){return this.getX()+this.width},g.prototype.getCenterY=function(){return this.y+this.height/2},g.prototype.getMinY=function(){return this.getY()},g.prototype.getMaxY=function(){return this.getY()+this.height},g.prototype.getWidthHalf=function(){return this.width/2},g.prototype.getHeightHalf=function(){return this.height/2},M.exports=g},function(M,b,T){var g=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a};function l(){}C(l,"UniqueIDGeneretor"),l.lastID=0,l.createID=function(a){return l.isPrimitive(a)?a:(a.uniqueID!=null||(a.uniqueID=l.getString(),l.lastID++),a.uniqueID)},l.getString=function(a){return a==null&&(a=l.lastID),"Object#"+a},l.isPrimitive=function(a){var e=typeof a>"u"?"undefined":g(a);return a==null||e!="object"&&e!="function"},M.exports=l},function(M,b,T){function g(s){if(Array.isArray(s)){for(var c=0,f=Array(s.length);c<s.length;c++)f[c]=s[c];return f}else return Array.from(s)}C(g,"_toConsumableArray");var l=T(0),a=T(7),e=T(3),r=T(1),h=T(6),i=T(5),u=T(17),t=T(29);function o(s){t.call(this),this.layoutQuality=l.QUALITY,this.createBendsAsNeeded=l.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=l.DEFAULT_INCREMENTAL,this.animationOnLayout=l.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=l.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=l.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=l.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new a(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,s!=null&&(this.isRemoteUse=s)}C(o,"Layout"),o.RANDOM_SEED=1,o.prototype=Object.create(t.prototype),o.prototype.getGraphManager=function(){return this.graphManager},o.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},o.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},o.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},o.prototype.newGraphManager=function(){var s=new a(this);return this.graphManager=s,s},o.prototype.newGraph=function(s){return new h(null,this.graphManager,s)},o.prototype.newNode=function(s){return new e(this.graphManager,s)},o.prototype.newEdge=function(s){return new r(null,null,s)},o.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},o.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var s;return this.checkLayoutSuccess()?s=!1:s=this.layout(),l.ANIMATE==="during"?!1:(s&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,s)},o.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},o.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var s=this.graphManager.getAllEdges(),c=0;c<s.length;c++)s[c];for(var f=this.graphManager.getRoot().getNodes(),c=0;c<f.length;c++)f[c];this.update(this.graphManager.getRoot())}},o.prototype.update=function(s){if(s==null)this.update2();else if(s instanceof e){var c=s;if(c.getChild()!=null)for(var f=c.getChild().getNodes(),N=0;N<f.length;N++)update(f[N]);if(c.vGraphObject!=null){var d=c.vGraphObject;d.update(c)}}else if(s instanceof r){var v=s;if(v.vGraphObject!=null){var L=v.vGraphObject;L.update(v)}}else if(s instanceof h){var G=s;if(G.vGraphObject!=null){var A=G.vGraphObject;A.update(G)}}},o.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=l.QUALITY,this.animationDuringLayout=l.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=l.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=l.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=l.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=l.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=l.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},o.prototype.transform=function(s){if(s==null)this.transform(new i(0,0));else{var c=new u,f=this.graphManager.getRoot().updateLeftTop();if(f!=null){c.setWorldOrgX(s.x),c.setWorldOrgY(s.y),c.setDeviceOrgX(f.x),c.setDeviceOrgY(f.y);for(var N=this.getAllNodes(),d,v=0;v<N.length;v++)d=N[v],d.transform(c)}}},o.prototype.positionNodesRandomly=function(s){if(s==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var c,f,N=s.getNodes(),d=0;d<N.length;d++)c=N[d],f=c.getChild(),f==null||f.getNodes().length==0?c.scatter():(this.positionNodesRandomly(f),c.updateBounds())},o.prototype.getFlatForest=function(){for(var s=[],c=!0,f=this.graphManager.getRoot().getNodes(),N=!0,d=0;d<f.length;d++)f[d].getChild()!=null&&(N=!1);if(!N)return s;var v=new Set,L=[],G=new Map,A=[];for(A=A.concat(f);A.length>0&&c;){for(L.push(A[0]);L.length>0&&c;){var U=L[0];L.splice(0,1),v.add(U);for(var q=U.getEdges(),d=0;d<q.length;d++){var X=q[d].getOtherEnd(U);if(G.get(U)!=X)if(!v.has(X))L.push(X),G.set(X,U);else{c=!1;break}}}if(!c)s=[];else{var _=[].concat(g(v));s.push(_);for(var d=0;d<_.length;d++){var D=_[d],at=A.indexOf(D);at>-1&&A.splice(at,1)}v=new Set,G=new Map}}return s},o.prototype.createDummyNodesForBendpoints=function(s){for(var c=[],f=s.source,N=this.graphManager.calcLowestCommonAncestor(s.source,s.target),d=0;d<s.bendpoints.length;d++){var v=this.newNode(null);v.setRect(new Point(0,0),new Dimension(1,1)),N.add(v);var L=this.newEdge(null);this.graphManager.add(L,f,v),c.add(v),f=v}var L=this.newEdge(null);return this.graphManager.add(L,f,s.target),this.edgeToDummyNodes.set(s,c),s.isInterGraph()?this.graphManager.remove(s):N.remove(s),c},o.prototype.createBendpointsFromDummyNodes=function(){var s=[];s=s.concat(this.graphManager.getAllEdges()),s=[].concat(g(this.edgeToDummyNodes.keys())).concat(s);for(var c=0;c<s.length;c++){var f=s[c];if(f.bendpoints.length>0){for(var N=this.edgeToDummyNodes.get(f),d=0;d<N.length;d++){var v=N[d],L=new i(v.getCenterX(),v.getCenterY()),G=f.bendpoints.get(d);G.x=L.x,G.y=L.y,v.getOwner().remove(v)}this.graphManager.add(f,f.source,f.target)}}},o.transform=function(s,c,f,N){if(f!=null&&N!=null){var d=c;if(s<=50){var v=c/f;d-=(c-v)/50*(50-s)}else{var L=c*N;d+=(L-c)/50*(s-50)}return d}else{var G,A;return s<=50?(G=9*c/500,A=c/10):(G=9*c/50,A=-8*c),G*s+A}},o.findCenterOfTree=function(s){var c=[];c=c.concat(s);var f=[],N=new Map,d=!1,v=null;(c.length==1||c.length==2)&&(d=!0,v=c[0]);for(var L=0;L<c.length;L++){var G=c[L],A=G.getNeighborsList().size;N.set(G,G.getNeighborsList().size),A==1&&f.push(G)}var U=[];for(U=U.concat(f);!d;){var q=[];q=q.concat(U),U=[];for(var L=0;L<c.length;L++){var G=c[L],X=c.indexOf(G);X>=0&&c.splice(X,1);var _=G.getNeighborsList();_.forEach(function(n){if(f.indexOf(n)<0){var E=N.get(n),p=E-1;p==1&&U.push(n),N.set(n,p)}})}f=f.concat(U),(c.length==1||c.length==2)&&(d=!0,v=c[0])}return v},o.prototype.setGraphManager=function(s){this.graphManager=s},M.exports=o},function(M,b,T){function g(){}C(g,"RandomSeed"),g.seed=1,g.x=0,g.nextDouble=function(){return g.x=Math.sin(g.seed++)*1e4,g.x-Math.floor(g.x)},M.exports=g},function(M,b,T){var g=T(5);function l(a,e){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}C(l,"Transform"),l.prototype.getWorldOrgX=function(){return this.lworldOrgX},l.prototype.setWorldOrgX=function(a){this.lworldOrgX=a},l.prototype.getWorldOrgY=function(){return this.lworldOrgY},l.prototype.setWorldOrgY=function(a){this.lworldOrgY=a},l.prototype.getWorldExtX=function(){return this.lworldExtX},l.prototype.setWorldExtX=function(a){this.lworldExtX=a},l.prototype.getWorldExtY=function(){return this.lworldExtY},l.prototype.setWorldExtY=function(a){this.lworldExtY=a},l.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},l.prototype.setDeviceOrgX=function(a){this.ldeviceOrgX=a},l.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},l.prototype.setDeviceOrgY=function(a){this.ldeviceOrgY=a},l.prototype.getDeviceExtX=function(){return this.ldeviceExtX},l.prototype.setDeviceExtX=function(a){this.ldeviceExtX=a},l.prototype.getDeviceExtY=function(){return this.ldeviceExtY},l.prototype.setDeviceExtY=function(a){this.ldeviceExtY=a},l.prototype.transformX=function(a){var e=0,r=this.lworldExtX;return r!=0&&(e=this.ldeviceOrgX+(a-this.lworldOrgX)*this.ldeviceExtX/r),e},l.prototype.transformY=function(a){var e=0,r=this.lworldExtY;return r!=0&&(e=this.ldeviceOrgY+(a-this.lworldOrgY)*this.ldeviceExtY/r),e},l.prototype.inverseTransformX=function(a){var e=0,r=this.ldeviceExtX;return r!=0&&(e=this.lworldOrgX+(a-this.ldeviceOrgX)*this.lworldExtX/r),e},l.prototype.inverseTransformY=function(a){var e=0,r=this.ldeviceExtY;return r!=0&&(e=this.lworldOrgY+(a-this.ldeviceOrgY)*this.lworldExtY/r),e},l.prototype.inverseTransformPoint=function(a){var e=new g(this.inverseTransformX(a.x),this.inverseTransformY(a.y));return e},M.exports=l},function(M,b,T){function g(t){if(Array.isArray(t)){for(var o=0,s=Array(t.length);o<t.length;o++)s[o]=t[o];return s}else return Array.from(t)}C(g,"_toConsumableArray");var l=T(15),a=T(4),e=T(0),r=T(8),h=T(9);function i(){l.call(this),this.useSmartIdealEdgeLengthCalculation=a.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=a.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=a.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=a.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=a.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*a.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=a.MAX_ITERATIONS}C(i,"FDLayout"),i.prototype=Object.create(l.prototype);for(var u in l)i[u]=l[u];i.prototype.initParameters=function(){l.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=a.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},i.prototype.calcIdealEdgeLengths=function(){for(var t,o,s,c,f,N,d,v=this.getGraphManager().getAllEdges(),L=0;L<v.length;L++)t=v[L],o=t.idealLength,t.isInterGraph&&(c=t.getSource(),f=t.getTarget(),N=t.getSourceInLca().getEstimatedSize(),d=t.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(t.idealLength+=N+d-2*e.SIMPLE_NODE_SIZE),s=t.getLca().getInclusionTreeDepth(),t.idealLength+=o*a.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(c.getInclusionTreeDepth()+f.getInclusionTreeDepth()-2*s))},i.prototype.initSpringEmbedder=function(){var t=this.getAllNodes().length;this.incremental?(t>a.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*a.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(t-a.ADAPTATION_LOWER_NODE_LIMIT)/(a.ADAPTATION_UPPER_NODE_LIMIT-a.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-a.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=a.MAX_NODE_DISPLACEMENT_INCREMENTAL):(t>a.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(a.COOLING_ADAPTATION_FACTOR,1-(t-a.ADAPTATION_LOWER_NODE_LIMIT)/(a.ADAPTATION_UPPER_NODE_LIMIT-a.ADAPTATION_LOWER_NODE_LIMIT)*(1-a.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=a.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.displacementThresholdPerNode=3*a.DEFAULT_EDGE_LENGTH/100,this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},i.prototype.calcSpringForces=function(){for(var t=this.getAllEdges(),o,s=0;s<t.length;s++)o=t[s],this.calcSpringForce(o,o.idealLength)},i.prototype.calcRepulsionForces=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s,c,f,N,d=this.getAllNodes(),v;if(this.useFRGridVariant)for(this.totalIterations%a.GRID_CALCULATION_CHECK_PERIOD==1&&t&&this.updateGrid(),v=new Set,s=0;s<d.length;s++)f=d[s],this.calculateRepulsionForceOfANode(f,v,t,o),v.add(f);else for(s=0;s<d.length;s++)for(f=d[s],c=s+1;c<d.length;c++)N=d[c],f.getOwner()==N.getOwner()&&this.calcRepulsionForce(f,N)},i.prototype.calcGravitationalForces=function(){for(var t,o=this.getAllNodesToApplyGravitation(),s=0;s<o.length;s++)t=o[s],this.calcGravitationalForce(t)},i.prototype.moveNodes=function(){for(var t=this.getAllNodes(),o,s=0;s<t.length;s++)o=t[s],o.move()},i.prototype.calcSpringForce=function(t,o){var s=t.getSource(),c=t.getTarget(),f,N,d,v;if(this.uniformLeafNodeSizes&&s.getChild()==null&&c.getChild()==null)t.updateLengthSimple();else if(t.updateLength(),t.isOverlapingSourceAndTarget)return;f=t.getLength(),f!=0&&(N=t.edgeElasticity*(f-o),d=N*(t.lengthX/f),v=N*(t.lengthY/f),s.springForceX+=d,s.springForceY+=v,c.springForceX-=d,c.springForceY-=v)},i.prototype.calcRepulsionForce=function(t,o){var s=t.getRect(),c=o.getRect(),f=new Array(2),N=new Array(4),d,v,L,G,A,U,q;if(s.intersects(c)){r.calcSeparationAmount(s,c,f,a.DEFAULT_EDGE_LENGTH/2),U=2*f[0],q=2*f[1];var X=t.noOfChildren*o.noOfChildren/(t.noOfChildren+o.noOfChildren);t.repulsionForceX-=X*U,t.repulsionForceY-=X*q,o.repulsionForceX+=X*U,o.repulsionForceY+=X*q}else this.uniformLeafNodeSizes&&t.getChild()==null&&o.getChild()==null?(d=c.getCenterX()-s.getCenterX(),v=c.getCenterY()-s.getCenterY()):(r.getIntersection(s,c,N),d=N[2]-N[0],v=N[3]-N[1]),Math.abs(d)<a.MIN_REPULSION_DIST&&(d=h.sign(d)*a.MIN_REPULSION_DIST),Math.abs(v)<a.MIN_REPULSION_DIST&&(v=h.sign(v)*a.MIN_REPULSION_DIST),L=d*d+v*v,G=Math.sqrt(L),A=(t.nodeRepulsion/2+o.nodeRepulsion/2)*t.noOfChildren*o.noOfChildren/L,U=A*d/G,q=A*v/G,t.repulsionForceX-=U,t.repulsionForceY-=q,o.repulsionForceX+=U,o.repulsionForceY+=q},i.prototype.calcGravitationalForce=function(t){var o,s,c,f,N,d,v,L;o=t.getOwner(),s=(o.getRight()+o.getLeft())/2,c=(o.getTop()+o.getBottom())/2,f=t.getCenterX()-s,N=t.getCenterY()-c,d=Math.abs(f)+t.getWidth()/2,v=Math.abs(N)+t.getHeight()/2,t.getOwner()==this.graphManager.getRoot()?(L=o.getEstimatedSize()*this.gravityRangeFactor,(d>L||v>L)&&(t.gravitationForceX=-this.gravityConstant*f,t.gravitationForceY=-this.gravityConstant*N)):(L=o.getEstimatedSize()*this.compoundGravityRangeFactor,(d>L||v>L)&&(t.gravitationForceX=-this.gravityConstant*f*this.compoundGravityConstant,t.gravitationForceY=-this.gravityConstant*N*this.compoundGravityConstant))},i.prototype.isConverged=function(){var t,o=!1;return this.totalIterations>this.maxIterations/3&&(o=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),t=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,t||o},i.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},i.prototype.calcNoOfChildrenForAllNodes=function(){for(var t,o=this.graphManager.getAllNodes(),s=0;s<o.length;s++)t=o[s],t.noOfChildren=t.getNoOfChildren()},i.prototype.calcGrid=function(t){var o=0,s=0;o=parseInt(Math.ceil((t.getRight()-t.getLeft())/this.repulsionRange)),s=parseInt(Math.ceil((t.getBottom()-t.getTop())/this.repulsionRange));for(var c=new Array(o),f=0;f<o;f++)c[f]=new Array(s);for(var f=0;f<o;f++)for(var N=0;N<s;N++)c[f][N]=new Array;return c},i.prototype.addNodeToGrid=function(t,o,s){var c=0,f=0,N=0,d=0;c=parseInt(Math.floor((t.getRect().x-o)/this.repulsionRange)),f=parseInt(Math.floor((t.getRect().width+t.getRect().x-o)/this.repulsionRange)),N=parseInt(Math.floor((t.getRect().y-s)/this.repulsionRange)),d=parseInt(Math.floor((t.getRect().height+t.getRect().y-s)/this.repulsionRange));for(var v=c;v<=f;v++)for(var L=N;L<=d;L++)this.grid[v][L].push(t),t.setGridCoordinates(c,f,N,d)},i.prototype.updateGrid=function(){var t,o,s=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),t=0;t<s.length;t++)o=s[t],this.addNodeToGrid(o,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},i.prototype.calculateRepulsionForceOfANode=function(t,o,s,c){if(this.totalIterations%a.GRID_CALCULATION_CHECK_PERIOD==1&&s||c){var f=new Set;t.surrounding=new Array;for(var N,d=this.grid,v=t.startX-1;v<t.finishX+2;v++)for(var L=t.startY-1;L<t.finishY+2;L++)if(!(v<0||L<0||v>=d.length||L>=d[0].length)){for(var G=0;G<d[v][L].length;G++)if(N=d[v][L][G],!(t.getOwner()!=N.getOwner()||t==N)&&!o.has(N)&&!f.has(N)){var A=Math.abs(t.getCenterX()-N.getCenterX())-(t.getWidth()/2+N.getWidth()/2),U=Math.abs(t.getCenterY()-N.getCenterY())-(t.getHeight()/2+N.getHeight()/2);A<=this.repulsionRange&&U<=this.repulsionRange&&f.add(N)}}t.surrounding=[].concat(g(f))}for(v=0;v<t.surrounding.length;v++)this.calcRepulsionForce(t,t.surrounding[v])},i.prototype.calcRepulsionRange=function(){return 0},M.exports=i},function(M,b,T){var g=T(1),l=T(4);function a(r,h,i){g.call(this,r,h,i),this.idealLength=l.DEFAULT_EDGE_LENGTH,this.edgeElasticity=l.DEFAULT_SPRING_STRENGTH}C(a,"FDLayoutEdge"),a.prototype=Object.create(g.prototype);for(var e in g)a[e]=g[e];M.exports=a},function(M,b,T){var g=T(3),l=T(4);function a(r,h,i,u){g.call(this,r,h,i,u),this.nodeRepulsion=l.DEFAULT_REPULSION_STRENGTH,this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}C(a,"FDLayoutNode"),a.prototype=Object.create(g.prototype);for(var e in g)a[e]=g[e];a.prototype.setGridCoordinates=function(r,h,i,u){this.startX=r,this.finishX=h,this.startY=i,this.finishY=u},M.exports=a},function(M,b,T){function g(l,a){this.width=0,this.height=0,l!==null&&a!==null&&(this.height=a,this.width=l)}C(g,"DimensionD"),g.prototype.getWidth=function(){return this.width},g.prototype.setWidth=function(l){this.width=l},g.prototype.getHeight=function(){return this.height},g.prototype.setHeight=function(l){this.height=l},M.exports=g},function(M,b,T){var g=T(14);function l(){this.map={},this.keys=[]}C(l,"HashMap"),l.prototype.put=function(a,e){var r=g.createID(a);this.contains(r)||(this.map[r]=e,this.keys.push(a))},l.prototype.contains=function(a){return g.createID(a),this.map[a]!=null},l.prototype.get=function(a){var e=g.createID(a);return this.map[e]},l.prototype.keySet=function(){return this.keys},M.exports=l},function(M,b,T){var g=T(14);function l(){this.set={}}C(l,"HashSet"),l.prototype.add=function(a){var e=g.createID(a);this.contains(e)||(this.set[e]=a)},l.prototype.remove=function(a){delete this.set[g.createID(a)]},l.prototype.clear=function(){this.set={}},l.prototype.contains=function(a){return this.set[g.createID(a)]==a},l.prototype.isEmpty=function(){return this.size()===0},l.prototype.size=function(){return Object.keys(this.set).length},l.prototype.addAllTo=function(a){for(var e=Object.keys(this.set),r=e.length,h=0;h<r;h++)a.push(this.set[e[h]])},l.prototype.size=function(){return Object.keys(this.set).length},l.prototype.addAll=function(a){for(var e=a.length,r=0;r<e;r++){var h=a[r];this.add(h)}},M.exports=l},function(M,b,T){function g(){}C(g,"Matrix"),g.multMat=function(l,a){for(var e=[],r=0;r<l.length;r++){e[r]=[];for(var h=0;h<a[0].length;h++){e[r][h]=0;for(var i=0;i<l[0].length;i++)e[r][h]+=l[r][i]*a[i][h]}}return e},g.transpose=function(l){for(var a=[],e=0;e<l[0].length;e++){a[e]=[];for(var r=0;r<l.length;r++)a[e][r]=l[r][e]}return a},g.multCons=function(l,a){for(var e=[],r=0;r<l.length;r++)e[r]=l[r]*a;return e},g.minusOp=function(l,a){for(var e=[],r=0;r<l.length;r++)e[r]=l[r]-a[r];return e},g.dotProduct=function(l,a){for(var e=0,r=0;r<l.length;r++)e+=l[r]*a[r];return e},g.mag=function(l){return Math.sqrt(this.dotProduct(l,l))},g.normalize=function(l){for(var a=[],e=this.mag(l),r=0;r<l.length;r++)a[r]=l[r]/e;return a},g.multGamma=function(l){for(var a=[],e=0,r=0;r<l.length;r++)e+=l[r];e*=-1/l.length;for(var h=0;h<l.length;h++)a[h]=e+l[h];return a},g.multL=function(l,a,e){for(var r=[],h=[],i=[],u=0;u<a[0].length;u++){for(var t=0,o=0;o<a.length;o++)t+=-.5*a[o][u]*l[o];h[u]=t}for(var s=0;s<e.length;s++){for(var c=0,f=0;f<e.length;f++)c+=e[s][f]*h[f];i[s]=c}for(var N=0;N<a.length;N++){for(var d=0,v=0;v<a[0].length;v++)d+=a[N][v]*i[v];r[N]=d}return r},M.exports=g},function(M,b,T){var g=(function(){function r(h,i){for(var u=0;u<i.length;u++){var t=i[u];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(h,t.key,t)}}return C(r,"defineProperties"),function(h,i,u){return i&&r(h.prototype,i),u&&r(h,u),h}})();function l(r,h){if(!(r instanceof h))throw new TypeError("Cannot call a class as a function")}C(l,"_classCallCheck");var a=T(11),e=(function(){function r(h,i){l(this,r),(i!==null||i!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var u=void 0;h instanceof a?u=h.size():u=h.length,this._quicksort(h,0,u-1)}return C(r,"Quicksort"),g(r,[{key:"_quicksort",value:C(function(i,u,t){if(u<t){var o=this._partition(i,u,t);this._quicksort(i,u,o),this._quicksort(i,o+1,t)}},"_quicksort")},{key:"_partition",value:C(function(i,u,t){for(var o=this._get(i,u),s=u,c=t;;){for(;this.compareFunction(o,this._get(i,c));)c--;for(;this.compareFunction(this._get(i,s),o);)s++;if(s<c)this._swap(i,s,c),s++,c--;else return c}},"_partition")},{key:"_get",value:C(function(i,u){return i instanceof a?i.get_object_at(u):i[u]},"_get")},{key:"_set",value:C(function(i,u,t){i instanceof a?i.set_object_at(u,t):i[u]=t},"_set")},{key:"_swap",value:C(function(i,u,t){var o=this._get(i,u);this._set(i,u,this._get(i,t)),this._set(i,t,o)},"_swap")},{key:"_defaultCompareFunction",value:C(function(i,u){return u>i},"_defaultCompareFunction")}]),r})();M.exports=e},function(M,b,T){function g(){}C(g,"SVD"),g.svd=function(l){this.U=null,this.V=null,this.s=null,this.m=0,this.n=0,this.m=l.length,this.n=l[0].length;var a=Math.min(this.m,this.n);this.s=(function(Tt){for(var Ct=[];Tt-- >0;)Ct.push(0);return Ct})(Math.min(this.m+1,this.n)),this.U=(function(Tt){var Ct=C(function Bt(bt){if(bt.length==0)return 0;for(var zt=[],St=0;St<bt[0];St++)zt.push(Bt(bt.slice(1)));return zt},"allocate");return Ct(Tt)})([this.m,a]),this.V=(function(Tt){var Ct=C(function Bt(bt){if(bt.length==0)return 0;for(var zt=[],St=0;St<bt[0];St++)zt.push(Bt(bt.slice(1)));return zt},"allocate");return Ct(Tt)})([this.n,this.n]);for(var e=(function(Tt){for(var Ct=[];Tt-- >0;)Ct.push(0);return Ct})(this.n),r=(function(Tt){for(var Ct=[];Tt-- >0;)Ct.push(0);return Ct})(this.m),h=!0,i=Math.min(this.m-1,this.n),u=Math.max(0,Math.min(this.n-2,this.m)),t=0;t<Math.max(i,u);t++){if(t<i){this.s[t]=0;for(var o=t;o<this.m;o++)this.s[t]=g.hypot(this.s[t],l[o][t]);if(this.s[t]!==0){l[t][t]<0&&(this.s[t]=-this.s[t]);for(var s=t;s<this.m;s++)l[s][t]/=this.s[t];l[t][t]+=1}this.s[t]=-this.s[t]}for(var c=t+1;c<this.n;c++){if((function(Tt,Ct){return Tt&&Ct})(t<i,this.s[t]!==0)){for(var f=0,N=t;N<this.m;N++)f+=l[N][t]*l[N][c];f=-f/l[t][t];for(var d=t;d<this.m;d++)l[d][c]+=f*l[d][t]}e[c]=l[t][c]}if((function(Tt,Ct){return Ct})(h,t<i))for(var v=t;v<this.m;v++)this.U[v][t]=l[v][t];if(t<u){e[t]=0;for(var L=t+1;L<this.n;L++)e[t]=g.hypot(e[t],e[L]);if(e[t]!==0){e[t+1]<0&&(e[t]=-e[t]);for(var G=t+1;G<this.n;G++)e[G]/=e[t];e[t+1]+=1}if(e[t]=-e[t],(function(Tt,Ct){return Tt&&Ct})(t+1<this.m,e[t]!==0)){for(var A=t+1;A<this.m;A++)r[A]=0;for(var U=t+1;U<this.n;U++)for(var q=t+1;q<this.m;q++)r[q]+=e[U]*l[q][U];for(var X=t+1;X<this.n;X++)for(var _=-e[X]/e[t+1],D=t+1;D<this.m;D++)l[D][X]+=_*r[D]}for(var at=t+1;at<this.n;at++)this.V[at][t]=e[at]}}var n=Math.min(this.n,this.m+1);i<this.n&&(this.s[i]=l[i][i]),this.m<n&&(this.s[n-1]=0),u+1<n&&(e[u]=l[u][n-1]),e[n-1]=0;{for(var E=i;E<a;E++){for(var p=0;p<this.m;p++)this.U[p][E]=0;this.U[E][E]=1}for(var m=i-1;m>=0;m--)if(this.s[m]!==0){for(var y=m+1;y<a;y++){for(var S=0,w=m;w<this.m;w++)S+=this.U[w][m]*this.U[w][y];S=-S/this.U[m][m];for(var F=m;F<this.m;F++)this.U[F][y]+=S*this.U[F][m]}for(var V=m;V<this.m;V++)this.U[V][m]=-this.U[V][m];this.U[m][m]=1+this.U[m][m];for(var R=0;R<m-1;R++)this.U[R][m]=0}else{for(var Q=0;Q<this.m;Q++)this.U[Q][m]=0;this.U[m][m]=1}}for(var z=this.n-1;z>=0;z--){if((function(Tt,Ct){return Tt&&Ct})(z<u,e[z]!==0))for(var H=z+1;H<a;H++){for(var rt=0,B=z+1;B<this.n;B++)rt+=this.V[B][z]*this.V[B][H];rt=-rt/this.V[z+1][z];for(var O=z+1;O<this.n;O++)this.V[O][H]+=rt*this.V[O][z]}for(var W=0;W<this.n;W++)this.V[W][z]=0;this.V[z][z]=1}for(var $=n-1,tt=Math.pow(2,-52),ht=Math.pow(2,-966);n>0;){var J=void 0,It=void 0;for(J=n-2;J>=-1&&J!==-1;J--)if(Math.abs(e[J])<=ht+tt*(Math.abs(this.s[J])+Math.abs(this.s[J+1]))){e[J]=0;break}if(J===n-2)It=4;else{var Nt=void 0;for(Nt=n-1;Nt>=J&&Nt!==J;Nt--){var vt=(Nt!==n?Math.abs(e[Nt]):0)+(Nt!==J+1?Math.abs(e[Nt-1]):0);if(Math.abs(this.s[Nt])<=ht+tt*vt){this.s[Nt]=0;break}}Nt===J?It=3:Nt===n-1?It=1:(It=2,J=Nt)}switch(J++,It){case 1:{var it=e[n-2];e[n-2]=0;for(var ut=n-2;ut>=J;ut--){var Et=g.hypot(this.s[ut],it),At=this.s[ut]/Et,Ot=it/Et;this.s[ut]=Et,ut!==J&&(it=-Ot*e[ut-1],e[ut-1]=At*e[ut-1]);for(var mt=0;mt<this.n;mt++)Et=At*this.V[mt][ut]+Ot*this.V[mt][n-1],this.V[mt][n-1]=-Ot*this.V[mt][ut]+At*this.V[mt][n-1],this.V[mt][ut]=Et}}break;case 2:{var Dt=e[J-1];e[J-1]=0;for(var Rt=J;Rt<n;Rt++){var Ht=g.hypot(this.s[Rt],Dt),Ut=this.s[Rt]/Ht,Pt=Dt/Ht;this.s[Rt]=Ht,Dt=-Pt*e[Rt],e[Rt]=Ut*e[Rt];for(var Ft=0;Ft<this.m;Ft++)Ht=Ut*this.U[Ft][Rt]+Pt*this.U[Ft][J-1],this.U[Ft][J-1]=-Pt*this.U[Ft][Rt]+Ut*this.U[Ft][J-1],this.U[Ft][Rt]=Ht}}break;case 3:{var Yt=Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[n-1]),Math.abs(this.s[n-2])),Math.abs(e[n-2])),Math.abs(this.s[J])),Math.abs(e[J])),Vt=this.s[n-1]/Yt,P=this.s[n-2]/Yt,Y=e[n-2]/Yt,k=this.s[J]/Yt,K=e[J]/Yt,Z=((P+Vt)*(P-Vt)+Y*Y)/2,nt=Vt*Y*(Vt*Y),ct=0;(function(Tt,Ct){return Tt||Ct})(Z!==0,nt!==0)&&(ct=Math.sqrt(Z*Z+nt),Z<0&&(ct=-ct),ct=nt/(Z+ct));for(var ot=(k+Vt)*(k-Vt)+ct,et=k*K,j=J;j<n-1;j++){var dt=g.hypot(ot,et),Mt=ot/dt,pt=et/dt;j!==J&&(e[j-1]=dt),ot=Mt*this.s[j]+pt*e[j],e[j]=Mt*e[j]-pt*this.s[j],et=pt*this.s[j+1],this.s[j+1]=Mt*this.s[j+1];for(var xt=0;xt<this.n;xt++)dt=Mt*this.V[xt][j]+pt*this.V[xt][j+1],this.V[xt][j+1]=-pt*this.V[xt][j]+Mt*this.V[xt][j+1],this.V[xt][j]=dt;if(dt=g.hypot(ot,et),Mt=ot/dt,pt=et/dt,this.s[j]=dt,ot=Mt*e[j]+pt*this.s[j+1],this.s[j+1]=-pt*e[j]+Mt*this.s[j+1],et=pt*e[j+1],e[j+1]=Mt*e[j+1],j<this.m-1)for(var ft=0;ft<this.m;ft++)dt=Mt*this.U[ft][j]+pt*this.U[ft][j+1],this.U[ft][j+1]=-pt*this.U[ft][j]+Mt*this.U[ft][j+1],this.U[ft][j]=dt}e[n-2]=ot}break;case 4:{if(this.s[J]<=0){this.s[J]=this.s[J]<0?-this.s[J]:0;for(var st=0;st<=$;st++)this.V[st][J]=-this.V[st][J]}for(;J<$&&!(this.s[J]>=this.s[J+1]);){var Lt=this.s[J];if(this.s[J]=this.s[J+1],this.s[J+1]=Lt,J<this.n-1)for(var gt=0;gt<this.n;gt++)Lt=this.V[gt][J+1],this.V[gt][J+1]=this.V[gt][J],this.V[gt][J]=Lt;if(J<this.m-1)for(var lt=0;lt<this.m;lt++)Lt=this.U[lt][J+1],this.U[lt][J+1]=this.U[lt][J],this.U[lt][J]=Lt;J++}n--}break}}var Xt={U:this.U,V:this.V,S:this.s};return Xt},g.hypot=function(l,a){var e=void 0;return Math.abs(l)>Math.abs(a)?(e=a/l,e=Math.abs(l)*Math.sqrt(1+e*e)):a!=0?(e=l/a,e=Math.abs(a)*Math.sqrt(1+e*e)):e=0,e},M.exports=g},function(M,b,T){var g=(function(){function e(r,h){for(var i=0;i<h.length;i++){var u=h[i];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(r,u.key,u)}}return C(e,"defineProperties"),function(r,h,i){return h&&e(r.prototype,h),i&&e(r,i),r}})();function l(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}C(l,"_classCallCheck");var a=(function(){function e(r,h){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,u=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;l(this,e),this.sequence1=r,this.sequence2=h,this.match_score=i,this.mismatch_penalty=u,this.gap_penalty=t,this.iMax=r.length+1,this.jMax=h.length+1,this.grid=new Array(this.iMax);for(var o=0;o<this.iMax;o++){this.grid[o]=new Array(this.jMax);for(var s=0;s<this.jMax;s++)this.grid[o][s]=0}this.tracebackGrid=new Array(this.iMax);for(var c=0;c<this.iMax;c++){this.tracebackGrid[c]=new Array(this.jMax);for(var f=0;f<this.jMax;f++)this.tracebackGrid[c][f]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return C(e,"NeedlemanWunsch"),g(e,[{key:"getScore",value:C(function(){return this.score},"getScore")},{key:"getAlignments",value:C(function(){return this.alignments},"getAlignments")},{key:"computeGrids",value:C(function(){for(var h=1;h<this.jMax;h++)this.grid[0][h]=this.grid[0][h-1]+this.gap_penalty,this.tracebackGrid[0][h]=[!1,!1,!0];for(var i=1;i<this.iMax;i++)this.grid[i][0]=this.grid[i-1][0]+this.gap_penalty,this.tracebackGrid[i][0]=[!1,!0,!1];for(var u=1;u<this.iMax;u++)for(var t=1;t<this.jMax;t++){var o=void 0;this.sequence1[u-1]===this.sequence2[t-1]?o=this.grid[u-1][t-1]+this.match_score:o=this.grid[u-1][t-1]+this.mismatch_penalty;var s=this.grid[u-1][t]+this.gap_penalty,c=this.grid[u][t-1]+this.gap_penalty,f=[o,s,c],N=this.arrayAllMaxIndexes(f);this.grid[u][t]=f[N[0]],this.tracebackGrid[u][t]=[N.includes(0),N.includes(1),N.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]},"computeGrids")},{key:"alignmentTraceback",value:C(function(){var h=[];for(h.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});h[0];){var i=h[0],u=this.tracebackGrid[i.pos[0]][i.pos[1]];u[0]&&h.push({pos:[i.pos[0]-1,i.pos[1]-1],seq1:this.sequence1[i.pos[0]-1]+i.seq1,seq2:this.sequence2[i.pos[1]-1]+i.seq2}),u[1]&&h.push({pos:[i.pos[0]-1,i.pos[1]],seq1:this.sequence1[i.pos[0]-1]+i.seq1,seq2:"-"+i.seq2}),u[2]&&h.push({pos:[i.pos[0],i.pos[1]-1],seq1:"-"+i.seq1,seq2:this.sequence2[i.pos[1]-1]+i.seq2}),i.pos[0]===0&&i.pos[1]===0&&this.alignments.push({sequence1:i.seq1,sequence2:i.seq2}),h.shift()}return this.alignments},"alignmentTraceback")},{key:"getAllIndexes",value:C(function(h,i){for(var u=[],t=-1;(t=h.indexOf(i,t+1))!==-1;)u.push(t);return u},"getAllIndexes")},{key:"arrayAllMaxIndexes",value:C(function(h){return this.getAllIndexes(h,Math.max.apply(null,h))},"arrayAllMaxIndexes")}]),e})();M.exports=a},function(M,b,T){var g=C(function(){},"layoutBase");g.FDLayout=T(18),g.FDLayoutConstants=T(4),g.FDLayoutEdge=T(19),g.FDLayoutNode=T(20),g.DimensionD=T(21),g.HashMap=T(22),g.HashSet=T(23),g.IGeometry=T(8),g.IMath=T(9),g.Integer=T(10),g.Point=T(12),g.PointD=T(5),g.RandomSeed=T(16),g.RectangleD=T(13),g.Transform=T(17),g.UniqueIDGeneretor=T(14),g.Quicksort=T(25),g.LinkedList=T(11),g.LGraphObject=T(2),g.LGraph=T(6),g.LEdge=T(1),g.LGraphManager=T(7),g.LNode=T(3),g.Layout=T(15),g.LayoutConstants=T(0),g.NeedlemanWunsch=T(27),g.Matrix=T(24),g.SVD=T(26),M.exports=g},function(M,b,T){function g(){this.listeners=[]}C(g,"Emitter");var l=g.prototype;l.addListener=function(a,e){this.listeners.push({event:a,callback:e})},l.removeListener=function(a,e){for(var r=this.listeners.length;r>=0;r--){var h=this.listeners[r];h.event===a&&h.callback===e&&this.listeners.splice(r,1)}},l.emit=function(a,e){for(var r=0;r<this.listeners.length;r++){var h=this.listeners[r];a===h.event&&h.callback(e)}},M.exports=g}])})}}),Me=ye({"../../node_modules/.pnpm/cose-base@2.2.0/node_modules/cose-base/cose-base.js"(x,I){C(function(b,T){typeof x=="object"&&typeof I=="object"?I.exports=T(Ae()):typeof define=="function"&&define.amd?define(["layout-base"],T):typeof x=="object"?x.coseBase=T(Ae()):b.coseBase=T(b.layoutBase)},"webpackUniversalModuleDefinition")(x,function(M){return(()=>{var b={45:((a,e,r)=>{var h={};h.layoutBase=r(551),h.CoSEConstants=r(806),h.CoSEEdge=r(767),h.CoSEGraph=r(880),h.CoSEGraphManager=r(578),h.CoSELayout=r(765),h.CoSENode=r(991),h.ConstraintHandler=r(902),a.exports=h}),806:((a,e,r)=>{var h=r(551).FDLayoutConstants;function i(){}C(i,"CoSEConstants");for(var u in h)i[u]=h[u];i.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,i.DEFAULT_RADIAL_SEPARATION=h.DEFAULT_EDGE_LENGTH,i.DEFAULT_COMPONENT_SEPERATION=60,i.TILE=!0,i.TILING_PADDING_VERTICAL=10,i.TILING_PADDING_HORIZONTAL=10,i.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,i.ENFORCE_CONSTRAINTS=!0,i.APPLY_LAYOUT=!0,i.RELAX_MOVEMENT_ON_CONSTRAINTS=!0,i.TREE_REDUCTION_ON_INCREMENTAL=!0,i.PURE_INCREMENTAL=i.DEFAULT_INCREMENTAL,a.exports=i}),767:((a,e,r)=>{var h=r(551).FDLayoutEdge;function i(t,o,s){h.call(this,t,o,s)}C(i,"CoSEEdge"),i.prototype=Object.create(h.prototype);for(var u in h)i[u]=h[u];a.exports=i}),880:((a,e,r)=>{var h=r(551).LGraph;function i(t,o,s){h.call(this,t,o,s)}C(i,"CoSEGraph"),i.prototype=Object.create(h.prototype);for(var u in h)i[u]=h[u];a.exports=i}),578:((a,e,r)=>{var h=r(551).LGraphManager;function i(t){h.call(this,t)}C(i,"CoSEGraphManager"),i.prototype=Object.create(h.prototype);for(var u in h)i[u]=h[u];a.exports=i}),765:((a,e,r)=>{var h=r(551).FDLayout,i=r(578),u=r(880),t=r(991),o=r(767),s=r(806),c=r(902),f=r(551).FDLayoutConstants,N=r(551).LayoutConstants,d=r(551).Point,v=r(551).PointD,L=r(551).DimensionD,G=r(551).Layout,A=r(551).Integer,U=r(551).IGeometry,q=r(551).LGraph,X=r(551).Transform,_=r(551).LinkedList;function D(){h.call(this),this.toBeTiled={},this.constraints={}}C(D,"CoSELayout"),D.prototype=Object.create(h.prototype);for(var at in h)D[at]=h[at];D.prototype.newGraphManager=function(){var n=new i(this);return this.graphManager=n,n},D.prototype.newGraph=function(n){return new u(null,this.graphManager,n)},D.prototype.newNode=function(n){return new t(this.graphManager,n)},D.prototype.newEdge=function(n){return new o(null,null,n)},D.prototype.initParameters=function(){h.prototype.initParameters.call(this,arguments),this.isSubLayout||(s.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=s.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=s.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=f.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=f.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=f.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=f.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1)},D.prototype.initSpringEmbedder=function(){h.prototype.initSpringEmbedder.call(this),this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/f.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=.04,this.coolingAdjuster=1},D.prototype.layout=function(){var n=N.DEFAULT_CREATE_BENDS_AS_NEEDED;return n&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},D.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(s.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var E=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(S){return E.has(S)});this.graphManager.setAllNodesToApplyGravitation(p)}}else{var n=this.getFlatForest();if(n.length>0)this.positionNodesRadially(n);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var E=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(m){return E.has(m)});this.graphManager.setAllNodesToApplyGravitation(p),this.positionNodesRandomly()}}return Object.keys(this.constraints).length>0&&(c.handleConstraints(this),this.initConstraintVariables()),this.initSpringEmbedder(),s.APPLY_LAYOUT&&this.runSpringEmbedder(),!0},D.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%f.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var n=new Set(this.getAllNodes()),E=this.nodesWithGravity.filter(function(y){return n.has(y)});this.graphManager.setAllNodesToApplyGravitation(E),this.graphManager.updateBounds(),this.updateGrid(),s.PURE_INCREMENTAL?this.coolingFactor=f.DEFAULT_COOLING_FACTOR_INCREMENTAL/2:this.coolingFactor=f.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),s.PURE_INCREMENTAL?this.coolingFactor=f.DEFAULT_COOLING_FACTOR_INCREMENTAL/2*((100-this.afterGrowthIterations)/100):this.coolingFactor=f.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var p=!this.isTreeGrowing&&!this.isGrowthFinished,m=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(p,m),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},D.prototype.getPositionsData=function(){for(var n=this.graphManager.getAllNodes(),E={},p=0;p<n.length;p++){var m=n[p].rect,y=n[p].id;E[y]={id:y,x:m.getCenterX(),y:m.getCenterY(),w:m.width,h:m.height}}return E},D.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var n=!1;if(f.ANIMATE==="during")this.emit("layoutstarted");else{for(;!n;)n=this.tick();this.graphManager.updateBounds()}},D.prototype.moveNodes=function(){for(var n=this.getAllNodes(),E,p=0;p<n.length;p++)E=n[p],E.calculateDisplacement();Object.keys(this.constraints).length>0&&this.updateDisplacements();for(var p=0;p<n.length;p++)E=n[p],E.move()},D.prototype.initConstraintVariables=function(){var n=this;this.idToNodeMap=new Map,this.fixedNodeSet=new Set;for(var E=this.graphManager.getAllNodes(),p=0;p<E.length;p++){var m=E[p];this.idToNodeMap.set(m.id,m)}var y=C(function O(W){for(var $=W.getChild().getNodes(),tt,ht=0,J=0;J<$.length;J++)tt=$[J],tt.getChild()==null?n.fixedNodeSet.has(tt.id)&&(ht+=100):ht+=O(tt);return ht},"calculateCompoundWeight");if(this.constraints.fixedNodeConstraint){this.constraints.fixedNodeConstraint.forEach(function($){n.fixedNodeSet.add($.nodeId)});for(var E=this.graphManager.getAllNodes(),m,p=0;p<E.length;p++)if(m=E[p],m.getChild()!=null){var S=y(m);S>0&&(m.fixedNodeWeight=S)}}if(this.constraints.relativePlacementConstraint){var w=new Map,F=new Map;if(this.dummyToNodeForVerticalAlignment=new Map,this.dummyToNodeForHorizontalAlignment=new Map,this.fixedNodesOnHorizontal=new Set,this.fixedNodesOnVertical=new Set,this.fixedNodeSet.forEach(function(O){n.fixedNodesOnHorizontal.add(O),n.fixedNodesOnVertical.add(O)}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var V=this.constraints.alignmentConstraint.vertical,p=0;p<V.length;p++)this.dummyToNodeForVerticalAlignment.set("dummy"+p,[]),V[p].forEach(function(W){w.set(W,"dummy"+p),n.dummyToNodeForVerticalAlignment.get("dummy"+p).push(W),n.fixedNodeSet.has(W)&&n.fixedNodesOnHorizontal.add("dummy"+p)});if(this.constraints.alignmentConstraint.horizontal)for(var R=this.constraints.alignmentConstraint.horizontal,p=0;p<R.length;p++)this.dummyToNodeForHorizontalAlignment.set("dummy"+p,[]),R[p].forEach(function(W){F.set(W,"dummy"+p),n.dummyToNodeForHorizontalAlignment.get("dummy"+p).push(W),n.fixedNodeSet.has(W)&&n.fixedNodesOnVertical.add("dummy"+p)})}if(s.RELAX_MOVEMENT_ON_CONSTRAINTS)this.shuffle=function(O){var W,$,tt;for(tt=O.length-1;tt>=2*O.length/3;tt--)W=Math.floor(Math.random()*(tt+1)),$=O[tt],O[tt]=O[W],O[W]=$;return O},this.nodesInRelativeHorizontal=[],this.nodesInRelativeVertical=[],this.nodeToRelativeConstraintMapHorizontal=new Map,this.nodeToRelativeConstraintMapVertical=new Map,this.nodeToTempPositionMapHorizontal=new Map,this.nodeToTempPositionMapVertical=new Map,this.constraints.relativePlacementConstraint.forEach(function(O){if(O.left){var W=w.has(O.left)?w.get(O.left):O.left,$=w.has(O.right)?w.get(O.right):O.right;n.nodesInRelativeHorizontal.includes(W)||(n.nodesInRelativeHorizontal.push(W),n.nodeToRelativeConstraintMapHorizontal.set(W,[]),n.dummyToNodeForVerticalAlignment.has(W)?n.nodeToTempPositionMapHorizontal.set(W,n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(W)[0]).getCenterX()):n.nodeToTempPositionMapHorizontal.set(W,n.idToNodeMap.get(W).getCenterX())),n.nodesInRelativeHorizontal.includes($)||(n.nodesInRelativeHorizontal.push($),n.nodeToRelativeConstraintMapHorizontal.set($,[]),n.dummyToNodeForVerticalAlignment.has($)?n.nodeToTempPositionMapHorizontal.set($,n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get($)[0]).getCenterX()):n.nodeToTempPositionMapHorizontal.set($,n.idToNodeMap.get($).getCenterX())),n.nodeToRelativeConstraintMapHorizontal.get(W).push({right:$,gap:O.gap}),n.nodeToRelativeConstraintMapHorizontal.get($).push({left:W,gap:O.gap})}else{var tt=F.has(O.top)?F.get(O.top):O.top,ht=F.has(O.bottom)?F.get(O.bottom):O.bottom;n.nodesInRelativeVertical.includes(tt)||(n.nodesInRelativeVertical.push(tt),n.nodeToRelativeConstraintMapVertical.set(tt,[]),n.dummyToNodeForHorizontalAlignment.has(tt)?n.nodeToTempPositionMapVertical.set(tt,n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(tt)[0]).getCenterY()):n.nodeToTempPositionMapVertical.set(tt,n.idToNodeMap.get(tt).getCenterY())),n.nodesInRelativeVertical.includes(ht)||(n.nodesInRelativeVertical.push(ht),n.nodeToRelativeConstraintMapVertical.set(ht,[]),n.dummyToNodeForHorizontalAlignment.has(ht)?n.nodeToTempPositionMapVertical.set(ht,n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(ht)[0]).getCenterY()):n.nodeToTempPositionMapVertical.set(ht,n.idToNodeMap.get(ht).getCenterY())),n.nodeToRelativeConstraintMapVertical.get(tt).push({bottom:ht,gap:O.gap}),n.nodeToRelativeConstraintMapVertical.get(ht).push({top:tt,gap:O.gap})}});else{var Q=new Map,z=new Map;this.constraints.relativePlacementConstraint.forEach(function(O){if(O.left){var W=w.has(O.left)?w.get(O.left):O.left,$=w.has(O.right)?w.get(O.right):O.right;Q.has(W)?Q.get(W).push($):Q.set(W,[$]),Q.has($)?Q.get($).push(W):Q.set($,[W])}else{var tt=F.has(O.top)?F.get(O.top):O.top,ht=F.has(O.bottom)?F.get(O.bottom):O.bottom;z.has(tt)?z.get(tt).push(ht):z.set(tt,[ht]),z.has(ht)?z.get(ht).push(tt):z.set(ht,[tt])}});var H=C(function(W,$){var tt=[],ht=[],J=new _,It=new Set,Nt=0;return W.forEach(function(vt,it){if(!It.has(it)){tt[Nt]=[],ht[Nt]=!1;var ut=it;for(J.push(ut),It.add(ut),tt[Nt].push(ut);J.length!=0;){ut=J.shift(),$.has(ut)&&(ht[Nt]=!0);var Et=W.get(ut);Et.forEach(function(At){It.has(At)||(J.push(At),It.add(At),tt[Nt].push(At))})}Nt++}}),{components:tt,isFixed:ht}},"constructComponents"),rt=H(Q,n.fixedNodesOnHorizontal);this.componentsOnHorizontal=rt.components,this.fixedComponentsOnHorizontal=rt.isFixed;var B=H(z,n.fixedNodesOnVertical);this.componentsOnVertical=B.components,this.fixedComponentsOnVertical=B.isFixed}}},D.prototype.updateDisplacements=function(){var n=this;if(this.constraints.fixedNodeConstraint&&this.constraints.fixedNodeConstraint.forEach(function(B){var O=n.idToNodeMap.get(B.nodeId);O.displacementX=0,O.displacementY=0}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var E=this.constraints.alignmentConstraint.vertical,p=0;p<E.length;p++){for(var m=0,y=0;y<E[p].length;y++){if(this.fixedNodeSet.has(E[p][y])){m=0;break}m+=this.idToNodeMap.get(E[p][y]).displacementX}for(var S=m/E[p].length,y=0;y<E[p].length;y++)this.idToNodeMap.get(E[p][y]).displacementX=S}if(this.constraints.alignmentConstraint.horizontal)for(var w=this.constraints.alignmentConstraint.horizontal,p=0;p<w.length;p++){for(var F=0,y=0;y<w[p].length;y++){if(this.fixedNodeSet.has(w[p][y])){F=0;break}F+=this.idToNodeMap.get(w[p][y]).displacementY}for(var V=F/w[p].length,y=0;y<w[p].length;y++)this.idToNodeMap.get(w[p][y]).displacementY=V}}if(this.constraints.relativePlacementConstraint)if(s.RELAX_MOVEMENT_ON_CONSTRAINTS)this.totalIterations%10==0&&(this.shuffle(this.nodesInRelativeHorizontal),this.shuffle(this.nodesInRelativeVertical)),this.nodesInRelativeHorizontal.forEach(function(B){if(!n.fixedNodesOnHorizontal.has(B)){var O=0;n.dummyToNodeForVerticalAlignment.has(B)?O=n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(B)[0]).displacementX:O=n.idToNodeMap.get(B).displacementX,n.nodeToRelativeConstraintMapHorizontal.get(B).forEach(function(W){if(W.right){var $=n.nodeToTempPositionMapHorizontal.get(W.right)-n.nodeToTempPositionMapHorizontal.get(B)-O;$<W.gap&&(O-=W.gap-$)}else{var $=n.nodeToTempPositionMapHorizontal.get(B)-n.nodeToTempPositionMapHorizontal.get(W.left)+O;$<W.gap&&(O+=W.gap-$)}}),n.nodeToTempPositionMapHorizontal.set(B,n.nodeToTempPositionMapHorizontal.get(B)+O),n.dummyToNodeForVerticalAlignment.has(B)?n.dummyToNodeForVerticalAlignment.get(B).forEach(function(W){n.idToNodeMap.get(W).displacementX=O}):n.idToNodeMap.get(B).displacementX=O}}),this.nodesInRelativeVertical.forEach(function(B){if(!n.fixedNodesOnHorizontal.has(B)){var O=0;n.dummyToNodeForHorizontalAlignment.has(B)?O=n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(B)[0]).displacementY:O=n.idToNodeMap.get(B).displacementY,n.nodeToRelativeConstraintMapVertical.get(B).forEach(function(W){if(W.bottom){var $=n.nodeToTempPositionMapVertical.get(W.bottom)-n.nodeToTempPositionMapVertical.get(B)-O;$<W.gap&&(O-=W.gap-$)}else{var $=n.nodeToTempPositionMapVertical.get(B)-n.nodeToTempPositionMapVertical.get(W.top)+O;$<W.gap&&(O+=W.gap-$)}}),n.nodeToTempPositionMapVertical.set(B,n.nodeToTempPositionMapVertical.get(B)+O),n.dummyToNodeForHorizontalAlignment.has(B)?n.dummyToNodeForHorizontalAlignment.get(B).forEach(function(W){n.idToNodeMap.get(W).displacementY=O}):n.idToNodeMap.get(B).displacementY=O}});else{for(var p=0;p<this.componentsOnHorizontal.length;p++){var R=this.componentsOnHorizontal[p];if(this.fixedComponentsOnHorizontal[p])for(var y=0;y<R.length;y++)this.dummyToNodeForVerticalAlignment.has(R[y])?this.dummyToNodeForVerticalAlignment.get(R[y]).forEach(function(W){n.idToNodeMap.get(W).displacementX=0}):this.idToNodeMap.get(R[y]).displacementX=0;else{for(var Q=0,z=0,y=0;y<R.length;y++)if(this.dummyToNodeForVerticalAlignment.has(R[y])){var H=this.dummyToNodeForVerticalAlignment.get(R[y]);Q+=H.length*this.idToNodeMap.get(H[0]).displacementX,z+=H.length}else Q+=this.idToNodeMap.get(R[y]).displacementX,z++;for(var rt=Q/z,y=0;y<R.length;y++)this.dummyToNodeForVerticalAlignment.has(R[y])?this.dummyToNodeForVerticalAlignment.get(R[y]).forEach(function(W){n.idToNodeMap.get(W).displacementX=rt}):this.idToNodeMap.get(R[y]).displacementX=rt}}for(var p=0;p<this.componentsOnVertical.length;p++){var R=this.componentsOnVertical[p];if(this.fixedComponentsOnVertical[p])for(var y=0;y<R.length;y++)this.dummyToNodeForHorizontalAlignment.has(R[y])?this.dummyToNodeForHorizontalAlignment.get(R[y]).forEach(function($){n.idToNodeMap.get($).displacementY=0}):this.idToNodeMap.get(R[y]).displacementY=0;else{for(var Q=0,z=0,y=0;y<R.length;y++)if(this.dummyToNodeForHorizontalAlignment.has(R[y])){var H=this.dummyToNodeForHorizontalAlignment.get(R[y]);Q+=H.length*this.idToNodeMap.get(H[0]).displacementY,z+=H.length}else Q+=this.idToNodeMap.get(R[y]).displacementY,z++;for(var rt=Q/z,y=0;y<R.length;y++)this.dummyToNodeForHorizontalAlignment.has(R[y])?this.dummyToNodeForHorizontalAlignment.get(R[y]).forEach(function(J){n.idToNodeMap.get(J).displacementY=rt}):this.idToNodeMap.get(R[y]).displacementY=rt}}}},D.prototype.calculateNodesToApplyGravitationTo=function(){var n=[],E,p=this.graphManager.getGraphs(),m=p.length,y;for(y=0;y<m;y++)E=p[y],E.updateConnected(),E.isConnected||(n=n.concat(E.getNodes()));return n},D.prototype.createBendpoints=function(){var n=[];n=n.concat(this.graphManager.getAllEdges());var E=new Set,p;for(p=0;p<n.length;p++){var m=n[p];if(!E.has(m)){var y=m.getSource(),S=m.getTarget();if(y==S)m.getBendpoints().push(new v),m.getBendpoints().push(new v),this.createDummyNodesForBendpoints(m),E.add(m);else{var w=[];if(w=w.concat(y.getEdgeListToNode(S)),w=w.concat(S.getEdgeListToNode(y)),!E.has(w[0])){if(w.length>1){var F;for(F=0;F<w.length;F++){var V=w[F];V.getBendpoints().push(new v),this.createDummyNodesForBendpoints(V)}}w.forEach(function(R){E.add(R)})}}}if(E.size==n.length)break}},D.prototype.positionNodesRadially=function(n){for(var E=new d(0,0),p=Math.ceil(Math.sqrt(n.length)),m=0,y=0,S=0,w=new v(0,0),F=0;F<n.length;F++){F%p==0&&(S=0,y=m,F!=0&&(y+=s.DEFAULT_COMPONENT_SEPERATION),m=0);var V=n[F],R=G.findCenterOfTree(V);E.x=S,E.y=y,w=D.radialLayout(V,R,E),w.y>m&&(m=Math.floor(w.y)),S=Math.floor(w.x+s.DEFAULT_COMPONENT_SEPERATION)}this.transform(new v(N.WORLD_CENTER_X-w.x/2,N.WORLD_CENTER_Y-w.y/2))},D.radialLayout=function(n,E,p){var m=Math.max(this.maxDiagonalInTree(n),s.DEFAULT_RADIAL_SEPARATION);D.branchRadialLayout(E,null,0,359,0,m);var y=q.calculateBounds(n),S=new X;S.setDeviceOrgX(y.getMinX()),S.setDeviceOrgY(y.getMinY()),S.setWorldOrgX(p.x),S.setWorldOrgY(p.y);for(var w=0;w<n.length;w++){var F=n[w];F.transform(S)}var V=new v(y.getMaxX(),y.getMaxY());return S.inverseTransformPoint(V)},D.branchRadialLayout=function(n,E,p,m,y,S){var w=(m-p+1)/2;w<0&&(w+=180);var F=(w+p)%360,V=F*U.TWO_PI/360,R=y*Math.cos(V),Q=y*Math.sin(V);n.setCenter(R,Q);var z=[];z=z.concat(n.getEdges());var H=z.length;E!=null&&H--;for(var rt=0,B=z.length,O,W=n.getEdgesBetween(E);W.length>1;){var $=W[0];W.splice(0,1);var tt=z.indexOf($);tt>=0&&z.splice(tt,1),B--,H--}E!=null?O=(z.indexOf(W[0])+1)%B:O=0;for(var ht=Math.abs(m-p)/H,J=O;rt!=H;J=++J%B){var It=z[J].getOtherEnd(n);if(It!=E){var Nt=(p+rt*ht)%360,vt=(Nt+ht)%360;D.branchRadialLayout(It,n,Nt,vt,y+S,S),rt++}}},D.maxDiagonalInTree=function(n){for(var E=A.MIN_VALUE,p=0;p<n.length;p++){var m=n[p],y=m.getDiagonal();y>E&&(E=y)}return E},D.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},D.prototype.groupZeroDegreeMembers=function(){var n=this,E={};this.memberGroups={},this.idToDummyNode={};for(var p=[],m=this.graphManager.getAllNodes(),y=0;y<m.length;y++){var S=m[y],w=S.getParent();this.getNodeDegreeWithChildren(S)===0&&(w.id==null||!this.getToBeTiled(w))&&p.push(S)}for(var y=0;y<p.length;y++){var S=p[y],F=S.getParent().id;typeof E[F]>"u"&&(E[F]=[]),E[F]=E[F].concat(S)}Object.keys(E).forEach(function(V){if(E[V].length>1){var R="DummyCompound_"+V;n.memberGroups[R]=E[V];var Q=E[V][0].getParent(),z=new t(n.graphManager);z.id=R,z.paddingLeft=Q.paddingLeft||0,z.paddingRight=Q.paddingRight||0,z.paddingBottom=Q.paddingBottom||0,z.paddingTop=Q.paddingTop||0,n.idToDummyNode[R]=z;var H=n.getGraphManager().add(n.newGraph(),z),rt=Q.getChild();rt.add(z);for(var B=0;B<E[V].length;B++){var O=E[V][B];rt.remove(O),H.add(O)}}})},D.prototype.clearCompounds=function(){var n={},E={};this.performDFSOnCompounds();for(var p=0;p<this.compoundOrder.length;p++)E[this.compoundOrder[p].id]=this.compoundOrder[p],n[this.compoundOrder[p].id]=[].concat(this.compoundOrder[p].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[p].getChild()),this.compoundOrder[p].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(n,E)},D.prototype.clearZeroDegreeMembers=function(){var n=this,E=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(p){var m=n.idToDummyNode[p];if(E[p]=n.tileNodes(n.memberGroups[p],m.paddingLeft+m.paddingRight),m.rect.width=E[p].width,m.rect.height=E[p].height,m.setCenter(E[p].centerX,E[p].centerY),m.labelMarginLeft=0,m.labelMarginTop=0,s.NODE_DIMENSIONS_INCLUDE_LABELS){var y=m.rect.width,S=m.rect.height;m.labelWidth&&(m.labelPosHorizontal=="left"?(m.rect.x-=m.labelWidth,m.setWidth(y+m.labelWidth),m.labelMarginLeft=m.labelWidth):m.labelPosHorizontal=="center"&&m.labelWidth>y?(m.rect.x-=(m.labelWidth-y)/2,m.setWidth(m.labelWidth),m.labelMarginLeft=(m.labelWidth-y)/2):m.labelPosHorizontal=="right"&&m.setWidth(y+m.labelWidth)),m.labelHeight&&(m.labelPosVertical=="top"?(m.rect.y-=m.labelHeight,m.setHeight(S+m.labelHeight),m.labelMarginTop=m.labelHeight):m.labelPosVertical=="center"&&m.labelHeight>S?(m.rect.y-=(m.labelHeight-S)/2,m.setHeight(m.labelHeight),m.labelMarginTop=(m.labelHeight-S)/2):m.labelPosVertical=="bottom"&&m.setHeight(S+m.labelHeight))}})},D.prototype.repopulateCompounds=function(){for(var n=this.compoundOrder.length-1;n>=0;n--){var E=this.compoundOrder[n],p=E.id,m=E.paddingLeft,y=E.paddingTop,S=E.labelMarginLeft,w=E.labelMarginTop;this.adjustLocations(this.tiledMemberPack[p],E.rect.x,E.rect.y,m,y,S,w)}},D.prototype.repopulateZeroDegreeMembers=function(){var n=this,E=this.tiledZeroDegreePack;Object.keys(E).forEach(function(p){var m=n.idToDummyNode[p],y=m.paddingLeft,S=m.paddingTop,w=m.labelMarginLeft,F=m.labelMarginTop;n.adjustLocations(E[p],m.rect.x,m.rect.y,y,S,w,F)})},D.prototype.getToBeTiled=function(n){var E=n.id;if(this.toBeTiled[E]!=null)return this.toBeTiled[E];var p=n.getChild();if(p==null)return this.toBeTiled[E]=!1,!1;for(var m=p.getNodes(),y=0;y<m.length;y++){var S=m[y];if(this.getNodeDegree(S)>0)return this.toBeTiled[E]=!1,!1;if(S.getChild()==null){this.toBeTiled[S.id]=!1;continue}if(!this.getToBeTiled(S))return this.toBeTiled[E]=!1,!1}return this.toBeTiled[E]=!0,!0},D.prototype.getNodeDegree=function(n){n.id;for(var E=n.getEdges(),p=0,m=0;m<E.length;m++){var y=E[m];y.getSource().id!==y.getTarget().id&&(p=p+1)}return p},D.prototype.getNodeDegreeWithChildren=function(n){var E=this.getNodeDegree(n);if(n.getChild()==null)return E;for(var p=n.getChild().getNodes(),m=0;m<p.length;m++){var y=p[m];E+=this.getNodeDegreeWithChildren(y)}return E},D.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},D.prototype.fillCompexOrderByDFS=function(n){for(var E=0;E<n.length;E++){var p=n[E];p.getChild()!=null&&this.fillCompexOrderByDFS(p.getChild().getNodes()),this.getToBeTiled(p)&&this.compoundOrder.push(p)}},D.prototype.adjustLocations=function(n,E,p,m,y,S,w){E+=m+S,p+=y+w;for(var F=E,V=0;V<n.rows.length;V++){var R=n.rows[V];E=F;for(var Q=0,z=0;z<R.length;z++){var H=R[z];H.rect.x=E,H.rect.y=p,E+=H.rect.width+n.horizontalPadding,H.rect.height>Q&&(Q=H.rect.height)}p+=Q+n.verticalPadding}},D.prototype.tileCompoundMembers=function(n,E){var p=this;this.tiledMemberPack=[],Object.keys(n).forEach(function(m){var y=E[m];if(p.tiledMemberPack[m]=p.tileNodes(n[m],y.paddingLeft+y.paddingRight),y.rect.width=p.tiledMemberPack[m].width,y.rect.height=p.tiledMemberPack[m].height,y.setCenter(p.tiledMemberPack[m].centerX,p.tiledMemberPack[m].centerY),y.labelMarginLeft=0,y.labelMarginTop=0,s.NODE_DIMENSIONS_INCLUDE_LABELS){var S=y.rect.width,w=y.rect.height;y.labelWidth&&(y.labelPosHorizontal=="left"?(y.rect.x-=y.labelWidth,y.setWidth(S+y.labelWidth),y.labelMarginLeft=y.labelWidth):y.labelPosHorizontal=="center"&&y.labelWidth>S?(y.rect.x-=(y.labelWidth-S)/2,y.setWidth(y.labelWidth),y.labelMarginLeft=(y.labelWidth-S)/2):y.labelPosHorizontal=="right"&&y.setWidth(S+y.labelWidth)),y.labelHeight&&(y.labelPosVertical=="top"?(y.rect.y-=y.labelHeight,y.setHeight(w+y.labelHeight),y.labelMarginTop=y.labelHeight):y.labelPosVertical=="center"&&y.labelHeight>w?(y.rect.y-=(y.labelHeight-w)/2,y.setHeight(y.labelHeight),y.labelMarginTop=(y.labelHeight-w)/2):y.labelPosVertical=="bottom"&&y.setHeight(w+y.labelHeight))}})},D.prototype.tileNodes=function(n,E){var p=this.tileNodesByFavoringDim(n,E,!0),m=this.tileNodesByFavoringDim(n,E,!1),y=this.getOrgRatio(p),S=this.getOrgRatio(m),w;return S<y?w=m:w=p,w},D.prototype.getOrgRatio=function(n){var E=n.width,p=n.height,m=E/p;return m<1&&(m=1/m),m},D.prototype.calcIdealRowWidth=function(n,E){var p=s.TILING_PADDING_VERTICAL,m=s.TILING_PADDING_HORIZONTAL,y=n.length,S=0,w=0,F=0;n.forEach(function(B){S+=B.getWidth(),w+=B.getHeight(),B.getWidth()>F&&(F=B.getWidth())});var V=S/y,R=w/y,Q=Math.pow(p-m,2)+4*(V+m)*(R+p)*y,z=(m-p+Math.sqrt(Q))/(2*(V+m)),H;E?(H=Math.ceil(z),H==z&&H++):H=Math.floor(z);var rt=H*(V+m)-m;return F>rt&&(rt=F),rt+=m*2,rt},D.prototype.tileNodesByFavoringDim=function(n,E,p){var m=s.TILING_PADDING_VERTICAL,y=s.TILING_PADDING_HORIZONTAL,S=s.TILING_COMPARE_BY,w={rows:[],rowWidth:[],rowHeight:[],width:0,height:E,verticalPadding:m,horizontalPadding:y,centerX:0,centerY:0};S&&(w.idealRowWidth=this.calcIdealRowWidth(n,p));var F=C(function(O){return O.rect.width*O.rect.height},"getNodeArea"),V=C(function(O,W){return F(W)-F(O)},"areaCompareFcn");n.sort(function(B,O){var W=V;return w.idealRowWidth?(W=S,W(B.id,O.id)):W(B,O)});for(var R=0,Q=0,z=0;z<n.length;z++){var H=n[z];R+=H.getCenterX(),Q+=H.getCenterY()}w.centerX=R/n.length,w.centerY=Q/n.length;for(var z=0;z<n.length;z++){var H=n[z];if(w.rows.length==0)this.insertNodeToRow(w,H,0,E);else if(this.canAddHorizontal(w,H.rect.width,H.rect.height)){var rt=w.rows.length-1;w.idealRowWidth||(rt=this.getShortestRowIndex(w)),this.insertNodeToRow(w,H,rt,E)}else this.insertNodeToRow(w,H,w.rows.length,E);this.shiftToLastRow(w)}return w},D.prototype.insertNodeToRow=function(n,E,p,m){var y=m;if(p==n.rows.length){var S=[];n.rows.push(S),n.rowWidth.push(y),n.rowHeight.push(0)}var w=n.rowWidth[p]+E.rect.width;n.rows[p].length>0&&(w+=n.horizontalPadding),n.rowWidth[p]=w,n.width<w&&(n.width=w);var F=E.rect.height;p>0&&(F+=n.verticalPadding);var V=0;F>n.rowHeight[p]&&(V=n.rowHeight[p],n.rowHeight[p]=F,V=n.rowHeight[p]-V),n.height+=V,n.rows[p].push(E)},D.prototype.getShortestRowIndex=function(n){for(var E=-1,p=Number.MAX_VALUE,m=0;m<n.rows.length;m++)n.rowWidth[m]<p&&(E=m,p=n.rowWidth[m]);return E},D.prototype.getLongestRowIndex=function(n){for(var E=-1,p=Number.MIN_VALUE,m=0;m<n.rows.length;m++)n.rowWidth[m]>p&&(E=m,p=n.rowWidth[m]);return E},D.prototype.canAddHorizontal=function(n,E,p){if(n.idealRowWidth){var m=n.rows.length-1,y=n.rowWidth[m];return y+E+n.horizontalPadding<=n.idealRowWidth}var S=this.getShortestRowIndex(n);if(S<0)return!0;var w=n.rowWidth[S];if(w+n.horizontalPadding+E<=n.width)return!0;var F=0;n.rowHeight[S]<p&&S>0&&(F=p+n.verticalPadding-n.rowHeight[S]);var V;n.width-w>=E+n.horizontalPadding?V=(n.height+F)/(w+E+n.horizontalPadding):V=(n.height+F)/n.width,F=p+n.verticalPadding;var R;return n.width<E?R=(n.height+F)/E:R=(n.height+F)/n.width,R<1&&(R=1/R),V<1&&(V=1/V),V<R},D.prototype.shiftToLastRow=function(n){var E=this.getLongestRowIndex(n),p=n.rowWidth.length-1,m=n.rows[E],y=m[m.length-1],S=y.width+n.horizontalPadding;if(n.width-n.rowWidth[p]>S&&E!=p){m.splice(-1,1),n.rows[p].push(y),n.rowWidth[E]=n.rowWidth[E]-S,n.rowWidth[p]=n.rowWidth[p]+S,n.width=n.rowWidth[instance.getLongestRowIndex(n)];for(var w=Number.MIN_VALUE,F=0;F<m.length;F++)m[F].height>w&&(w=m[F].height);E>0&&(w+=n.verticalPadding);var V=n.rowHeight[E]+n.rowHeight[p];n.rowHeight[E]=w,n.rowHeight[p]<y.height+n.verticalPadding&&(n.rowHeight[p]=y.height+n.verticalPadding);var R=n.rowHeight[E]+n.rowHeight[p];n.height+=R-V,this.shiftToLastRow(n)}},D.prototype.tilingPreLayout=function(){s.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},D.prototype.tilingPostLayout=function(){s.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},D.prototype.reduceTrees=function(){for(var n=[],E=!0,p;E;){var m=this.graphManager.getAllNodes(),y=[];E=!1;for(var S=0;S<m.length;S++)if(p=m[S],p.getEdges().length==1&&!p.getEdges()[0].isInterGraph&&p.getChild()==null){if(s.PURE_INCREMENTAL){var w=p.getEdges()[0].getOtherEnd(p),F=new L(p.getCenterX()-w.getCenterX(),p.getCenterY()-w.getCenterY());y.push([p,p.getEdges()[0],p.getOwner(),F])}else y.push([p,p.getEdges()[0],p.getOwner()]);E=!0}if(E==!0){for(var V=[],R=0;R<y.length;R++)y[R][0].getEdges().length==1&&(V.push(y[R]),y[R][0].getOwner().remove(y[R][0]));n.push(V),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=n},D.prototype.growTree=function(n){for(var E=n.length,p=n[E-1],m,y=0;y<p.length;y++)m=p[y],this.findPlaceforPrunedNode(m),m[2].add(m[0]),m[2].add(m[1],m[1].source,m[1].target);n.splice(n.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},D.prototype.findPlaceforPrunedNode=function(n){var E,p,m=n[0];if(m==n[1].source?p=n[1].target:p=n[1].source,s.PURE_INCREMENTAL)m.setCenter(p.getCenterX()+n[3].getWidth(),p.getCenterY()+n[3].getHeight());else{var y=p.startX,S=p.finishX,w=p.startY,F=p.finishY,V=0,R=0,Q=0,z=0,H=[V,Q,R,z];if(w>0)for(var rt=y;rt<=S;rt++)H[0]+=this.grid[rt][w-1].length+this.grid[rt][w].length-1;if(S<this.grid.length-1)for(var rt=w;rt<=F;rt++)H[1]+=this.grid[S+1][rt].length+this.grid[S][rt].length-1;if(F<this.grid[0].length-1)for(var rt=y;rt<=S;rt++)H[2]+=this.grid[rt][F+1].length+this.grid[rt][F].length-1;if(y>0)for(var rt=w;rt<=F;rt++)H[3]+=this.grid[y-1][rt].length+this.grid[y][rt].length-1;for(var B=A.MAX_VALUE,O,W,$=0;$<H.length;$++)H[$]<B?(B=H[$],O=1,W=$):H[$]==B&&O++;if(O==3&&B==0)H[0]==0&&H[1]==0&&H[2]==0?E=1:H[0]==0&&H[1]==0&&H[3]==0?E=0:H[0]==0&&H[2]==0&&H[3]==0?E=3:H[1]==0&&H[2]==0&&H[3]==0&&(E=2);else if(O==2&&B==0){var tt=Math.floor(Math.random()*2);H[0]==0&&H[1]==0?tt==0?E=0:E=1:H[0]==0&&H[2]==0?tt==0?E=0:E=2:H[0]==0&&H[3]==0?tt==0?E=0:E=3:H[1]==0&&H[2]==0?tt==0?E=1:E=2:H[1]==0&&H[3]==0?tt==0?E=1:E=3:tt==0?E=2:E=3}else if(O==4&&B==0){var tt=Math.floor(Math.random()*4);E=tt}else E=W;E==0?m.setCenter(p.getCenterX(),p.getCenterY()-p.getHeight()/2-f.DEFAULT_EDGE_LENGTH-m.getHeight()/2):E==1?m.setCenter(p.getCenterX()+p.getWidth()/2+f.DEFAULT_EDGE_LENGTH+m.getWidth()/2,p.getCenterY()):E==2?m.setCenter(p.getCenterX(),p.getCenterY()+p.getHeight()/2+f.DEFAULT_EDGE_LENGTH+m.getHeight()/2):m.setCenter(p.getCenterX()-p.getWidth()/2-f.DEFAULT_EDGE_LENGTH-m.getWidth()/2,p.getCenterY())}},a.exports=D}),991:((a,e,r)=>{var h=r(551).FDLayoutNode,i=r(551).IMath;function u(o,s,c,f){h.call(this,o,s,c,f)}C(u,"CoSENode"),u.prototype=Object.create(h.prototype);for(var t in h)u[t]=h[t];u.prototype.calculateDisplacement=function(){var o=this.graphManager.getLayout();this.getChild()!=null&&this.fixedNodeWeight?(this.displacementX+=o.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.fixedNodeWeight,this.displacementY+=o.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.fixedNodeWeight):(this.displacementX+=o.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY+=o.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren),Math.abs(this.displacementX)>o.coolingFactor*o.maxNodeDisplacement&&(this.displacementX=o.coolingFactor*o.maxNodeDisplacement*i.sign(this.displacementX)),Math.abs(this.displacementY)>o.coolingFactor*o.maxNodeDisplacement&&(this.displacementY=o.coolingFactor*o.maxNodeDisplacement*i.sign(this.displacementY)),this.child&&this.child.getNodes().length>0&&this.propogateDisplacementToChildren(this.displacementX,this.displacementY)},u.prototype.propogateDisplacementToChildren=function(o,s){for(var c=this.getChild().getNodes(),f,N=0;N<c.length;N++)f=c[N],f.getChild()==null?(f.displacementX+=o,f.displacementY+=s):f.propogateDisplacementToChildren(o,s)},u.prototype.move=function(){var o=this.graphManager.getLayout();(this.child==null||this.child.getNodes().length==0)&&(this.moveBy(this.displacementX,this.displacementY),o.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY)),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},u.prototype.setPred1=function(o){this.pred1=o},u.prototype.getPred1=function(){return pred1},u.prototype.getPred2=function(){return pred2},u.prototype.setNext=function(o){this.next=o},u.prototype.getNext=function(){return next},u.prototype.setProcessed=function(o){this.processed=o},u.prototype.isProcessed=function(){return processed},a.exports=u}),902:((a,e,r)=>{function h(c){if(Array.isArray(c)){for(var f=0,N=Array(c.length);f<c.length;f++)N[f]=c[f];return N}else return Array.from(c)}C(h,"_toConsumableArray");var i=r(806),u=r(551).LinkedList,t=r(551).Matrix,o=r(551).SVD;function s(){}C(s,"ConstraintHandler"),s.handleConstraints=function(c){var f={};f.fixedNodeConstraint=c.constraints.fixedNodeConstraint,f.alignmentConstraint=c.constraints.alignmentConstraint,f.relativePlacementConstraint=c.constraints.relativePlacementConstraint;for(var N=new Map,d=new Map,v=[],L=[],G=c.getAllNodes(),A=0,U=0;U<G.length;U++){var q=G[U];q.getChild()==null&&(d.set(q.id,A++),v.push(q.getCenterX()),L.push(q.getCenterY()),N.set(q.id,q))}f.relativePlacementConstraint&&f.relativePlacementConstraint.forEach(function(P){!P.gap&&P.gap!=0&&(P.left?P.gap=i.DEFAULT_EDGE_LENGTH+N.get(P.left).getWidth()/2+N.get(P.right).getWidth()/2:P.gap=i.DEFAULT_EDGE_LENGTH+N.get(P.top).getHeight()/2+N.get(P.bottom).getHeight()/2)});var X=C(function(Y,k){return{x:Y.x-k.x,y:Y.y-k.y}},"calculatePositionDiff"),_=C(function(Y){var k=0,K=0;return Y.forEach(function(Z){k+=v[d.get(Z)],K+=L[d.get(Z)]}),{x:k/Y.size,y:K/Y.size}},"calculateAvgPosition"),D=C(function(Y,k,K,Z,nt){function ct(ft,st){var Lt=new Set(ft),gt=!0,lt=!1,Xt=void 0;try{for(var Tt=st[Symbol.iterator](),Ct;!(gt=(Ct=Tt.next()).done);gt=!0){var Bt=Ct.value;Lt.add(Bt)}}catch(bt){lt=!0,Xt=bt}finally{try{!gt&&Tt.return&&Tt.return()}finally{if(lt)throw Xt}}return Lt}C(ct,"setUnion");var ot=new Map;Y.forEach(function(ft,st){ot.set(st,0)}),Y.forEach(function(ft,st){ft.forEach(function(Lt){ot.set(Lt.id,ot.get(Lt.id)+1)})});var et=new Map,j=new Map,dt=new u;ot.forEach(function(ft,st){ft==0?(dt.push(st),K||(k=="horizontal"?et.set(st,d.has(st)?v[d.get(st)]:Z.get(st)):et.set(st,d.has(st)?L[d.get(st)]:Z.get(st)))):et.set(st,Number.NEGATIVE_INFINITY),K&&j.set(st,new Set([st]))}),K&&nt.forEach(function(ft){var st=[];if(ft.forEach(function(lt){K.has(lt)&&st.push(lt)}),st.length>0){var Lt=0;st.forEach(function(lt){k=="horizontal"?(et.set(lt,d.has(lt)?v[d.get(lt)]:Z.get(lt)),Lt+=et.get(lt)):(et.set(lt,d.has(lt)?L[d.get(lt)]:Z.get(lt)),Lt+=et.get(lt))}),Lt=Lt/st.length,ft.forEach(function(lt){K.has(lt)||et.set(lt,Lt)})}else{var gt=0;ft.forEach(function(lt){k=="horizontal"?gt+=d.has(lt)?v[d.get(lt)]:Z.get(lt):gt+=d.has(lt)?L[d.get(lt)]:Z.get(lt)}),gt=gt/ft.length,ft.forEach(function(lt){et.set(lt,gt)})}});for(var Mt=C(function(){var st=dt.shift(),Lt=Y.get(st);Lt.forEach(function(gt){if(et.get(gt.id)<et.get(st)+gt.gap)if(K&&K.has(gt.id)){var lt=void 0;if(k=="horizontal"?lt=d.has(gt.id)?v[d.get(gt.id)]:Z.get(gt.id):lt=d.has(gt.id)?L[d.get(gt.id)]:Z.get(gt.id),et.set(gt.id,lt),lt<et.get(st)+gt.gap){var Xt=et.get(st)+gt.gap-lt;j.get(st).forEach(function(Tt){et.set(Tt,et.get(Tt)-Xt)})}}else et.set(gt.id,et.get(st)+gt.gap);ot.set(gt.id,ot.get(gt.id)-1),ot.get(gt.id)==0&&dt.push(gt.id),K&&j.set(gt.id,ct(j.get(st),j.get(gt.id)))})},"_loop");dt.length!=0;)Mt();if(K){var pt=new Set;Y.forEach(function(ft,st){ft.length==0&&pt.add(st)});var xt=[];j.forEach(function(ft,st){if(pt.has(st)){var Lt=!1,gt=!0,lt=!1,Xt=void 0;try{for(var Tt=ft[Symbol.iterator](),Ct;!(gt=(Ct=Tt.next()).done);gt=!0){var Bt=Ct.value;K.has(Bt)&&(Lt=!0)}}catch(St){lt=!0,Xt=St}finally{try{!gt&&Tt.return&&Tt.return()}finally{if(lt)throw Xt}}if(!Lt){var bt=!1,zt=void 0;xt.forEach(function(St,kt){St.has([].concat(h(ft))[0])&&(bt=!0,zt=kt)}),bt?ft.forEach(function(St){xt[zt].add(St)}):xt.push(new Set(ft))}}}),xt.forEach(function(ft,st){var Lt=Number.POSITIVE_INFINITY,gt=Number.POSITIVE_INFINITY,lt=Number.NEGATIVE_INFINITY,Xt=Number.NEGATIVE_INFINITY,Tt=!0,Ct=!1,Bt=void 0;try{for(var bt=ft[Symbol.iterator](),zt;!(Tt=(zt=bt.next()).done);Tt=!0){var St=zt.value,kt=void 0;k=="horizontal"?kt=d.has(St)?v[d.get(St)]:Z.get(St):kt=d.has(St)?L[d.get(St)]:Z.get(St);var Kt=et.get(St);kt<Lt&&(Lt=kt),kt>lt&&(lt=kt),Kt<gt&&(gt=Kt),Kt>Xt&&(Xt=Kt)}}catch(ee){Ct=!0,Bt=ee}finally{try{!Tt&&bt.return&&bt.return()}finally{if(Ct)throw Bt}}var le=(Lt+lt)/2-(gt+Xt)/2,Qt=!0,_t=!1,jt=void 0;try{for(var Jt=ft[Symbol.iterator](),oe;!(Qt=(oe=Jt.next()).done);Qt=!0){var te=oe.value;et.set(te,et.get(te)+le)}}catch(ee){_t=!0,jt=ee}finally{try{!Qt&&Jt.return&&Jt.return()}finally{if(_t)throw jt}}})}return et},"findAppropriatePositionForRelativePlacement"),at=C(function(Y){var k=0,K=0,Z=0,nt=0;if(Y.forEach(function(j){j.left?v[d.get(j.left)]-v[d.get(j.right)]>=0?k++:K++:L[d.get(j.top)]-L[d.get(j.bottom)]>=0?Z++:nt++}),k>K&&Z>nt)for(var ct=0;ct<d.size;ct++)v[ct]=-1*v[ct],L[ct]=-1*L[ct];else if(k>K)for(var ot=0;ot<d.size;ot++)v[ot]=-1*v[ot];else if(Z>nt)for(var et=0;et<d.size;et++)L[et]=-1*L[et]},"applyReflectionForRelativePlacement"),n=C(function(Y){var k=[],K=new u,Z=new Set,nt=0;return Y.forEach(function(ct,ot){if(!Z.has(ot)){k[nt]=[];var et=ot;for(K.push(et),Z.add(et),k[nt].push(et);K.length!=0;){et=K.shift();var j=Y.get(et);j.forEach(function(dt){Z.has(dt.id)||(K.push(dt.id),Z.add(dt.id),k[nt].push(dt.id))})}nt++}}),k},"findComponents"),E=C(function(Y){var k=new Map;return Y.forEach(function(K,Z){k.set(Z,[])}),Y.forEach(function(K,Z){K.forEach(function(nt){k.get(Z).push(nt),k.get(nt.id).push({id:Z,gap:nt.gap,direction:nt.direction})})}),k},"dagToUndirected"),p=C(function(Y){var k=new Map;return Y.forEach(function(K,Z){k.set(Z,[])}),Y.forEach(function(K,Z){K.forEach(function(nt){k.get(nt.id).push({id:Z,gap:nt.gap,direction:nt.direction})})}),k},"dagToReversed"),m=[],y=[],S=!1,w=!1,F=new Set,V=new Map,R=new Map,Q=[];if(f.fixedNodeConstraint&&f.fixedNodeConstraint.forEach(function(P){F.add(P.nodeId)}),f.relativePlacementConstraint&&(f.relativePlacementConstraint.forEach(function(P){P.left?(V.has(P.left)?V.get(P.left).push({id:P.right,gap:P.gap,direction:"horizontal"}):V.set(P.left,[{id:P.right,gap:P.gap,direction:"horizontal"}]),V.has(P.right)||V.set(P.right,[])):(V.has(P.top)?V.get(P.top).push({id:P.bottom,gap:P.gap,direction:"vertical"}):V.set(P.top,[{id:P.bottom,gap:P.gap,direction:"vertical"}]),V.has(P.bottom)||V.set(P.bottom,[]))}),R=E(V),Q=n(R)),i.TRANSFORM_ON_CONSTRAINT_HANDLING){if(f.fixedNodeConstraint&&f.fixedNodeConstraint.length>1)f.fixedNodeConstraint.forEach(function(P,Y){m[Y]=[P.position.x,P.position.y],y[Y]=[v[d.get(P.nodeId)],L[d.get(P.nodeId)]]}),S=!0;else if(f.alignmentConstraint)(function(){var P=0;if(f.alignmentConstraint.vertical){for(var Y=f.alignmentConstraint.vertical,k=C(function(et){var j=new Set;Y[et].forEach(function(pt){j.add(pt)});var dt=new Set([].concat(h(j)).filter(function(pt){return F.has(pt)})),Mt=void 0;dt.size>0?Mt=v[d.get(dt.values().next().value)]:Mt=_(j).x,Y[et].forEach(function(pt){m[P]=[Mt,L[d.get(pt)]],y[P]=[v[d.get(pt)],L[d.get(pt)]],P++})},"_loop2"),K=0;K<Y.length;K++)k(K);S=!0}if(f.alignmentConstraint.horizontal){for(var Z=f.alignmentConstraint.horizontal,nt=C(function(et){var j=new Set;Z[et].forEach(function(pt){j.add(pt)});var dt=new Set([].concat(h(j)).filter(function(pt){return F.has(pt)})),Mt=void 0;dt.size>0?Mt=v[d.get(dt.values().next().value)]:Mt=_(j).y,Z[et].forEach(function(pt){m[P]=[v[d.get(pt)],Mt],y[P]=[v[d.get(pt)],L[d.get(pt)]],P++})},"_loop3"),ct=0;ct<Z.length;ct++)nt(ct);S=!0}f.relativePlacementConstraint&&(w=!0)})();else if(f.relativePlacementConstraint){for(var z=0,H=0,rt=0;rt<Q.length;rt++)Q[rt].length>z&&(z=Q[rt].length,H=rt);if(z<R.size/2)at(f.relativePlacementConstraint),S=!1,w=!1;else{var B=new Map,O=new Map,W=[];Q[H].forEach(function(P){V.get(P).forEach(function(Y){Y.direction=="horizontal"?(B.has(P)?B.get(P).push(Y):B.set(P,[Y]),B.has(Y.id)||B.set(Y.id,[]),W.push({left:P,right:Y.id})):(O.has(P)?O.get(P).push(Y):O.set(P,[Y]),O.has(Y.id)||O.set(Y.id,[]),W.push({top:P,bottom:Y.id}))})}),at(W),w=!1;var $=D(B,"horizontal"),tt=D(O,"vertical");Q[H].forEach(function(P,Y){y[Y]=[v[d.get(P)],L[d.get(P)]],m[Y]=[],$.has(P)?m[Y][0]=$.get(P):m[Y][0]=v[d.get(P)],tt.has(P)?m[Y][1]=tt.get(P):m[Y][1]=L[d.get(P)]}),S=!0}}if(S){for(var ht=void 0,J=t.transpose(m),It=t.transpose(y),Nt=0;Nt<J.length;Nt++)J[Nt]=t.multGamma(J[Nt]),It[Nt]=t.multGamma(It[Nt]);var vt=t.multMat(J,t.transpose(It)),it=o.svd(vt);ht=t.multMat(it.V,t.transpose(it.U));for(var ut=0;ut<d.size;ut++){var Et=[v[ut],L[ut]],At=[ht[0][0],ht[1][0]],Ot=[ht[0][1],ht[1][1]];v[ut]=t.dotProduct(Et,At),L[ut]=t.dotProduct(Et,Ot)}w&&at(f.relativePlacementConstraint)}}if(i.ENFORCE_CONSTRAINTS){if(f.fixedNodeConstraint&&f.fixedNodeConstraint.length>0){var mt={x:0,y:0};f.fixedNodeConstraint.forEach(function(P,Y){var k={x:v[d.get(P.nodeId)],y:L[d.get(P.nodeId)]},K=P.position,Z=X(K,k);mt.x+=Z.x,mt.y+=Z.y}),mt.x/=f.fixedNodeConstraint.length,mt.y/=f.fixedNodeConstraint.length,v.forEach(function(P,Y){v[Y]+=mt.x}),L.forEach(function(P,Y){L[Y]+=mt.y}),f.fixedNodeConstraint.forEach(function(P){v[d.get(P.nodeId)]=P.position.x,L[d.get(P.nodeId)]=P.position.y})}if(f.alignmentConstraint){if(f.alignmentConstraint.vertical)for(var Dt=f.alignmentConstraint.vertical,Rt=C(function(Y){var k=new Set;Dt[Y].forEach(function(nt){k.add(nt)});var K=new Set([].concat(h(k)).filter(function(nt){return F.has(nt)})),Z=void 0;K.size>0?Z=v[d.get(K.values().next().value)]:Z=_(k).x,k.forEach(function(nt){F.has(nt)||(v[d.get(nt)]=Z)})},"_loop4"),Ht=0;Ht<Dt.length;Ht++)Rt(Ht);if(f.alignmentConstraint.horizontal)for(var Ut=f.alignmentConstraint.horizontal,Pt=C(function(Y){var k=new Set;Ut[Y].forEach(function(nt){k.add(nt)});var K=new Set([].concat(h(k)).filter(function(nt){return F.has(nt)})),Z=void 0;K.size>0?Z=L[d.get(K.values().next().value)]:Z=_(k).y,k.forEach(function(nt){F.has(nt)||(L[d.get(nt)]=Z)})},"_loop5"),Ft=0;Ft<Ut.length;Ft++)Pt(Ft)}f.relativePlacementConstraint&&(function(){var P=new Map,Y=new Map,k=new Map,K=new Map,Z=new Map,nt=new Map,ct=new Set,ot=new Set;if(F.forEach(function(Gt){ct.add(Gt),ot.add(Gt)}),f.alignmentConstraint){if(f.alignmentConstraint.vertical)for(var et=f.alignmentConstraint.vertical,j=C(function(yt){k.set("dummy"+yt,[]),et[yt].forEach(function(wt){P.set(wt,"dummy"+yt),k.get("dummy"+yt).push(wt),F.has(wt)&&ct.add("dummy"+yt)}),Z.set("dummy"+yt,v[d.get(et[yt][0])])},"_loop6"),dt=0;dt<et.length;dt++)j(dt);if(f.alignmentConstraint.horizontal)for(var Mt=f.alignmentConstraint.horizontal,pt=C(function(yt){K.set("dummy"+yt,[]),Mt[yt].forEach(function(wt){Y.set(wt,"dummy"+yt),K.get("dummy"+yt).push(wt),F.has(wt)&&ot.add("dummy"+yt)}),nt.set("dummy"+yt,L[d.get(Mt[yt][0])])},"_loop7"),xt=0;xt<Mt.length;xt++)pt(xt)}var ft=new Map,st=new Map,Lt=C(function(yt){V.get(yt).forEach(function(wt){var Zt=void 0,$t=void 0;wt.direction=="horizontal"?(Zt=P.get(yt)?P.get(yt):yt,P.get(wt.id)?$t={id:P.get(wt.id),gap:wt.gap,direction:wt.direction}:$t=wt,ft.has(Zt)?ft.get(Zt).push($t):ft.set(Zt,[$t]),ft.has($t.id)||ft.set($t.id,[])):(Zt=Y.get(yt)?Y.get(yt):yt,Y.get(wt.id)?$t={id:Y.get(wt.id),gap:wt.gap,direction:wt.direction}:$t=wt,st.has(Zt)?st.get(Zt).push($t):st.set(Zt,[$t]),st.has($t.id)||st.set($t.id,[]))})},"_loop8"),gt=!0,lt=!1,Xt=void 0;try{for(var Tt=V.keys()[Symbol.iterator](),Ct;!(gt=(Ct=Tt.next()).done);gt=!0){var Bt=Ct.value;Lt(Bt)}}catch(Gt){lt=!0,Xt=Gt}finally{try{!gt&&Tt.return&&Tt.return()}finally{if(lt)throw Xt}}var bt=E(ft),zt=E(st),St=n(bt),kt=n(zt),Kt=p(ft),le=p(st),Qt=[],_t=[];St.forEach(function(Gt,yt){Qt[yt]=[],Gt.forEach(function(wt){Kt.get(wt).length==0&&Qt[yt].push(wt)})}),kt.forEach(function(Gt,yt){_t[yt]=[],Gt.forEach(function(wt){le.get(wt).length==0&&_t[yt].push(wt)})});var jt=D(ft,"horizontal",ct,Z,Qt),Jt=D(st,"vertical",ot,nt,_t),oe=C(function(yt){k.get(yt)?k.get(yt).forEach(function(wt){v[d.get(wt)]=jt.get(yt)}):v[d.get(yt)]=jt.get(yt)},"_loop9"),te=!0,ee=!1,Ee=void 0;try{for(var he=jt.keys()[Symbol.iterator](),Te;!(te=(Te=he.next()).done);te=!0){var fe=Te.value;oe(fe)}}catch(Gt){ee=!0,Ee=Gt}finally{try{!te&&he.return&&he.return()}finally{if(ee)throw Ee}}var Ve=C(function(yt){K.get(yt)?K.get(yt).forEach(function(wt){L[d.get(wt)]=Jt.get(yt)}):L[d.get(yt)]=Jt.get(yt)},"_loop10"),ce=!0,Ne=!1,Le=void 0;try{for(var ge=Jt.keys()[Symbol.iterator](),Ce;!(ce=(Ce=ge.next()).done);ce=!0){var fe=Ce.value;Ve(fe)}}catch(Gt){Ne=!0,Le=Gt}finally{try{!ce&&ge.return&&ge.return()}finally{if(Ne)throw Le}}})()}for(var Yt=0;Yt<G.length;Yt++){var Vt=G[Yt];Vt.getChild()==null&&Vt.setCenter(v[d.get(Vt.id)],L[d.get(Vt.id)])}},a.exports=s}),551:(a=>{a.exports=M})},T={};function g(a){var e=T[a];if(e!==void 0)return e.exports;var r=T[a]={exports:{}};return b[a](r,r.exports,g),r.exports}C(g,"__webpack_require__");var l=g(45);return l})()})}}),lr=ye({"../../node_modules/.pnpm/cytoscape-fcose@2.2.0_cytoscape@3.31.0/node_modules/cytoscape-fcose/cytoscape-fcose.js"(x,I){C(function(b,T){typeof x=="object"&&typeof I=="object"?I.exports=T(Me()):typeof define=="function"&&define.amd?define(["cose-base"],T):typeof x=="object"?x.cytoscapeFcose=T(Me()):b.cytoscapeFcose=T(b.coseBase)},"webpackUniversalModuleDefinition")(x,function(M){return(()=>{var b={658:(a=>{a.exports=Object.assign!=null?Object.assign.bind(Object):function(e){for(var r=arguments.length,h=Array(r>1?r-1:0),i=1;i<r;i++)h[i-1]=arguments[i];return h.forEach(function(u){Object.keys(u).forEach(function(t){return e[t]=u[t]})}),e}}),548:((a,e,r)=>{var h=(function(){function t(o,s){var c=[],f=!0,N=!1,d=void 0;try{for(var v=o[Symbol.iterator](),L;!(f=(L=v.next()).done)&&(c.push(L.value),!(s&&c.length===s));f=!0);}catch(G){N=!0,d=G}finally{try{!f&&v.return&&v.return()}finally{if(N)throw d}}return c}return C(t,"sliceIterator"),function(o,s){if(Array.isArray(o))return o;if(Symbol.iterator in Object(o))return t(o,s);throw new TypeError("Invalid attempt to destructure non-iterable instance")}})(),i=r(140).layoutBase.LinkedList,u={};u.getTopMostNodes=function(t){for(var o={},s=0;s<t.length;s++)o[t[s].id()]=!0;var c=t.filter(function(f,N){typeof f=="number"&&(f=N);for(var d=f.parent()[0];d!=null;){if(o[d.id()])return!1;d=d.parent()[0]}return!0});return c},u.connectComponents=function(t,o,s,c){var f=new i,N=new Set,d=[],v=void 0,L=void 0,G=void 0,A=!1,U=1,q=[],X=[],_=C(function(){var at=t.collection();X.push(at);var n=s[0],E=t.collection();E.merge(n).merge(n.descendants().intersection(o)),d.push(n),E.forEach(function(y){f.push(y),N.add(y),at.merge(y)});for(var p=C(function(){n=f.shift();var S=t.collection();n.neighborhood().nodes().forEach(function(R){o.intersection(n.edgesWith(R)).length>0&&S.merge(R)});for(var w=0;w<S.length;w++){var F=S[w];if(v=s.intersection(F.union(F.ancestors())),v!=null&&!N.has(v[0])){var V=v.union(v.descendants());V.forEach(function(R){f.push(R),N.add(R),at.merge(R),s.has(R)&&d.push(R)})}}},"_loop2");f.length!=0;)p();if(at.forEach(function(y){o.intersection(y.connectedEdges()).forEach(function(S){at.has(S.source())&&at.has(S.target())&&at.merge(S)})}),d.length==s.length&&(A=!0),!A||A&&U>1){L=d[0],G=L.connectedEdges().length,d.forEach(function(y){y.connectedEdges().length<G&&(G=y.connectedEdges().length,L=y)}),q.push(L.id());var m=t.collection();m.merge(d[0]),d.forEach(function(y){m.merge(y)}),d=[],s=s.difference(m),U++}},"_loop");do _();while(!A);return c&&q.length>0&&c.set("dummy"+(c.size+1),q),X},u.relocateComponent=function(t,o,s){if(!s.fixedNodeConstraint){var c=Number.POSITIVE_INFINITY,f=Number.NEGATIVE_INFINITY,N=Number.POSITIVE_INFINITY,d=Number.NEGATIVE_INFINITY;if(s.quality=="draft"){var v=!0,L=!1,G=void 0;try{for(var A=o.nodeIndexes[Symbol.iterator](),U;!(v=(U=A.next()).done);v=!0){var q=U.value,X=h(q,2),_=X[0],D=X[1],at=s.cy.getElementById(_);if(at){var n=at.boundingBox(),E=o.xCoords[D]-n.w/2,p=o.xCoords[D]+n.w/2,m=o.yCoords[D]-n.h/2,y=o.yCoords[D]+n.h/2;E<c&&(c=E),p>f&&(f=p),m<N&&(N=m),y>d&&(d=y)}}}catch(R){L=!0,G=R}finally{try{!v&&A.return&&A.return()}finally{if(L)throw G}}var S=t.x-(f+c)/2,w=t.y-(d+N)/2;o.xCoords=o.xCoords.map(function(R){return R+S}),o.yCoords=o.yCoords.map(function(R){return R+w})}else{Object.keys(o).forEach(function(R){var Q=o[R],z=Q.getRect().x,H=Q.getRect().x+Q.getRect().width,rt=Q.getRect().y,B=Q.getRect().y+Q.getRect().height;z<c&&(c=z),H>f&&(f=H),rt<N&&(N=rt),B>d&&(d=B)});var F=t.x-(f+c)/2,V=t.y-(d+N)/2;Object.keys(o).forEach(function(R){var Q=o[R];Q.setCenter(Q.getCenterX()+F,Q.getCenterY()+V)})}}},u.calcBoundingBox=function(t,o,s,c){for(var f=Number.MAX_SAFE_INTEGER,N=Number.MIN_SAFE_INTEGER,d=Number.MAX_SAFE_INTEGER,v=Number.MIN_SAFE_INTEGER,L=void 0,G=void 0,A=void 0,U=void 0,q=t.descendants().not(":parent"),X=q.length,_=0;_<X;_++){var D=q[_];L=o[c.get(D.id())]-D.width()/2,G=o[c.get(D.id())]+D.width()/2,A=s[c.get(D.id())]-D.height()/2,U=s[c.get(D.id())]+D.height()/2,f>L&&(f=L),N<G&&(N=G),d>A&&(d=A),v<U&&(v=U)}var at={};return at.topLeftX=f,at.topLeftY=d,at.width=N-f,at.height=v-d,at},u.calcParentsWithoutChildren=function(t,o){var s=t.collection();return o.nodes(":parent").forEach(function(c){var f=!1;c.children().forEach(function(N){N.css("display")!="none"&&(f=!0)}),f||s.merge(c)}),s},a.exports=u}),816:((a,e,r)=>{var h=r(548),i=r(140).CoSELayout,u=r(140).CoSENode,t=r(140).layoutBase.PointD,o=r(140).layoutBase.DimensionD,s=r(140).layoutBase.LayoutConstants,c=r(140).layoutBase.FDLayoutConstants,f=r(140).CoSEConstants,N=C(function(v,L){var G=v.cy,A=v.eles,U=A.nodes(),q=A.edges(),X=void 0,_=void 0,D=void 0,at={};v.randomize&&(X=L.nodeIndexes,_=L.xCoords,D=L.yCoords);var n=C(function(R){return typeof R=="function"},"isFn"),E=C(function(R,Q){return n(R)?R(Q):R},"optFn"),p=h.calcParentsWithoutChildren(G,A),m=C(function V(R,Q,z,H){for(var rt=Q.length,B=0;B<rt;B++){var O=Q[B],W=null;O.intersection(p).length==0&&(W=O.children());var $=void 0,tt=O.layoutDimensions({nodeDimensionsIncludeLabels:H.nodeDimensionsIncludeLabels});if(O.outerWidth()!=null&&O.outerHeight()!=null)if(H.randomize)if(!O.isParent())$=R.add(new u(z.graphManager,new t(_[X.get(O.id())]-tt.w/2,D[X.get(O.id())]-tt.h/2),new o(parseFloat(tt.w),parseFloat(tt.h))));else{var ht=h.calcBoundingBox(O,_,D,X);O.intersection(p).length==0?$=R.add(new u(z.graphManager,new t(ht.topLeftX,ht.topLeftY),new o(ht.width,ht.height))):$=R.add(new u(z.graphManager,new t(ht.topLeftX,ht.topLeftY),new o(parseFloat(tt.w),parseFloat(tt.h))))}else $=R.add(new u(z.graphManager,new t(O.position("x")-tt.w/2,O.position("y")-tt.h/2),new o(parseFloat(tt.w),parseFloat(tt.h))));else $=R.add(new u(this.graphManager));if($.id=O.data("id"),$.nodeRepulsion=E(H.nodeRepulsion,O),$.paddingLeft=parseInt(O.css("padding")),$.paddingTop=parseInt(O.css("padding")),$.paddingRight=parseInt(O.css("padding")),$.paddingBottom=parseInt(O.css("padding")),H.nodeDimensionsIncludeLabels&&($.labelWidth=O.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).w,$.labelHeight=O.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).h,$.labelPosVertical=O.css("text-valign"),$.labelPosHorizontal=O.css("text-halign")),at[O.data("id")]=$,isNaN($.rect.x)&&($.rect.x=0),isNaN($.rect.y)&&($.rect.y=0),W!=null&&W.length>0){var J=void 0;J=z.getGraphManager().add(z.newGraph(),$),V(J,W,z,H)}}},"processChildrenList"),y=C(function(R,Q,z){for(var H=0,rt=0,B=0;B<z.length;B++){var O=z[B],W=at[O.data("source")],$=at[O.data("target")];if(W&&$&&W!==$&&W.getEdgesBetween($).length==0){var tt=Q.add(R.newEdge(),W,$);tt.id=O.id(),tt.idealLength=E(v.idealEdgeLength,O),tt.edgeElasticity=E(v.edgeElasticity,O),H+=tt.idealLength,rt++}}v.idealEdgeLength!=null&&(rt>0?f.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=H/rt:n(v.idealEdgeLength)?f.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=50:f.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=v.idealEdgeLength,f.MIN_REPULSION_DIST=c.MIN_REPULSION_DIST=c.DEFAULT_EDGE_LENGTH/10,f.DEFAULT_RADIAL_SEPARATION=c.DEFAULT_EDGE_LENGTH)},"processEdges"),S=C(function(R,Q){Q.fixedNodeConstraint&&(R.constraints.fixedNodeConstraint=Q.fixedNodeConstraint),Q.alignmentConstraint&&(R.constraints.alignmentConstraint=Q.alignmentConstraint),Q.relativePlacementConstraint&&(R.constraints.relativePlacementConstraint=Q.relativePlacementConstraint)},"processConstraints");v.nestingFactor!=null&&(f.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=c.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=v.nestingFactor),v.gravity!=null&&(f.DEFAULT_GRAVITY_STRENGTH=c.DEFAULT_GRAVITY_STRENGTH=v.gravity),v.numIter!=null&&(f.MAX_ITERATIONS=c.MAX_ITERATIONS=v.numIter),v.gravityRange!=null&&(f.DEFAULT_GRAVITY_RANGE_FACTOR=c.DEFAULT_GRAVITY_RANGE_FACTOR=v.gravityRange),v.gravityCompound!=null&&(f.DEFAULT_COMPOUND_GRAVITY_STRENGTH=c.DEFAULT_COMPOUND_GRAVITY_STRENGTH=v.gravityCompound),v.gravityRangeCompound!=null&&(f.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=c.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=v.gravityRangeCompound),v.initialEnergyOnIncremental!=null&&(f.DEFAULT_COOLING_FACTOR_INCREMENTAL=c.DEFAULT_COOLING_FACTOR_INCREMENTAL=v.initialEnergyOnIncremental),v.tilingCompareBy!=null&&(f.TILING_COMPARE_BY=v.tilingCompareBy),v.quality=="proof"?s.QUALITY=2:s.QUALITY=0,f.NODE_DIMENSIONS_INCLUDE_LABELS=c.NODE_DIMENSIONS_INCLUDE_LABELS=s.NODE_DIMENSIONS_INCLUDE_LABELS=v.nodeDimensionsIncludeLabels,f.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=s.DEFAULT_INCREMENTAL=!v.randomize,f.ANIMATE=c.ANIMATE=s.ANIMATE=v.animate,f.TILE=v.tile,f.TILING_PADDING_VERTICAL=typeof v.tilingPaddingVertical=="function"?v.tilingPaddingVertical.call():v.tilingPaddingVertical,f.TILING_PADDING_HORIZONTAL=typeof v.tilingPaddingHorizontal=="function"?v.tilingPaddingHorizontal.call():v.tilingPaddingHorizontal,f.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=s.DEFAULT_INCREMENTAL=!0,f.PURE_INCREMENTAL=!v.randomize,s.DEFAULT_UNIFORM_LEAF_NODE_SIZES=v.uniformNodeDimensions,v.step=="transformed"&&(f.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,f.ENFORCE_CONSTRAINTS=!1,f.APPLY_LAYOUT=!1),v.step=="enforced"&&(f.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,f.ENFORCE_CONSTRAINTS=!0,f.APPLY_LAYOUT=!1),v.step=="cose"&&(f.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,f.ENFORCE_CONSTRAINTS=!1,f.APPLY_LAYOUT=!0),v.step=="all"&&(v.randomize?f.TRANSFORM_ON_CONSTRAINT_HANDLING=!0:f.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,f.ENFORCE_CONSTRAINTS=!0,f.APPLY_LAYOUT=!0),v.fixedNodeConstraint||v.alignmentConstraint||v.relativePlacementConstraint?f.TREE_REDUCTION_ON_INCREMENTAL=!1:f.TREE_REDUCTION_ON_INCREMENTAL=!0;var w=new i,F=w.newGraphManager();return m(F.addRoot(),h.getTopMostNodes(U),w,v),y(w,F,q),S(w,v),w.runLayout(),at},"coseLayout");a.exports={coseLayout:N}}),212:((a,e,r)=>{var h=(function(){function v(L,G){for(var A=0;A<G.length;A++){var U=G[A];U.enumerable=U.enumerable||!1,U.configurable=!0,"value"in U&&(U.writable=!0),Object.defineProperty(L,U.key,U)}}return C(v,"defineProperties"),function(L,G,A){return G&&v(L.prototype,G),A&&v(L,A),L}})();function i(v,L){if(!(v instanceof L))throw new TypeError("Cannot call a class as a function")}C(i,"_classCallCheck");var u=r(658),t=r(548),o=r(657),s=o.spectralLayout,c=r(816),f=c.coseLayout,N=Object.freeze({quality:"default",randomize:!0,animate:!0,animationDuration:1e3,animationEasing:void 0,fit:!0,padding:30,nodeDimensionsIncludeLabels:!1,uniformNodeDimensions:!1,packComponents:!0,step:"all",samplingType:!0,sampleSize:25,nodeSeparation:75,piTol:1e-7,nodeRepulsion:C(function(L){return 4500},"nodeRepulsion"),idealEdgeLength:C(function(L){return 50},"idealEdgeLength"),edgeElasticity:C(function(L){return .45},"edgeElasticity"),nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,tilingCompareBy:void 0,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.3,fixedNodeConstraint:void 0,alignmentConstraint:void 0,relativePlacementConstraint:void 0,ready:C(function(){},"ready"),stop:C(function(){},"stop")}),d=(function(){function v(L){i(this,v),this.options=u({},N,L)}return C(v,"Layout"),h(v,[{key:"run",value:C(function(){var G=this,A=this.options,U=A.cy,q=A.eles,X=[],_=[],D=void 0,at=[];A.fixedNodeConstraint&&(!Array.isArray(A.fixedNodeConstraint)||A.fixedNodeConstraint.length==0)&&(A.fixedNodeConstraint=void 0),A.alignmentConstraint&&(A.alignmentConstraint.vertical&&(!Array.isArray(A.alignmentConstraint.vertical)||A.alignmentConstraint.vertical.length==0)&&(A.alignmentConstraint.vertical=void 0),A.alignmentConstraint.horizontal&&(!Array.isArray(A.alignmentConstraint.horizontal)||A.alignmentConstraint.horizontal.length==0)&&(A.alignmentConstraint.horizontal=void 0)),A.relativePlacementConstraint&&(!Array.isArray(A.relativePlacementConstraint)||A.relativePlacementConstraint.length==0)&&(A.relativePlacementConstraint=void 0);var n=A.fixedNodeConstraint||A.alignmentConstraint||A.relativePlacementConstraint;n&&(A.tile=!1,A.packComponents=!1);var E=void 0,p=!1;if(U.layoutUtilities&&A.packComponents&&(E=U.layoutUtilities("get"),E||(E=U.layoutUtilities()),p=!0),q.nodes().length>0)if(p){var S=t.getTopMostNodes(A.eles.nodes());if(D=t.connectComponents(U,A.eles,S),D.forEach(function(vt){var it=vt.boundingBox();at.push({x:it.x1+it.w/2,y:it.y1+it.h/2})}),A.randomize&&D.forEach(function(vt){A.eles=vt,X.push(s(A))}),A.quality=="default"||A.quality=="proof"){var w=U.collection();if(A.tile){var F=new Map,V=[],R=[],Q=0,z={nodeIndexes:F,xCoords:V,yCoords:R},H=[];if(D.forEach(function(vt,it){vt.edges().length==0&&(vt.nodes().forEach(function(ut,Et){w.merge(vt.nodes()[Et]),ut.isParent()||(z.nodeIndexes.set(vt.nodes()[Et].id(),Q++),z.xCoords.push(vt.nodes()[0].position().x),z.yCoords.push(vt.nodes()[0].position().y))}),H.push(it))}),w.length>1){var rt=w.boundingBox();at.push({x:rt.x1+rt.w/2,y:rt.y1+rt.h/2}),D.push(w),X.push(z);for(var B=H.length-1;B>=0;B--)D.splice(H[B],1),X.splice(H[B],1),at.splice(H[B],1)}}D.forEach(function(vt,it){A.eles=vt,_.push(f(A,X[it])),t.relocateComponent(at[it],_[it],A)})}else D.forEach(function(vt,it){t.relocateComponent(at[it],X[it],A)});var O=new Set;if(D.length>1){var W=[],$=q.filter(function(vt){return vt.css("display")=="none"});D.forEach(function(vt,it){var ut=void 0;if(A.quality=="draft"&&(ut=X[it].nodeIndexes),vt.nodes().not($).length>0){var Et={};Et.edges=[],Et.nodes=[];var At=void 0;vt.nodes().not($).forEach(function(Ot){if(A.quality=="draft")if(!Ot.isParent())At=ut.get(Ot.id()),Et.nodes.push({x:X[it].xCoords[At]-Ot.boundingbox().w/2,y:X[it].yCoords[At]-Ot.boundingbox().h/2,width:Ot.boundingbox().w,height:Ot.boundingbox().h});else{var mt=t.calcBoundingBox(Ot,X[it].xCoords,X[it].yCoords,ut);Et.nodes.push({x:mt.topLeftX,y:mt.topLeftY,width:mt.width,height:mt.height})}else _[it][Ot.id()]&&Et.nodes.push({x:_[it][Ot.id()].getLeft(),y:_[it][Ot.id()].getTop(),width:_[it][Ot.id()].getWidth(),height:_[it][Ot.id()].getHeight()})}),vt.edges().forEach(function(Ot){var mt=Ot.source(),Dt=Ot.target();if(mt.css("display")!="none"&&Dt.css("display")!="none")if(A.quality=="draft"){var Rt=ut.get(mt.id()),Ht=ut.get(Dt.id()),Ut=[],Pt=[];if(mt.isParent()){var Ft=t.calcBoundingBox(mt,X[it].xCoords,X[it].yCoords,ut);Ut.push(Ft.topLeftX+Ft.width/2),Ut.push(Ft.topLeftY+Ft.height/2)}else Ut.push(X[it].xCoords[Rt]),Ut.push(X[it].yCoords[Rt]);if(Dt.isParent()){var Yt=t.calcBoundingBox(Dt,X[it].xCoords,X[it].yCoords,ut);Pt.push(Yt.topLeftX+Yt.width/2),Pt.push(Yt.topLeftY+Yt.height/2)}else Pt.push(X[it].xCoords[Ht]),Pt.push(X[it].yCoords[Ht]);Et.edges.push({startX:Ut[0],startY:Ut[1],endX:Pt[0],endY:Pt[1]})}else _[it][mt.id()]&&_[it][Dt.id()]&&Et.edges.push({startX:_[it][mt.id()].getCenterX(),startY:_[it][mt.id()].getCenterY(),endX:_[it][Dt.id()].getCenterX(),endY:_[it][Dt.id()].getCenterY()})}),Et.nodes.length>0&&(W.push(Et),O.add(it))}});var tt=E.packComponents(W,A.randomize).shifts;if(A.quality=="draft")X.forEach(function(vt,it){var ut=vt.xCoords.map(function(At){return At+tt[it].dx}),Et=vt.yCoords.map(function(At){return At+tt[it].dy});vt.xCoords=ut,vt.yCoords=Et});else{var ht=0;O.forEach(function(vt){Object.keys(_[vt]).forEach(function(it){var ut=_[vt][it];ut.setCenter(ut.getCenterX()+tt[ht].dx,ut.getCenterY()+tt[ht].dy)}),ht++})}}}else{var m=A.eles.boundingBox();if(at.push({x:m.x1+m.w/2,y:m.y1+m.h/2}),A.randomize){var y=s(A);X.push(y)}A.quality=="default"||A.quality=="proof"?(_.push(f(A,X[0])),t.relocateComponent(at[0],_[0],A)):t.relocateComponent(at[0],X[0],A)}var J=C(function(it,ut){if(A.quality=="default"||A.quality=="proof"){typeof it=="number"&&(it=ut);var Et=void 0,At=void 0,Ot=it.data("id");return _.forEach(function(Dt){Ot in Dt&&(Et={x:Dt[Ot].getRect().getCenterX(),y:Dt[Ot].getRect().getCenterY()},At=Dt[Ot])}),A.nodeDimensionsIncludeLabels&&(At.labelWidth&&(At.labelPosHorizontal=="left"?Et.x+=At.labelWidth/2:At.labelPosHorizontal=="right"&&(Et.x-=At.labelWidth/2)),At.labelHeight&&(At.labelPosVertical=="top"?Et.y+=At.labelHeight/2:At.labelPosVertical=="bottom"&&(Et.y-=At.labelHeight/2))),Et==null&&(Et={x:it.position("x"),y:it.position("y")}),{x:Et.x,y:Et.y}}else{var mt=void 0;return X.forEach(function(Dt){var Rt=Dt.nodeIndexes.get(it.id());Rt!=null&&(mt={x:Dt.xCoords[Rt],y:Dt.yCoords[Rt]})}),mt==null&&(mt={x:it.position("x"),y:it.position("y")}),{x:mt.x,y:mt.y}}},"getPositions");if(A.quality=="default"||A.quality=="proof"||A.randomize){var It=t.calcParentsWithoutChildren(U,q),Nt=q.filter(function(vt){return vt.css("display")=="none"});A.eles=q.not(Nt),q.nodes().not(":parent").not(Nt).layoutPositions(G,A,J),It.length>0&&It.forEach(function(vt){vt.position(J(vt))})}else console.log("If randomize option is set to false, then quality option must be 'default' or 'proof'.")},"run")}]),v})();a.exports=d}),657:((a,e,r)=>{var h=r(548),i=r(140).layoutBase.Matrix,u=r(140).layoutBase.SVD,t=C(function(s){var c=s.cy,f=s.eles,N=f.nodes(),d=f.nodes(":parent"),v=new Map,L=new Map,G=new Map,A=[],U=[],q=[],X=[],_=[],D=[],at=[],n=[],E=void 0,p=1e8,m=1e-9,y=s.piTol,S=s.samplingType,w=s.nodeSeparation,F=void 0,V=C(function(){for(var Y=0,k=0,K=!1;k<F;){Y=Math.floor(Math.random()*E),K=!1;for(var Z=0;Z<k;Z++)if(X[Z]==Y){K=!0;break}if(!K)X[k]=Y,k++;else continue}},"randomSampleCR"),R=C(function(Y,k,K){for(var Z=[],nt=0,ct=0,ot=0,et=void 0,j=[],dt=0,Mt=1,pt=0;pt<E;pt++)j[pt]=p;for(Z[ct]=Y,j[Y]=0;ct>=nt;){ot=Z[nt++];for(var xt=A[ot],ft=0;ft<xt.length;ft++)et=L.get(xt[ft]),j[et]==p&&(j[et]=j[ot]+1,Z[++ct]=et);D[ot][k]=j[ot]*w}if(K){for(var st=0;st<E;st++)D[st][k]<_[st]&&(_[st]=D[st][k]);for(var Lt=0;Lt<E;Lt++)_[Lt]>dt&&(dt=_[Lt],Mt=Lt)}return Mt},"BFS"),Q=C(function(Y){var k=void 0;if(Y){k=Math.floor(Math.random()*E);for(var Z=0;Z<E;Z++)_[Z]=p;for(var nt=0;nt<F;nt++)X[nt]=k,k=R(k,nt,Y)}else{V();for(var K=0;K<F;K++)R(X[K],K,Y,!1)}for(var ct=0;ct<E;ct++)for(var ot=0;ot<F;ot++)D[ct][ot]*=D[ct][ot];for(var et=0;et<F;et++)at[et]=[];for(var j=0;j<F;j++)for(var dt=0;dt<F;dt++)at[j][dt]=D[X[dt]][j]},"allBFS"),z=C(function(){for(var Y=u.svd(at),k=Y.S,K=Y.U,Z=Y.V,nt=k[0]*k[0]*k[0],ct=[],ot=0;ot<F;ot++){ct[ot]=[];for(var et=0;et<F;et++)ct[ot][et]=0,ot==et&&(ct[ot][et]=k[ot]/(k[ot]*k[ot]+nt/(k[ot]*k[ot])))}n=i.multMat(i.multMat(Z,ct),i.transpose(K))},"sample"),H=C(function(){for(var Y=void 0,k=void 0,K=[],Z=[],nt=[],ct=[],ot=0;ot<E;ot++)K[ot]=Math.random(),Z[ot]=Math.random();K=i.normalize(K),Z=i.normalize(Z);for(var et=m,j=m,dt=void 0;;){for(var Mt=0;Mt<E;Mt++)nt[Mt]=K[Mt];if(K=i.multGamma(i.multL(i.multGamma(nt),D,n)),Y=i.dotProduct(nt,K),K=i.normalize(K),et=i.dotProduct(nt,K),dt=Math.abs(et/j),dt<=1+y&&dt>=1)break;j=et}for(var pt=0;pt<E;pt++)nt[pt]=K[pt];for(j=m;;){for(var xt=0;xt<E;xt++)ct[xt]=Z[xt];if(ct=i.minusOp(ct,i.multCons(nt,i.dotProduct(nt,ct))),Z=i.multGamma(i.multL(i.multGamma(ct),D,n)),k=i.dotProduct(ct,Z),Z=i.normalize(Z),et=i.dotProduct(ct,Z),dt=Math.abs(et/j),dt<=1+y&&dt>=1)break;j=et}for(var ft=0;ft<E;ft++)ct[ft]=Z[ft];U=i.multCons(nt,Math.sqrt(Math.abs(Y))),q=i.multCons(ct,Math.sqrt(Math.abs(k)))},"powerIteration");h.connectComponents(c,f,h.getTopMostNodes(N),v),d.forEach(function(P){h.connectComponents(c,f,h.getTopMostNodes(P.descendants().intersection(f)),v)});for(var rt=0,B=0;B<N.length;B++)N[B].isParent()||L.set(N[B].id(),rt++);var O=!0,W=!1,$=void 0;try{for(var tt=v.keys()[Symbol.iterator](),ht;!(O=(ht=tt.next()).done);O=!0){var J=ht.value;L.set(J,rt++)}}catch(P){W=!0,$=P}finally{try{!O&&tt.return&&tt.return()}finally{if(W)throw $}}for(var It=0;It<L.size;It++)A[It]=[];d.forEach(function(P){for(var Y=P.children().intersection(f);Y.nodes(":childless").length==0;)Y=Y.nodes()[0].children().intersection(f);var k=0,K=Y.nodes(":childless")[0].connectedEdges().length;Y.nodes(":childless").forEach(function(Z,nt){Z.connectedEdges().length<K&&(K=Z.connectedEdges().length,k=nt)}),G.set(P.id(),Y.nodes(":childless")[k].id())}),N.forEach(function(P){var Y=void 0;P.isParent()?Y=L.get(G.get(P.id())):Y=L.get(P.id()),P.neighborhood().nodes().forEach(function(k){f.intersection(P.edgesWith(k)).length>0&&(k.isParent()?A[Y].push(G.get(k.id())):A[Y].push(k.id()))})});var Nt=C(function(Y){var k=L.get(Y),K=void 0;v.get(Y).forEach(function(Z){c.getElementById(Z).isParent()?K=G.get(Z):K=Z,A[k].push(K),A[L.get(K)].push(Y)})},"_loop"),vt=!0,it=!1,ut=void 0;try{for(var Et=v.keys()[Symbol.iterator](),At;!(vt=(At=Et.next()).done);vt=!0){var Ot=At.value;Nt(Ot)}}catch(P){it=!0,ut=P}finally{try{!vt&&Et.return&&Et.return()}finally{if(it)throw ut}}E=L.size;var mt=void 0;if(E>2){F=E<s.sampleSize?E:s.sampleSize;for(var Dt=0;Dt<E;Dt++)D[Dt]=[];for(var Rt=0;Rt<F;Rt++)n[Rt]=[];return s.quality=="draft"||s.step=="all"?(Q(S),z(),H(),mt={nodeIndexes:L,xCoords:U,yCoords:q}):(L.forEach(function(P,Y){U.push(c.getElementById(Y).position("x")),q.push(c.getElementById(Y).position("y"))}),mt={nodeIndexes:L,xCoords:U,yCoords:q}),mt}else{var Ht=L.keys(),Ut=c.getElementById(Ht.next().value),Pt=Ut.position(),Ft=Ut.outerWidth();if(U.push(Pt.x),q.push(Pt.y),E==2){var Yt=c.getElementById(Ht.next().value),Vt=Yt.outerWidth();U.push(Pt.x+Ft/2+Vt/2+s.idealEdgeLength),q.push(Pt.y)}return mt={nodeIndexes:L,xCoords:U,yCoords:q},mt}},"spectralLayout");a.exports={spectralLayout:t}}),579:((a,e,r)=>{var h=r(212),i=C(function(t){t&&t("layout","fcose",h)},"register");typeof cytoscape<"u"&&i(cytoscape),a.exports=i}),140:(a=>{a.exports=M})},T={};function g(a){var e=T[a];if(e!==void 0)return e.exports;var r=T[a]={exports:{}};return b[a](r,r.exports,g),r.exports}C(g,"__webpack_require__");var l=g(579);return l})()})}}),we={L:"left",R:"right",T:"top",B:"bottom"},Oe={L:C(x=>`${x},${x/2} 0,${x} 0,0`,"L"),R:C(x=>`0,${x/2} ${x},0 ${x},${x}`,"R"),T:C(x=>`0,0 ${x},0 ${x/2},${x}`,"T"),B:C(x=>`${x/2},0 ${x},${x} 0,${x}`,"B")},se={L:C((x,I)=>x-I+2,"L"),R:C((x,I)=>x-2,"R"),T:C((x,I)=>x-I+2,"T"),B:C((x,I)=>x-2,"B")},hr=C(function(x){return Wt(x)?x==="L"?"R":"L":x==="T"?"B":"T"},"getOppositeArchitectureDirection"),De=C(function(x){const I=x;return I==="L"||I==="R"||I==="T"||I==="B"},"isArchitectureDirection"),Wt=C(function(x){const I=x;return I==="L"||I==="R"},"isArchitectureDirectionX"),qt=C(function(x){const I=x;return I==="T"||I==="B"},"isArchitectureDirectionY"),me=C(function(x,I){const M=Wt(x)&&qt(I),b=qt(x)&&Wt(I);return M||b},"isArchitectureDirectionXY"),fr=C(function(x){const I=x[0],M=x[1],b=Wt(I)&&qt(M),T=qt(I)&&Wt(M);return b||T},"isArchitecturePairXY"),cr=C(function(x){return x!=="LL"&&x!=="RR"&&x!=="TT"&&x!=="BB"},"isValidArchitectureDirectionPair"),de=C(function(x,I){const M=`${x}${I}`;return cr(M)?M:void 0},"getArchitectureDirectionPair"),gr=C(function([x,I],M){const b=M[0],T=M[1];return Wt(b)?qt(T)?[x+(b==="L"?-1:1),I+(T==="T"?1:-1)]:[x+(b==="L"?-1:1),I]:Wt(T)?[x+(T==="L"?1:-1),I+(b==="T"?1:-1)]:[x,I+(b==="T"?1:-1)]},"shiftPositionByArchitectureDirectionPair"),ur=C(function(x){return x==="LT"||x==="TL"?[1,1]:x==="BL"||x==="LB"?[1,-1]:x==="BR"||x==="RB"?[-1,-1]:[-1,1]},"getArchitectureDirectionXYFactors"),dr=C(function(x,I){return me(x,I)?"bend":Wt(x)?"horizontal":"vertical"},"getArchitectureDirectionAlignment"),vr=C(function(x){return x.type==="service"},"isArchitectureService"),pr=C(function(x){return x.type==="junction"},"isArchitectureJunction"),Re=C(x=>x.data(),"edgeData"),ie=C(x=>x.data(),"nodeData"),yr=rr.architecture,ae,Se=(ae=class{constructor(){this.nodes={},this.groups={},this.edges=[],this.registeredIds={},this.elements={},this.setAccTitle=Ze,this.getAccTitle=qe,this.setDiagramTitle=Qe,this.getDiagramTitle=Je,this.getAccDescription=Ke,this.setAccDescription=_e,this.clear()}clear(){this.nodes={},this.groups={},this.edges=[],this.registeredIds={},this.dataStructures=void 0,this.elements={},je()}addService({id:I,icon:M,in:b,title:T,iconText:g}){if(this.registeredIds[I]!==void 0)throw new Error(`The service id [${I}] is already in use by another ${this.registeredIds[I]}`);if(b!==void 0){if(I===b)throw new Error(`The service [${I}] cannot be placed within itself`);if(this.registeredIds[b]===void 0)throw new Error(`The service [${I}]'s parent does not exist. Please make sure the parent is created before this service`);if(this.registeredIds[b]==="node")throw new Error(`The service [${I}]'s parent is not a group`)}this.registeredIds[I]="node",this.nodes[I]={id:I,type:"service",icon:M,iconText:g,title:T,edges:[],in:b}}getServices(){return Object.values(this.nodes).filter(vr)}addJunction({id:I,in:M}){this.registeredIds[I]="node",this.nodes[I]={id:I,type:"junction",edges:[],in:M}}getJunctions(){return Object.values(this.nodes).filter(pr)}getNodes(){return Object.values(this.nodes)}getNode(I){return this.nodes[I]??null}addGroup({id:I,icon:M,in:b,title:T}){if(this.registeredIds?.[I]!==void 0)throw new Error(`The group id [${I}] is already in use by another ${this.registeredIds[I]}`);if(b!==void 0){if(I===b)throw new Error(`The group [${I}] cannot be placed within itself`);if(this.registeredIds?.[b]===void 0)throw new Error(`The group [${I}]'s parent does not exist. Please make sure the parent is created before this group`);if(this.registeredIds?.[b]==="node")throw new Error(`The group [${I}]'s parent is not a group`)}this.registeredIds[I]="group",this.groups[I]={id:I,icon:M,title:T,in:b}}getGroups(){return Object.values(this.groups)}addEdge({lhsId:I,rhsId:M,lhsDir:b,rhsDir:T,lhsInto:g,rhsInto:l,lhsGroup:a,rhsGroup:e,title:r}){if(!De(b))throw new Error(`Invalid direction given for left hand side of edge ${I}--${M}. Expected (L,R,T,B) got ${String(b)}`);if(!De(T))throw new Error(`Invalid direction given for right hand side of edge ${I}--${M}. Expected (L,R,T,B) got ${String(T)}`);if(this.nodes[I]===void 0&&this.groups[I]===void 0)throw new Error(`The left-hand id [${I}] does not yet exist. Please create the service/group before declaring an edge to it.`);if(this.nodes[M]===void 0&&this.groups[M]===void 0)throw new Error(`The right-hand id [${M}] does not yet exist. Please create the service/group before declaring an edge to it.`);const h=this.nodes[I].in,i=this.nodes[M].in;if(a&&h&&i&&h==i)throw new Error(`The left-hand id [${I}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);if(e&&h&&i&&h==i)throw new Error(`The right-hand id [${M}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);const u={lhsId:I,lhsDir:b,lhsInto:g,lhsGroup:a,rhsId:M,rhsDir:T,rhsInto:l,rhsGroup:e,title:r};this.edges.push(u),this.nodes[I]&&this.nodes[M]&&(this.nodes[I].edges.push(this.edges[this.edges.length-1]),this.nodes[M].edges.push(this.edges[this.edges.length-1]))}getEdges(){return this.edges}getDataStructures(){if(this.dataStructures===void 0){const I={},M=Object.entries(this.nodes).reduce((e,[r,h])=>(e[r]=h.edges.reduce((i,u)=>{const t=this.getNode(u.lhsId)?.in,o=this.getNode(u.rhsId)?.in;if(t&&o&&t!==o){const s=dr(u.lhsDir,u.rhsDir);s!=="bend"&&(I[t]??={},I[t][o]=s,I[o]??={},I[o][t]=s)}if(u.lhsId===r){const s=de(u.lhsDir,u.rhsDir);s&&(i[s]=u.rhsId)}else{const s=de(u.rhsDir,u.lhsDir);s&&(i[s]=u.lhsId)}return i},{}),e),{}),b=Object.keys(M)[0],T={[b]:1},g=Object.keys(M).reduce((e,r)=>r===b?e:{...e,[r]:1},{}),l=C(e=>{const r={[e]:[0,0]},h=[e];for(;h.length>0;){const i=h.shift();if(i){T[i]=1,delete g[i];const u=M[i],[t,o]=r[i];Object.entries(u).forEach(([s,c])=>{T[c]||(r[c]=gr([t,o],s),h.push(c))})}}return r},"BFS"),a=[l(b)];for(;Object.keys(g).length>0;)a.push(l(Object.keys(g)[0]));this.dataStructures={adjList:M,spatialMaps:a,groupAlignments:I}}return this.dataStructures}setElementForId(I,M){this.elements[I]=M}getElementById(I){return this.elements[I]}getConfig(){return tr({...yr,...er().architecture})}getConfigField(I){return this.getConfig()[I]}},C(ae,"ArchitectureDB"),ae),mr=C((x,I)=>{ze(x,I),x.groups.map(M=>I.addGroup(M)),x.services.map(M=>I.addService({...M,type:"service"})),x.junctions.map(M=>I.addJunction({...M,type:"junction"})),x.edges.map(M=>I.addEdge(M))},"populateDb"),Fe={parser:{yy:void 0},parse:C(async x=>{const I=await Be("architecture",x);Ie.debug(I);const M=Fe.parser?.yy;if(!(M instanceof Se))throw new Error("parser.parser?.yy was not a ArchitectureDB. This is due to a bug within Mermaid, please report this issue at https://github.com/mermaid-js/mermaid/issues.");mr(I,M)},"parse")},Er=C(x=>`
  .edge {
    stroke-width: ${x.archEdgeWidth};
    stroke: ${x.archEdgeColor};
    fill: none;
  }

  .arrow {
    fill: ${x.archEdgeArrowColor};
  }

  .node-bkg {
    fill: none;
    stroke: ${x.archGroupBorderColor};
    stroke-width: ${x.archGroupBorderWidth};
    stroke-dasharray: 8;
  }
  .node-icon-text {
    display: flex; 
    align-items: center;
  }
  
  .node-icon-text > div {
    color: #fff;
    margin: 1px;
    height: fit-content;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
`,"getStyles"),Tr=Er,Nr=nr(lr()),re=C(x=>`<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/>${x}</g>`,"wrapIcon"),ne={prefix:"mermaid-architecture",height:80,width:80,icons:{database:{body:re('<path id="b" data-name="4" d="m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="c" data-name="3" d="m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="d" data-name="2" d="m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse id="e" data-name="1" cx="40" cy="22.14" rx="20" ry="7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="20" y1="57.86" x2="20" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="60" y1="57.86" x2="60" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},server:{body:re('<rect x="17.5" y="17.5" width="45" height="45" rx="2" ry="2" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="32.5" x2="62.5" y2="32.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="47.5" x2="62.5" y2="47.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><g><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g>')},disk:{body:re('<rect x="20" y="15" width="40" height="50" rx="1" ry="1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="14" ry="14.58" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="4" ry="4.17" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z" style="fill: #fff; stroke-width: 0px;"/>')},internet:{body:re('<circle cx="40" cy="40" r="22.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="40" y1="17.5" x2="40" y2="62.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="40" x2="62.5" y2="40" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="30.1" x2="60.25" y2="30.1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="49.9" x2="60.25" y2="49.9" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},cloud:{body:re('<path d="m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},unknown:sr,blank:{body:re("")}}},Lr=C(async function(x,I,M){const b=M.getConfigField("padding"),T=M.getConfigField("iconSize"),g=T/2,l=T/6,a=l/2;await Promise.all(I.edges().map(async e=>{const{source:r,sourceDir:h,sourceArrow:i,sourceGroup:u,target:t,targetDir:o,targetArrow:s,targetGroup:c,label:f}=Re(e);let{x:N,y:d}=e[0].sourceEndpoint();const{x:v,y:L}=e[0].midpoint();let{x:G,y:A}=e[0].targetEndpoint();const U=b+4;if(u&&(Wt(h)?N+=h==="L"?-U:U:d+=h==="T"?-U:U+18),c&&(Wt(o)?G+=o==="L"?-U:U:A+=o==="T"?-U:U+18),!u&&M.getNode(r)?.type==="junction"&&(Wt(h)?N+=h==="L"?g:-g:d+=h==="T"?g:-g),!c&&M.getNode(t)?.type==="junction"&&(Wt(o)?G+=o==="L"?g:-g:A+=o==="T"?g:-g),e[0]._private.rscratch){const q=x.insert("g");if(q.insert("path").attr("d",`M ${N},${d} L ${v},${L} L${G},${A} `).attr("class","edge"),i){const X=Wt(h)?se[h](N,l):N-a,_=qt(h)?se[h](d,l):d-a;q.insert("polygon").attr("points",Oe[h](l)).attr("transform",`translate(${X},${_})`).attr("class","arrow")}if(s){const X=Wt(o)?se[o](G,l):G-a,_=qt(o)?se[o](A,l):A-a;q.insert("polygon").attr("points",Oe[o](l)).attr("transform",`translate(${X},${_})`).attr("class","arrow")}if(f){const X=me(h,o)?"XY":Wt(h)?"X":"Y";let _=0;X==="X"?_=Math.abs(N-G):X==="Y"?_=Math.abs(d-A)/1.5:_=Math.abs(N-G)/2;const D=q.append("g");if(await pe(D,f,{useHtmlLabels:!1,width:_,classes:"architecture-service-label"},ve()),D.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),X==="X")D.attr("transform","translate("+v+", "+L+")");else if(X==="Y")D.attr("transform","translate("+v+", "+L+") rotate(-90)");else if(X==="XY"){const at=de(h,o);if(at&&fr(at)){const n=D.node().getBoundingClientRect(),[E,p]=ur(at);D.attr("dominant-baseline","auto").attr("transform",`rotate(${-1*E*p*45})`);const m=D.node().getBoundingClientRect();D.attr("transform",`
                translate(${v}, ${L-n.height/2})
                translate(${E*m.width/2}, ${p*m.height/2})
                rotate(${-1*E*p*45}, 0, ${n.height/2})
              `)}}}}}))},"drawEdges"),Cr=C(async function(x,I,M){const T=M.getConfigField("padding")*.75,g=M.getConfigField("fontSize"),a=M.getConfigField("iconSize")/2;await Promise.all(I.nodes().map(async e=>{const r=ie(e);if(r.type==="group"){const{h,w:i,x1:u,y1:t}=e.boundingBox();x.append("rect").attr("x",u+a).attr("y",t+a).attr("width",i).attr("height",h).attr("class","node-bkg");const o=x.append("g");let s=u,c=t;if(r.icon){const f=o.append("g");f.html(`<g>${await ue(r.icon,{height:T,width:T,fallbackPrefix:ne.prefix})}</g>`),f.attr("transform","translate("+(s+a+1)+", "+(c+a+1)+")"),s+=T,c+=g/2-1-2}if(r.label){const f=o.append("g");await pe(f,r.label,{useHtmlLabels:!1,width:i,classes:"architecture-service-label"},ve()),f.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","start").attr("text-anchor","start"),f.attr("transform","translate("+(s+a+4)+", "+(c+a+2)+")")}}}))},"drawGroups"),Ar=C(async function(x,I,M){const b=ve();for(const T of M){const g=I.append("g"),l=x.getConfigField("iconSize");if(T.title){const h=g.append("g");await pe(h,T.title,{useHtmlLabels:!1,width:l*1.5,classes:"architecture-service-label"},b),h.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),h.attr("transform","translate("+l/2+", "+l+")")}const a=g.append("g");if(T.icon)a.html(`<g>${await ue(T.icon,{height:l,width:l,fallbackPrefix:ne.prefix})}</g>`);else if(T.iconText){a.html(`<g>${await ue("blank",{height:l,width:l,fallbackPrefix:ne.prefix})}</g>`);const u=a.append("g").append("foreignObject").attr("width",l).attr("height",l).append("div").attr("class","node-icon-text").attr("style",`height: ${l}px;`).append("div").html(ir(T.iconText,b)),t=parseInt(window.getComputedStyle(u.node(),null).getPropertyValue("font-size").replace(/\D/g,""))??16;u.attr("style",`-webkit-line-clamp: ${Math.floor((l-2)/t)};`)}else a.append("path").attr("class","node-bkg").attr("id","node-"+T.id).attr("d",`M0 ${l} v${-l} q0,-5 5,-5 h${l} q5,0 5,5 v${l} H0 Z`);g.attr("class","architecture-service");const{width:e,height:r}=g._groups[0][0].getBBox();T.width=e,T.height=r,x.setElementForId(T.id,g)}return 0},"drawServices"),Mr=C(function(x,I,M){M.forEach(b=>{const T=I.append("g"),g=x.getConfigField("iconSize");T.append("g").append("rect").attr("id","node-"+b.id).attr("fill-opacity","0").attr("width",g).attr("height",g),T.attr("class","architecture-junction");const{width:a,height:e}=T._groups[0][0].getBBox();T.width=a,T.height=e,x.setElementForId(b.id,T)})},"drawJunctions");or([{name:ne.prefix,icons:ne}]);xe.use(Nr.default);function be(x,I,M){x.forEach(b=>{I.add({group:"nodes",data:{type:"service",id:b.id,icon:b.icon,label:b.title,parent:b.in,width:M.getConfigField("iconSize"),height:M.getConfigField("iconSize")},classes:"node-service"})})}C(be,"addServices");function Pe(x,I,M){x.forEach(b=>{I.add({group:"nodes",data:{type:"junction",id:b.id,parent:b.in,width:M.getConfigField("iconSize"),height:M.getConfigField("iconSize")},classes:"node-junction"})})}C(Pe,"addJunctions");function Ge(x,I){I.nodes().map(M=>{const b=ie(M);if(b.type==="group")return;b.x=M.position().x,b.y=M.position().y,x.getElementById(b.id).attr("transform","translate("+(b.x||0)+","+(b.y||0)+")")})}C(Ge,"positionNodes");function Ue(x,I){x.forEach(M=>{I.add({group:"nodes",data:{type:"group",id:M.id,icon:M.icon,label:M.title,parent:M.in},classes:"node-group"})})}C(Ue,"addGroups");function Ye(x,I){x.forEach(M=>{const{lhsId:b,rhsId:T,lhsInto:g,lhsGroup:l,rhsInto:a,lhsDir:e,rhsDir:r,rhsGroup:h,title:i}=M,u=me(M.lhsDir,M.rhsDir)?"segments":"straight",t={id:`${b}-${T}`,label:i,source:b,sourceDir:e,sourceArrow:g,sourceGroup:l,sourceEndpoint:e==="L"?"0 50%":e==="R"?"100% 50%":e==="T"?"50% 0":"50% 100%",target:T,targetDir:r,targetArrow:a,targetGroup:h,targetEndpoint:r==="L"?"0 50%":r==="R"?"100% 50%":r==="T"?"50% 0":"50% 100%"};I.add({group:"edges",data:t,classes:u})})}C(Ye,"addEdges");function Xe(x,I,M){const b=C((a,e)=>Object.entries(a).reduce((r,[h,i])=>{let u=0;const t=Object.entries(i);if(t.length===1)return r[h]=t[0][1],r;for(let o=0;o<t.length-1;o++)for(let s=o+1;s<t.length;s++){const[c,f]=t[o],[N,d]=t[s];if(M[c]?.[N]===e)r[h]??=[],r[h]=[...r[h],...f,...d];else if(c==="default"||N==="default")r[h]??=[],r[h]=[...r[h],...f,...d];else{const L=`${h}-${u++}`;r[L]=f;const G=`${h}-${u++}`;r[G]=d}}return r},{}),"flattenAlignments"),T=I.map(a=>{const e={},r={};return Object.entries(a).forEach(([h,[i,u]])=>{const t=x.getNode(h)?.in??"default";e[u]??={},e[u][t]??=[],e[u][t].push(h),r[i]??={},r[i][t]??=[],r[i][t].push(h)}),{horiz:Object.values(b(e,"horizontal")).filter(h=>h.length>1),vert:Object.values(b(r,"vertical")).filter(h=>h.length>1)}}),[g,l]=T.reduce(([a,e],{horiz:r,vert:h})=>[[...a,...r],[...e,...h]],[[],[]]);return{horizontal:g,vertical:l}}C(Xe,"getAlignments");function He(x,I){const M=[],b=C(g=>`${g[0]},${g[1]}`,"posToStr"),T=C(g=>g.split(",").map(l=>parseInt(l)),"strToPos");return x.forEach(g=>{const l=Object.fromEntries(Object.entries(g).map(([h,i])=>[b(i),h])),a=[b([0,0])],e={},r={L:[-1,0],R:[1,0],T:[0,1],B:[0,-1]};for(;a.length>0;){const h=a.shift();if(h){e[h]=1;const i=l[h];if(i){const u=T(h);Object.entries(r).forEach(([t,o])=>{const s=b([u[0]+o[0],u[1]+o[1]]),c=l[s];c&&!e[s]&&(a.push(s),M.push({[we[t]]:c,[we[hr(t)]]:i,gap:1.5*I.getConfigField("iconSize")}))})}}}}),M}C(He,"getRelativeConstraints");function We(x,I,M,b,T,{spatialMaps:g,groupAlignments:l}){return new Promise(a=>{const e=ar("body").append("div").attr("id","cy").attr("style","display:none"),r=xe({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"straight",label:"data(label)","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"edge.segments",style:{"curve-style":"segments","segment-weights":"0","segment-distances":[.5],"edge-distances":"endpoints","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"node",style:{"compound-sizing-wrt-labels":"include"}},{selector:"node[label]",style:{"text-valign":"bottom","text-halign":"center","font-size":`${T.getConfigField("fontSize")}px`}},{selector:".node-service",style:{label:"data(label)",width:"data(width)",height:"data(height)"}},{selector:".node-junction",style:{width:"data(width)",height:"data(height)"}},{selector:".node-group",style:{padding:`${T.getConfigField("padding")}px`}}],layout:{name:"grid",boundingBox:{x1:0,x2:100,y1:0,y2:100}}});e.remove(),Ue(M,r),be(x,r,T),Pe(I,r,T),Ye(b,r);const h=Xe(T,g,l),i=He(g,T),u=r.layout({name:"fcose",quality:"proof",styleEnabled:!1,animate:!1,nodeDimensionsIncludeLabels:!1,idealEdgeLength(t){const[o,s]=t.connectedNodes(),{parent:c}=ie(o),{parent:f}=ie(s);return c===f?1.5*T.getConfigField("iconSize"):.5*T.getConfigField("iconSize")},edgeElasticity(t){const[o,s]=t.connectedNodes(),{parent:c}=ie(o),{parent:f}=ie(s);return c===f?.45:.001},alignmentConstraint:h,relativePlacementConstraint:i});u.one("layoutstop",()=>{function t(o,s,c,f){let N,d;const{x:v,y:L}=o,{x:G,y:A}=s;d=(f-L+(v-c)*(L-A)/(v-G))/Math.sqrt(1+Math.pow((L-A)/(v-G),2)),N=Math.sqrt(Math.pow(f-L,2)+Math.pow(c-v,2)-Math.pow(d,2));const U=Math.sqrt(Math.pow(G-v,2)+Math.pow(A-L,2));N=N/U;let q=(G-v)*(f-L)-(A-L)*(c-v);switch(!0){case q>=0:q=1;break;case q<0:q=-1;break}let X=(G-v)*(c-v)+(A-L)*(f-L);switch(!0){case X>=0:X=1;break;case X<0:X=-1;break}return d=Math.abs(d)*q,N=N*X,{distances:d,weights:N}}C(t,"getSegmentWeights"),r.startBatch();for(const o of Object.values(r.edges()))if(o.data?.()){const{x:s,y:c}=o.source().position(),{x:f,y:N}=o.target().position();if(s!==f&&c!==N){const d=o.sourceEndpoint(),v=o.targetEndpoint(),{sourceDir:L}=Re(o),[G,A]=qt(L)?[d.x,v.y]:[v.x,d.y],{weights:U,distances:q}=t(d,v,G,A);o.style("segment-distances",q),o.style("segment-weights",U)}}r.endBatch(),u.run()}),u.run(),r.ready(t=>{Ie.info("Ready",t),a(r)})})}C(We,"layoutArchitecture");var wr=C(async(x,I,M,b)=>{const T=b.db,g=T.getServices(),l=T.getJunctions(),a=T.getGroups(),e=T.getEdges(),r=T.getDataStructures(),h=$e(I),i=h.append("g");i.attr("class","architecture-edges");const u=h.append("g");u.attr("class","architecture-services");const t=h.append("g");t.attr("class","architecture-groups"),await Ar(T,u,g),Mr(T,u,l);const o=await We(g,l,a,e,T,r);await Lr(i,o,T),await Cr(t,o,T),Ge(T,o),ke(void 0,h,T.getConfigField("padding"),T.getConfigField("useMaxWidth"))},"draw"),Or={draw:wr},Fr={parser:Fe,get db(){return new Se},renderer:Or,styles:Tr};export{Fr as diagram};
