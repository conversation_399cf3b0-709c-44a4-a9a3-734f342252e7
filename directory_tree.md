.
├── app
│   ├── Account
│   │   ├── Domain
│   │   │   ├── Integration
│   │   │   │   ├── ConnectionInfo.php
│   │   │   │   ├── IntegrationId.php
│   │   │   │   └── Integration.php
│   │   │   ├── Password
│   │   │   │   ├── EncryptedPassword.php
│   │   │   │   └── Password.php
│   │   │   ├── AccountId.php
│   │   │   ├── AccountIntegrationsSet.php
│   │   │   ├── Account.php
│   │   │   ├── AccountRolesSet.php
│   │   │   └── Authentication.php
│   │   └── Infrastructure
│   │       └── Persistence
│   │           └── Doctrine
│   │               ├── Fixture
│   │               │   └── AccountFixture.php
│   │               └── Type
│   │                   ├── AccountIdType.php
│   │                   └── IntegrationIdType.php
│   ├── Blog
│   │   ├── Application
│   │   │   ├── Output
│   │   │   │   ├── Article
│   │   │   │   │   ├── ArticleOutput.php
│   │   │   │   │   ├── FullArticleOutput.php
│   │   │   │   │   ├── ShortArticleOutput.php
│   │   │   │   │   └── ShortArticlesListOutput.php
│   │   │   │   └── Category
│   │   │   │       ├── CategoriesListOutput.php
│   │   │   │       └── CategoryOutput.php
│   │   │   └── UseCase
│   │   │       ├── GetArticleByName
│   │   │       │   ├── Exception
│   │   │       │   │   └── ArticleNotFoundException.php
│   │   │       │   ├── GetArticleByNameOutput.php
│   │   │       │   ├── GetArticleByNameQuery.php
│   │   │       │   └── GetArticleByNameUseCase.php
│   │   │       ├── GetArticlesList
│   │   │       │   ├── Exception
│   │   │       │   │   └── CategoryNotFoundException.php
│   │   │       │   ├── GetArticlesListOutput.php
│   │   │       │   ├── GetArticlesListQuery.php
│   │   │       │   └── GetArticlesListUseCase.php
│   │   │       └── GetCategoriesList
│   │   │           ├── GetCategoriesListOutput.php
│   │   │           ├── GetCategoriesListQuery.php
│   │   │           └── GetCategoriesListUseCase.php
│   │   ├── Domain
│   │   │   ├── Category
│   │   │   │   ├── Repository
│   │   │   │   │   ├── CategoriesListProviderInterface.php
│   │   │   │   │   └── CategoryByUriProviderInterface.php
│   │   │   │   ├── CategoryArticleSet.php
│   │   │   │   ├── CategoryId.php
│   │   │   │   ├── Category.php
│   │   │   │   ├── CategoryRepositoryInterface.php
│   │   │   │   └── CategorySlugGeneratorInterface.php
│   │   │   ├── Content
│   │   │   │   ├── ArticleContent.php
│   │   │   │   ├── ArticleContentRendererInterface.php
│   │   │   │   └── ArticlePreviewGeneratorInterface.php
│   │   │   ├── Repository
│   │   │   │   ├── ArticleByUriProviderInterface.php
│   │   │   │   └── ArticlesListPaginateProviderInterface.php
│   │   │   ├── ArticleId.php
│   │   │   ├── Article.php
│   │   │   ├── ArticleRepositoryInterface.php
│   │   │   └── ArticleSlugGeneratorInterface.php
│   │   ├── Infrastructure
│   │   │   ├── Content
│   │   │   │   ├── ContentRenderer
│   │   │   │   │   └── MarkdownArticleContentRenderer.php
│   │   │   │   └── PreviewGenerator
│   │   │   │       └── SimpleHtmlContentBasedPreviewGenerator.php
│   │   │   ├── Persistence
│   │   │   │   └── Doctrine
│   │   │   │       ├── Fixture
│   │   │   │       │   ├── ArticleFixture
│   │   │   │       │   │   ├── article_fixture.01.md
│   │   │   │       │   │   ├── article_fixture.02.md
│   │   │   │       │   │   └── article_fixture.03.md
│   │   │   │       │   ├── ArticleFixture.php
│   │   │   │       │   └── CategoryFixture.php
│   │   │   │       ├── Listener
│   │   │   │       │   ├── GeneratePreviewOnArticleCreateListener.php
│   │   │   │       │   ├── GenerateSlugOnArticleCreateListener.php
│   │   │   │       │   ├── GenerateSlugOnCategoryCreateListener.php
│   │   │   │       │   └── RenderContentOnArticleSaveListener.php
│   │   │   │       ├── Repository
│   │   │   │       │   ├── ArticleDatabaseRepository.php
│   │   │   │       │   └── CategoryDatabaseRepository.php
│   │   │   │       └── Type
│   │   │   │           ├── ArticleCategoryIdType.php
│   │   │   │           └── ArticleIdType.php
│   │   │   ├── Slug
│   │   │   │   ├── ArticleByTitleSlugGenerator.php
│   │   │   │   └── CategoryByTitleSlugGenerator.php
│   │   │   └── Twig
│   │   │       └── Provider
│   │   │           └── BlogProvider.php
│   │   └── Presentation
│   │       ├── Api
│   │       │   └── Controller
│   │       │       └── .gitignore
│   │       ├── Console
│   │       │   └── .gitignore
│   │       └── Web
│   │           └── Controller
│   │               ├── IndexByCategoryController.php
│   │               ├── IndexController.php
│   │               └── ShowController.php
│   ├── Documentation
│   │   ├── Application
│   │   │   ├── Output
│   │   │   │   ├── Category
│   │   │   │   │   ├── CategoriesListOutput.php
│   │   │   │   │   └── CategoryOutput.php
│   │   │   │   ├── Page
│   │   │   │   │   ├── DocumentOutput.php
│   │   │   │   │   ├── LinkOutput.php
│   │   │   │   │   ├── PageOutput.php
│   │   │   │   │   ├── PagesListOutput.php
│   │   │   │   │   └── PageTypeOutput.php
│   │   │   │   └── Version
│   │   │   │       ├── VersionOutput.php
│   │   │   │       ├── VersionsListOutput.php
│   │   │   │       └── VersionStatusOutput.php
│   │   │   └── UseCase
│   │   │       ├── GetPageByName
│   │   │       │   ├── Exception
│   │   │       │   │   └── PageNotFoundException.php
│   │   │       │   ├── GetPageByNameOutput.php
│   │   │       │   ├── GetPageByNameQuery.php
│   │   │       │   └── GetPageByNameUseCase.php
│   │   │       ├── GetVersionByName
│   │   │       │   ├── Exception
│   │   │       │   │   └── VersionNotFoundException.php
│   │   │       │   ├── GetVersionByNameOutput.php
│   │   │       │   ├── GetVersionByNameQuery.php
│   │   │       │   └── GetVersionByNameUseCase.php
│   │   │       ├── GetVersionsList
│   │   │       │   ├── GetVersionsListOutput.php
│   │   │       │   ├── GetVersionsListQuery.php
│   │   │       │   └── GetVersionsListUseCase.php
│   │   │       ├── UpdateCategories
│   │   │       │   ├── UpdateCategoriesCommand
│   │   │       │   │   └── CategoryIndex.php
│   │   │       │   ├── UpdateCategoriesCommand.php
│   │   │       │   └── UpdateCategoriesUseCase.php
│   │   │       ├── UpdatePages
│   │   │       │   ├── UpdatePagesIndexCommand
│   │   │       │   │   ├── DocumentIndex.php
│   │   │       │   │   ├── LinkIndex.php
│   │   │       │   │   └── PageIndex.php
│   │   │       │   ├── UpdatePagesCommand.php
│   │   │       │   └── UpdatePagesUseCase.php
│   │   │       └── UpdateVersions
│   │   │           ├── UpdateVersionsCommand
│   │   │           │   └── VersionIndex.php
│   │   │           ├── UpdateVersionsCommand.php
│   │   │           └── UpdateVersionsUseCase.php
│   │   ├── Domain
│   │   │   ├── Category
│   │   │   │   ├── Event
│   │   │   │   │   ├── CategoryCreated.php
│   │   │   │   │   ├── CategoryEvent.php
│   │   │   │   │   ├── CategoryRemoved.php
│   │   │   │   │   └── CategoryUpdated.php
│   │   │   │   ├── Repository
│   │   │   │   │   └── CategoryListProviderInterface.php
│   │   │   │   ├── Service
│   │   │   │   │   ├── CategoriesChangeSetComputer
│   │   │   │   │   │   ├── CategoriesComputerInterface.php
│   │   │   │   │   │   ├── CategoriesToCreateComputer.php
│   │   │   │   │   │   ├── CategoriesToRemoveComputer.php
│   │   │   │   │   │   └── CategoriesToUpdateComputer.php
│   │   │   │   │   ├── CategoriesChangePlan.php
│   │   │   │   │   ├── CategoriesChangeSetComputer.php
│   │   │   │   │   └── CategoryInfo.php
│   │   │   │   ├── CategoryId.php
│   │   │   │   ├── CategoryPagesSet.php
│   │   │   │   ├── Category.php
│   │   │   │   └── CategoryRepositoryInterface.php
│   │   │   ├── Content
│   │   │   │   ├── DocumentContent.php
│   │   │   │   └── DocumentContentRendererInterface.php
│   │   │   ├── Event
│   │   │   │   ├── DocumentCreated.php
│   │   │   │   ├── DocumentEvent.php
│   │   │   │   ├── DocumentRemoved.php
│   │   │   │   ├── DocumentUpdated.php
│   │   │   │   ├── LinkCreated.php
│   │   │   │   ├── LinkEvent.php
│   │   │   │   ├── LinkRemoved.php
│   │   │   │   ├── LinkUpdated.php
│   │   │   │   └── PageEvent.php
│   │   │   ├── Repository
│   │   │   │   └── PageByNameProviderInterface.php
│   │   │   ├── Service
│   │   │   │   ├── PagesChangeSetComputer
│   │   │   │   │   ├── PagesComputerInterface.php
│   │   │   │   │   ├── PagesToCreateComputer.php
│   │   │   │   │   ├── PagesToRemoveComputer.php
│   │   │   │   │   └── PagesToUpdateComputer.php
│   │   │   │   ├── DocumentInfo.php
│   │   │   │   ├── LinkInfo.php
│   │   │   │   ├── PageInfo.php
│   │   │   │   ├── PagesChangePlan.php
│   │   │   │   └── PagesChangeSetComputer.php
│   │   │   ├── Version
│   │   │   │   ├── Event
│   │   │   │   │   ├── VersionCreated.php
│   │   │   │   │   ├── VersionDisabled.php
│   │   │   │   │   ├── VersionEnabled.php
│   │   │   │   │   ├── VersionEvent.php
│   │   │   │   │   └── VersionUpdated.php
│   │   │   │   ├── Repository
│   │   │   │   │   ├── CurrentVersionProviderInterface.php
│   │   │   │   │   ├── VersionByNameProviderInterface.php
│   │   │   │   │   └── VersionsListProviderInterface.php
│   │   │   │   ├── Service
│   │   │   │   │   ├── VersionsChangeSetComputer
│   │   │   │   │   │   ├── VersionsComputerInterface.php
│   │   │   │   │   │   ├── VersionsToCreateComputer.php
│   │   │   │   │   │   └── VersionsToUpdateComputer.php
│   │   │   │   │   ├── VersionInfo.php
│   │   │   │   │   ├── VersionsChangePlan.php
│   │   │   │   │   └── VersionsChangeSetComputer.php
│   │   │   │   ├── Status.php
│   │   │   │   ├── VersionCategoriesSet.php
│   │   │   │   ├── VersionId.php
│   │   │   │   ├── Version.php
│   │   │   │   └── VersionRepositoryInterface.php
│   │   │   ├── Document.php
│   │   │   ├── Link.php
│   │   │   ├── PageId.php
│   │   │   ├── Page.php
│   │   │   ├── PageRepositoryInterface.php
│   │   │   ├── PageTitleExtractorInterface.php
│   │   │   └── PageType.php
│   │   ├── Infrastructure
│   │   │   ├── Content
│   │   │   │   └── ContentRenderer
│   │   │   │       ├── Language
│   │   │   │       │   ├── Json5Language.php
│   │   │   │       │   └── MermaidLanguage.php
│   │   │   │       ├── Markdown
│   │   │   │       │   ├── NormalizeInternalLinksProcessor.php
│   │   │   │       │   └── RemoveH1TitleNodeRenderer.php
│   │   │   │       └── MarkdownDocumentContentRenderer.php
│   │   │   ├── Listener
│   │   │   │   ├── ClearCacheOnCategoryChangedListener.php
│   │   │   │   ├── ClearCacheOnPageChangedListener.php
│   │   │   │   └── ClearCacheOnVersionChangedListener.php
│   │   │   ├── PageTitleExtractor
│   │   │   │   ├── MarkdownTitleExtractor.php
│   │   │   │   └── UriTitleExtractor.php
│   │   │   ├── Persistence
│   │   │   │   └── Doctrine
│   │   │   │       ├── Fixture
│   │   │   │       │   ├── CategoryFixture.php
│   │   │   │       │   ├── PageFixture.php
│   │   │   │       │   └── VersionFixture.php
│   │   │   │       ├── Listener
│   │   │   │       │   └── RenderContentOnDocumentSaveListener.php
│   │   │   │       ├── Repository
│   │   │   │       │   ├── CategoryDatabaseRepository.php
│   │   │   │       │   ├── PageDatabaseRepository.php
│   │   │   │       │   └── VersionDatabaseRepository.php
│   │   │   │       └── Type
│   │   │   │           ├── CategoryIdType.php
│   │   │   │           ├── PageIdType.php
│   │   │   │           ├── PageTypeType.php
│   │   │   │           ├── VersionIdType.php
│   │   │   │           └── VersionStatusType.php
│   │   │   └── Twig
│   │   │       └── Provider
│   │   │           └── DocsProvider.php
│   │   └── Presentation
│   │       ├── Api
│   │       │   ├── Controller
│   │       │   │   └── .gitignore
│   │       │   └── Response
│   │       │       └── DTO
│   │       │           └── VersionResponseDTO.php
│   │       ├── Console
│   │       │   └── DocsRerenderCommand.php
│   │       └── Web
│   │           └── Controller
│   │               ├── IndexController.php
│   │               └── ShowController.php
│   ├── Search
│   │   ├── Application
│   │   │   ├── Output
│   │   │   │   ├── SearchResultOutput.php
│   │   │   │   └── SearchResultsListOutput.php
│   │   │   └── UseCase
│   │   │       └── GetDocumentationSearchResults
│   │   │           ├── GetDocumentationSearchResultsOutput.php
│   │   │           ├── GetDocumentationSearchResultsQuery.php
│   │   │           └── GetDocumentationSearchResultsUseCase.php
│   │   ├── Domain
│   │   │   ├── Repository
│   │   │   │   └── SearchByOccurrenceProviderInterface.php
│   │   │   ├── SearchResultId.php
│   │   │   ├── SearchResult.php
│   │   │   └── SearchResultRepositoryInterface.php
│   │   ├── Infrastructure
│   │   │   └── Persistence
│   │   │       └── Doctrine
│   │   │           ├── Listener
│   │   │           │   └── AddHiddenSearchVectorColumn.php
│   │   │           └── Repository
│   │   │               └── SearchResultDatabaseRepository.php
│   │   └── Presentation
│   │       ├── Api
│   │       │   └── Controller
│   │       │       └── .gitignore
│   │       ├── Console
│   │       │   └── .gitignore
│   │       └── Web
│   │           └── Controller
│   │               └── IndexController.php
│   ├── Shared
│   │   ├── Application
│   │   │   └── Output
│   │   │       ├── CollectionOutput.php
│   │   │       └── CountableCollectionOutput.php
│   │   ├── Domain
│   │   │   ├── Bus
│   │   │   │   ├── CommandBusInterface.php
│   │   │   │   ├── CommandId.php
│   │   │   │   ├── EventBusInterface.php
│   │   │   │   ├── EventId.php
│   │   │   │   ├── QueryBusInterface.php
│   │   │   │   └── QueryId.php
│   │   │   ├── Date
│   │   │   │   ├── CreatedDateProviderInterface.php
│   │   │   │   ├── CreatedDateProvider.php
│   │   │   │   ├── UpdatedDateProviderInterface.php
│   │   │   │   └── UpdatedDateProvider.php
│   │   │   ├── Id
│   │   │   │   ├── Exception
│   │   │   │   │   ├── IdException.php
│   │   │   │   │   └── InvalidIdException.php
│   │   │   │   ├── Hash64Id.php
│   │   │   │   ├── IdentifiableInterface.php
│   │   │   │   ├── IdFactoryInterface.php
│   │   │   │   ├── IdInterface.php
│   │   │   │   ├── UniversalUniqueIdFactory.php
│   │   │   │   └── UniversalUniqueId.php
│   │   │   ├── ValueObject
│   │   │   │   ├── BinaryStringValueObjectInterface.php
│   │   │   │   ├── DateTimeValueObjectInterface.php
│   │   │   │   ├── IntValueObjectInterface.php
│   │   │   │   ├── StringValueObjectInterface.php
│   │   │   │   └── ValueObjectInterface.php
│   │   │   ├── AggregateRootInterface.php
│   │   │   ├── ContentPreviewGeneratorInterface.php
│   │   │   ├── ContentRendererInterface.php
│   │   │   ├── DomainException.php
│   │   │   └── SlugGeneratorInterface.php
│   │   ├── Infrastructure
│   │   │   ├── Bus
│   │   │   │   ├── CommandBus
│   │   │   │   │   ├── SymfonyMessengerCommandBus.php
│   │   │   │   │   └── TraceableCommandBus.php
│   │   │   │   ├── EventBus
│   │   │   │   │   ├── FailFreeEventBus.php
│   │   │   │   │   ├── PsrDispatcherEventBus.php
│   │   │   │   │   └── TraceableEventBus.php
│   │   │   │   └── QueryBus
│   │   │   │       ├── InMemoryQueryBus.php
│   │   │   │       ├── SymfonyMessengerQueryBus.php
│   │   │   │       └── TraceableQueryBus.php
│   │   │   ├── Faker
│   │   │   │   └── MarkdownProvider.php
│   │   │   ├── Listener
│   │   │   │   └── ClearUnitOfWorkListener.php
│   │   │   ├── Persistence
│   │   │   │   └── Doctrine
│   │   │   │       ├── Fun
│   │   │   │       │   └── CastFunction.php
│   │   │   │       ├── Listener
│   │   │   │       │   ├── CreatedDateListener.php
│   │   │   │       │   ├── SchemaMigrationsListener.php
│   │   │   │       │   └── UpdatedDateListener.php
│   │   │   │       ├── Migration
│   │   │   │       │   ├── Version20240603184220.php
│   │   │   │       │   ├── Version20240830211215.php
│   │   │   │       │   ├── Version20250701164835.php
│   │   │   │       │   ├── Version20250701164836.php
│   │   │   │       │   ├── Version20250702174835.php
│   │   │   │       │   ├── Version20250702174837.php
│   │   │   │       │   ├── Version20250702175313.php
│   │   │   │       │   ├── Version20250703161455.php
│   │   │   │       │   ├── Version20250706203750.php
│   │   │   │       │   ├── Version20250706204801.php
│   │   │   │       │   ├── Version20250707095239.php
│   │   │   │       │   ├── Version20250707120658.php
│   │   │   │       │   ├── Version20250707124313.php
│   │   │   │       │   ├── Version20250709135133.php
│   │   │   │       │   ├── Version20250711233148.php
│   │   │   │       │   ├── Version20250711233457.php
│   │   │   │       │   ├── Version20250714000328.php
│   │   │   │       │   └── Version20250822160000.php
│   │   │   │       └── Type
│   │   │   │           ├── StringArrayType.php
│   │   │   │           ├── StringEnumArrayType.php
│   │   │   │           ├── StringEnumType.php
│   │   │   │           ├── TsVectorType.php
│   │   │   │           └── UniversalUniqueIdType.php
│   │   │   ├── Response
│   │   │   │   └── ApiV1Mapper.php
│   │   │   ├── Slug
│   │   │   │   └── SlugGenerator.php
│   │   │   └── Kernel.php
│   │   └── Presentation
│   │       ├── Api
│   │       │   ├── Controller
│   │       │   │   └── .gitignore
│   │       │   ├── Request
│   │       │   │   └── RequestTransformer.php
│   │       │   ├── Response
│   │       │   │   ├── DTO
│   │       │   │   │   ├── ApiFailureResponseDTO.php
│   │       │   │   │   ├── ApiResponseDTO.php
│   │       │   │   │   └── ApiSuccessResponseDTO.php
│   │       │   │   └── ResponseTransformer.php
│   │       │   └── Transformer
│   │       │       ├── TransformerInterface.php
│   │       │       └── Transformer.php
│   │       ├── Console
│   │       │   └── .gitignore
│   │       └── Web
│   │           └── Controller
│   │               ├── HomeController.php
│   │               └── LandingController.php
│   └── Sync
│       ├── Application
│       │   ├── Output
│       │   │   └── ExternalDocumentOutput.php
│       │   └── UseCase
│       │       ├── GetExternalDocumentByName
│       │       │   ├── Exception
│       │       │   │   └── DocumentNotFoundException.php
│       │       │   ├── GetExternalDocumentByNameOutput.php
│       │       │   ├── GetExternalDocumentByNameQuery.php
│       │       │   └── GetExternalDocumentByNameUseCase.php
│       │       ├── GitHubWebhookValidate
│       │       │   ├── GitHubWebhookValidateOutput.php
│       │       │   ├── GitHubWebhookValidateQuery.php
│       │       │   └── GitHubWebhookValidateUseCase.php
│       │       ├── SyncCategories
│       │       │   ├── SyncCategoriesCommand.php
│       │       │   └── SyncCategoriesUseCase.php
│       │       ├── SyncPages
│       │       │   ├── SyncPagesCommand.php
│       │       │   └── SyncPagesUseCase.php
│       │       └── SyncVersions
│       │           ├── SyncVersionsCommand.php
│       │           └── SyncVersionsUseCase.php
│       ├── Domain
│       │   ├── Category
│       │   │   ├── Repository
│       │   │   │   └── ExternalCategoriesListProviderInterface.php
│       │   │   ├── ExternalCategoryId.php
│       │   │   ├── ExternalCategory.php
│       │   │   └── ExternalCategoryRepositoryInterface.php
│       │   ├── Repository
│       │   │   ├── ExternalDocumentByNameProviderInterface.php
│       │   │   └── ExternalPageReferencesListProviderInterface.php
│       │   ├── Version
│       │   │   ├── Repository
│       │   │   │   └── ExternalVersionsListProviderInterface.php
│       │   │   ├── ExternalVersionId.php
│       │   │   ├── ExternalVersion.php
│       │   │   └── ExternalVersionRepositoryInterface.php
│       │   ├── ExternalDocument.php
│       │   ├── ExternalLink.php
│       │   ├── ExternalPageId.php
│       │   ├── ExternalPage.php
│       │   └── ExternalPageRepositoryInterface.php
│       ├── Infrastructure
│       │   ├── Listener
│       │   │   ├── OnCategoryCreated.php
│       │   │   ├── OnCategoryUpdated.php
│       │   │   ├── OnVersionCreated.php
│       │   │   └── OnVersionUpdated.php
│       │   └── Persistence
│       │       └── Repository
│       │           ├── ExternalDocumentGitHubRepository
│       │           │   └── LazyInitializedExternalDocumentContent.php
│       │           ├── ExternalCategoryGitHubRepository.php
│       │           ├── ExternalCategoryInMemoryRepository.php
│       │           ├── ExternalPageGitHubRepository.php
│       │           ├── ExternalVersionGitHubRepository.php
│       │           ├── ExternalVersionInMemoryRepository.php
│       │           └── GitHubRepository.php
│       └── Presentation
│           ├── Api
│           │   └── Controller
│           │       ├── GitHubWebhookController
│           │       │   ├── GitHubWebhookRequestDTO.php
│           │       │   └── GitHubWebhookResponseDTO.php
│           │       └── GitHubWebhookController.php
│           ├── Console
│           │   └── DocsUpdateCommand.php
│           └── Web
│               └── Controller
│                   └── .gitignore
├── bin
│   ├── console
│   └── phpunit
├── config
│   ├── packages
│   │   ├── cache.yaml
│   │   ├── debug.yaml.disabled
│   │   ├── doctrine_migrations.yaml
│   │   ├── doctrine.yaml
│   │   ├── framework.yaml
│   │   ├── http_client.yaml
│   │   ├── messenger.yaml
│   │   ├── monolog.yaml
│   │   ├── property_info.yaml
│   │   ├── routing.yaml
│   │   ├── security.yaml
│   │   ├── translation.yaml
│   │   ├── twig.yaml
│   │   ├── validator.yaml
│   │   └── web_profiler.yaml.disabled
│   ├── routes
│   │   ├── blog.yaml
│   │   ├── documentation.yaml
│   │   ├── framework.yaml
│   │   ├── pentatrion_vite.yaml
│   │   ├── search.yaml
│   │   ├── security.yaml
│   │   ├── shared.yaml
│   │   ├── sync.yaml
│   │   └── web_profiler.yaml.disabled
│   ├── secrets
│   │   ├── dev
│   │   │   └── .gitignore
│   │   ├── prod
│   │   │   └── .gitignore
│   │   └── test
│   │       ├── auth.key
│   │       └── auth.key.pub
│   ├── services
│   │   ├── account
│   │   │   ├── domain
│   │   │   │   └── service.yaml
│   │   │   ├── infrastructure
│   │   │   │   └── repositories.yaml
│   │   │   ├── domain.yaml
│   │   │   └── infrastructure.yaml
│   │   ├── blog
│   │   │   ├── application
│   │   │   │   └── use-case.yaml
│   │   │   ├── domain
│   │   │   │   └── service.yaml
│   │   │   ├── infrastructure
│   │   │   │   ├── content
│   │   │   │   │   ├── content-renderer.yaml
│   │   │   │   │   └── preview-generator.yaml
│   │   │   │   ├── content.yaml
│   │   │   │   ├── listeners.yaml
│   │   │   │   ├── repositories.yaml
│   │   │   │   ├── slug.yaml
│   │   │   │   └── twig.yaml
│   │   │   ├── presentation
│   │   │   │   ├── api.yaml
│   │   │   │   ├── console.yaml
│   │   │   │   └── web.yaml
│   │   │   ├── tests
│   │   │   │   └── fixtures.yaml.disabled
│   │   │   ├── application.yaml
│   │   │   ├── domain.yaml
│   │   │   ├── infrastructure.yaml
│   │   │   ├── presentation.yaml
│   │   │   └── tests.yaml
│   │   ├── documentation
│   │   │   ├── application
│   │   │   │   └── use-case.yaml
│   │   │   ├── domain
│   │   │   │   ├── service
│   │   │   │   │   ├── categories.yaml
│   │   │   │   │   ├── pages.yaml
│   │   │   │   │   └── versions.yaml
│   │   │   │   └── service.yaml
│   │   │   ├── infrastructure
│   │   │   │   ├── content
│   │   │   │   │   └── content-renderer.yaml
│   │   │   │   ├── persistence
│   │   │   │   │   └── listeners.yaml
│   │   │   │   ├── content.yaml
│   │   │   │   ├── listeners.yaml
│   │   │   │   ├── persistence.yaml
│   │   │   │   ├── repositories.yaml
│   │   │   │   ├── title-extractor.yaml
│   │   │   │   └── twig.yaml
│   │   │   ├── presentation
│   │   │   │   ├── api.yaml
│   │   │   │   ├── console.yaml
│   │   │   │   └── web.yaml
│   │   │   ├── tests
│   │   │   │   └── fixtures.yaml.disabled
│   │   │   ├── application.yaml
│   │   │   ├── domain.yaml
│   │   │   ├── infrastructure.yaml
│   │   │   ├── presentation.yaml
│   │   │   └── tests.yaml
│   │   ├── search
│   │   │   ├── application
│   │   │   │   └── use-case.yaml
│   │   │   ├── domain
│   │   │   │   └── service.yaml
│   │   │   ├── infrastructure
│   │   │   │   ├── listeners.yaml
│   │   │   │   └── repositories.yaml
│   │   │   ├── presentation
│   │   │   │   ├── api.yaml
│   │   │   │   ├── console.yaml
│   │   │   │   └── web.yaml
│   │   │   ├── application.yaml
│   │   │   ├── domain.yaml
│   │   │   ├── infrastructure.yaml
│   │   │   └── presentation.yaml
│   │   ├── shared
│   │   │   ├── infrastructure
│   │   │   │   ├── bus
│   │   │   │   │   ├── command-bus.yaml
│   │   │   │   │   ├── event-bus.yaml
│   │   │   │   │   └── query-bus.yaml
│   │   │   │   ├── content
│   │   │   │   │   └── code-highlight.yaml
│   │   │   │   ├── persistence
│   │   │   │   │   └── listeners.yaml
│   │   │   │   ├── bus.yaml
│   │   │   │   ├── content.yaml
│   │   │   │   ├── date.yaml
│   │   │   │   ├── faker.yaml.disabled
│   │   │   │   ├── listeners.yaml
│   │   │   │   ├── persistence.yaml
│   │   │   │   ├── response.yaml
│   │   │   │   └── twig.yaml
│   │   │   ├── presentation
│   │   │   │   ├── api.yaml
│   │   │   │   ├── console.yaml
│   │   │   │   └── web.yaml
│   │   │   ├── infrastructure.yaml
│   │   │   └── presentation.yaml
│   │   ├── sync
│   │   │   ├── application
│   │   │   │   └── use-case.yaml
│   │   │   ├── domain
│   │   │   │   └── service.yaml
│   │   │   ├── infrastructure
│   │   │   │   ├── github.yaml
│   │   │   │   ├── listeners.yaml
│   │   │   │   └── repositories.yaml
│   │   │   ├── presentation
│   │   │   │   ├── api.yaml
│   │   │   │   ├── console.yaml
│   │   │   │   └── web.yaml
│   │   │   ├── application.yaml
│   │   │   ├── domain.yaml
│   │   │   ├── infrastructure.yaml
│   │   │   └── presentation.yaml
│   │   ├── account.yaml
│   │   ├── blog.yaml
│   │   ├── documentation.yaml
│   │   ├── search.yaml
│   │   ├── shared.yaml
│   │   ├── sync.yaml
│   │   └── tests.yaml
│   ├── bundles.php
│   ├── preload.php
│   └── services.yaml
├── resources
│   ├── assets
│   │   ├── components
│   │   │   ├── sections
│   │   │   │   ├── call-to-action-section.js
│   │   │   │   ├── docs-toc.js
│   │   │   │   ├── hero-section.js
│   │   │   │   ├── how-it-works-section.js
│   │   │   │   ├── mobile-development-section.js
│   │   │   │   ├── nativeness-section.js
│   │   │   │   ├── right-choice-section.js
│   │   │   │   ├── segment-section.js
│   │   │   │   ├── solves-section.js
│   │   │   │   └── testimonials-section.js
│   │   │   └── ui
│   │   │       ├── logos
│   │   │       │   └── logo.js
│   │   │       ├── breadcrumbs.js
│   │   │       ├── button.js
│   │   │       ├── dots-container.js
│   │   │       ├── dropdown.js
│   │   │       ├── footer.js
│   │   │       ├── header.js
│   │   │       ├── horizontal-accordion.js
│   │   │       ├── mobile-header-menu.js
│   │   │       ├── page-title.js
│   │   │       ├── search-input.js
│   │   │       ├── slider.js
│   │   │       └── subtitle.js
│   │   ├── layout
│   │   │   ├── blog.js
│   │   │   ├── default.js
│   │   │   ├── docs.js
│   │   │   ├── landing.js
│   │   │   └── search.js
│   │   ├── styles
│   │   │   ├── docs.css
│   │   │   ├── layout.css
│   │   │   └── typography.css
│   │   ├── utils
│   │   │   └── sharedStyles.js
│   │   ├── app.css
│   │   └── app.js
│   ├── translations
│   │   └── .gitignore
│   └── views
│       ├── bundles
│       │   └── TwigBundle
│       │       └── Exception
│       │           ├── error404.html.twig
│       │           ├── error500.html.twig
│       │           └── error.html.twig
│       ├── layout
│       │   └── master.html.twig
│       ├── page
│       │   ├── blog
│       │   │   ├── partial
│       │   │   │   ├── articles_list.html.twig
│       │   │   │   └── categories_list.html.twig
│       │   │   ├── index_by_category.html.twig
│       │   │   ├── index.html.twig
│       │   │   └── show.html.twig
│       │   ├── docs
│       │   │   ├── partials
│       │   │   │   └── version_selector.html.twig
│       │   │   ├── index.html.twig
│       │   │   └── show.html.twig
│       │   ├── search
│       │   │   └── index.html.twig
│       │   └── home.html.twig
│       └── partials
│           ├── footer.html.twig
│           └── header.html.twig
├── tests
│   ├── Behat
│   │   ├── Context
│   │   │   ├── Database
│   │   │   │   ├── DatabaseContext.php
│   │   │   │   └── VersionsDatabaseContext.php
│   │   │   ├── Http
│   │   │   │   ├── RequestContext.php
│   │   │   │   ├── ResponseContext.php
│   │   │   │   ├── ResponseJsonPathContext.php
│   │   │   │   ├── ResponseJsonSchemaContext.php
│   │   │   │   ├── ResponseStatusContext.php
│   │   │   │   └── ResponseTypeContext.php
│   │   │   ├── Support
│   │   │   │   └── VarDumperContext.php
│   │   │   └── SymfonyContext.php
│   │   └── Extension
│   │       ├── ContextArgumentTransformerExtension
│   │       │   ├── AsTestingContext.php
│   │       │   ├── ContextSet.php
│   │       │   ├── EnvironmentSet.php
│   │       │   └── PlaceholderArgumentTransformer.php
│   │       └── ContextArgumentTransformerExtension.php
│   ├── Feature
│   │   └── error.v1.json
│   ├── PHPUnit
│   │   └── Constraint
│   │       └── JsonSchemaMatches.php
│   ├── Unit
│   │   ├── Documentation
│   │   │   └── Domain
│   │   │       ├── Category
│   │   │       │   └── CategoriesChangeSetComputerTest.php
│   │   │       ├── Service
│   │   │       │   └── PagesChangeSetComputerTest.php
│   │   │       └── Version
│   │   │           └── Service
│   │   │               └── VersionsChangeSetComputerTest.php
│   │   ├── Relations
│   │   │   ├── CategoryToPagesTest.php
│   │   │   ├── RelationsTestCase.php
│   │   │   └── VersionToCategoriesTest.php
│   │   └── TestCase.php
│   └── bootstrap.php
├── behat.yaml
├── composer.json
├── composer.lock
├── directory_tree.md
├── docker-compose.yml
├── .editorconfig
├── .env
├── .env.example
├── .gitattrbutes
├── .gitignore
├── Makefile
├── package.json
├── package-lock.json
├── .php-cs-fixer.php
├── phpstan.neon
├── phpunit.xml
├── pnpm-lock.yaml
├── README.md
└── vite.config.js

269 directories, 552 files
