default:
  suites:
    default:
      paths:
        - '%paths.base%/tests/Feature'
      contexts:
        # Database
        - App\Tests\Behat\Context\Database\DatabaseContext
        - App\Tests\Behat\Context\Database\VersionsDatabaseContext
        # HTTP
        - App\Tests\Behat\Context\Http\RequestContext
        - App\Tests\Behat\Context\Http\ResponseContext
        - App\Tests\Behat\Context\Http\ResponseJsonPathContext
        - App\Tests\Behat\Context\Http\ResponseJsonSchemaContext
        - App\Tests\Behat\Context\Http\ResponseStatusContext
        - App\Tests\Behat\Context\Http\ResponseTypeContext
        # Utils
        - App\Tests\Behat\Context\Support\VarDumperContext

  extensions:
    App\Tests\Behat\Extension\ContextArgumentTransformerExtension:
      capture:
        start: '{{'
        end: '}}'
    FriendsOfBehat\SymfonyExtension:
      bootstrap: tests/bootstrap.php
      kernel:
        class: App\Shared\Infrastructure\Kernel
        environment: test
        debug: true
