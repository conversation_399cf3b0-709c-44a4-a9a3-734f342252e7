<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         colors="true"
         backupGlobals="true"
         stopOnFailure="false"
         processIsolation="false"
         bootstrap="tests/bootstrap.php"
         cacheDirectory="vendor/.cache.phpunit"
         backupStaticProperties="false"
>
    <php>
        <ini name="display_errors" value="1" />
        <ini name="error_reporting" value="-1" />
        <server name="APP_ENV" value="test" force="true" />
        <server name="APP_SECRET" value="$ecretf0rt3st" force="true" />
        <server name="APP_DEBUG" value="1" force="true" />
        <server name="KERNEL_CLASS" value="App\Shared\Infrastructure\Kernel" />
        <server name="SHELL_VERBOSITY" value="-1" />
        <server name="SYMFONY_PHPUNIT_REMOVE" value="" />
        <server name="SYMFONY_PHPUNIT_VERSION" value="12.2" />
        <server name="SYMFONY_DEPRECATIONS_HELPER" value="999999"/>

        <!-- ###+ symfony/messenger ### -->
        <!-- Choose one of the transports below -->
        <!-- MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages -->
        <!-- MESSENGER_TRANSPORT_DSN=redis://localhost:6379/messages -->
        <env name="MESSENGER_TRANSPORT_DSN" value="doctrine://default?auto_setup=0"/>
        <!-- ###- symfony/messenger ### -->

        <!-- ###+ knplabs/github-api ### -->
        <env name="GITHUB_AUTH_METHOD" value="http_password"/>
        <env name="GITHUB_USERNAME" value="username"/>
        <env name="GITHUB_SECRET" value="password_or_token"/>
        <!-- ###- knplabs/github-api ### -->
    </php>

    <testsuites>
        <testsuite name="unit">
            <directory>tests/Unit</directory>
        </testsuite>
    </testsuites>

    <coverage/>

    <source ignoreSuppressionOfDeprecations="true"
            ignoreIndirectDeprecations="true"
            restrictNotices="true"
            restrictWarnings="true"
    >
        <include>
            <directory>app</directory>
        </include>

        <deprecationTrigger>
            <method>Doctrine\Deprecations\Deprecation::trigger</method>
            <method>Doctrine\Deprecations\Deprecation::delegateTriggerToBackend</method>
            <function>trigger_deprecation</function>
        </deprecationTrigger>
    </source>

    <extensions>
    </extensions>
</phpunit>
