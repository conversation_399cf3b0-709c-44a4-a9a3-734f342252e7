; Custom PHP configuration overrides
; priority=98
[PHP]
; Maximum allowed size for uploaded files.
; https://php.net/upload-max-filesize
upload_max_filesize = 0M

; Maximum number of files that can be uploaded via a single request
max_file_uploads = 0

; Maximum size of POST data that P<PERSON> will accept.
; Its value may be 0 to disable the limit. It is ignored if POST data reading
; is disabled through enable_post_data_reading.
; https://php.net/post-max-size
post_max_size = 10M

memory_limit = 128M

[Session]
; Handler used to serialize data. php is the standard serializer of PHP.
; https://php.net/session.serialize-handler
session.serialize_handler=igbinary

[Date]
; Defines the default timezone used by the date functions
; https://php.net/date.timezone
date.timezone = UTC

[opcache]
; Determines if Zend OPCache is enabled
opcache.enable=1

; Determines if Zend OPCache is enabled for the CLI version of PHP
opcache.enable_cli=1

[apcu]
apc.enabled=1
apc.enable_cli=1
