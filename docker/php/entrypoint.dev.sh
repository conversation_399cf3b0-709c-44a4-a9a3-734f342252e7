#!/usr/bin/env bash
set -e

# ------------------------------------------------------------------------------
#   Generate Dev Keys
# ------------------------------------------------------------------------------

if [ ! -f /home/<USER>/bosonphp.com/config/secrets/dev/auth.key ]; then
  cp -R /home/<USER>/bosonphp.com/config/secrets/test/auth.key \
        /home/<USER>/bosonphp.com/config/secrets/dev/auth.key
fi

if [ ! -f /home/<USER>/bosonphp.com/config/secrets/dev/auth.key.pub ]; then
  cp -R /home/<USER>/bosonphp.com/config/secrets/test/auth.key.pub \
        /home/<USER>/bosonphp.com/config/secrets/dev/auth.key.pub
fi

source /usr/local/bin/entrypoint.sh;
