server {
    charset utf-8;
    server_tokens off;
    server_name boson.localhost;
    root /home/<USER>/bosonphp.com/public;
    index index.php index.html index.htm;

    listen 80;
    listen [::]:80;

    http2 on;
    listen 443 ssl;
    listen [::]:443 ssl;

    http3 on;
    listen 443 quic reuseport;
    listen [::]:443 quic reuseport;
    add_header Alt-Svc 'h3=":443"; ma=86400';

    # SSL configuration
    ssl_certificate /home/<USER>/bosonphp.com/docker/angie/certs/localhost.crt;
    ssl_certificate_key /home/<USER>/bosonphp.com/docker/angie/certs/localhost.key;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # Enable GZIP for any static files
    gzip on;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/javascript;

    # Security
    add_header X-Frame-Options 'DENY';
    add_header X-XSS-Protection '1; mode=block';

    # if you don't like seeing all the errors for missing favicon.ico requests
    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }

    # if you don't like seeing errors for a missing robots.txt in root
    location = /robots.txt {
        access_log off;
        log_not_found off;
    }

    # try to serve file directly, fallback to index.php
    location / {
        gzip_static on;

        try_files $uri /index.php$is_args$args;
    }

    location ~ ^/index\.php(/|$) {
        status_zone index;

        # when using PHP-FPM as a unix socket
        fastcgi_pass php:9000;

        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;

        # When you are using symlinks to link the document root to the current
        # version of your application, you should pass the real application path
        # instead of the path to the symlink to PHP FPM.
        #
        # Otherwise, PHP's OPcache may not properly detect changes to your PHP
        # files (see https://github.com/zendtech/ZendOptimizerPlus/issues/126
        # for more information).
        #
        # Caveat: When PHP-FPM is hosted on a different machine from nginx
        #         $realpath_root may not resolve as you expect! In this case try using
        #         $document_root instead.
        #
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;

        # Prevents URIs that include the front controller. This will 404:
        # http://example.com/index.php/some-path
        # Remove the internal directive to allow URIs like this
        internal;
    }

    # return 404 for all other php files not matching the front controller
    # this prevents access to other php files you don't want to be accessible.
    location ~ \.php$ {
        return 404;
    }

    # return 404 for all "dot" files.
    location ~ /\. {
        return 404;
    }

    # enables caching of static files
    # location ~* ^.+\.(jpg|jpeg|gif|png|svg|ico|css|js|ttf|woff|woff2|md)$ {
    #     root /home/<USER>/phplrt.org/public/;
    #     access_log off;
    #     expires 3d;
    # }

    # Angie monitoring interface
    location /console/ {
        # Local access only
        allow **********;
        allow 127.0.0.1;
        deny all;

        auto_redirect on;

        alias /usr/share/angie-console-light/html/;
        index index.html;

        location /console/api/ {
            api /status/;
            api_config_files on;
        }
    }
}
