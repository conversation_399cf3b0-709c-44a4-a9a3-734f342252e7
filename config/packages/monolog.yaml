monolog:
  channels:
    - deprecation

when@prod:
  monolog:
    handlers:
      file_log:
        type: rotating_file
        path: "%kernel.logs_dir%/%kernel.environment%.log"
        level: error
        max_files: 10
      stderr:
        type: stream
        path: php://stderr
        level: error
        formatter: monolog.formatter.line

when@dev: &dev
  monolog:
    handlers:
      stderr:
        type: stream
        path: php://stderr
        formatter: monolog.formatter.line
        process_psr_3_messages: true
        channels: [ "!event", "!doctrine", "!console", "!deprecation" ]

when@test: *dev

