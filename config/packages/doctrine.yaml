parameters:
  env(DATABASE_URL): 'sqlite:///%kernel.project_dir%/var/data.db'
  env(DATABASE_DRIVER): 'pdo_sqlite'

  db.main.url: '%env(DATABASE_URL)%'
  db.main.driver: '%env(DATABASE_DRIVER)%'

doctrine:
  dbal:
    default_connection: default
    connections:
      default:
        url: '%db.main.url%'
        driver: '%db.main.driver%'
        server_version: '3.40'
        logging: "%kernel.debug%"
        profiling: "%kernel.debug%"
        profiling_collect_backtrace: '%kernel.debug%'
        use_savepoints: true
        mapping_types:
          app_domain_documentation_version_status: string

    types:
      # Account
      App\Account\Domain\Integration\IntegrationId: App\Account\Infrastructure\Persistence\Doctrine\Type\IntegrationIdType
      App\Account\Domain\AccountId: App\Account\Infrastructure\Persistence\Doctrine\Type\AccountIdType
      # Blog
      App\Blog\Domain\ArticleId: App\Blog\Infrastructure\Persistence\Doctrine\Type\ArticleIdType
      App\Blog\Domain\Category\CategoryId: App\Blog\Infrastructure\Persistence\Doctrine\Type\ArticleCategoryIdType
      # Documentation
      App\Documentation\Domain\Category\CategoryId: App\Documentation\Infrastructure\Persistence\Doctrine\Type\CategoryIdType
      App\Documentation\Domain\PageId: App\Documentation\Infrastructure\Persistence\Doctrine\Type\PageIdType
      App\Documentation\Domain\PageType: App\Documentation\Infrastructure\Persistence\Doctrine\Type\PageTypeType
      App\Documentation\Domain\Version\VersionId: App\Documentation\Infrastructure\Persistence\Doctrine\Type\VersionIdType
      App\Documentation\Domain\Version\Status: App\Documentation\Infrastructure\Persistence\Doctrine\Type\VersionStatusType
      # Shared
      string[]: App\Shared\Infrastructure\Persistence\Doctrine\Type\StringArrayType
      tsvector: App\Shared\Infrastructure\Persistence\Doctrine\Type\TsVectorType

  orm:
    dql:
      string_functions:
        cast: App\Shared\Infrastructure\Persistence\Doctrine\Fun\CastFunction
    auto_generate_proxy_classes: true
    enable_lazy_ghost_objects: true
    report_fields_where_declared: true
    validate_xml_mapping: true
    naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware

    controller_resolver:
      auto_mapping: false
    auto_mapping: false
    enable_native_lazy_objects: true
    mappings:
      Account:
        type: attribute
        is_bundle: false
        dir: '%kernel.project_dir%/app/Account/Domain'
        prefix: 'App\Account\Domain'
        alias: account
      Blog:
        type: attribute
        is_bundle: false
        dir: '%kernel.project_dir%/app/Blog/Domain'
        prefix: 'App\Blog\Domain'
        alias: blog
      Documentation:
        type: attribute
        is_bundle: false
        dir: '%kernel.project_dir%/app/Documentation/Domain'
        prefix: 'App\Documentation\Domain'
        alias: documentation

when@test:
  doctrine:
    dbal:
      # "TEST_TOKEN" is typically set by ParaTest
      dbname_suffix: '_test%env(default::TEST_TOKEN)%'

when@prod:
  doctrine:
    orm:
      auto_generate_proxy_classes: false
      proxy_dir: '%kernel.build_dir%/doctrine/orm/Proxies'
      query_cache_driver:
        type: pool
        pool: doctrine.system_cache_pool
      result_cache_driver:
        type: pool
        pool: doctrine.result_cache_pool

  framework:
    cache:
      pools:
        doctrine.result_cache_pool:
          adapter: cache.app
        doctrine.system_cache_pool:
          adapter: cache.system
