
services:
  _defaults:
    autowire: true
    autoconfigure: true

  # Tempest\Highlight\Theme:
  #   class: Tempest\Highlight\Themes\InlineTheme
  #   arguments:
  #     $themePath: '%kernel.project_dir%/vendor/tempest/highlight/src/Themes/Css/one-dark-pro.css'

  App\Documentation\Infrastructure\Content\ContentRenderer\Language\MermaidLanguage: ~
  App\Documentation\Infrastructure\Content\ContentRenderer\Language\Json5Language: ~

  Tempest\Highlight\Theme:
    class: Tempest\Highlight\Themes\CssTheme

  Tempest\Highlight\Highlighter:
    arguments:
      $theme: '@Tempest\Highlight\Theme'
    calls:
      - ['addLanguage', ['@App\Documentation\Infrastructure\Content\ContentRenderer\Language\MermaidLanguage']]
      - ['addLanguage', ['@App\Documentation\Infrastructure\Content\ContentRenderer\Language\Json5Language']]

  Tempest\Highlight\CommonMark\HighlightExtension:
    arguments:
      $highlighter: '@Tempest\Highlight\Highlighter'
