
services:
  _defaults:
    autowire: true
    autoconfigure: true

  App\Shared\Domain\Bus\QueryBusInterface:
    class: App\Shared\Infrastructure\Bus\QueryBus\SymfonyMessengerQueryBus
    arguments:
      $bus: '@query.bus'

  App\Shared\Infrastructure\Bus\QueryBus\InMemoryQueryBus:
    decorates: App\Shared\Domain\Bus\QueryBusInterface
    arguments:
      $delegate: '@.inner'

when@dev:
  services:
    _defaults:
      autowire: true
      autoconfigure: true

    App\Shared\Infrastructure\Bus\QueryBus\TraceableQueryBus:
      decorates: App\Shared\Domain\Bus\QueryBusInterface
      arguments:
        $delegate: '@.inner'
