
services:
  _defaults:
    autowire: true
    autoconfigure: true

  App\Blog\Infrastructure\Persistence\Doctrine\Repository\:
    resource: '%kernel.project_dir%/app/Blog/Infrastructure/Persistence/Doctrine/Repository'

  # ----------------------------------------------------------------------------
  #   Article
  # ----------------------------------------------------------------------------

  App\Blog\Domain\ArticleRepositoryInterface:
    alias: App\Blog\Infrastructure\Persistence\Doctrine\Repository\ArticleDatabaseRepository

  App\Blog\Domain\Repository\ArticlesListPaginateProviderInterface:
    alias: App\Blog\Infrastructure\Persistence\Doctrine\Repository\ArticleDatabaseRepository

  App\Blog\Domain\Repository\ArticleByUriProviderInterface:
    alias: App\Blog\Infrastructure\Persistence\Doctrine\Repository\ArticleDatabaseRepository

  # ----------------------------------------------------------------------------
  #   Category
  # ----------------------------------------------------------------------------

  App\Blog\Domain\Category\CategoryRepositoryInterface:
    alias: App\Blog\Infrastructure\Persistence\Doctrine\Repository\CategoryDatabaseRepository

  App\Blog\Domain\Category\Repository\CategoriesListProviderInterface:
    alias: App\Blog\Infrastructure\Persistence\Doctrine\Repository\CategoryDatabaseRepository

  App\Blog\Domain\Category\Repository\CategoryByUriProviderInterface:
    alias: App\Blog\Infrastructure\Persistence\Doctrine\Repository\CategoryDatabaseRepository
