parameters:
  env(SYNC_GITHUB_OWNER): 'boson-php'
  env(SYNC_GITHUB_REPOSITORY): 'docs'
  env(SYNC_NAV_PATHNAME): '.nav.json'

  app.sync.github_owner: '%env(SYNC_GITHUB_OWNER)%'
  app.sync.github_repository: '%env(SYNC_GITHUB_REPOSITORY)%'
  app.sync.nav_pathname: '%env(SYNC_NAV_PATHNAME)%'

services:
  _defaults:
    autowire: true
    autoconfigure: true

  App\Sync\Infrastructure\Persistence\Repository\:
    resource: '%kernel.project_dir%/app/Sync/Infrastructure/Persistence/Repository'

  # ----------------------------------------------------------------------------
  #   External Version
  # ----------------------------------------------------------------------------

  App\Sync\Infrastructure\Persistence\Repository\ExternalVersionGitHubRepository:
    arguments:
      $owner: '%app.sync.github_owner%'
      $repository: '%app.sync.github_repository%'

  App\Sync\Infrastructure\Persistence\Repository\ExternalVersionInMemoryRepository:
    decorates: App\Sync\Infrastructure\Persistence\Repository\ExternalVersionGitHubRepository
    arguments:
      $delegate: '@.inner'

  App\Sync\Domain\Version\ExternalVersionRepositoryInterface:
    alias: App\Sync\Infrastructure\Persistence\Repository\ExternalVersionGitHubRepository

  App\Sync\Domain\Version\Repository\ExternalVersionsListProviderInterface:
    alias: App\Sync\Infrastructure\Persistence\Repository\ExternalVersionGitHubRepository

  # ----------------------------------------------------------------------------
  #   External Category
  # ----------------------------------------------------------------------------

  App\Sync\Infrastructure\Persistence\Repository\ExternalCategoryGitHubRepository:
    arguments:
      $navigation: '%app.sync.nav_pathname%'

  App\Sync\Infrastructure\Persistence\Repository\ExternalCategoryInMemoryRepository:
    decorates: App\Sync\Infrastructure\Persistence\Repository\ExternalCategoryGitHubRepository
    arguments:
      $delegate: '@.inner'

  App\Sync\Domain\Category\ExternalCategoryRepositoryInterface:
    alias: App\Sync\Infrastructure\Persistence\Repository\ExternalCategoryGitHubRepository

  App\Sync\Domain\Category\Repository\ExternalCategoriesListProviderInterface:
    alias: App\Sync\Infrastructure\Persistence\Repository\ExternalCategoryGitHubRepository

  # ----------------------------------------------------------------------------
  #   External Documentation
  # ----------------------------------------------------------------------------

  App\Sync\Infrastructure\Persistence\Repository\ExternalPageGitHubRepository:
    arguments:
      $owner: '%app.sync.github_owner%'
      $repository: '%app.sync.github_repository%'

  App\Sync\Domain\ExternalPageRepositoryInterface:
    alias: App\Sync\Infrastructure\Persistence\Repository\ExternalPageGitHubRepository

  App\Sync\Domain\Repository\ExternalPageReferencesListProviderInterface:
    alias: App\Sync\Infrastructure\Persistence\Repository\ExternalPageGitHubRepository

  App\Sync\Domain\Repository\ExternalDocumentByNameProviderInterface:
    alias: App\Sync\Infrastructure\Persistence\Repository\ExternalPageGitHubRepository
