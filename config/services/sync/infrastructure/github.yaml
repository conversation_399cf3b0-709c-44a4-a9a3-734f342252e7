
parameters:
  env(GITHUB_WEBHOOK_SECRET): 'webhook_secret_value'
  env(GITHUB_SECRET): 'secret_value'

  app.github.webhook.secret: '%env(GITHUB_WEBHOOK_SECRET)%'
  app.github.secret: '%env(GITHUB_SECRET)%'

services:
  _defaults:
    autowire: true
    autoconfigure: true

  Github\HttpClient\Builder:
    arguments:
      - '@psr18.http_client'
      - '@psr18.http_client'
      - '@psr18.http_client'

  Github\Client:
    arguments:
      $httpClientBuilder: '@Github\HttpClient\Builder'
    calls:
      - ['authenticate', ['%app.github.secret%', 'access_token_header']]
