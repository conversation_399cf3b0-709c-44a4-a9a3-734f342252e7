@import url("../../vendor/tempest/highlight/src/Themes/Css/one-dark-pro.css");

@import url("./styles/layout.css");
@import url("./styles/typography.css");
@import url("./styles/docs.css");

/**
 * Naming:
 *
 *  --[type]-[task]-[context]-[action]: ...
 *
 * Examples:
 *
 *  --[color|width|font] : Type is color, or width, or font, or etc
 *  --color-[bg|text|border] : Type is color and task is background, or ...
 *  --color-text-[button|link|tag] : Context is button, dropdown, link, tag, etc
 *  --color-text-button-[*|hover|opacity|active|disabled|placeholder]
 */
:root {
  /**
   * ---------------------------------------------------------------------------
   *  Overlay
   * ---------------------------------------------------------------------------
   */

  /**
   * Main background colors
   */
  --color-bg: #0d1119;
  --color-bg-opacity: rgba(var(--color-bg), .5);
  --color-bg-hover: rgba(158, 174, 242, 0.1);

  /**
   * Layer (dropdowns, popups, etc) element background colors
   */
  --color-bg-layer: #0f131c;
  --color-bg-layer-opacity: rgba(var(--color-bg-layer), .5);
  --color-bg-layer-hover: #0A0A0A;

  /**
   * Main + secondary text colors
   */
  --color-text: rgba(255, 255, 255, 0.9);
  --color-text-secondary: rgba(255, 255, 255, 0.6);

  /**
   * Main brand background color
   */
  --color-text-brand: #F93904;
  --color-text-brand-opacity: rgba(var(--color-text-brand), .5);
  --color-text-brand-hover: #972203;

  /**
   * Main border color
   */
  --color-border: rgba(255, 255, 255, .05);

  --color-quote: rgba(158, 174, 242, .1);
  --color-quote-text: rgba(222, 225, 241, 0.8);
  --color-quote-border: rgba(158, 174, 242, .1);

  --color-quote-tip: var(--color-quote);
  --color-quote-tip-text: var(--color-quote-text);
  --color-quote-tip-border: var(--color-quote-border);

  --color-quote-note: rgba(135, 223, 152, .15);
  --color-quote-note-text: rgba(229, 238, 219, .88);
  --color-quote-note-border: rgba(233, 248, 94, .17);

  --color-quote-warning: rgba(215, 65, 84, 0.2);
  --color-quote-warning-text: rgba(241, 229, 229, 0.9);
  --color-quote-warning-border: rgba(240, 158, 169, .18);

  /**
   * Overlay Text
   */
  --font-fallback-main: BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  --font-fallback-mono: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;

  --font-title: 'Roboto Condensed', var(--font-fallback-main);
  --font-mono: 'JetBrains Mono', var(--font-fallback-mono);
  --font-main: Inter, var(--font-fallback-main);

  --font-size: 17px;
  --font-line-height: 1.7;

  --font-size-secondary: 15px;
  --font-line-height-secondary: 1.6;

  --font-size-h1: 96px;
  --font-line-height-h1: 110%;

  --font-size-h2: 64px;
  --font-line-height-h2: 120%;

  --font-size-h3: 40px;
  --font-line-height-h3: 120%;

  --font-size-h4: 32px;
  --font-line-height-h4: 130%;

  --font-size-h5: 24px;
  --font-line-height-h5: 130%;

  --font-size-h6: 20px;
  --font-line-height-h6: 130%;

  /**
   * Overlay Width
   */
  --width-content: 90vw;
  --width-max: 1440px;
  --height-ui: 56px;
  --height-ui-small: 36px;

  /**
   * ---------------------------------------------------------------------------
   *  Button
   * ---------------------------------------------------------------------------
   */

  /**
   * Main button colors
   */
  --color-bg-button: #3A1309;
  --color-bg-button-opacity: rgba(var(--color-bg-button), .5);
  --color-bg-button-hover: #601A08;

  --color-text-button: var(--color-text-brand);

  /**
   * Secondary button background color
   */
  --color-bg-button-secondary: #151521;
  --color-bg-button-secondary-opacity: rgba(var(--color-bg-button-secondary), .5);
  --color-bg-button-secondary-hover: #1d1d2d;

  --color-text-button-secondary: var(--color-text);

  --landing-layout-gap: 4em;

  interpolate-size: allow-keywords;
}

:host {
  display: block;
  min-height: 100vh;
}

@media (orientation: portrait) {
  :root {
    --width-content: unset;
  }
  segment-section [slot="title"] br {
    display: none;
  }
}
