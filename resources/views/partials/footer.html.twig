{% if blog_categories is not defined %}
    {% set blog_categories = blog.categories %}
{% endif %}

{% if docs_version is not defined %}
    {% set docs_version = docs.currentVersion %}
{% endif %}

{% if docs_categories is not defined %}
    {% set docs_categories = docs_version.categories ?? [] %}
{% endif %}

<boson-footer>
    <a class="social" href="https://github.com/boson-php/boson" target="_blank" slot="main-link">
        <img src="/images/icons/github.svg" alt="github"/>
    </a>

    <a class="social" href="https://discord.gg/vCg52Jdwvc" target="_blank" slot="main-link">
        <img src="/images/icons/discord.svg" alt="discord"/>
    </a>

    <a class="social" href="https://t.me/boson_php" target="_blank" slot="main-link">
        <img src="/images/icons/telegram.svg" alt="telegram"/>
    </a>

    <a href="{{ path('doc.show', {version: docs_version.name, page: 'introduction'}) }}" slot="aside-link">
        Get started
    </a>

    <a href="{{ path('doc.index') }}" slot="aside-link">
        Documentation
    </a>

    <a href="{{ path('doc.show', {version: docs_version.name, page: 'contribution'}) }}" slot="secondary-link">
        Contribution Guide
    </a>

    <a href="{{ path('doc.show', {version: docs_version.name, page: 'license'}) }}" slot="secondary-link">
        License
    </a>

    <a href="{{ path('doc.show', {version: docs_version.name, page: 'release-notes'}) }}" slot="secondary-link">
        Release Notes
    </a>

    <small slot="copyright">
        BOSON PHP © 2025. All Rights Reversed.
    </small>
</boson-footer>
