{% if blog_categories is not defined %}
    {% set blog_categories = blog.categories %}
{% endif %}

{% if docs_version is not defined %}
    {% set docs_version = docs.currentVersion %}
{% endif %}

{% if docs_categories is not defined %}
    {% set docs_categories = docs_version.categories ?? [] %}
{% endif %}

<boson-header>
    <boson-button class="logo" type="ghost" slot="logo" href="{{ path('home') }}">
        <img class="logo" src="/images/logo.svg" alt="logo" />
    </boson-button>

    {% if docs_categories|length > 0 %}
        <boson-dropdown>
            <boson-button type="ghost" slot="summary"
                {{ app.current_route starts with 'doc.' ? 'active' : '' }}
                href="{{ path('doc.index') }}">
                References
            </boson-button>

            {% for docs_category in docs_categories %}
                <boson-button type="ghost"
                    {% if docs_category.page is not null and docs_version is not null %}
                        href="{{ path('doc.show', {
                            page: docs_category.page.uri,
                            version: docs_version.name
                        }) }}"
                    {% endif %}
                >
                    {% if docs_category.icon is not null %}
                        <img src="{{ docs_category.icon }}"
                             width="16" height="16" alt="{{ docs_category.title }}" />
                    {% endif %}

                    {{ docs_category.title }}
                </boson-button>
            {% endfor %}
        </boson-dropdown>
    {% endif %}

    {% if blog_categories|length > 0 %}
        <boson-dropdown>
            <boson-button type="ghost" slot="summary"
                {{ app.current_route starts with 'blog.' ? 'active' : '' }}
                href="{{ path('blog.index') }}">
                Blog
            </boson-button>

            {% for blog_category in blog_categories %}
            <boson-button type="ghost"
                    href="{{ path('blog.index_by_category', {
                    slug: blog_category.uri
                }) }}">
                    {{ blog_category.title }}
            </boson-button>
            {% endfor %}
        </boson-dropdown>
    {% endif %}

    {% if not (app.current_route starts with 'search') %}
    <boson-search-input
        action="{{ path('search.index') }}"
        query="{{ app.request.get('q') }}"></boson-search-input>
    {% endif %}

    <boson-button type="ghost" slot="aside" external href="https://github.com/boson-php/boson" pc="true">
        <img src="/images/icons/github.svg" alt="github"/>
        GitHub
    </boson-button>

    <boson-button type="ghost" slot="aside" external href="https://github.com/boson-php/boson" mobile="true">
        <img src="/images/icons/github.svg" alt="github"/>
    </boson-button>

    {% if docs.currentVersion %}
    <boson-button type="ghost" slot="aside" href="{{ path('doc.show', {
        version: docs.currentVersion.name,
        page: 'introduction'
    }) }}">
        Get Started
        <img src="/images/icons/arrow_up_right.svg" alt="arrow_up_right" />
    </boson-button>
    {% endif %}

    <mobile-header-menu slot="mobile-menu">
        {% if docs_categories|length > 0 %}
            <div slot="references">
                {% for docs_category in docs_categories %}
                    <boson-button type="ghost"
                        inheader="true"
                        slot="references"
                        {% if docs_category.page is not null and docs_version is not null %}
                            href="{{ path('doc.show', {
                                page: docs_category.page.uri,
                                version: docs_version.name
                            }) }}"
                        {% endif %}
                    >
                        {% if docs_category.icon is not null %}
                            <img src="{{ docs_category.icon }}"
                                 width="16" height="16" alt="{{ docs_category.title }}" />
                        {% endif %}
                        {{ docs_category.title }}
                    </boson-button>
                {% endfor %}
            </div>
        {% endif %}

        {% if blog_categories|length > 0 %}
            <div slot="blog">
                {% for blog_category in blog_categories %}
                    <boson-button type="ghost"
                        inheader="true"
                        slot="blog"
                        href="{{ path('blog.index_by_category', {
                            slug: blog_category.uri
                        }) }}">
                        {{ blog_category.title }}
                    </boson-button>
                {% endfor %}
            </div>
        {% endif %}

        <div slot="actions" class="menu-section">
            <boson-button type="ghost" external href="https://github.com/boson-php/boson">
                <img src="/images/icons/github.svg" alt="github"/>
                GitHub
            </boson-button>

            <boson-button type="ghost" href="{{ path('doc.show', {
                version: docs_version.name,
                page: 'introduction'
            }) }}">
                Get Started
                <img src="/images/icons/arrow_up_right.svg" alt="arrow_up_right" />
            </boson-button>
        </div>

        {% if not (app.current_route starts with 'search') %}
            <div slot="search" class="menu-section">
                <boson-search-input
                    action="{{ path('search.index') }}"
                    query="{{ app.request.get('q') }}">
                </boson-search-input>
            </div>
        {% endif %}
    </mobile-header-menu>
</boson-header>
