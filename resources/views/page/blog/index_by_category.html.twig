{% extends 'layout/master.html.twig' %}

{% block title %}
    {{ category.title }} :: {{ parent() }}
{% endblock %}

{% block body %}

    <boson-default-layout>
        <boson-page-title>
            <h2>&laquo{{ category.title }}&raquo; blog posts</h2>
        </boson-page-title>

        <boson-breadcrumbs>
            {% apply spaceless %}
                <div class="breadcrumb-item">
                    <boson-button type="ghost" href="{{ path('home') }}">
                        Home
                    </boson-button>
                </div>

                <div class="breadcrumb-item">
                    <boson-button type="ghost" href="{{ path('blog.index') }}">
                        Blog
                    </boson-button>
                </div>

                <div class="breadcrumb-item">
                    <boson-button type="ghost">
                        {{ category.title }}
                    </boson-button>
                </div>
            {% endapply %}
        </boson-breadcrumbs>

    <div style="display: grid; grid-template-columns: 2fr 1fr;">
        <section>
            {% include 'page/blog/partial/articles_list.html.twig' with {
                articles: articles
            } %}

            <footer style="display: grid; grid-template-columns: 1fr 1fr 1fr;">
                <span>
                    {% if page > 1 %}
                        <a style="float: left" href="{{ path('blog.index_by_category', {
                            page: 1,
                            slug: category.uri
                        }) }}">&lt;&lt; first page</a>

                        <a style="float: left; margin-left: 16px;" href="{{ path('blog.index_by_category', {
                            page: page - 1,
                            slug: category.uri
                        }) }}">&lt; newest posts</a>
                    {% endif %}
                </span>

                <span style="text-align: center">{{ page }} of {{ articles.pages }}</span>

                <span>
                    {% if page < articles.pages %}
                        <a style="float: right" href="{{ path('blog.index_by_category', {
                            page: articles.pages,
                            slug: category.uri
                        }) }}">last page &gt;&gt;</a>

                        <a style="float: right; margin-right: 16px;" href="{{ path('blog.index_by_category', {
                            page: page + 1,
                            slug: category.uri
                        }) }}">older posts &gt;</a>
                    {% endif %}
                </span>
            </footer>
        </section>

        {% include 'page/blog/partial/categories_list.html.twig' with {
            categories: categories,
            category: category
        } %}
    </div>

    </boson-default-layout>
{% endblock %}
