{% extends 'layout/master.html.twig' %}

{% block title %}
    Blog :: {{ parent() }}
{% endblock %}

{% block body %}

    <boson-page-title>
        <h2>Blog</h2>
    </boson-page-title>

    <boson-breadcrumbs>
        {% apply spaceless %}
            <div class="breadcrumb-item">
                <boson-button type="ghost" href="{{ path('home') }}">
                    Home
                </boson-button>
            </div>

            <div class="breadcrumb-item">
                <boson-button type="ghost">
                    Blog
                </boson-button>
            </div>
        {% endapply %}
    </boson-breadcrumbs>

    <boson-blog-layout>
        {% if app.request.get('_route') == 'blog.index' %}
            <strong slot="sidebar">All Blog Posts</strong>
        {% else %}
            <a slot="sidebar" href="{{ path('blog.index') }}">All Blog Posts</a>
        {% endif %}

        {% for available_category in categories %}
            {% if category is defined and category.uri == available_category.uri %}
                <strong slot="sidebar">{{ available_category.title }}</strong>
            {% else %}
                <a slot="sidebar" href="{{ path('blog.index_by_category', {
                    slug: available_category.uri
                }) }}">{{ available_category.title }}</a>
            {% endif %}
        {% endfor %}

        {% include 'page/blog/partial/articles_list.html.twig' with {
            articles: articles
        } %}

        <footer style="display: grid; grid-template-columns: 1fr 1fr 1fr; margin-top: 2em;">
            <span>
                {% if page > 1 %}
                    <a style="float: left" href="{{ path('blog.index', {
                       page: 1
                    }) }}">&lt;&lt; first page</a>

                    <a style="float: left; margin-left: 16px;" href="{{ path('blog.index', {
                        page: page - 1
                    }) }}">&lt; newest posts</a>
                {% endif %}
            </span>

            <span style="text-align: center">{{ page }} of {{ articles.pages }}</span>

            <span>
                {% if page < articles.pages %}
                    <a style="float: right" href="{{ path('blog.index', {
                        page: articles.pages
                    }) }}">last page &gt;&gt;</a>

                    <a style="float: right; margin-right: 16px;" href="{{ path('blog.index', {
                        page: page + 1
                    }) }}">older posts &gt;</a>
                {% endif %}
            </span>
        </footer>

    </boson-blog-layout>
{% endblock %}
