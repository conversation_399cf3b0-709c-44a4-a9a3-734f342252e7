<boson-dropdown>
    {% if app.current_route == 'doc.index_by_version' %}
        <boson-button type="ghost" slot="summary">
            Documentation ({{ version.name }})
        </boson-button>
    {% else %}
        <boson-button type="ghost"
                      slot="summary"
                      href="{{ path('doc.index_by_version', { version: version.name }) }}">
            Documentation ({{ version.name }})
        </boson-button>
    {% endif %}

    {% for dev in versions.dev %}
        <boson-button type="ghost"
            {% if page is defined %}
                href="{{ path('doc.show', { page: page.uri, version: dev.name }) }}"
            {% else %}
                href="{{ path('doc.index_by_version', { version: dev.name }) }}"
            {% endif %}
        >
            {{ dev.name }}
        </boson-button>
    {% endfor %}

    {% for stable in versions.stable %}
        <boson-button type="ghost"
            {% if page is defined %}
                href="{{ path('doc.show', { page: page.uri, version: stable.name }) }}"
            {% else %}
                href="{{ path('doc.index_by_version', { version: stable.name }) }}"
            {% endif %}
        >
            {{ stable.name }}
        </boson-button>
    {% endfor %}

    {% if versions.deprecated|length > 0 %}
        <strong>Unmaintained Versions</strong>
    {% endif %}

    {% for deprecated in versions.deprecated %}
        <boson-button type="ghost"
            {% if page is defined %}
                href="{{ path('doc.show', { page: page.uri, version: deprecated.name }) }}"
            {% else %}
                href="{{ path('doc.index_by_version', { version: deprecated.name }) }}"
            {% endif %}
        >
            {{ deprecated.name }}
        </boson-button>
    {% endfor %}
</boson-dropdown>
