{% extends 'layout/master.html.twig' %}

{% block title %}
    Documentation :: {{ parent() }}
{% endblock %}

{% block body %}

    <boson-page-title>
        <h2>{{ category.title }}</h2>
    </boson-page-title>

    <boson-breadcrumbs>
        {% apply spaceless %}
            <div class="breadcrumb-item">
                <boson-button type="ghost" href="{{ path('home') }}">
                    Home
                </boson-button>
            </div>

            <div class="breadcrumb-item">
                {% include 'page/docs/partials/version_selector.html.twig' with {
                    page: page,
                    version: version,
                    versions: versions,
                } %}
            </div>

            <div class="breadcrumb-item">
                <boson-button type="ghost"
                    {% if category.icon is not null %}
                        icon="{{ category.icon }}"
                    {% endif %}
                >
                    {{ category.title }}
                </boson-button>
            </div>
        {% endapply %}
    </boson-breadcrumbs>

    <boson-docs-layout>
        {% for nav_page in category.pages %}

            {% if page.uri == nav_page.uri %}
                <strong slot="menu">{{ nav_page.title }}</strong>
            {% else %}
                {% if nav_page.type.name == 'Link' %}
                    <a slot="menu" href="{{ nav_page.uri }}" target="_blank">
                        {{ nav_page.title }}
                        <img src="https://intellij-icons.jetbrains.design/icons/AllIcons/expui/ide/externalLink_dark.svg"
                             width="18" height="18" />
                        &nbsp;
                    </a>
                {% else %}
                    <a slot="menu" href="{{ path('doc.show', {
                        page: nav_page.uri,
                        version: version.name
                    }) }}">
                        {{ nav_page.title }}
                    </a>
                {% endif %}
            {% endif %}

        {% endfor %}

        {% for nav_category in version.categories %}
            <a slot="category" href="{{ path('doc.show', {
                page: nav_category.page.uri,
                version: version.name
            }) }}">
                {% if nav_category.icon is not null %}
                    <img src="{{ nav_category.icon }}"
                         width="16" height="16" />
                {% endif %}
                {{ nav_category.title }}
            </a>
        {% endfor %}

        <section class="documentation">
            <h1>{{ page.title }}</h1>

            <article>
                {{ page.content | replace({ '%25version%25': version.name }) | raw }}
            </article>
        </section>
    </boson-docs-layout>

{% endblock %}
