{% extends 'layout/master.html.twig' %}

{% block title %}
    Page Not Found :: {{ parent() }}
{% endblock %}

{% block body %}

    <boson-default-layout>


        <section class="error-content">
            <hgroup style="padding: 3em 0; text-align: center;">
                <h1 style="font-size: 4em; color: #F93904; margin-bottom: 0.5em;">404</h1>
                <h2 style="margin-bottom: 1em;">Oops! Page not found</h2>
                <p style="font-size: 1.2em; margin-bottom: 2em; opacity: 0.8;">
                    The page you're looking for doesn't exist or has been moved.
                </p>
                
                <div style="display: flex; gap: 1em; justify-content: center; flex-wrap: wrap;">
                    <boson-button href="{{ path('home') }}" icon="/images/icons/arrow_primary.svg">
                        Go Home
                    </boson-button>
                    
                    <boson-button type="secondary" href="{{ path('doc.show', {
                        version: '0.15',
                        page: 'introduction'
                    }) }}" icon="/images/icons/arrow_secondary.svg">
                        Documentation
                    </boson-button>
                </div>
            </hgroup>

            <div style="margin-top: 3em; padding: 2em; background: rgba(249, 57, 4, 0.1); border-radius: 8px;">
                <h3 style="margin-bottom: 1em;">What can you do?</h3>
                <ul style="list-style: none; padding: 0;">
                    <li style="margin-bottom: 0.5em;">
                        <strong>🏠 Go back to homepage</strong> - Start fresh from our main page
                    </li>
                    <li style="margin-bottom: 0.5em;">
                        <strong>📚 Check documentation</strong> - Learn about Boson PHP framework
                    </li>
                    <li style="margin-bottom: 0.5em;">
                        <strong>🔍 Use search</strong> - Find what you're looking for
                    </li>
                    <li>
                        <strong>📧 Contact us</strong> - If you think this is an error
                    </li>
                </ul>
            </div>
        </section>

    </boson-default-layout>
{% endblock %}
